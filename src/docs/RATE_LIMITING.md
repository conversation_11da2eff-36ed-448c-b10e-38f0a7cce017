# Rate Limiting Implementation Guide

Tento dokument popisuje implementáciu komplexného rate limiting systému v Swapka aplikácii.

## Prehľad

Rate limiting je implementovaný na úrovni middleware a automaticky sa aplikuje na všetky API endpointy na základe URL patterns. Použ<PERSON>va in-memory riešenie s express-rate-limit knižnicou pre vysoký výkon a jednoduchosť nasadenia.

## Konfigurácia

### Environment Variables

```env
# Rate Limiting Configuration
RATE_LIMIT_ENABLED=true
RATE_LIMIT_AUTH_MAX=5
RATE_LIMIT_AUTH_WINDOW_MS=60000
RATE_LIMIT_GENERAL_MAX=100
RATE_LIMIT_GENERAL_WINDOW_MS=60000
RATE_LIMIT_ADMIN_MAX=20
RATE_LIMIT_ADMIN_WINDOW_MS=60000
RATE_LIMIT_UPLOAD_MAX=10
RATE_LIMIT_UPLOAD_WINDOW_MS=60000
RATE_LIMIT_ATTACK_MAX=10
RATE_LIMIT_ATTACK_WINDOW_MS=60000
RATE_LIMIT_SKIP_SUCCESSFUL_REQUESTS=false
RATE_LIMIT_SKIP_FAILED_REQUESTS=false
RATE_LIMIT_WHITELIST_IPS=""
```

### Typy Rate Limitov

| Typ | Limit | Okno | Popis | URL Patterns |
|-----|-------|------|-------|--------------|
| **Auth** | 5 req/min | 60s | Autentifikačné operácie | `/api/auth/`, `/api/users/route` (POST), `/api/csrf/` |
| **General** | 100 req/min | 60s | Štandardné API operácie | Všetky ostatné `/api/*` endpointy |
| **Admin** | 20 req/min | 60s | Admin operácie | `/api/admin/`, `/anonymize`, `/api/toy-types/`, `/api/cache-bust/` |
| **Upload** | 10 req/min | 60s | Upload súborov | `/api/cloudinary/upload`, `/api/toys/create` |
| **Attack** | 10 req/min | 60s | **Ochrana proti útokom** | **WordPress, CMS, exploit patterns** |

## Automatická detekcia

Rate limiting sa aplikuje automaticky na základe URL pattern bez potreby manuálnej konfigurácie v každom endpointe:

```typescript
// Automaticky dostane "auth" rate limit (5 req/min)
// /api/auth/login

// Automaticky dostane "admin" rate limit (20 req/min)  
// /api/admin/users

// Automaticky dostane "upload" rate limit (10 req/min)
// /api/cloudinary/upload

// Automaticky dostane "general" rate limit (100 req/min)
// /api/toys
```

## HTTP Headers

### Request Headers
Rate limiting middleware automaticky číta tieto headers pre identifikáciu IP adresy:
- `x-forwarded-for` (priorita 1)
- `x-real-ip` (priorita 2) 
- `cf-connecting-ip` (priorita 3)

### Response Headers
Každá odpoveď obsahuje rate limit informácie:
```
X-RateLimit-Limit: 100
X-RateLimit-Remaining: 95
X-RateLimit-Reset: 2024-01-15T10:30:00.000Z
X-RateLimit-Type: general
```

### 429 Too Many Requests Response
```json
{
  "error": "Príliš veľa požiadaviek",
  "message": "Príliš veľa pokusov o prihlásenie. Skúste to znovu neskôr."
}
```

**Bezpečnostné vylepšenia:**
- ✅ Odstránené presné časové údaje (napr. "za 60 sekúnd")
- ✅ Odstránené konkrétne limity (napr. "5 požiadaviek")
- ✅ Všeobecné formulácie namiesto špecifických čísel
- ✅ Odstránený `Retry-After` header pre bezpečnosť
- ✅ Kontextové správy pre rôzne typy endpointov

## Whitelist IP Adries

Dôveryhodné IP adresy môžu byť vynechané z rate limiting:

```env
RATE_LIMIT_WHITELIST_IPS="127.0.0.1,::1,*************,********"
```

## Admin Monitoring

### Získanie štatistík
```bash
GET /api/admin/rate-limit
```

Response:
```json
{
  "config": {
    "enabled": true,
    "auth": { "max": 5, "windowMs": 60000 },
    "general": { "max": 100, "windowMs": 60000 }
  },
  "stats": {
    "totalEntries": 15,
    "entriesByType": {
      "auth": 3,
      "general": 10,
      "admin": 2
    }
  }
}
```

### Vyčistenie záznamov pre konkrétnu IP
```bash
DELETE /api/admin/rate-limit?ip=*************
```

### Vyčistenie všetkých záznamov
```bash
DELETE /api/admin/rate-limit?all=true
```

## Testovanie

### Test endpoint
```bash
# General rate limit test (100 req/min)
GET /api/test/rate-limit

# Auth rate limit test (5 req/min) 
POST /api/test/rate-limit
```

### Manuálne testovanie
```bash
# Rýchle testovanie auth limitu
for i in {1..10}; do
  curl -X POST http://localhost:3000/api/test/rate-limit
  echo "Request $i"
done

# Testovanie attack pattern detection
curl http://localhost:3000/wp-admin/setup-config.php
curl http://localhost:3000/wordpress/wp-admin/
curl http://localhost:3000/phpmyadmin
curl http://localhost:3000/.env
```

## Bezpečnostné aspekty

### Logging
- Prekročenia limitov sa logujú pre monitoring
- IP adresy sú čiastočne skryté v logoch (privacy)
- Žiadne citlivé údaje sa nelogujú

### Privacy
- IP adresy sú sanitizované v logoch a odpovediach
- Iba prvých 8 znakov IP adresy + "***"

### DDoS ochrana
- Sliding window algoritmus
- In-memory store s automatickým čistením
- Rôzne limity pre rôzne typy operácií

## Výkon

### In-Memory Store
- Vysoký výkon bez externých závislostí
- Automatické čistenie expirovaných záznamov každých 5 minút
- Optimalizované pre single-instance deployment

### Memory Usage
- Každý záznam: ~100 bytes
- 1000 aktívnych IP adries: ~100KB
- Automatické garbage collection

## Troubleshooting

### Rate limiting nefunguje
1. Skontrolujte `RATE_LIMIT_ENABLED=true` v .env
2. Overte, že middleware je správne načítaný
3. Skontrolujte logy pre chybové hlásenia

### Nesprávne IP adresy
1. Skontrolujte proxy konfiguráciu
2. Overte `x-forwarded-for` headers
3. Použite admin endpoint pre debugging

### Vysoké memory usage
1. Skontrolujte počet aktívnych IP adries
2. Znížte window time pre častejšie čistenie
3. Implementujte Redis store pre production

## Rozšírenia

### Redis Store (budúcnosť)
Pre distribuované nasadenie môže byť pridaný Redis store:

```typescript
import RedisStore from 'rate-limit-redis';
import { createClient } from 'redis';

const redisClient = createClient({
  url: process.env.REDIS_URL
});

const store = new RedisStore({
  sendCommand: (...args: string[]) => redisClient.sendCommand(args),
});
```

### Dynamická konfigurácia
Možnosť zmeny limitov bez reštartu aplikácie cez admin interface.

### Geografické rate limiting
Rôzne limity pre rôzne krajiny/regióny.

## Bezpečnostné vylepšenia

### Ochrana proti information disclosure
- ✅ **Všeobecné error správy** bez odhalenia presných limitov
- ✅ **Odstránené časové údaje** z error správ (napr. "za 60 sekúnd")
- ✅ **Kontextové správy** pre rôzne typy endpointov
- ✅ **Odstránený Retry-After header** pre bezpečnosť
- ✅ **Obmedzené rate limit headers** (iba základné informácie)

### Príklady bezpečných správ
```json
// Auth endpoints
{
  "error": "Príliš veľa požiadaviek",
  "message": "Príliš veľa pokusov o prihlásenie. Skúste to znovu neskôr."
}

// Upload endpoints
{
  "error": "Príliš veľa požiadaviek",
  "message": "Príliš veľa nahrávaní súborov. Skúste to znovu neskôr."
}

// Admin endpoints
{
  "error": "Príliš veľa požiadaviek",
  "message": "Príliš veľa požiadaviek na admin operácie. Skúste to znovu neskôr."
}

// General endpoints
{
  "error": "Príliš veľa požiadaviek",
  "message": "Príliš veľa požiadaviek. Skúste to znovu neskôr."
}

// Attack patterns
{
  "error": "Príliš veľa požiadaviek",
  "message": "Príliš veľa podozrivých požiadaviek. Prístup bol dočasne obmedzený."
}
```

## Ochrana proti útokom

### Attack Pattern Detection
Automatická detekcia a blokovanie známych attack patterns:

**WordPress patterns:**
- `/wp-admin/*` - WordPress admin panel
- `/wp-login.php` - WordPress login
- `/wp-config.php` - WordPress konfigurácia
- `/xmlrpc.php` - WordPress XML-RPC
- `/wp-admin/setup-config.php` - WordPress setup

**CMS patterns:**
- `/admin.php` - Všeobecný admin panel
- `/administrator` - Joomla admin
- `/phpmyadmin` - phpMyAdmin
- `/mysql` - MySQL admin

**Exploit patterns:**
- `/.env` - Environment súbory
- `/.git` - Git repository
- `/config.php` - Konfiguračné súbory
- `/database.php` - Databázové súbory
- `/backup` - Backup súbory

**API abuse patterns:**
- `/api/v1`, `/api/v2` - Neexistujúce API verzie
- `/rest/api` - REST API abuse
- `/graphql` - GraphQL abuse

### Implementácia
```typescript
// Automatická detekcia v middleware
export function isAttackPattern(pathname: string): boolean {
  const attackPatterns = [
    '/wp-admin', '/wp-login.php', '/phpmyadmin', '/.env', ...
  ];
  return attackPatterns.some(pattern =>
    pathname.toLowerCase().includes(pattern.toLowerCase())
  );
}

// Attack patterns majú najvyššiu prioritu v rate limiting
if (isAttackPattern(pathname)) {
  return { type: 'attack', ...rateLimitConfig.attack };
}
```

### Monitoring
- Všetky attack attempts sa logujú s IP adresou (sanitizovanou)
- Štatistiky dostupné cez admin endpoint
- Možnosť manuálneho vyčistenia pre konkrétnu IP

## Compliance

Implementácia dodržiava:
- ✅ OWASP rate limiting guidelines
- ✅ Privacy requirements (IP sanitization)
- ✅ Security best practices (information disclosure prevention)
- ✅ Performance optimization
- ✅ Monitoring and logging standards
- ✅ Defense against timing attacks
