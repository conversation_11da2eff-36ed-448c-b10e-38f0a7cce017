# Oprava mazania fotografií z Cloudinary pri úprave hračiek

## Problém

Pri úprave hračiek sa fotografie **NEMAŽÚ z Cloudinary** pri ich odstránení. Konkrétne:

- **`/api/toys/update`** - mazal len z databázy, nie z Cloudinary
- **`/api/admin/toys/update`** - mazal len z datab<PERSON>zy, nie z Cloudinary  
- **CloudinaryUpload komponent** - odstraňoval len z lokálneho stavu

Toto spôsobovalo nahromadenie nepoužívaných súborov v Cloudinary úložisku.

## Riešenie

### 1. Upravené API endpointy

#### `/api/toys/update` (pre bežných používateľov)
- **Súbor**: `src/app/api/toys/update/route.ts`
- **Pridané**: Import `deleteCloudinaryImage` funkcie
- **Logika**: Pred mazaním z databázy sa získajú metadáta existujúcich fotografií a následne sa mažú z Cloudinary

#### `/api/admin/toys/update` (pre administrátorov)  
- **Súbor**: `src/app/api/admin/toys/update/route.ts`
- **Pridané**: Import `deleteCloudinaryImage` funkcie
- **Logika**: Rovnaká ako pre bežných používateľov, ale s admin oprávneniami

### 2. Implementovaný proces mazania

```typescript
// 1. Získanie existujúcich fotografií pred mazaním z databázy
const existingImages = await prisma.toyImage.findMany({
  where: { toyId },
  select: {
    id: true,
    url: true,
    hashedFilename: true,
    originalFilename: true
  }
});

// 2. Mazanie z databázy
await prisma.toyImage.deleteMany({
  where: { toyId },
});

// 3. Mazanie z Cloudinary
for (const image of existingImages) {
  try {
    const result = await deleteCloudinaryImage(image.url, image.hashedFilename || undefined);
    console.log(`Výsledok odstránenia obrázku: ${result ? 'úspešné' : 'neúspešné'}`);
  } catch (imageError) {
    console.error(`Chyba pri odstraňovaní obrázku z Cloudinary:`, imageError);
  }
}

// 4. Pridanie nových fotografií
for (const imageUrl of data.images) {
  await prisma.toyImage.create({
    data: { url: imageUrl, toyId },
  });
}
```

### 3. Error handling

- **Graceful degradation**: Ak zlyhá mazanie z Cloudinary, aplikácia pokračuje
- **Individuálne spracovanie**: Každá fotografia sa maže samostatne
- **Detailné logovanie**: Všetky operácie sa logujú pre debugging

### 4. Bezpečnostné aspekty

- **Autentifikácia**: Zachované pôvodné autentifikačné mechanizmy
- **Autorizácia**: Používatelia môžu upravovať len svoje hračky, admini všetky
- **Validácia**: Zachovaná pôvodná validácia vstupných dát

## Testovanie

### Vytvorený test súbor
- **Súbor**: `src/tests/toy-photo-deletion.test.js`
- **Pokrytie**: 
  - Mazanie fotografií pri úprave hračky používateľom
  - Mazanie fotografií pri úprave hračky administrátorom
  - Error handling pri zlyhaní Cloudinary API
  - Edge cases (žiadne existujúce fotografie, chýbajúce hashedFilename)

### Spustenie testov
```bash
cd src
npm test toy-photo-deletion.test.js
```

## Ovplyvnené súbory

### Upravené súbory:
1. `src/app/api/toys/update/route.ts`
   - Pridaný import `deleteCloudinaryImage`
   - Implementovaná logika mazania z Cloudinary

2. `src/app/api/admin/toys/update/route.ts`
   - Pridaný import `deleteCloudinaryImage`
   - Implementovaná logika mazania z Cloudinary

### Nové súbory:
1. `src/tests/toy-photo-deletion.test.js`
   - Kompletné testy pre overenie funkcionality

2. `src/docs/toy-photo-deletion-fix.md`
   - Táto dokumentácia

## Porovnanie s existujúcou implementáciou

Riešenie je konzistentné s už fungujúcim mazaním fotografií pri odstránení celých hračiek:

- **`/api/toys/delete`** - už správne maže z Cloudinary ✅
- **`/api/admin/toys/delete`** - už správne maže z Cloudinary ✅
- **`/api/toys/update`** - teraz správne maže z Cloudinary ✅
- **`/api/admin/toys/update`** - teraz správne maže z Cloudinary ✅

## Výhody riešenia

1. **Konzistentnosť**: Rovnaký prístup ako pri mazaní celých hračiek
2. **Bezpečnosť**: Zachované všetky bezpečnostné mechanizmy
3. **Robustnosť**: Error handling zabezpečuje stabilitu aplikácie
4. **Udržateľnosť**: Použitie existujúcich funkcií z `cloudinary.ts`
5. **Testovateľnosť**: Kompletné testy pre overenie funkcionality

## Budúce vylepšenia

1. **Batch deletion**: Optimalizácia pre mazanie viacerých fotografií naraz
2. **Retry mechanizmus**: Automatické opakovanie pri dočasných chybách Cloudinary API
3. **Monitoring**: Pridanie metrík pre sledovanie úspešnosti mazania fotografií

## Záver

Problém s nahromadením nepoužívaných fotografií v Cloudinary je vyriešený. Všetky endpointy pre úpravu hračiek teraz správne mažú staré fotografie z Cloudinary úložiska pred pridaním nových.
