# Implementácia komplexnej sanitizácie vstupov

Tento dokument popisuje implementáciu komplexnej sanitizácie vstupov v Swapka aplikácii.

## Prehľad implementácie

### 1. Centrálna sanitizačná knižnica

**Súbor:** `src/lib/inputSanitization.ts`

Obsahuje kompletný set sanitizačných funkcií pre všetky typy vstupov:

- `sanitizeText()` - sanitizácia textových vstupov
- `sanitizeEmail()` - validácia a sanitizácia emailov
- `sanitizeNumericInput()` - sanitizácia číselných vstupov
- `sanitizeSearchQuery()` - sanitizácia vyhľadávacích dotazov s ochranou proti SQL injection
- `sanitizeHtml()` - sanitizácia HTML obsahu
- `validateFileUpload()` - valid<PERSON>cia nahrávaných súborov
- `sanitizeCoordinates()` - sanitizácia geografických súradníc
- `sanitizePhoneNumber()` - sanitizácia telefónnych čísel
- `sanitizeUrl()` - sanitizácia URL adries
- `sanitizeObject()` - komplexná sanitizácia objektov s viacerými poľami

### 2. Client-side validácia

**Súbor:** `src/lib/clientValidation.ts`

Poskytuje okamžitú spätnú väzbu používateľom:

- Validácia formulárových polí v reálnom čase
- Používateľsky prívetivé chybové správy
- Varovania pre optimalizáciu (napr. veľkosť súborov)

**DÔLEŽITÉ:** Client-side validácia je len pre UX. Server-side validácia je vždy povinná!

### 3. Aktualizované API endpointy

Všetky API endpointy boli aktualizované na použitie sanitizačných funkcií:

#### 3.1 Users endpoint (`/api/users/route.ts`)
- Sanitizácia emailu, mena, role
- Validácia povinných polí
- Detailné chybové správy

#### 3.2 Toys endpoint (`/api/toys/route.ts`)
- Sanitizácia všetkých URL parametrov
- Ochrana vyhľadávacích dotazov proti SQL injection
- Validácia geografických súradníc

#### 3.3 Cloudinary upload (`/api/cloudinary/upload/route.ts`)
- Komplexná validácia súborov
- Kontrola typu, veľkosti a prípony súborov
- Bezpečné generovanie názvov súborov

#### 3.4 Toy types (`/api/toy-types/route.ts`)
- Sanitizácia názvov a labelov typov hračiek
- Použitie template literal syntax pre databázové dotazy

#### 3.5 Reservations (`/api/reservations/route.ts`)
- Sanitizácia ID hračiek
- Validácia hashovaných ID

#### 3.6 User profile (`/api/users/[id]/route.ts`)
- Rôzne sanitizačné schémy pre používateľov a adminov
- Sanitizácia telefónnych čísel, adries, súradníc
- Bezpečné logovanie bez citlivých údajov

### 4. Databázové operácie

#### 4.1 Type-safe Prisma metódy
- Preferované pre štandardné operácie
- Automatická ochrana proti SQL injection

#### 4.2 Template literal syntax
- Pre raw SQL dotazy používame `$executeRaw` template literal syntax
- Nahradené všetky `$executeRawUnsafe` volania

**Príklad:**
```typescript
// ✅ SPRÁVNE
await prisma.$executeRaw`INSERT INTO ToyType (name, label) VALUES (${name}, ${label})`;

// ❌ NESPRÁVNE
await prisma.$executeRawUnsafe(`INSERT INTO ToyType (name, label) VALUES ('${name}', '${label}')`);
```

### 5. Bezpečnostné opatrenia

#### 5.1 XSS ochrana
- Odstránenie nebezpečných HTML tagov
- Filtrovanie JavaScript kódu
- Enkódovanie špeciálnych znakov

#### 5.2 SQL Injection ochrana
- Template literal syntax pre raw SQL
- Filtrovanie SQL kľúčových slov vo vyhľadávacích dotazoch
- Type-safe Prisma metódy

#### 5.3 File upload bezpečnosť
- Validácia MIME typov
- Kontrola veľkosti súborov
- Sanitizácia názvov súborov
- Kontrola prípony súborov

#### 5.4 Input validácia
- Kontrola dĺžky vstupov
- Validácia formátov (email, telefón, súradnice)
- Filtrovanie nebezpečných znakov

### 6. Aktualizované bezpečnostné pravidlá

**Súbor:** `rules/security_sql.md`

Rozšírené o:
- Pravidlá pre použitie centrálnej sanitizačnej knižnice
- Príklady sanitizácie pre rôzne typy vstupov
- Aktualizovaný compliance checklist
- Pravidlá pre databázové operácie

### 7. Implementované funkcie

#### 7.1 Sanitizácia textových vstupov
```typescript
const result = sanitizeText(input, {
  maxLength: 100,
  allowSpecialChars: false,
  removeHtml: true
});
```

#### 7.2 Validácia súborov
```typescript
const validation = validateFileUpload(file, {
  allowedTypes: ['image/jpeg', 'image/png'],
  maxSize: 5 * 1024 * 1024,
  allowedExtensions: ['jpg', 'png']
});
```

#### 7.3 Komplexná sanitizácia objektov
```typescript
const result = sanitizeObject(data, {
  email: (value) => sanitizeEmail(value),
  name: (value) => sanitizeText(value, { maxLength: 100 }),
  phone: (value) => sanitizePhoneNumber(value)
});
```

### 8. Testovanie

Pre testovanie sanitizačných funkcií:

1. **Unit testy** - testovanie jednotlivých sanitizačných funkcií
2. **Integration testy** - testovanie API endpointov s rôznymi vstupmi
3. **Security testy** - testovanie proti XSS, SQL injection útokom

### 9. Monitoring a logovanie

- Bezpečné logovanie bez citlivých údajov
- Monitoring neplatných vstupov
- Štatistiky sanitizačných operácií

### 10. Výkon

- Optimalizované sanitizačné funkcie
- Minimálny dopad na výkon API
- Efektívne validačné algoritmy

## Záver

Implementácia poskytuje komplexnú ochranu proti:
- XSS útokom
- SQL injection
- File upload vulnerabilities
- Input validation bypasses
- Data corruption

Všetky vstupy sú teraz sanitizované na viacerých úrovniach s detailnou validáciou a bezpečným spracovaním.
