# Funkcia Typ Ceny - Dokumentácia

## Prehľad
Táto funkcia pridáva možnosť výberu typu ceny pre hračky v administračnom rozhraní. Majitelia hračiek si môžu vybrať medzi:
- **<PERSON><PERSON> za <PERSON>** (PER_DAY) - tradi<PERSON>n<PERSON> model denného prenájmu
- **Cena za výpožičku** (PER_RENTAL) - paušálna cena za celú výpožičku

## Zmeny v databáze

### Nový stĺpec v tabuľke Toy
```sql
ALTER TABLE `Toy` ADD COLUMN `priceType` VARCHAR(191) NOT NULL DEFAULT 'PER_DAY';
```

**Možné hodnoty:**
- `PER_DAY` - cena za de<PERSON> (predvolená hodnota)
- `PER_RENTAL` - cena za výpožičku

## Zmeny v kóde

### 1. Databázov<PERSON> schéma (Prisma)
- Pridaný `priceType` field do `Toy` modelu
- Predvolená hodnota: `"PER_DAY"`

### 2. API endpointy
Aktualizované endpointy:
- `/api/toys/create` - validácia a uloženie priceType
- `/api/toys/update` - validácia a aktualizácia priceType
- `/api/admin/toys/update` - admin aktualizácia priceType
- `/api/toys/init` - inicializácia s predvoleným priceType

### 3. Používateľské rozhranie

#### Admin rozhranie
- **Stránka:** `/admin/toys/edit/[id]`
- **Nové pole:** Dropdown "Typ ceny" s možnosťami:
  - "Cena za deň"
  - "Cena za výpožičku"

#### Používateľské rozhranie
- **Stránka:** `/moje-hracky/nova`
- **Nové pole:** Rovnaký dropdown ako v admin rozhraní

### 4. Zobrazenie cien
Aktualizované komponenty:
- Zoznam hračiek (`/hracky`)
- Detail hračky (`/hracky/[id]`)
- Moje hračky (`/moje-hracky`)
- Rezervácie (`/rezervacie`)
- Profil používateľa (`/profil/[userHash]`)
- Admin zoznam hračiek (`/admin/toys`)

### 5. Utility funkcie
Aktualizované funkcie v `src/lib/priceUtils.ts`:
- `formatToyPricePerDay()` - pridaný parameter priceType
- `formatToyPriceWithEuro()` - pridaný parameter priceType
- `formatToyPriceAdmin()` - pridaný parameter priceType
- Nové funkcie:
  - `getPriceTypeLabel()` - získanie slovenského názvu typu ceny
  - `getPriceTypeSuffix()` - získanie prípony pre typ ceny

## Migrácia

### Spustenie migrácie
```bash
# Cez Prisma (odporúčané)
cd src
npx prisma db push

# Alebo priamo v databáze
mysql -u username -p database_name < database/migrations/add_price_type_column.sql
```

### Rollback (ak je potrebný)
```bash
mysql -u username -p database_name < database/migrations/rollback_price_type_column.sql
```

## Testovanie

### Funkčné testovanie
1. **Admin rozhranie:**
   - Vytvorenie novej hračky s rôznymi typmi cien
   - Úprava existujúcej hračky a zmena typu ceny
   - Overenie validácie formulára

2. **Používateľské rozhranie:**
   - Vytvorenie hračky s výberom typu ceny
   - Úprava existujúcej hračky

3. **Zobrazenie:**
   - Kontrola správneho zobrazenia cien na všetkých stránkach
   - Overenie správnych prípon ("/ deň" vs "/ výpožičku")

### Regresné testovanie
- Existujúce hračky majú predvolený typ "PER_DAY"
- Všetky existujúce funkcie fungujú bez zmien
- API endpointy sú spätne kompatibilné

## Bezpečnosť

### Validácia
- Server-side validácia typu ceny v API endpointoch
- Kontrola povolených hodnôt: `['PER_DAY', 'PER_RENTAL']`
- Povinné pole pri vytváraní/aktualizácii hračiek

### Spätná kompatibilita
- Existujúce hračky automaticky dostanú typ "PER_DAY"
- API endpointy akceptujú požiadavky bez priceType (použije sa predvolená hodnota)

## Budúce rozšírenia

### Možné vylepšenia
1. **Pokročilé cenové modely:**
   - Víkendové vs. pracovné dni
   - Sezónne ceny
   - Množstevné zľavy

2. **Automatické kalkulácie:**
   - Automatický prepočet celkovej ceny na základe dĺžky výpožičky
   - Zobrazenie porovnania cien pri rôznych typoch

3. **Štatistiky:**
   - Analýza obľúbenosti rôznych cenových modelov
   - Optimalizácia cien na základe dopytu

## Kontakt
Pre otázky alebo problémy kontaktujte vývojový tím.
