# Cascading Soft Delete Implementation

## Overview

This document describes the implementation of cascading soft delete functionality for user anonymization in the Swapka application. When a user is anonymized, all their related data is soft deleted to maintain data integrity while preserving referential relationships for audit purposes.

## Features Implemented

### 1. Database Schema Enhancement

**Added `deletedAt` fields to core models:**
- `User.deletedAt` - Timestamp when user was soft deleted
- `Toy.deletedAt` - Timestamp when toy was soft deleted  
- `Reservation.deletedAt` - Timestamp when reservation was soft deleted

**Database indexes added for performance:**
- `User_deletedAt_idx` - Index on User.deletedAt
- `Toy_deletedAt_idx` - Index on Toy.deletedAt
- `Reservation_deletedAt_idx` - Index on Reservation.deletedAt
- Composite indexes for common query patterns

### 2. Cascading Soft Delete Logic

When a user is anonymized, the following cascading actions occur:

1. **User Anonymization:**
   - Personal data replaced with anonymized values
   - `isAnonymized` set to `true`
   - `deletedAt` timestamp set
   - `status` changed to `BLOCKED`

2. **User's Toys:**
   - All user's toys get `deletedAt` timestamp
   - `status` changed to `UNAVAILABLE`
   - Toys become hidden from public listings

3. **User's Reservations:**
   - All reservations where user is requester or owner get `deletedAt` timestamp
   - `status` changed to `CANCELLED`
   - Reservations become inactive

### 3. Soft Delete Utility Library

**Location:** `src/lib/softDelete.ts`

**Key Features:**
- Pre-defined query filters for excluding soft-deleted records
- Type-safe soft delete operations
- Utility functions for common soft delete patterns
- Restore functionality for admin operations

**Query Filters:**
```typescript
// Exclude soft-deleted users
userFilters.active
userFilters.notDeleted
userFilters.public

// Exclude soft-deleted toys
toyFilters.available
toyFilters.public

// Exclude soft-deleted reservations
reservationFilters.active
reservationFilters.valid
```

### 4. Enhanced Anonymization Process

**Location:** `src/lib/anonymization.ts`

**Improvements:**
- Transaction-based cascading soft delete
- Comprehensive logging of all actions
- Atomic operation ensuring data consistency
- Detailed audit trail in SystemLog

## Security Improvements

### 1. Removed x-user-id Header Dependencies

**Files Updated:**
- `src/app/moje-hracky/page.tsx`
- `src/app/moje-hracky/nova/page.tsx`
- `src/app/rezervacie/page.tsx`
- `src/tests/csrf-test.js`
- `src/tests/auth-test.js`

**Security Benefits:**
- Eliminates client-side user ID manipulation
- Relies solely on server-side token verification
- Prevents unauthorized access through header spoofing

### 2. Bearer Token Authentication

All API endpoints now use only:
```typescript
headers: {
  'Authorization': `Bearer ${token}`,
  'Content-Type': 'application/json'
}
```

## Database Migration

**Migration File:** `src/prisma/migrations/add-soft-delete-support.sql`

**To Apply Migration:**
```bash
cd src
mysql -u username -p database_name < prisma/migrations/add-soft-delete-support.sql
npx prisma generate
```

## Usage Examples

### 1. Anonymizing a User with Cascading Soft Delete

```typescript
import { anonymizeUser } from '../lib/anonymization';

// Anonymize user and cascade soft delete to all related data
const result = await anonymizeUser(userId, adminId);
```

### 2. Querying Non-Deleted Records

```typescript
import { userFilters, toyFilters } from '../lib/softDelete';

// Get active users only
const activeUsers = await prisma.user.findMany({
  where: userFilters.active
});

// Get available toys only
const availableToys = await prisma.toy.findMany({
  where: toyFilters.available
});
```

### 3. Admin Queries Including Deleted Records

```typescript
// Get all users including soft-deleted ones
const allUsers = await prisma.user.findMany({
  // No deletedAt filter - includes all records
});

// Get only soft-deleted users
const deletedUsers = await prisma.user.findMany({
  where: { deletedAt: { not: null } }
});
```

## GDPR Compliance

### Data Anonymization
- Personal data replaced with anonymized values
- Original data cannot be recovered
- User identity completely obscured

### Data Retention
- Soft delete preserves referential integrity
- Audit trail maintained for compliance
- Related data properly handled

### Right to be Forgotten
- User data effectively "forgotten" through anonymization
- Related content hidden from public view
- Database relationships preserved for system integrity

## Testing Recommendations

### 1. Authentication Testing
- Verify all endpoints work without x-user-id headers
- Test Bearer token authentication exclusively
- Confirm user identification through token verification

### 2. Soft Delete Testing
- Test cascading soft delete during anonymization
- Verify soft-deleted records are excluded from public queries
- Test admin access to soft-deleted records

### 3. Data Integrity Testing
- Verify referential integrity after soft delete
- Test restoration of soft-deleted records (admin only)
- Confirm transaction atomicity

## Performance Considerations

### Database Indexes
- Added indexes on `deletedAt` fields for fast filtering
- Composite indexes for common query patterns
- Optimized for both inclusion and exclusion queries

### Query Optimization
- Use provided filter utilities for consistent performance
- Leverage indexes for soft delete queries
- Consider pagination for large datasets

## Monitoring and Logging

### System Logs
- All anonymization actions logged with details
- Cascading actions tracked in audit trail
- Admin actions recorded for compliance

### Error Handling
- Graceful degradation if logging fails
- Transaction rollback on errors
- Detailed error messages for debugging

## Future Enhancements

### Potential Improvements
1. **Automated Cleanup:** Scheduled hard delete of old soft-deleted records
2. **Bulk Operations:** Batch soft delete for multiple users
3. **Restoration UI:** Admin interface for restoring soft-deleted records
4. **Advanced Filtering:** More granular soft delete query options

### Considerations
- Hard delete policies for long-term storage
- Performance impact of large soft-deleted datasets
- Backup strategies for soft-deleted data
