# Ochrana proti útokom - Implementačná dokumentácia

## Prehľad

Táto dokumentácia popisuje implementáciu rozšírenej ochrany proti útokom v Swapka aplikácii, ktor<PERSON> bola pridaná k existujúcemu rate limiting systému pre ochranu proti WordPress a iným známym attack patterns.

## Problém

Aplikácia zaznamenávala opakované 404 požiadavky na WordPress admin endpointy:
- `/wordpress/wp-admin/setup-config.php`
- `/wp-admin/setup-config.php`
- Ďalšie WordPress a CMS attack patterns

Tieto požiadavky indikovali automatizované skenovanie/útoky, ktoré obchádzali existujúci rate limiting (ktorý sa aplikoval iba na `/api/*` routes).

## Riešenie

### 1. Rozšírenie Rate Limiting Systému

**Pridaný nový typ rate limitera:**
```typescript
// Ochrana proti útokom - prísne limity (10 req/min)
attack: {
  max: parseInt(process.env.RATE_LIMIT_ATTACK_MAX || '10'),
  windowMs: parseInt(process.env.RATE_LIMIT_ATTACK_WINDOW_MS || '60000'), // 1 minúta
}
```

**Environment konfigurácia:**
```env
RATE_LIMIT_ATTACK_MAX=10
RATE_LIMIT_ATTACK_WINDOW_MS=60000
```

### 2. Attack Pattern Detection

**Implementovaná detekcia známych attack patterns:**

```typescript
export function isAttackPattern(pathname: string): boolean {
  const attackPatterns = [
    // WordPress admin patterns
    '/wp-admin',
    '/wordpress/wp-admin',
    '/wp-login.php',
    '/wp-config.php',
    '/wp-content',
    '/wp-includes',
    '/xmlrpc.php',
    
    // WordPress setup patterns
    '/wp-admin/setup-config.php',
    '/wordpress/wp-admin/setup-config.php',
    
    // Common CMS patterns
    '/admin.php',
    '/administrator',
    '/phpmyadmin',
    '/phpMyAdmin',
    '/mysql',
    
    // Common exploit patterns
    '/.env',
    '/.git',
    '/config.php',
    '/database.php',
    '/backup',
    '/sql',
    '/dump',
    
    // API abuse patterns
    '/api/v1',
    '/api/v2',
    '/rest/api',
    '/graphql',
  ];

  return attackPatterns.some(pattern => 
    pathname.toLowerCase().includes(pattern.toLowerCase())
  );
}
```

### 3. Middleware Rozšírenie

**Rozšírený middleware pre všetky routes:**
```typescript
// Aplikujeme rate limiting na API routes a attack patterns
if (!pathname.startsWith('/api/') && !isAttackPattern(pathname)) {
  return null;
}
```

**Priorita attack patterns:**
```typescript
// Priorita: attack patterns majú najvyššiu prioritu
if (isAttackPattern(pathname)) {
  return { type: 'attack', ...rateLimitConfig.attack };
}
```

### 4. Bezpečné Error Správy

**Všeobecné správy bez odhalenia limitov:**
```json
{
  "error": "Príliš veľa požiadaviek",
  "message": "Príliš veľa podozrivých požiadaviek. Prístup bol dočasne obmedzený."
}
```

### 5. Security Logging

**Detailné loggovanie s privacy ochranou:**
```javascript
console.warn(`Rate limit exceeded for ${config.type} endpoint`, {
  ip: ip.substring(0, 8) + '***', // Čiastočne skryť IP pre privacy
  type: config.type,
  count: current.count,
  limit: config.max,
  pathname: pathname,
  timestamp: new Date().toISOString(),
  userAgent: request.headers.get('user-agent')?.substring(0, 50) + '...',
});
```

## Testovanie

### Manuálne testovanie attack protection

```bash
# Test WordPress attack pattern (10 req/min limit)
for i in {1..15}; do
  echo "Request $i:"
  curl -s -w "HTTP Status: %{http_code}\n" http://localhost:3000/wp-admin/setup-config.php | head -1
done

# Test iných attack patterns
curl http://localhost:3000/phpmyadmin
curl http://localhost:3000/.env
curl http://localhost:3000/wordpress/wp-admin/
```

### Očakávané výsledky

1. **Prvých 10 požiadaviek**: 404 status (normálna 404 stránka)
2. **11+ požiadaviek**: 429 status s attack protection správou
3. **Server logy**: Detailné loggovanie s IP sanitizáciou

## Konfigurácia Rate Limitov

| Typ endpointu | Limit | Okno | Popis |
|---------------|-------|------|-------|
| Auth | 5 req/min | 60s | Autentifikačné operácie |
| General | 100 req/min | 60s | Štandardné API operácie |
| Admin | 20 req/min | 60s | Admin operácie |
| Upload | 10 req/min | 60s | Upload súborov |
| **Attack** | **10 req/min** | **60s** | **WordPress a iné attack patterns** |

## Bezpečnostné vlastnosti

### ✅ Implementované

- **Attack pattern detection** pre WordPress, CMS a exploit patterns
- **Prísne rate limiting** (10 req/min) pre attack patterns
- **Všeobecné error správy** bez odhalenia presných limitov
- **IP sanitizácia** v logoch (prvých 8 znakov + "***")
- **Detailné security logging** s timestamp a user agent
- **Prioritné spracovanie** attack patterns pred ostatnými rules
- **Environment konfigurácia** pre flexibilné nastavenie limitov

### 🔒 Bezpečnostné výhody

- **Ochrana proti automatizovaným útokom** na WordPress endpointy
- **Prevencia information disclosure** cez všeobecné error správy
- **Privacy ochrana** cez IP sanitizáciu
- **Monitoring a alerting** cez detailné loggovanie
- **Flexibilná konfigurácia** bez potreby reštartu aplikácie

## Monitoring

### Admin Monitoring

```bash
# Získanie štatistík rate limiting
GET /api/admin/rate-limit

# Vyčistenie rate limitov pre konkrétnu IP
DELETE /api/admin/rate-limit?ip=*************

# Vyčistenie všetkých rate limitov
DELETE /api/admin/rate-limit?all=true
```

### Log Monitoring

Všetky attack attempts sa logujú vo formáte:
```
Rate limit exceeded for attack endpoint {
  ip: '192.168.***',
  type: 'attack',
  count: 11,
  limit: 10,
  pathname: '/wp-admin/setup-config.php',
  timestamp: '2025-05-30T11:44:19.081Z',
  userAgent: 'curl/8.7.1...'
}
```

## Súbory

### Upravené súbory

1. **`src/lib/rateLimiting.ts`**
   - Pridaný `attack` rate limiter typ
   - Implementovaná `isAttackPattern()` funkcia
   - Rozšírená priorita pre attack patterns

2. **`src/middleware/rateLimit.ts`**
   - Rozšírená aplikácia rate limiting na attack patterns
   - Pridané attack error správy
   - Aktualizované utility funkcie

3. **`src/middleware.ts`**
   - Rozšírené aplikovanie rate limiting na všetky routes

4. **`src/app/api/test/rate-limit/route.ts`**
   - Pridaný PUT endpoint pre testovanie attack patterns

5. **`rules/security_sql.md`**
   - Aktualizovaná dokumentácia s attack protection
   - Pridané testovanie a monitoring sekcie

6. **`src/docs/RATE_LIMITING.md`**
   - Rozšírená dokumentácia o attack protection
   - Pridané príklady testovania

## Verzia

**Implementované vo verzii:** 0.16
**Dátum implementácie:** 30.05.2025
**Autor:** Augment Agent

## Záver

Implementácia úspešne rozšírila existujúci rate limiting systém o komplexnú ochranu proti útokom. Systém teraz automaticky detekuje a blokuje známe attack patterns s prísnejšími limitmi, pričom zachováva všetky existujúce bezpečnostné vlastnosti a pridáva detailné monitoring a logging capabilities.
