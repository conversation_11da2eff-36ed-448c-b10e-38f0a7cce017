# HEIC Upload Troubleshooting Guide

**Dátum:** 2025-01-27  
**Verzia:** 0.14  
**Problém:** HEIC fotografie z iPhone sa nedajú nahrať, zatiaľ čo z PC fungujú

## 🔍 Identifikované problémy

### 1. **Browser MIME Type Issues**
- **Problém**: iPhone HEIC súbory často majú nesprávny MIME type
- **Príčina**: <PERSON><PERSON><PERSON><PERSON> prehliada<PERSON>e a WebViews reportujú HEIC súbory ako `application/octet-stream` namiesto `image/heic`
- **Riešenie**: ✅ Implementované - rozšírená detekcia na základe file extension

### 2. **WebView Restrictions**
- **Problém**: Sociálne siete (Instagram, Facebook, TikTok) používajú obmedzené WebViews
- **Príčina**: WebViews majú limitované File API capabilities
- **Riešenie**: ✅ Implementované - WebView detector s upozornením

### 3. **Client-side Validation**
- **Problém**: Dropzone a react-dropzone môžu odmietnuť HEIC súbory
- **Príčina**: Strict MIME type checking
- **Riešenie**: ✅ Implementované - custom validator s fallback na extension

### 4. **Server-side Processing**
- **Problém**: HEIC konverzia môže zlyhať na poškodených súboroch
- **Príčina**: Nekompatibilné HEIC varianty alebo poškodené súbory
- **Riešenie**: ✅ Implementované - lepšie error handling a diagnostika

## 🛠️ Implementované riešenia

### 1. **Vylepšená HEIC detekcia**
```typescript
// Pred opravou - strict MIME checking
return isHeicExtension || (isHeicMimeType && isHeicExtension);

// Po oprave - priorita extension
return isHeicExtension || (isHeicMimeType && file.name.toLowerCase().includes('heic'));
```

### 2. **Rozšírená client-side validácia**
```typescript
const isValidImageFile = (file: File): boolean => {
  const extension = file.name.split('.').pop()?.toLowerCase();
  const isHeicFile = extension === 'heic' || extension === 'heif';
  
  // HEIC súbory sú vždy platné bez ohľadu na MIME type
  return isValidExtension || isValidMimeType || isHeicFile;
};
```

### 3. **Dropzone konfigurácia**
```typescript
accept: {
  'image/*': ['.jpeg', '.jpg', '.png', '.gif', '.webp', '.heic', '.heif'],
  'application/octet-stream': ['.heic', '.heif'] // Pre iPhone HEIC súbory
}
```

### 4. **Custom validator s fallback**
```typescript
validator: (file) => {
  if (!isValidImageFile(file)) {
    return { code: 'file-invalid-type', message: '...' };
  }
  return null;
}
```

### 5. **Lepšie error handling**
```typescript
// Špecifické error messages pre HEIC problémy
if (heicErrorMessage.includes('Invalid input')) {
  throw new Error(`HEIC súbor "${fileName}" nie je platný alebo je poškodený. Skúste otvoriť súbor v aplikácii Fotografie a uložiť ho znovu.`);
}
```

## 🧪 Testovanie

### Automatické testy
```bash
# Spustenie HEIC testov v browser console
runHeicTests()
```

### Manuálne testovanie
1. **iPhone Safari** - ✅ Primárny test case
2. **iPhone Chrome** - ✅ Sekundárny test
3. **Instagram WebView** - ⚠️ Očakávané obmedzenia
4. **Facebook WebView** - ⚠️ Očakávané obmedzenia

## 📱 Používateľské riešenia

### Pre iPhone používateľov:
1. **Použiť Safari** namiesto aplikácií sociálnych sietí
2. **Otvoriť v prehliadači** cez WebView detector
3. **Konvertovať do JPEG** v aplikácii Fotografie ak problémy pretrvávajú

### Pre vývojárov:
1. **Kontrolovať server logs** pre detailné error messages
2. **Spustiť HEIC testy** v browser console
3. **Monitorovať Cloudinary upload logs**

## 🔧 Debugging

### Browser Console Commands
```javascript
// Spustenie všetkých testov
runHeicTests()

// Test detekcie HEIC súborov
testHeicDetection()

// Simulácia HEIC uploadu
simulateHeicUpload()

// Test browser capabilities
testBrowserCapabilities()
```

### Server Logs
```bash
# Sledovanie HEIC konverzie
grep "HEIC" /var/log/app.log

# Cloudinary upload errors
grep "cloudinary" /var/log/app.log
```

## 📊 Očakávané výsledky

### ✅ Funguje:
- iPhone Safari + HEIC súbory
- iPhone Chrome + HEIC súbory  
- Desktop prehliadače + všetky formáty
- Automatická konverzia HEIC → JPEG → WebP

### ⚠️ Obmedzené:
- Instagram/Facebook WebView (odporúčanie otvoriť v prehliadači)
- Poškodené HEIC súbory (error message s návrhom riešenia)
- Veľmi staré HEIC varianty (fallback na JPEG konverziu)

### ❌ Neočakávané problémy:
- Ak problémy pretrvávajú po implementácii, skontrolovať:
  1. heic-convert library verziu
  2. Sharp library kompatibilitu
  3. Server memory limits pre veľké súbory

## 🚀 Ďalšie vylepšenia

### Možné budúce riešenia:
1. **Client-side HEIC konverzia** pomocou WebAssembly
2. **Progressive upload** s fallback na JPEG
3. **Real-time format detection** pomocou magic bytes
4. **Batch upload** pre viacero HEIC súborov naraz

---

**Poznámka**: Táto dokumentácia pokrýva všetky identifikované problémy s HEIC uploadom a ich riešenia implementované vo verzii 0.14.
