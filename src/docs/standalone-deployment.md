# Next.js Standalone Deployment Guide

Swapka aplikácia používa Next.js standalone režim pre optimálnu produkčnú výkonnosť. Tento dokument vysvetľuje, ako správne nasadiť aplikáciu v standalone režime.

## Čo je standalone režim?

Standalone režim je Next.js funkcia, ktorá vytvára samostatný server súbor obsahujúci všetky potrebné závislosti. To poskytuje:

- **Menšiu veľkosť nasadenia** - iba potrebné súbory
- **Lepšiu výkonnosť** - optimalizovaný runtime
- **Jednoduchšie nasadenie** - jeden server súbor
- **Menšiu spotrebu pamäte** - bez zbytočných závislostí

## Konfigurácia

### Next.js konfigurácia

V `src/next.config.ts`:

```typescript
const nextConfig: NextConfig = {
  output: 'standalone',
  // ... ostatné nastavenia
};
```

### Package.json skripty

```json
{
  "scripts": {
    "build": "node scripts/version-info.js && next build",
    "start": "node scripts/version-info.js && node .next/standalone/server.js",
    "start:legacy": "node scripts/version-info.js && next start"
  }
}
```

## Deployment proces

### 1. Build aplikácie

```bash
cd src
npm install
npx prisma generate
npm run build
```

Po build procese sa vytvorí:
- `.next/standalone/server.js` - hlavný server súbor
- `.next/standalone/` - všetky potrebné závislosti
- `.next/static/` - statické súbory (automaticky skopírované do standalone)
- `.next/standalone/.next/static/` - statické súbory v standalone adresári
- `.next/standalone/public/` - verejné súbory v standalone adresári

### 2. Spustenie produkčného servera

**Správne:**
```bash
node .next/standalone/server.js
```

**Nesprávne (nekompatibilné so standalone):**
```bash
npm start  # Ak používa next start
next start
```

## PM2 konfigurácia

### Ecosystem konfigurácia

```javascript
{
  name: 'swapka-prod',
  script: 'node',
  args: '.next/standalone/server.js',
  cwd: './src',
  instances: 1,
  exec_mode: 'cluster'
}
```

### Spustenie cez PM2

```bash
# Build a spustenie
cd src
npm run build
pm2 start ../ecosystem.config.js --only swapka-prod
```

## Docker konfigurácia

### Dockerfile

```dockerfile
# Build aplikácie
RUN npm run build

# Spustenie standalone servera
CMD ["node", ".next/standalone/server.js"]
```

## Riešenie problémov

### Chyba: "Cannot find module"

**Príčina**: Server súbor nebol vytvorený alebo build zlyhal

**Riešenie**:
```bash
cd src
rm -rf .next
npm run build
# Skontrolujte, či existuje .next/standalone/server.js
```

### Chyba: "ENOENT: no such file"

**Príčina**: Nesprávny working directory

**Riešenie**: Uistite sa, že spúšťate server z `src` adresára

### Statické súbory sa nenačítavají (404 chyby)

**Príčina**: Chýbajúce statické súbory v standalone adresári

**Riešenie**:
```bash
cd src
# Manuálne spustenie kopírovania súborov
node scripts/copy-standalone-assets.js

# Alebo rebuild aplikácie (automaticky skopíruje súbory)
npm run build
```

**Kontrola**: Skontrolujte, či existujú:
- `.next/standalone/.next/static/`
- `.next/standalone/public/`

## Migrácia z next start

Ak migrujete z `next start` na standalone:

1. **Aktualizujte package.json**:
   ```json
   "start": "node .next/standalone/server.js"
   ```

2. **Aktualizujte PM2 konfiguráciu**:
   ```javascript
   script: 'node',
   args: '.next/standalone/server.js'
   ```

3. **Aktualizujte Dockerfile**:
   ```dockerfile
   CMD ["node", ".next/standalone/server.js"]
   ```

4. **Rebuild aplikáciu**:
   ```bash
   npm run build
   ```

## Výhody standalone režimu

- **Rýchlejšie spustenie** - menší overhead
- **Menšia spotreba pamäte** - iba potrebné moduly
- **Lepšia bezpečnosť** - menší attack surface
- **Jednoduchšie nasadenie** - jeden server súbor
- **Kompatibilita s kontajnermi** - optimálne pre Docker

## Poznámky

- Standalone režim vyžaduje build krok pred spustením
- Všetky environment variables musia byť dostupné pri spustení
- Statické súbory sú automaticky zahrnuté v standalone bundle
- Režim je kompatibilný s všetkými Next.js funkciami
