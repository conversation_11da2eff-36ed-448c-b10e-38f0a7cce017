# Email Monitoring System - Dokumentácia

## Prehľad

Email Monitoring System je komplexný administrátorský nástroj pre sledovanie a správu emailových notifikácií v aplikácii Swapka. Systém poskytuje real-time monitoring email fronty, detailné štatistiky a logy všetkých emailových aktivít.

## Funkcie

### 🎯 **Email Queue Status Dashboard**
- **Real-time stav fronty**: Zobrazenie aktuálneho počtu emailov vo fronte
- **Stav spracovania**: Indikátor či sa fronta práve spracováva
- **Čakajúce emaile**: Počet emailov čakajúcich na odoslanie
- **Detaily čakajúcich emailov**: Tabuľka s informáciami o jednotlivých emailoch vo fronte

### 📊 **Email Statistics**
- **Celkové štatistiky**: Po<PERSON><PERSON> v<PERSON><PERSON><PERSON><PERSON><PERSON> emailov, úsp<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
- **Úspešnosť**: Percentuálna úspešnosť doručenia emailov
- **Rozdelenie podľa typu**: Štatistiky pre rôzne typy emailov (rezervácie, schválenia)
- **Časové štatistiky**: Denné, týždenné a mesačné prehľady

### 📝 **Email Logs**
- **Detailné logy**: Záznamy všetkých pokusov o odoslanie emailov
- **Anonymizované údaje**: Príjemcovia sú anonymizovaní pre ochranu súkromia
- **Chybové správy**: Detailné informácie o neúspešných pokusoch
- **Časové značky**: Presné časy vytvorenia, odoslania a zlyhaní

### 🔧 **Správa fronty**
- **Vymazanie fronty**: Možnosť vymazať všetky čakajúce emaile (núdzové použitie)
- **Automatické obnovenie**: 30-sekundové automatické obnovenie údajov
- **Manuálne obnovenie**: Tlačidlo pre okamžité obnovenie údajov

## Technická implementácia

### **Databázová schéma**

Nová tabuľka `EmailLog` pre sledovanie emailových aktivít:

```sql
CREATE TABLE EmailLog (
  id INT PRIMARY KEY AUTO_INCREMENT,
  emailId VARCHAR(255) NOT NULL,           -- Jedinečný identifikátor emailu
  emailType VARCHAR(100) NOT NULL,         -- Typ emailu (RESERVATION_CREATED, atď.)
  recipientEmail VARCHAR(255) NOT NULL,    -- Anonymizovaný email príjemcu
  recipientUserId INT,                     -- ID používateľa (ak dostupné)
  subject VARCHAR(500),                    -- Predmet emailu
  status VARCHAR(50) NOT NULL,             -- QUEUED, SENT, FAILED, RETRY
  attempt INT DEFAULT 1,                   -- Číslo pokusu
  maxAttempts INT DEFAULT 3,               -- Maximálny počet pokusov
  errorMessage TEXT,                       -- Chybová správa pri zlyhaní
  messageId VARCHAR(255),                  -- ID správy z externej služby
  queuedAt DATETIME DEFAULT NOW(),         -- Čas zaradenia do fronty
  sentAt DATETIME,                         -- Čas úspešného odoslania
  failedAt DATETIME,                       -- Čas konečného zlyhania
  processingTime INT,                      -- Čas spracovania v ms
  createdAt DATETIME DEFAULT NOW(),
  updatedAt DATETIME DEFAULT NOW() ON UPDATE NOW()
);
```

### **API Endpointy**

#### `GET /api/admin/email-monitoring`
Získanie údajov o email monitoringu.

**Parametre:**
- `type`: `all|queue|statistics|logs` (predvolené: `all`)
- `limit`: počet posledných logov (predvolené: 50)

**Odpoveď:**
```json
{
  "success": true,
  "data": {
    "queue": {
      "queueLength": 5,
      "isProcessing": true,
      "pendingEmails": [...]
    },
    "statistics": {
      "totalEmails": 1250,
      "sentEmails": 1180,
      "failedEmails": 70,
      "successRate": 94.4,
      "emailTypeBreakdown": [...]
    },
    "recentLogs": [...]
  }
}
```

#### `POST /api/admin/email-monitoring`
Vykonanie akcií nad email monitoringom.

**Akcie:**
- `clear-queue`: Vymazanie email fronty
- `refresh-stats`: Obnovenie štatistík

### **Komponenty**

#### `EmailStatisticsCard`
Znovupoužiteľná karta pre zobrazenie štatistík s farebným kódovaním.

#### `EmailTypeBreakdown`
Komponent pre zobrazenie rozdelenia emailov podľa typu s progress barmi.

#### `EmailQueueStatus`
Komplexný prehľad stavu fronty s ikonami a detailmi čakajúcich emailov.

#### `EmailLogsTable`
Tabuľka s detailnými logmi emailov, filtrovanie a farebné označenie stavov.

## Bezpečnosť

### **Autentifikácia**
- Všetky endpointy používajú `withAdminAuth` middleware
- Prístup len pre používateľov s rolou `ADMIN`
- Firebase token verifikácia

### **Ochrana súkromia**
- Emailové adresy sú anonymizované v logoch
- Formát: `us***@example.com`
- Žiadne citlivé údaje v chybových správach

### **Rate Limiting**
- Štandardné rate limiting pre admin endpointy (20 req/min)
- Dodatočná ochrana proti zneužitiu

## Používanie

### **Prístup k systému**
1. Prihláste sa ako administrátor (<EMAIL>)
2. Prejdite do admin sekcie
3. Kliknite na záložku "Email monitoring"

### **Monitorovanie**
- Systém sa automaticky obnovuje každých 30 sekúnd
- Možnosť vypnutia automatického obnovovania
- Manuálne obnovenie tlačidlom "Obnoviť"

### **Riešenie problémov**
- Červené indikátory označujú problémy
- Chybové správy v tabuľke logov
- Možnosť vymazania fronty v núdzových prípadoch

## Migrácia

### **Databázová migrácia**
```bash
# Spustenie migrácie
mysql -u username -p database_name < src/prisma/migrations/add-email-log-table.sql

# Alebo pomocou Prisma
npx prisma db push
```

### **Aktualizácia kódu**
```bash
# Aktualizácia Prisma klienta
npx prisma generate

# Reštart aplikácie
pm2 restart swapka
```

## Monitoring a údržba

### **Kľúčové metriky**
- Úspešnosť doručenia emailov (cieľ: >95%)
- Priemerný čas spracovania
- Počet emailov vo fronte
- Chybovosť podľa typu emailu

### **Pravidelné úlohy**
- Týždenné kontroly logov
- Mesačné vyhodnotenie štatistík
- Čistenie starých logov (odporúčané: 6 mesiacov)

### **Alerting**
- Sledovanie vysokého počtu neúspešných emailov
- Monitoring dlhých front
- Upozornenia na chyby v konfigurácii

## Changelog

### Verzia 0.22 (2024-12-17)
- ✅ Implementácia email monitoring systému
- ✅ Nová tabuľka EmailLog
- ✅ Admin dashboard pre monitoring
- ✅ Real-time štatistiky a logy
- ✅ Automatické logovanie všetkých email aktivít
- ✅ Anonymizácia emailových adries
- ✅ Reusable React komponenty

## Budúce vylepšenia

### **Plánované funkcie**
- **WebSocket real-time updates**: Live aktualizácie bez obnovovania
- **Email templates preview**: Náhľad emailových šablón
- **Advanced filtering**: Filtrovanie logov podľa dátumu, typu, stavu
- **Export funkcionalita**: Export štatistík do CSV/PDF
- **Email performance metrics**: Detailné metriky výkonu
- **Automated alerts**: Automatické upozornenia na problémy

### **Optimalizácie**
- **Database indexing**: Optimalizácia indexov pre rýchlejšie dotazy
- **Log rotation**: Automatické archivácie starých logov
- **Caching**: Cache pre často používané štatistiky
- **Batch processing**: Dávkové spracovanie pre lepší výkon
