# Secure Filename Hashing - Dokumentácia

## Prehľad

Implementácia bezpečného hashovania názvov súborov pre systém nahrávania fotografií. Táto funkcionalita zabezpečuje, že všetky nahrané súbory majú kryptograficky bezpečné n<PERSON>, čo zvyšuje bezpečnosť a konzistentnosť systému.

## Motivácia

### Problémy pôvodného systému:
- **Bezpečnostné riziká**: Pôvodné názvy súborov mohli obsahovať citlivé informácie
- **Filename-based útoky**: Možnosť útokov založených na názvoch súborov
- **Kolízie názvov**: Riziko konfliktu názvov súborov
- **Nekonzistentnosť**: Rôzne formáty názvov súborov

### Výhody nového systému:
- **Kryptografická bezpečnosť**: SHA-256 hash s vysokou entropiou
- **URL-safe formát**: Base64URL encoding pre bezpečné URL
- **Prevencia útokov**: Skrytie pôvodných názvov súborov
- **Konzistentnosť**: Jednotný formát pre všetky súbory

## Architektúra

### Databázové zmeny

```sql
-- Pridanie nových polí do ToyImage tabuľky
ALTER TABLE `ToyImage` 
ADD COLUMN `originalFilename` VARCHAR(191) NULL COMMENT 'Pôvodný názov súboru',
ADD COLUMN `hashedFilename` VARCHAR(191) NULL COMMENT 'Hashovaný názov súboru používaný v Cloudinary',
ADD COLUMN `fileFormat` VARCHAR(191) NULL COMMENT 'Finálny formát súboru (napr. webp)';

-- Index pre rýchlejšie vyhľadávanie
CREATE INDEX `hashedFilename` ON `ToyImage`(`hashedFilename`);
```

### Nové polia:
- **`originalFilename`**: Pôvodný názov súboru (pre referenčné účely)
- **`hashedFilename`**: Bezpečný hashovaný názov súboru
- **`fileFormat`**: Finálny formát súboru (napr. 'webp', 'jpg')

## Implementácia

### 1. Generovanie bezpečného názvu súboru

```typescript
// src/lib/hashUtils.ts
export function generateSecureFilename(
  originalFilename: string,
  userId: string | number,
  fileExtension: string
): {
  hashedFilename: string;
  publicId: string;
  fullPath: string;
}
```

**Proces generovania:**
1. **Timestamp s vysokou presnosťou**: `Date.now()` + `process.hrtime.bigint()`
2. **Kryptograficky bezpečný random**: `crypto.randomBytes(16)`
3. **Sanitizácia pôvodného názvu**: Odstránenie nebezpečných znakov
4. **SHA-256 hash**: Kombinácia všetkých vstupov + SECRET_KEY
5. **Base64URL encoding**: URL-safe formát (24 znakov)

### 2. Upload proces

```typescript
// src/app/api/cloudinary/upload/route.ts
const secureFilename = generateSecureFilename(file.name, userId, fileExtension);

// Cloudinary upload s hashovaným názvom
const uploadResult = await cloudinary.uploader.upload_stream({
  folder: CLOUDINARY_FOLDER,
  public_id: secureFilename.publicId,
  // ...
});

// Uloženie do databázy
const toyImage = await prisma.toyImage.create({
  data: {
    url: result.secure_url,
    toyId: numericToyId,
    originalFilename: file.name,
    hashedFilename: secureFilename.hashedFilename,
    fileFormat: fileExtension,
  },
});
```

### 3. Deletion proces

```typescript
// src/lib/cloudinary.ts
export async function deleteCloudinaryImage(
  imageIdentifier: string, 
  hashedFilename?: string
): Promise<boolean>
```

**Deletion stratégia:**
- **Primárne**: Použitie `hashedFilename` ak je k dispozícii
- **Fallback**: Extrakcia public_id z URL (backward compatibility)

## Bezpečnostné vlastnosti

### Kryptografická bezpečnosť
- **SHA-256 hash**: Kryptograficky bezpečná hash funkcia
- **Vysoká entropia**: Timestamp + random bytes + user ID + secret key
- **Base64URL encoding**: URL-safe bez padding znakov

### Prevencia útokov
- **Filename-based útoky**: Pôvodné názvy nie sú viditeľné v URL
- **Directory traversal**: Hashované názvy nemôžu obsahovať '../'
- **Information disclosure**: Žiadne citlivé informácie v názvoch súborov

### Príklad hashovaného názvu:
```
Pôvodný: "Snímka obrazovky 2025-01-27 o 19.30.19.heic"
Hashovaný: "kJ8mN2pQ7rS9tU3vW6xY1z.webp"
Public ID: "kJ8mN2pQ7rS9tU3vW6xY1z"
```

## Backward Compatibility

### Existujúce súbory
- **Zachovaná funkcionalita**: Starý systém funguje cez URL extraction
- **Postupná migrácia**: Nové uploady používajú secure hashing
- **Žiadne breaking changes**: Existujúce súbory sa nemusia migrovať

### Deletion compatibility
```typescript
// Funguje pre oba systémy
if (hashedFilename) {
  // Nový systém - priame použitie hashed filename
  publicId = hashedFilename.replace(/\.[^/.]+$/, '');
} else {
  // Starý systém - extrakcia z URL
  publicId = extractPublicIdFromUrl(imageUrl);
}
```

## Integrácia s image processing

### Kompletný workflow:
1. **Upload súboru** (JPG, PNG, GIF, HEIC, HEIF)
2. **Image processing** (HEIC konverzia, resize, WebP konverzia)
3. **Secure filename generation** (hash + extension)
4. **Cloudinary upload** (s hashovaným názvom)
5. **Database storage** (všetky metadáta)

### Výsledný objekt:
```typescript
{
  message: 'Obrázok bol úspešne nahraný a uložený',
  imageUrl: 'https://res.cloudinary.com/.../kJ8mN2pQ7rS9tU3vW6xY1z.webp',
  processing: {
    originalFormat: 'image/heic',
    finalFormat: 'image/webp',
    originalSize: 2048, // KB
    finalSize: 456,     // KB
    compressionRatio: 78,
    dimensions: '1024x768'
  },
  security: {
    originalFilename: 'IMG_1234.HEIC',
    hashedFilename: 'kJ8mN2pQ7rS9tU3vW6xY1z.webp',
    publicId: 'kJ8mN2pQ7rS9tU3vW6xY1z'
  }
}
```

## Testovanie

### Test scenáre:
1. **Upload nových súborov** - overenie generovania secure filenames
2. **Deletion nových súborov** - použitie hashed filenames
3. **Deletion starých súborov** - fallback na URL extraction
4. **Rôzne formáty** - HEIC, JPG, PNG, GIF
5. **Špeciálne znaky v názvoch** - Unicode, medzery, diakritika

### Monitoring:
- **Console logy**: Detailné informácie o processing a deletion
- **Database queries**: Overenie uloženia metadát
- **Cloudinary API**: Overenie správnych public_id

## Údržba

### Monitoring bodov:
- **Hash kolízie**: Extrémne nepravdepodobné (2^128 možností)
- **Performance**: Minimálny overhead pri generovaní hashov
- **Storage**: Dodatočné 3 polia v databáze

### Budúce vylepšenia:
- **Migrácia starých súborov**: Postupné prehashovanie existujúcich súborov
- **Cleanup utility**: Odstránenie orphaned súborov z Cloudinary
- **Analytics**: Štatistiky o compression ratios a formátoch

## Záver

Secure filename hashing poskytuje robustné bezpečnostné vylepšenie pre systém nahrávania fotografií. Implementácia je backward compatible a integruje sa seamlessly s existujúcim image processing pipeline.

**Kľúčové výhody:**
- ✅ Kryptografická bezpečnosť
- ✅ Prevencia filename-based útokov  
- ✅ Konzistentné pomenovanie súborov
- ✅ Backward compatibility
- ✅ Integrácia s WebP konverziou
- ✅ Detailné logging a monitoring
