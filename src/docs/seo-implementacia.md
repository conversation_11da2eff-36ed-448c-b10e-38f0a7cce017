# SEO Implementácia - Swapka

## Prehľad

Táto dokumentácia popisuje kompletnú SEO implementáciu pre Swapka aplikáciu, ktorá transformuje generické SEO na dynamický, SEO-friendly systém s lepšou URL štruktúrou, meta tagmi a structured data.

## Implementované funkcie

### 1. SEF URLs (Search Engine Friendly URLs)

**Pred:** `/hracky/h-c514910b9a5e`
**Po:** `/hracky/lego-technic-bagr-c514910b9a5e`

#### Kľúčové súbory:
- `src/lib/seoUtils.ts` - Hlavná SEO knižnica
- `src/app/hracky/[id]/page.tsx` - Toy detail stránka s SEO meta tagmi
- `src/app/hracky/[id]/ToyDetailClient.tsx` - Client komponenta s redirect logikou

#### Funkcie:
- `generateToyUrl(toyName: string, toyId: number): string` - Generuje SEF URL
- `parseToyUrl(urlPath: string): string | null` - Parsuje SEF URL na hashed ID
- `createSeoSlug(text: string): string` - Vytvára URL-friendly slug zo slovenského textu

### 2. Dynamické Meta Tagy

Implementované v `src/app/hracky/[id]/page.tsx`:

```typescript
export async function generateMetadata({ params }: ToyDetailPageProps): Promise<Metadata> {
  const resolvedParams = await params;
  const toyId = await validateToyUrlParam(resolvedParams.id);
  
  if (!toyId) {
    return {
      title: 'Hračka nenájdená | Swapka',
      description: 'Požadovaná hračka nebola nájdená.'
    };
  }

  const toy = await getToyById(toNumericId(toyId));
  
  if (!toy) {
    return {
      title: 'Hračka nenájdená | Swapka',
      description: 'Požadovaná hračka nebola nájdená.'
    };
  }

  const metaTitle = generateToyMetaTitle(toy.name, toy.user?.city);
  const metaDescription = generateToyMetaDescription(toy.name, toy.description, toy.user?.city);
  const canonicalUrl = `${process.env.NEXT_PUBLIC_BASE_URL}${generateToyUrl(toy.name, toy.id)}`;

  return {
    title: metaTitle,
    description: metaDescription,
    keywords: generateToyKeywords(toy.name, toy.type, toy.user?.city),
    openGraph: {
      title: metaTitle,
      description: metaDescription,
      url: canonicalUrl,
      type: 'website',
      images: toy.images?.length > 0 ? [
        {
          url: toy.images[0].url,
          width: 800,
          height: 600,
          alt: toy.name
        }
      ] : []
    },
    alternates: {
      canonical: canonicalUrl
    }
  };
}
```

### 3. JSON-LD Structured Data

Implementované Product schema pre lepšie indexovanie:

```typescript
const structuredData = {
  "@context": "https://schema.org",
  "@type": "Product",
  "name": toy.name,
  "description": toy.description,
  "image": toy.images?.length > 0 ? toy.images.map(img => img.url) : [],
  "brand": {
    "@type": "Brand",
    "name": "Swapka"
  },
  "offers": {
    "@type": "Offer",
    "price": toy.price.toString(),
    "priceCurrency": "EUR",
    "availability": toy.status === 'AVAILABLE' ? "https://schema.org/InStock" : "https://schema.org/OutOfStock",
    "priceValidUntil": new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]
  },
  "category": translateToyType(toy.type),
  "url": canonicalUrl
};
```

### 4. Sitemap Generátor

Aktualizovaný `src/lib/sitemapGenerator.ts`:

```typescript
// Pridanie hračiek do sitemap s SEF URLs
const toys = await prisma.toy.findMany({
  where: { status: 'AVAILABLE' },
  select: { id: true, name: true, updatedAt: true }
});

toys.forEach(toy => {
  urls.push({
    loc: generateToyUrl(toy.name, toy.id),
    lastmod: toy.updatedAt.toISOString().split('T')[0],
    changefreq: 'weekly',
    priority: 0.8
  });
});
```

### 5. Backward Compatibility

Systém podporuje oba formáty URL:
- **Legacy:** `/hracky/h-c514910b9a5e` (automaticky redirectuje na SEF URL)
- **SEF:** `/hracky/lego-technic-bagr-c514910b9a5e`

#### Redirect logika v ToyDetailClient:
```typescript
// Redirect legacy URLs to SEF URLs
if (isLegacyUrl && data.name && data.numericId) {
  const sefUrl = generateToyUrl(data.name, data.numericId);
  router.replace(sefUrl);
  return;
}
```

### 6. API Endpoint Updates

Aktualizovaný `src/app/api/toy/[id]/route.ts` podporuje oba formáty:

```typescript
// Pokus o parsovanie SEF URL alebo legacy hashovaného ID
let hashedId: string | null = null;

// Najprv skúsime parsovať ako SEF URL
hashedId = parseToyUrl(url.pathname);

// Ak to nie je SEF URL, skúsime či je to priamo hashované ID
if (!hashedId && isHashedId(idParam)) {
  hashedId = idParam;
}
```

## Aktualizované komponenty

### Frontend komponenty s SEF URLs:
1. `src/app/hracky/page.tsx` - Hlavný listing hračiek
2. `src/app/profil/[userHash]/page.tsx` - Profil používateľa
3. `src/app/rezervacie/page.tsx` - Rezervácie
4. `src/app/admin/toys/page.tsx` - Admin toys management

### API endpoints:
1. `src/app/api/toys/route.ts` - Pridané `numericId` field
2. `src/app/api/toy/[id]/route.ts` - Podpora SEF URLs a `numericId` field

## Testovanie

### Manuálne testovanie:
1. Otvorte `http://localhost:3000/hracky`
2. Kliknite na ľubovoľnú hračku - URL by mal byť v SEF formáte
3. Skúste legacy URL - mal by redirectovať na SEF URL
4. Skontrolujte sitemap na `http://localhost:3000/sitemap.xml`

### SEO validácia:
1. Použite Google Rich Results Test
2. Skontrolujte meta tagy v browser dev tools
3. Validujte JSON-LD structured data

## Výkon

- Build time: ~4-5 sekúnd
- Žiadne runtime chyby
- Backward compatibility zachovaná
- Všetky existujúce funkcie fungujú bez zmien

## Verzia

Implementované vo verzii **0.30** aplikácie Swapka.
