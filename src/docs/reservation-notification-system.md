# Notifikačný systém pre rezervácie hračiek

## Prehľad

Implementovaný notifikačný systém zobrazuje floating element v pravom dolnom rohu stránky, ktorý informuje používateľov o ich aktívnych rezerváciách hračiek. Systém rozlišuje medzi dvoma typmi rezervácií:

1. **<PERSON>je rezerv<PERSON>** - rezervácie, ktoré si používateľ vytvoril pre cudzie hračky
2. **Dopyty na moje hračky** - požiadavky iných používateľov na rezervácie hračiek, ktorých je používateľ vlastníkom

Element sa zobrazuje len ak má používateľ aspoň jednu čakajúcu alebo potvrdenú rezerváciu z oboch kategórií.

## Komponenty

### 1. `useReservationNotifications` Hook
**Súbor:** `src/hooks/useReservationNotifications.ts`

Custom React hook, ktorý:
- Načítava rezervácie používateľa z API endpointu `/api/reservations`
- Načítava rezervácie hračiek používateľa z API endpointu `/api/reservations/owner`
- Filtruje aktívne rezervácie (PENDING, CONFIRMED) pre oba typy
- Počíta počet čakajúcich a potvrdených rezervácií pre každú kategóriu
- Poskytuje funkciu `refresh()` pre manuálne obnovenie údajov

**Rozhranie:**
```typescript
interface ReservationCounts {
  // Moje rezervácie (rezervácie, ktoré si používateľ vytvoril)
  myPending: number;
  myConfirmed: number;
  myTotal: number;

  // Rezervácie mojich hračiek (požiadavky na moje hračky)
  ownerPending: number;
  ownerConfirmed: number;
  ownerTotal: number;

  // Celkové počty
  totalPending: number;
  totalConfirmed: number;
  total: number;
}

return {
  counts: ReservationCounts;
  loading: boolean;
  error: string | null;
  refresh: () => void;
  hasActiveReservations: boolean;
}
```

### 2. `ReservationNotificationContext` Kontext
**Súbor:** `src/contexts/ReservationNotificationContext.tsx`

React kontext, ktorý:
- Poskytuje globálny prístup k notifikačným údajom
- Umožňuje zdieľanie stavu medzi komponentami
- Zabezpečuje konzistentné správanie naprieč aplikáciou

### 3. `ReservationNotification` Komponenta
**Súbor:** `src/components/ReservationNotification.tsx`

Hlavná UI komponenta, ktorá:
- Zobrazuje floating element v pravom dolnom rohu
- Používa rôzne ikony pre čakajúce (hodiny) a potvrdené (checkmark) rezervácie
- Mení farbu pozadia podľa typu rezervácií (žltá pre čakajúce, zelená pre potvrdené)
- Zobrazuje celkový počet aktívnych rezervácií v badge
- Je klikateľná a presmerováva na stránku `/rezervacie`
- Obsahuje hover efekty a animácie
- Zobrazuje inteligentné textové správy rozlišujúce medzi mojimi rezerváciami a dopytmi na moje hračky

## Dizajn a UX

### Farebné schémy
- **Čakajúce rezervácie:** Žltá farba (`var(--warning)`) s ikonou hodín
- **Potvrdené rezervácie:** Zelená farba (`var(--success)`) s ikonou checkmark
- **Badge s počtom:** Tyrkysová farba (`var(--primary)`)

### Responzívny dizajn
- **Desktop:** Väčšie padding, väčšie ikony a text
- **Mobile:** Kompaktnejší dizajn s menšími rozmermi
- **Maximálna šírka:** `max-w-xs` na mobile, `max-w-sm` na desktop

### Animácie
- **Fade-in animácia** pri zobrazení
- **Scale efekt** pri hover (105% zväčšenie)
- **Posun šípky** pri hover
- **Smooth transitions** pre všetky interakcie

## Logika zobrazovania

Element sa zobrazuje len ak:
1. Používateľ je prihlásený (`user` existuje)
2. Má aspoň jednu aktívnu rezerváciu (`hasActiveReservations === true`)
3. Údaje sa práve nenačítavajú (`loading === false`)

## Textové správy

Komponenta zobrazuje inteligentné správy na základe stavu rezervácií:

### Jednotlivé typy správ:
- `"X čakajúcich rezervácií"` - moje čakajúce rezervácie
- `"X potvrdených rezervácií"` - moje potvrdené rezervácie
- `"X dopytov na moje hračky"` - čakajúce požiadavky na moje hračky
- `"X potvrdených dopytov"` - potvrdené požiadavky na moje hračky

### Kombinované správy:
- Ak existuje len jeden typ, zobrazí sa konkrétna správa
- Ak existujú dva typy, spoja sa čiarkou: `"2 čakajúcich rezervácií, 1 dopyt na moje hračky"`
- Ak existujú viac ako dva typy, zobrazí sa: `"X aktívnych rezervácií"`

## Integrácia do aplikácie

### Layout integrácia
Komponenta je integrovaná do hlavného layoutu (`src/app/layout.tsx`):

```tsx
<ReservationNotificationProvider>
  <Header />
  <main>{children}</main>
  <Footer />
  <ReservationNotification />
</ReservationNotificationProvider>
```

### Autentifikácia
Používa existujúci autentifikačný systém:
- `useAuth()` hook pre získanie informácií o používateľovi
- Bearer token autentifikácia pre API volania
- Automatické skrytie pre neprihlásených používateľov

## API Endpointy

### GET `/api/reservations`
Načítava rezervácie aktuálne prihláseného používateľa (rezervácie, ktoré si používateľ vytvoril).

**Autentifikácia:** Bearer token v Authorization header
**Odpoveď:** Pole rezervácií s informáciami o hračkách a vlastníkoch

### GET `/api/reservations/owner`
Načítava rezervácie hračiek, ktorých je aktuálne prihlásený používateľ vlastníkom (dopyty na moje hračky).

**Autentifikácia:** Bearer token v Authorization header
**Odpoveď:** Pole rezervácií s informáciami o hračkách a používateľoch, ktorí si rezerváciu vytvorili

## Bezpečnosť

- Všetky API volania používajú Bearer token autentifikáciu
- Komponenta sa nezobrazuje pre neprihlásených používateľov
- Používa existujúce bezpečnostné middleware (`withAuth`)

## Výkon

- Hook používa `useEffect` s dependency na `user` objekt
- Údaje sa načítavajú len pri zmene používateľa
- Paralelné načítanie oboch typov rezervácií pre lepší výkon
- Poskytuje `refresh()` funkciu pre manuálne obnovenie
- Minimálne re-rendery vďaka optimalizovanému state managementu

## Budúce rozšírenia

1. **Real-time aktualizácie:** WebSocket pripojenie pre okamžité aktualizácie
2. **Push notifikácie:** Integrácia s browser notification API
3. **Zvukové upozornenia:** Voliteľné zvukové signály pre nové rezervácie
4. **Konfigurácia:** Možnosť vypnutia notifikácií v používateľskom profile
5. **Animované prechody:** Sofistikovanejšie animácie pri zmene stavu

## Testovanie

Pre testovanie funkcionality:

### Testovanie mojich rezervácií:
1. Prihláste sa do aplikácie
2. Vytvorte rezerváciu cudzej hračky
3. Overte zobrazenie notifikačného elementu s textom "1 čakajúcich rezervácií"
4. Kliknite na element a overte presmerovanie na `/rezervacie`

### Testovanie dopytov na moje hračky:
1. Prihláste sa do aplikácie
2. Pridajte hračku do systému
3. Prihláste sa ako iný používateľ a vytvorte rezerváciu tejto hračky
4. Vráťte sa k pôvodnému používateľovi
5. Overte zobrazenie notifikačného elementu s textom "1 dopyt na moje hračky"

### Testovanie kombinovaných notifikácií:
1. Vytvorte scenár s oboma typmi rezervácií
2. Overte zobrazenie kombinovanej správy
3. Otestujte responzívny dizajn na rôznych zariadeniach
