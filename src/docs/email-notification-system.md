# Emailový notifikačný systém pre rezervácie hračiek

## Prehľad

Implementovaný emailový notifikačný systém automaticky odosiela emailové potvrdenia používateľom v dvoch kľúčových scenároch:

1. **Vytvorenie novej rezervácie** - Potvrdenie pre používateľa, ktorý vytvoril rezerváciu
2. **Schválenie rezervácie** - Notifikácia s kontaktnými údajmi po schválení rezervácie vlastníkom

Systém je plne GDPR-kompatibilný s možnosťou odhlásenia a rešpektuje používateľské preferencie.

## Technické riešenie

### Architektúra

- **Email Service Provider**: Resend API
- **Asynchronné spracovanie**: In-memory fronta s retry logikou
- **Templating**: HTML + text verzie emailov v slovenčine
- **GDPR compliance**: Unsubscribe mechanizmus a audit log

### Komponenty

#### 1. Email Service (`lib/emailService.ts`)
- Základné funkcie pre odosielanie emailov
- HTML a text templaty pre oba typy notifikácií
- Integrácia s Resend API
- Generovanie unsubscribe linkov

#### 2. Email Notification Service (`lib/emailNotificationService.ts`)
- Asynchronná fronta pre spracovanie emailov
- Retry logika s exponenciálnym backoff
- Kontrola používateľských preferencií
- Error handling a logging

#### 3. Email Preferences (`lib/emailPreferences.ts`)
- Správa používateľských preferencií
- GDPR-kompatibilné unsubscribe funkcie
- Audit log pre odhlásenia
- Token-based unsubscribe systém

### Databázové zmeny

Pridané stĺpce do tabuľky `User`:
```sql
emailNotificationsEnabled BOOLEAN DEFAULT TRUE
emailReservationCreated BOOLEAN DEFAULT TRUE  
emailReservationApproved BOOLEAN DEFAULT TRUE
emailUnsubscribeToken VARCHAR(255)
```

Nová tabuľka `EmailUnsubscribe`:
```sql
id INT PRIMARY KEY AUTO_INCREMENT
email VARCHAR(191)
token VARCHAR(255) UNIQUE
reason VARCHAR(255)
unsubscribedAt DATETIME DEFAULT NOW()
ipAddress VARCHAR(45)
userAgent TEXT
```

## Integrácia s existujúcimi API

### Vytvorenie rezervácie
**Endpoint**: `POST /api/reservations`

Po úspešnom vytvorení rezervácie sa automaticky pridá email do fronty:
```javascript
const emailData = {
  userId: user.id,
  userName: user.name,
  userEmail: user.email,
  toyName: toy.name,
  reservationId: transformedReservation.hashedId,
  position: position,
  queueLength: activeReservations.length + 1
};

queueReservationCreatedEmail(emailData);
```

### Schválenie rezervácie
**Endpoint**: `PUT /api/reservations/[id]/status`

Pri zmene statusu na 'CONFIRMED' sa automaticky pridá email do fronty:
```javascript
if (status === 'CONFIRMED') {
  const emailData = {
    userId: updatedReservation.user.id,
    userName: updatedReservation.user.name,
    userEmail: updatedReservation.user.email,
    toyName: updatedReservation.toy.name,
    reservationId: transformedReservation.hashedId,
    ownerName: updatedReservation.owner.name,
    ownerEmail: updatedReservation.owner.email,
    ownerPhone: updatedReservation.owner.phone,
    ownerCity: updatedReservation.owner.city,
  };
  
  queueReservationApprovedEmail(emailData);
}
```

## GDPR Compliance

### Unsubscribe mechanizmus
- Každý email obsahuje unsubscribe link
- Jedinečný token pre každého používateľa
- Automatické vypnutie všetkých emailových notifikácií
- Audit log pre compliance

### Unsubscribe endpoint
**URL**: `GET /api/email/unsubscribe?token=<token>`

Spracuje odhlásenie a zobrazí potvrdzovaciu stránku.

### Používateľské preferencie
Používatelia môžu kontrolovať:
- Globálne emailové notifikácie (`emailNotificationsEnabled`)
- Notifikácie o vytvorení rezervácie (`emailReservationCreated`)
- Notifikácie o schválení rezervácie (`emailReservationApproved`)

## Konfigurácia

### Environment variables
```env
# Email konfigurácia
RESEND_API_KEY="re_AShf6Fz1_4bivz5PVs9chLJSLRfNU4JVY"
EMAIL_FROM="<EMAIL>"
EMAIL_FROM_NAME="Swapka - Zdieľanie hračiek"
NEXT_PUBLIC_BASE_URL="https://swapka.sk"
```

### Retry konfigurácia
```javascript
const EMAIL_CONFIG = {
  maxRetries: 3,
  retryDelayMs: 5000, // 5 sekúnd
  maxRetryDelayMs: 60000, // 1 minúta
  enabled: process.env.NODE_ENV !== 'test'
};
```

## Deployment

### 1. Spustenie migrácie
```bash
cd src
node scripts/run-email-migration.js
```

### 2. Generovanie Prisma klienta
```bash
npx prisma generate
```

### 3. Testovanie
```bash
node scripts/test-email-send.js
```

### 4. Reštart aplikácie
```bash
pm2 restart swapka
```

## Monitoring a debugging

### Queue status
```javascript
import { getEmailQueueStatus } from './lib/emailNotificationService';

const status = getEmailQueueStatus();
console.log('Queue length:', status.queueLength);
console.log('Is processing:', status.isProcessing);
console.log('Pending emails:', status.pendingEmails);
```

### Logs
Systém loguje všetky dôležité udalosti:
- Pridanie emailu do fronty
- Úspešné odoslanie
- Chyby a retry pokusy
- Unsubscribe udalosti

## Bezpečnosť

### Input sanitization
- Všetky používateľské údaje sú sanitizované pred použitím v emailoch
- XSS ochrana v HTML templatech
- SQL injection ochrana cez Prisma

### Rate limiting
- Emailové API má vlastné rate limiting
- Retry logika rešpektuje API limity
- Exponenciálny backoff pre retry pokusy

## Budúce vylepšenia

1. **Redis queue** - Pre produkčné nasazenie s viacerými servermi
2. **Email templates editor** - Admin rozhranie pre úpravu templateov
3. **Email analytics** - Sledovanie open rates a click rates
4. **Personalizácia** - Pokročilejšie personalizované emaily
5. **Bulk operations** - Hromadné odosielanie emailov

## Troubleshooting

### Emaily sa neodosielajú
1. Skontrolujte `RESEND_API_KEY` v .env
2. Overte, že migrácia bola úspešná
3. Skontrolujte logy aplikácie
4. Overte používateľské preferencie

### Unsubscribe nefunguje
1. Skontrolujte, že `EmailUnsubscribe` tabuľka existuje
2. Overte `NEXT_PUBLIC_BASE_URL` konfiguráciu
3. Skontrolujte token validáciu

### Performance problémy
1. Monitorujte veľkosť email fronty
2. Skontrolujte retry rate
3. Zvážte Redis implementáciu pre produkciu
