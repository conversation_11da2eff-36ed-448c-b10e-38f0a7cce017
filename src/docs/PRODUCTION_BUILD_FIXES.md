# Production Build Fixes

## Overview

This document describes the fixes applied to resolve critical production build issues in the Next.js Swapka application.

## Issues Fixed

### 1. libheif-js Dependency Extraction Issue

**Problem:** The libheif-js library (used by heic-convert) was causing webpack dependency extraction errors during production builds due to dynamic require() calls.

**Solution:** 
- Replaced static import with dynamic import in `src/lib/imageProcessing.ts`
- Changed from `import heicConvert from 'heic-convert'` to `const heicConvert = (await import('heic-convert')).default`
- This allows webpack to properly handle the dependency without static analysis issues

**Files Modified:**
- `src/lib/imageProcessing.ts` - Updated convertHeicToJpeg function to use dynamic import

### 2. express-rate-limit Edge Runtime Compatibility

**Problem:** The express-rate-limit package was trying to load Node.js modules ('crypto' and 'net') that are not supported in the Edge Runtime.

**Solution:**
- Removed express-rate-limit dependency from rate limiting implementation
- Created custom in-memory rate limiting implementation that is Edge Runtime compatible
- Added `runtime: 'nodejs'` to middleware configuration to ensure Node.js runtime is used
- Replaced all express-rate-limit usage with custom rate limiters

**Files Modified:**
- `src/lib/rateLimiting.ts` - Replaced express-rate-limit with custom implementation
- `src/middleware/rateLimit.ts` - Updated to use new custom rate limiters
- `src/middleware.ts` - Added `runtime: 'nodejs'` configuration

### 3. PM2 Configuration Cleanup

**Problem:** PM2 configuration contained outdated New Relic references that could cause issues.

**Solution:**
- Removed New Relic environment variables from PM2 configuration
- Cleaned up production and development configurations
- PM2 already correctly configured to use standalone server with `node .next/standalone/server.js`

**Files Modified:**
- `ecosystem.config.js` - Removed NEW_RELIC_APP_NAME and NEW_RELIC_LICENSE_KEY

## Technical Details

### Custom Rate Limiting Implementation

The new rate limiting implementation:
- Uses in-memory Map for storing rate limit counters
- Automatically cleans up expired entries every 5 minutes
- Supports all existing rate limit types (auth, general, admin, upload, attack)
- Maintains the same API and functionality as before
- Is fully compatible with Edge Runtime and Node.js runtime

### Dynamic Import Benefits

Using dynamic imports for heic-convert:
- Resolves webpack static analysis issues
- Allows the library to be loaded only when needed
- Maintains full functionality for HEIC image processing
- Reduces initial bundle size

### Middleware Runtime Configuration

Setting `runtime: 'nodejs'` in middleware:
- Ensures access to Node.js APIs needed for rate limiting
- Avoids Edge Runtime compatibility issues
- Maintains performance while ensuring functionality

## Testing

To test the fixes:

1. **Build the application:**
   ```bash
   cd src
   npm run build
   ```

2. **Start production server:**
   ```bash
   npm start
   ```

3. **Test HEIC image processing:**
   - Upload a HEIC image through the application
   - Verify it converts properly to JPEG/WebP

4. **Test rate limiting:**
   - Make multiple rapid requests to API endpoints
   - Verify rate limiting responses (429 status codes)

5. **Test PM2 deployment:**
   ```bash
   pm2 start ../ecosystem.config.js --only swapka-prod
   ```

## Deployment Notes

- The application now builds successfully with `output: 'standalone'`
- PM2 correctly uses `node .next/standalone/server.js` for production
- All rate limiting functionality is preserved
- HEIC image processing continues to work as expected
- No external dependencies on express-rate-limit or problematic Node.js modules

## Rollback Plan

If issues arise, the changes can be rolled back by:
1. Reverting to static imports in `imageProcessing.ts`
2. Reinstalling express-rate-limit and reverting rate limiting implementation
3. Restoring New Relic configuration in PM2 if needed

However, this would reintroduce the original build issues.
