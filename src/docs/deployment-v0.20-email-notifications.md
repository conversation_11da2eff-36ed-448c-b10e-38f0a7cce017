# Deployment Notes - Verzia 0.20: Emailové notifikácie

## Prehľad zmien

Verzia 0.20 pridáva kompletný emailový notifikačný systém pre rezervácie hračiek s GDPR compliance.

### Nové funkcie
- ✅ Automatické emailové potvrdenia pri vytvorení rezervácie
- ✅ Emailové notifikácie pri schválení rezervácie s kontaktnými údajmi
- ✅ GDPR-kompatibilný unsubscribe systém
- ✅ Používateľské preferencie pre emailové notifikácie
- ✅ Asynchronné spracovanie emailov s retry logikou
- ✅ Audit log pre odhlásenia z emailov

### Technické zmeny
- Pridaný Resend email service provider
- Nové databázové stĺpce pre email preferencie
- Nová tabuľka EmailUnsubscribe pre GDPR compliance
- Nové API endpointy pre unsubscribe
- Rozšírené existujúce API pre email integráciu

## Pre-deployment checklist

### 1. Backup databázy
```bash
# Vytvorte backup pred migráciou
mysqldump -u username -p database_name > backup_before_v0.20.sql
```

### 2. Overenie environment variables
Skontrolujte, že sú nastavené v `.env`:
```env
RESEND_API_KEY="re_AShf6Fz1_4bivz5PVs9chLJSLRfNU4JVY"
EMAIL_FROM="<EMAIL>"
EMAIL_FROM_NAME="Swapka - Zdieľanie hračiek"
NEXT_PUBLIC_BASE_URL="https://swapka.sk"
```

### 3. Testovanie Resend API
```bash
# Test pripojenia k Resend API
curl -X POST 'https://api.resend.com/emails' \
  -H 'Authorization: Bearer re_AShf6Fz1_4bivz5PVs9chLJSLRfNU4JVY' \
  -H 'Content-Type: application/json' \
  -d '{
    "from": "<EMAIL>",
    "to": ["<EMAIL>"],
    "subject": "Test",
    "text": "Test email"
  }'
```

## Deployment kroky

### 1. Zastavenie aplikácie
```bash
pm2 stop swapka
```

### 2. Aktualizácia kódu
```bash
cd /path/to/swapka/src
git pull origin main
```

### 3. Inštalácia nových závislostí
```bash
npm install
```

### 4. Spustenie databázovej migrácie
```bash
node scripts/run-email-migration.js
```

Očakávaný výstup:
```
🔄 Spúšťam migráciu emailových preferencií...
✅ Pripojenie k databáze úspešné
📝 Spúšťam 6 SQL príkazov...
✅ Migrácia úspešne dokončená!
🔍 Overujem pridané stĺpce...
   ✅ emailNotificationsEnabled
   ✅ emailReservationCreated
   ✅ emailReservationApproved
   ✅ emailUnsubscribeToken
   ✅ EmailUnsubscribe tabuľka existuje
```

### 5. Generovanie Prisma klienta
```bash
npx prisma generate
```

### 6. Build aplikácie
```bash
npm run build
```

### 7. Testovanie emailového systému
```bash
# Upravte email adresu v scripte na vašu testovaciu adresu
node scripts/test-email-send.js
```

### 8. Spustenie aplikácie
```bash
pm2 start swapka
```

### 9. Overenie funkčnosti
```bash
pm2 logs swapka --lines 50
```

## Post-deployment verifikácia

### 1. Kontrola databázy
```sql
-- Overte nové stĺpce
DESCRIBE User;

-- Overte novú tabuľku
DESCRIBE EmailUnsubscribe;

-- Skontrolujte default hodnoty
SELECT emailNotificationsEnabled, emailReservationCreated, emailReservationApproved 
FROM User LIMIT 5;
```

### 2. Test vytvorenia rezervácie
1. Prihláste sa do aplikácie
2. Vytvorte novú rezerváciu
3. Skontrolujte, či prišiel email s potvrdením
4. Skontrolujte logy: `pm2 logs swapka | grep "Email notification queued"`

### 3. Test schválenia rezervácie
1. Prihláste sa ako vlastník hračky
2. Schváľte existujúcu rezerváciu
3. Skontrolujte, či prišiel email s kontaktnými údajmi
4. Skontrolujte logy: `pm2 logs swapka | grep "Approval email notification queued"`

### 4. Test unsubscribe funkcionality
1. Kliknite na unsubscribe link v emaili
2. Overte, že sa zobrazí potvrdenie
3. Skontrolujte databázu: `SELECT * FROM EmailUnsubscribe ORDER BY unsubscribedAt DESC LIMIT 5;`

## Monitoring

### Kľúčové metriky na sledovanie
- Počet odoslaných emailov
- Úspešnosť doručenia
- Počet unsubscribe udalostí
- Chyby v email fronte

### Log monitoring
```bash
# Sledovanie email logov
pm2 logs swapka | grep -E "(Email|email)"

# Sledovanie chýb
pm2 logs swapka | grep -E "(Error|error|ERROR)"

# Sledovanie email fronty
pm2 logs swapka | grep "queue"
```

## Rollback plán

Ak sa vyskytne kritická chyba:

### 1. Okamžité riešenie
```bash
# Zastavenie aplikácie
pm2 stop swapka

# Návrat na predchádzajúcu verziu
git checkout v0.19

# Obnovenie závislostí
npm install

# Build a spustenie
npm run build
pm2 start swapka
```

### 2. Databázový rollback (ak potrebný)
```sql
-- Odstránenie nových stĺpcov (POZOR: stratia sa dáta!)
ALTER TABLE User DROP COLUMN emailNotificationsEnabled;
ALTER TABLE User DROP COLUMN emailReservationCreated;
ALTER TABLE User DROP COLUMN emailReservationApproved;
ALTER TABLE User DROP COLUMN emailUnsubscribeToken;

-- Odstránenie novej tabuľky
DROP TABLE EmailUnsubscribe;
```

### 3. Obnovenie z backupu
```bash
# Ak je potrebný úplný rollback databázy
mysql -u username -p database_name < backup_before_v0.20.sql
```

## Známe problémy a riešenia

### 1. Email sa neodosielajú
**Príčina**: Neplatný RESEND_API_KEY alebo nesprávna konfigurácia
**Riešenie**: 
- Overte API key v .env súbore
- Skontrolujte Resend dashboard pre limity
- Overte FROM email adresu

### 2. Unsubscribe link nefunguje
**Príčina**: Nesprávny NEXT_PUBLIC_BASE_URL
**Riešenie**: 
- Overte BASE_URL v .env
- Skontrolujte, že API endpoint je dostupný

### 3. Vysoká záťaž na server
**Príčina**: Veľká email fronta
**Riešenie**: 
- Monitorujte veľkosť fronty
- Zvážte Redis implementáciu pre produkciu

## Kontakt pre podporu

V prípade problémov kontaktujte:
- **Vývojár**: Michal Gašparík
- **Email**: <EMAIL>
- **Dokumentácia**: `/docs/email-notification-system.md`

## Changelog

### v0.20.0 (2025-01-17)
- ✅ Pridaný emailový notifikačný systém
- ✅ Resend API integrácia
- ✅ GDPR-kompatibilný unsubscribe systém
- ✅ Databázové migrácie pre email preferencie
- ✅ Asynchronné spracovanie emailov
- ✅ Kompletná dokumentácia a testy
