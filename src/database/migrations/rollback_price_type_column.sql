-- Rollback Migration: Remove priceType column from Toy table
-- Date: 2024-12-19
-- Description: Removes the priceType field if needed to rollback the pricing type feature

-- Remove the check constraint first (if it exists)
ALTER TABLE `Toy` DROP CONSTRAINT IF EXISTS `chk_toy_price_type`;

-- Remove the priceType column
ALTER TABLE `Toy` DROP COLUMN `priceType`;

-- Verify the rollback
DESCRIBE `Toy`;
