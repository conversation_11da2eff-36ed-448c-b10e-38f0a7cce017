-- Migration: Add priceType column to Toy table
-- Date: 2024-12-19
-- Description: Adds priceType field to support both daily and per-rental pricing

-- Add the priceType column with default value 'PER_DAY'
ALTER TABLE `Toy` ADD COLUMN `priceType` VARCHAR(191) NOT NULL DEFAULT 'PER_DAY';

-- Add a comment to document the column
ALTER TABLE `Toy` MODIFY COLUMN `priceType` VARCHAR(191) NOT NULL DEFAULT 'PER_DAY' COMMENT 'Pricing type: PER_DAY or PER_RENTAL';

-- Optional: Add a check constraint to ensure only valid values are allowed
-- Note: MySQL 8.0+ supports check constraints, older versions will ignore this
ALTER TABLE `Toy` ADD CONSTRAINT `chk_toy_price_type` CHECK (`priceType` IN ('PER_DAY', 'PER_RENTAL'));

-- Update existing toys to have the default pricing type (PER_DAY)
-- This is redundant since we set a default value, but included for clarity
UPDATE `Toy` SET `priceType` = 'PER_DAY' WHERE `priceType` IS NULL OR `priceType` = '';

-- Verify the migration
SELECT COUNT(*) as total_toys, 
       COUNT(CASE WHEN priceType = 'PER_DAY' THEN 1 END) as per_day_toys,
       COUNT(CASE WHEN priceType = 'PER_RENTAL' THEN 1 END) as per_rental_toys
FROM `Toy`;
