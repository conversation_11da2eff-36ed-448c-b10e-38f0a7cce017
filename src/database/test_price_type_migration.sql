-- Test script for priceType migration
-- This script tests the priceType functionality after migration

-- 1. Check if the priceType column exists and has correct structure
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT,
    COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_NAME = 'Toy' AND COLUMN_NAME = 'priceType';

-- 2. Check current distribution of priceType values
SELECT 
    priceType,
    COUNT(*) as count,
    ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM Toy), 2) as percentage
FROM Toy 
GROUP BY priceType;

-- 3. Test inserting a toy with PER_DAY pricing
INSERT INTO Toy (name, description, type, price, deposit, priceType, status, userId, createdAt, updatedAt)
VALUES ('Test Hračka PER_DAY', 'Test popis', 'OTHER', 5.00, 10.00, 'PER_DAY', 'DRAFT', 1, NOW(), NOW());

-- 4. Test inserting a toy with PER_RENTAL pricing
INSERT INTO Toy (name, description, type, price, deposit, priceType, status, userId, createdAt, updatedAt)
VALUES ('Test Hračka PER_RENTAL', 'Test popis', 'OTHER', 15.00, 10.00, 'PER_RENTAL', 'DRAFT', 1, NOW(), NOW());

-- 5. Test inserting a toy without priceType (should default to PER_DAY)
INSERT INTO Toy (name, description, type, price, deposit, status, userId, createdAt, updatedAt)
VALUES ('Test Hračka Default', 'Test popis', 'OTHER', 8.00, 10.00, 'DRAFT', 1, NOW(), NOW());

-- 6. Verify the test inserts
SELECT id, name, price, priceType, createdAt
FROM Toy 
WHERE name LIKE 'Test Hračka%'
ORDER BY createdAt DESC;

-- 7. Test updating priceType
UPDATE Toy 
SET priceType = 'PER_RENTAL' 
WHERE name = 'Test Hračka Default';

-- 8. Verify the update
SELECT id, name, price, priceType, updatedAt
FROM Toy 
WHERE name = 'Test Hračka Default';

-- 9. Test constraint (this should fail if constraint exists)
-- Uncomment the following line to test invalid value rejection:
-- INSERT INTO Toy (name, description, type, price, deposit, priceType, status, userId, createdAt, updatedAt)
-- VALUES ('Test Invalid', 'Test', 'OTHER', 5.00, 10.00, 'INVALID_TYPE', 'DRAFT', 1, NOW(), NOW());

-- 10. Clean up test data
DELETE FROM Toy WHERE name LIKE 'Test Hračka%';

-- 11. Final verification - show sample of toys with their pricing types
SELECT 
    id,
    name,
    price,
    priceType,
    CASE 
        WHEN priceType = 'PER_DAY' THEN CONCAT(price, '€ / deň')
        WHEN priceType = 'PER_RENTAL' THEN CONCAT(price, '€ / výpožičku')
        ELSE CONCAT(price, '€')
    END as formatted_price
FROM Toy 
LIMIT 10;
