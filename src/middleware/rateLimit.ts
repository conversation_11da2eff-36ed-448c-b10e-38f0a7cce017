import { NextRequest, NextResponse } from 'next/server';
import {
  rateLimitConfig,
  isAuthEndpoint,
  isAdminEndpoint,
  isUploadEndpoint,
  isAttackPattern,
  authRateLimiter,
  generalRateLimiter,
  adminRateLimiter,
  uploadRateLimiter,
  attackRateLimiter
} from '../lib/rateLimiting';

// Removed old memory store implementation - now using custom rate limiters from rateLimiting.ts

/**
 * Získanie IP adresy z NextRequest objektu
 */
function getClientIP(request: NextRequest): string {
  // Skúsime rôzne hlavičky pre získanie skutočnej IP adresy
  const forwarded = request.headers.get('x-forwarded-for');
  const realIP = request.headers.get('x-real-ip');
  const cfConnectingIP = request.headers.get('cf-connecting-ip');

  if (forwarded) {
    // x-forwarded-for môže obsahovať viacero IP adries oddelených čiarkami
    return forwarded.split(',')[0].trim();
  }

  if (realIP) {
    return realIP.trim();
  }

  if (cfConnectingIP) {
    return cfConnectingIP.trim();
  }

  // Fallback na localhost pre development (NextRequest nemá vlastnosť ip v Next.js 15)
  return '127.0.0.1';
}

// Removed old utility functions - whitelist checking is now handled in rateLimiting.ts

/**
 * Hlavná rate limiting middleware funkcia (using custom rate limiters)
 */
export function rateLimitMiddleware(request: NextRequest): NextResponse | null {
  // Ak je rate limiting vypnuté, preskočíme
  if (!rateLimitConfig.enabled) {
    return null;
  }

  const { pathname } = request.nextUrl;

  // Aplikujeme rate limiting na API routes a attack patterns
  if (!pathname.startsWith('/api/') && !isAttackPattern(pathname)) {
    return null;
  }

  const ip = getClientIP(request);

  // Determine which rate limiter to use
  let limiter;
  if (isAttackPattern(pathname)) {
    limiter = attackRateLimiter;
  } else if (isAuthEndpoint(pathname)) {
    limiter = authRateLimiter;
  } else if (isAdminEndpoint(pathname)) {
    limiter = adminRateLimiter;
  } else if (isUploadEndpoint(pathname)) {
    limiter = uploadRateLimiter;
  } else {
    limiter = generalRateLimiter;
  }

  // Check rate limit
  const result = limiter.check(ip);

  // Create response with rate limit headers
  const response = NextResponse.next();
  response.headers.set('X-RateLimit-Limit', limiter.config.max.toString());
  response.headers.set('X-RateLimit-Remaining', result.remaining.toString());
  response.headers.set('X-RateLimit-Reset', new Date(result.resetTime).toISOString());
  response.headers.set('X-RateLimit-Type', limiter.type);

  // If rate limit exceeded, return 429 response
  if (!result.allowed) {
    const genericMessages = {
      auth: 'Príliš veľa pokusov o prihlásenie. Skúste to znovu neskôr.',
      admin: 'Príliš veľa požiadaviek na admin operácie. Skúste to znovu neskôr.',
      upload: 'Príliš veľa nahrávaní súborov. Skúste to znovu neskôr.',
      attack: 'Príliš veľa podozrivých požiadaviek. Prístup bol dočasne obmedzený.',
      general: 'Príliš veľa požiadaviek. Skúste to znovu neskôr.',
    };

    return NextResponse.json(
      {
        error: 'Príliš veľa požiadaviek',
        message: genericMessages[limiter.type as keyof typeof genericMessages] || genericMessages.general,
      },
      {
        status: 429,
        headers: {
          'X-RateLimit-Limit': limiter.config.max.toString(),
          'X-RateLimit-Remaining': '0',
          'X-RateLimit-Reset': new Date(result.resetTime).toISOString(),
          'X-RateLimit-Type': limiter.type,
          'Content-Type': 'application/json',
        },
      }
    );
  }

  return response;
}

/**
 * Utility functions for admin rate limit management
 * These functions provide access to the rate limiting store for monitoring and management
 */

// Import the rate limit store from rateLimiting.ts
import { rateLimitStore } from '../lib/rateLimiting';

/**
 * Utility funkcia pre získanie štatistík rate limiting
 * Používa sa pre monitoring a debugging
 */
export function getRateLimitStats(): {
  totalEntries: number;
  entriesByType: Record<string, number>;
  oldestEntry: Date | null;
  newestEntry: Date | null;
} {
  const entries: [string, { count: number; resetTime: number }][] = Array.from(rateLimitStore.entries());
  const stats = {
    totalEntries: entries.length,
    entriesByType: {} as Record<string, number>,
    oldestEntry: null as Date | null,
    newestEntry: null as Date | null,
  };

  let oldestTime = Infinity;
  let newestTime = 0;

  for (const [key, entry] of entries) {
    const type = key.split(':')[0];
    stats.entriesByType[type] = (stats.entriesByType[type] || 0) + 1;

    if (entry.resetTime < oldestTime) {
      oldestTime = entry.resetTime;
      stats.oldestEntry = new Date(entry.resetTime);
    }

    if (entry.resetTime > newestTime) {
      newestTime = entry.resetTime;
      stats.newestEntry = new Date(entry.resetTime);
    }
  }

  return stats;
}

/**
 * Utility funkcia pre manuálne vyčistenie rate limit záznamov
 * Používa sa pre admin operácie
 */
export function clearRateLimitForIP(ip: string): boolean {
  const types = ['auth', 'general', 'admin', 'upload', 'attack'];
  let cleared = false;

  for (const type of types) {
    const key = `${type}:${ip}`;
    if (rateLimitStore.has(key)) {
      rateLimitStore.delete(key);
      cleared = true;
    }
  }

  return cleared;
}

/**
 * Utility funkcia pre manuálne vyčistenie všetkých rate limit záznamov
 * Používa sa pre admin operácie alebo reštart
 */
export function clearAllRateLimits(): number {
  const count = rateLimitStore.size;
  rateLimitStore.clear();
  return count;
}
