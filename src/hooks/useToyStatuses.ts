import { useState, useEffect } from 'react';
import { translateToyStatus } from '../lib/constants';

export interface ToyStatusOption {
  value: string;
  label: string;
}

export interface UseToyStatusesResult {
  statuses: ToyStatusOption[];
  loading: boolean;
  error: string | null;
  refetch: () => void;
}

/**
 * Custom hook for fetching toy statuses from the database
 * Includes fallback to hardcoded translations if API fails
 */
export function useToyStatuses(): UseToyStatusesResult {
  const [statuses, setStatuses] = useState<ToyStatusOption[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchStatuses = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch('/api/toy-statuses/public');
      
      if (!response.ok) {
        throw new Error('Nepodarilo sa načítať statusy hračiek');
      }

      const data: ToyStatusOption[] = await response.json();
      setStatuses(data);
    } catch (err) {
      console.error('Chyba pri načítaní statusov hračiek:', err);
      setError(err instanceof Error ? err.message : 'Nastala chyba pri načítaní statusov');
      
      // Fallback to hardcoded statuses from constants
      const fallbackStatuses: ToyStatusOption[] = [
        { value: 'DRAFT', label: translateToyStatus('DRAFT') },
        { value: 'AVAILABLE', label: translateToyStatus('AVAILABLE') },
        { value: 'UNAVAILABLE', label: translateToyStatus('UNAVAILABLE') },
        { value: 'RESERVED', label: translateToyStatus('RESERVED') },
      ];
      
      setStatuses(fallbackStatuses);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchStatuses();
  }, []);

  const refetch = () => {
    fetchStatuses();
  };

  return {
    statuses,
    loading,
    error,
    refetch,
  };
}
