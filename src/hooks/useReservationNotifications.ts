import { useState, useEffect } from 'react';
import { useAuth } from '../contexts/AuthContext';

interface ReservationCounts {
  // <PERSON><PERSON> (rezervácie, ktoré si používateľ vytvoril)
  myPending: number;
  myConfirmed: number;
  myTotal: number;

  // Rezervácie mojich hračiek (požiadavky na moje hračky)
  ownerPending: number;
  ownerConfirmed: number;
  ownerTotal: number;

  // Celkové počty
  totalPending: number;
  totalConfirmed: number;
  total: number;
}

export function useReservationNotifications() {
  const { user } = useAuth();
  const [counts, setCounts] = useState<ReservationCounts>({
    myPending: 0,
    myConfirmed: 0,
    myTotal: 0,
    ownerPending: 0,
    ownerConfirmed: 0,
    ownerTotal: 0,
    totalPending: 0,
    totalConfirmed: 0,
    total: 0
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchReservationCounts = async () => {
    if (!user) {
      setCounts({
        myPending: 0,
        myConfirmed: 0,
        myTotal: 0,
        ownerPending: 0,
        ownerConfirmed: 0,
        ownerTotal: 0,
        totalPending: 0,
        totalConfirmed: 0,
        total: 0
      });
      return;
    }

    try {
      setLoading(true);
      setError(null);

      // Prepare headers
      const headers: Record<string, string> = {
        'Content-Type': 'application/json',
      };

      // Add authorization token if available
      if (typeof user.getIdToken === 'function') {
        headers['Authorization'] = `Bearer ${await user.getIdToken()}`;
      }

      // Načítanie mojich rezervácií (rezervácie, ktoré si používateľ vytvoril)
      const myReservationsResponse = await fetch('/api/reservations', {
        method: 'GET',
        headers,
      });

      if (!myReservationsResponse.ok) {
        throw new Error('Nepodarilo sa načítať moje rezervácie');
      }

      const myReservations = await myReservationsResponse.json();

      // Načítanie rezervácií mojich hračiek (požiadavky na moje hračky)
      const ownerReservationsResponse = await fetch('/api/reservations/owner', {
        method: 'GET',
        headers,
      });

      if (!ownerReservationsResponse.ok) {
        throw new Error('Nepodarilo sa načítať rezervácie mojich hračiek');
      }

      const ownerReservations = await ownerReservationsResponse.json();

      // Spracovanie mojich rezervácií - len čakajúce
      const myActiveReservations = myReservations.filter((reservation: any) =>
        reservation.status === 'PENDING'
      );

      const myPendingCount = myReservations.filter((reservation: any) =>
        reservation.status === 'PENDING'
      ).length;

      // Spracovanie rezervácií mojich hračiek - len čakajúce
      const ownerActiveReservations = ownerReservations.filter((reservation: any) =>
        reservation.status === 'PENDING'
      );

      const ownerPendingCount = ownerReservations.filter((reservation: any) =>
        reservation.status === 'PENDING'
      ).length;

      // Celkové počty - len čakajúce rezervácie
      const totalPending = myPendingCount + ownerPendingCount;
      const total = myActiveReservations.length + ownerActiveReservations.length;

      setCounts({
        myPending: myPendingCount,
        myConfirmed: 0, // Nebudeme zobrazovať potvrdené rezervácie
        myTotal: myActiveReservations.length,
        ownerPending: ownerPendingCount,
        ownerConfirmed: 0, // Nebudeme zobrazovať potvrdené rezervácie
        ownerTotal: ownerActiveReservations.length,
        totalPending,
        totalConfirmed: 0, // Nebudeme zobrazovať potvrdené rezervácie
        total
      });

    } catch (err) {
      console.error('Chyba pri načítavaní rezervácií:', err);
      setError(err instanceof Error ? err.message : 'Neznáma chyba');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchReservationCounts();
  }, [user]);

  // Refresh function for manual updates
  const refresh = () => {
    fetchReservationCounts();
  };

  return {
    counts,
    loading,
    error,
    refresh,
    hasActiveReservations: counts.total > 0
  };
}
