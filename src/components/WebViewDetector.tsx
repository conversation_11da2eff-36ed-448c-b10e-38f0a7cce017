'use client';

import { useState, useEffect } from 'react';

/**
 * Component that detects if the application is running inside an in-app WebView
 * and displays a notification with a button to open in the default browser.
 */
export default function WebViewDetector() {
  const [isInWebView, setIsInWebView] = useState(false);
  const [isDismissed, setIsDismissed] = useState(false);

  useEffect(() => {
    // Only run on client-side
    if (typeof window === 'undefined') return;

    // Check if the app is running in a WebView
    const isWebView = detectWebView();
    setIsInWebView(isWebView);

    // Check if the user has previously dismissed the notification
    const dismissed = localStorage.getItem('webview-notification-dismissed');
    if (dismissed === 'true') {
      setIsDismissed(true);
    }
  }, []);

  /**
   * Detects if the current browser is an in-app WebView
   * by analyzing the userAgent string
   */
  const detectWebView = (): boolean => {
    const ua = navigator.userAgent.toLowerCase();

    // Common WebView indicators
    const webViewIndicators = [
      // Facebook
      'fban', 'fbav', 'fbios',
      // Instagram
      'instagram',
      // TikTok
      'tiktok',
      // Snapchat
      'snapchat',
      // Twitter/X
      'twitter', 'twitter for iphone',
      // WhatsApp
      'whatsapp',
      // Messenger
      'messenger',
      // WeChat
      'micromessenger',
      // Line
      'line/',
      // Generic WebView indicators
      'wv)', 'webview',
      // Android WebView
      '; wv)',
      // iOS WebView
      'mobile safari/[.0-9]* (wkwebview)'
    ];

    // Check if any of the WebView indicators are present in the user agent
    return webViewIndicators.some(indicator => ua.includes(indicator));
  };

  /**
   * Opens the current URL in the device's default browser
   */
  const openInBrowser = () => {
    // Get the current URL
    const currentUrl = window.location.href;

    // Open in a new tab/window
    window.open(currentUrl, '_blank');
  };

  /**
   * Dismisses the notification and stores the preference
   */
  const dismissNotification = () => {
    setIsDismissed(true);
    localStorage.setItem('webview-notification-dismissed', 'true');
  };

  // Don't render anything if not in a WebView or if dismissed
  if (!isInWebView || isDismissed) {
    return null;
  }

  return (
    <div className="fixed bottom-0 left-0 right-0 bg-yellow-100 border-t-2 border-yellow-400 text-neutral-800 p-4 shadow-lg z-50">
      <div className="container mx-auto flex flex-col sm:flex-row items-center justify-between">
        <div className="flex-1 mb-3 sm:mb-0 flex items-center">
          <div className="mr-3 text-yellow-600">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
            </svg>
          </div>
          <p className="font-medium">
            Pre plnú funkcionalitu (napr. prihlásenie a nahrávanie HEIC fotografií) otvorte túto stránku v predvolenom prehliadači vášho zariadenia.
          </p>
        </div>
        <div className="flex space-x-3">
          <button
            onClick={openInBrowser}
            className="btn btn-primary text-sm py-1"
          >
            Otvoriť v prehliadači
          </button>
          <button
            onClick={dismissNotification}
            className="text-neutral-600 hover:text-neutral-800"
            aria-label="Zavrieť upozornenie"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
      </div>
    </div>
  );
}
