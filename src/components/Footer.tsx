'use client';

import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { useState, useEffect } from 'react';
import CookieSettingsButton from './CookieSettingsButton';

// Hook pre získanie verzie aplikácie
function useAppVersion() {
  const [version, setVersion] = useState<string>('');

  useEffect(() => {
    // Pokúsime sa načítať verziu z package.json súboru
    try {
      // Načítanie verzie z package.json
      import('../package.json').then((pkg) => {
        setVersion(pkg.version);
      }).catch(() => {
        // Fallback verzia
        setVersion('0.11');
      });
    } catch (error) {
      setVersion('0.11');
    }
  }, []);

  return version;
}

export default function Footer() {
  const currentYear = new Date().getFullYear();
  const pathname = usePathname();
  const version = useAppVersion();

  // Funkcia na kontrolu, či je položka menu aktívna
  const isActive = (path: string) => {
    if (path === '/') {
      return pathname === '/';
    }
    return pathname.startsWith(path);
  };

  // Funkcia na generovanie tried pre položky menu
  const getLinkClassName = (path: string) => {
    return isActive(path)
      ? "text-primary font-medium hover:text-primary-dark"
      : "text-neutral-600 hover:text-primary";
  };

  return (
    <footer className="relative bg-gradient-to-br from-neutral-50 via-white to-primary/5 py-12 mt-auto overflow-hidden">
      {/* Decorative background elements */}
      <div className="absolute inset-0 bg-gradient-to-r from-primary/3 to-secondary/3 opacity-50"></div>
      <div className="absolute top-0 left-1/4 w-96 h-96 bg-primary/5 rounded-full blur-3xl -translate-y-1/2"></div>
      <div className="absolute bottom-0 right-1/4 w-96 h-96 bg-secondary/5 rounded-full blur-3xl translate-y-1/2"></div>

      <div className="relative container mx-auto px-4">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8 lg:gap-12">
          {/* Logo a popis */}
          <div className="col-span-1 md:col-span-1">
            <Link href="/" className="group flex items-center mb-4">
              <span className="text-2xl font-bold bg-gradient-to-r from-primary to-primary-dark bg-clip-text text-transparent group-hover:from-primary-dark group-hover:to-primary transition-all duration-300">
                🎠 Swapka
              </span>
            </Link>
            <p className="text-sm text-neutral-700 leading-relaxed">
              Aplikácia na zdieľanie a požičiavanie detských hračiek medzi rodičmi.
            </p>
            <div className="mt-4 w-16 h-1 bg-gradient-to-r from-primary to-secondary rounded-full"></div>
          </div>

          {/* Navigácia */}
          <div className="col-span-1">
            <h3 className="text-sm font-bold text-neutral-900 uppercase tracking-wider mb-6 relative">
              Navigácia
              <div className="absolute -bottom-2 left-0 w-8 h-0.5 bg-primary rounded-full"></div>
            </h3>
            <ul className="space-y-3">
              <li>
                <Link href="/" className={`${getLinkClassName('/')} block py-1 transition-all duration-200 hover:translate-x-1`}>
                  Domov
                </Link>
              </li>
              <li>
                <Link href="/hracky" className={`${getLinkClassName('/hracky')} block py-1 transition-all duration-200 hover:translate-x-1`}>
                  Hračky
                </Link>
              </li>
              <li>
                <Link href="/ako-to-funguje" className={`${getLinkClassName('/ako-to-funguje')} block py-1 transition-all duration-200 hover:translate-x-1`}>
                  Ako to funguje
                </Link>
              </li>
            </ul>
          </div>

          {/* Právne informácie */}
          <div className="col-span-1">
            <h3 className="text-sm font-bold text-neutral-900 uppercase tracking-wider mb-6 relative">
              Právne informácie
              <div className="absolute -bottom-2 left-0 w-8 h-0.5 bg-primary rounded-full"></div>
            </h3>
            <ul className="space-y-3">
              <li>
                <Link href="/podmienky-pouzitia" className={`${getLinkClassName('/podmienky-pouzitia')} block py-1 transition-all duration-200 hover:translate-x-1`}>
                  Podmienky použitia
                </Link>
              </li>
              <li>
                <Link href="/ochrana-osobnych-udajov" className={`${getLinkClassName('/ochrana-osobnych-udajov')} block py-1 transition-all duration-200 hover:translate-x-1`}>
                  Ochrana osobných údajov
                </Link>
              </li>
              <li>
                <Link href="/cookies" className={`${getLinkClassName('/cookies')} block py-1 transition-all duration-200 hover:translate-x-1`}>
                  Cookies
                </Link>
              </li>
            </ul>
          </div>

          {/* Kontakt */}
          <div className="col-span-1">
            <h3 className="text-sm font-bold text-neutral-900 uppercase tracking-wider mb-6 relative">
              Kontakt
              <div className="absolute -bottom-2 left-0 w-8 h-0.5 bg-primary rounded-full"></div>
            </h3>
            <div className="space-y-3">
              <div className="flex items-center space-x-2 text-neutral-700">
                <svg className="w-4 h-4 text-primary" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z"></path>
                  <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z"></path>
                </svg>
                <span className="text-sm"><EMAIL></span>
              </div>
            </div>
          </div>
        </div>

        {/* Copyright */}
        <div className="mt-12 pt-8 relative">
          <div className="footer-gradient-border absolute top-0 left-0 right-0"></div>
          <div className="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
            <div className="flex flex-col md:flex-row items-center space-y-2 md:space-y-0 md:space-x-4">
              <p className="text-sm text-neutral-600">
                &copy; {currentYear} Swapka. Všetky práva vyhradené.
              </p>
              {version && (
                <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-primary/10 text-primary border border-primary/20 hover:bg-primary/20 transition-colors duration-200">
                  Verzia {version}
                </span>
              )}
            </div>
            <div className="flex items-center space-x-4">
              <CookieSettingsButton />
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
}
