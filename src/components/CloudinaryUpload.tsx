'use client';

import { useState, useEffect, useRef } from 'react';
import { MAX_IMAGES } from '../lib/cloudinary';
import { useAuth } from '../contexts/AuthContext';
import { useDropzone } from 'react-dropzone';
import {
  compressImage,
  progressiveCompress,
  needsCompression,
  isValidImageFile as clientIsValidImageFile,
  formatFileSize,
  CompressionResult
} from '../lib/clientImageProcessing';

interface CloudinaryUploadProps {
  images: string[];
  onChange: (images: string[]) => void;
  toyId?: string; // ID hračky, ku ktorej sa fotografie viažu
}

export default function CloudinaryUpload({ images, onChange, toyId }: CloudinaryUploadProps) {
  const { user } = useAuth();
  const [uploadedImages, setUploadedImages] = useState<string[]>(images || []);
  const [isUploading, setIsUploading] = useState(false);
  const [isCompressing, setIsCompressing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [processingInfo, setProcessingInfo] = useState<string | null>(null);
  const [compressionInfo, setCompressionInfo] = useState<string | null>(null);
  const [cameraStatus, setCameraStatus] = useState<'idle' | 'loading' | 'error'>('idle');
  const [cameraError, setCameraError] = useState<string | null>(null);
  const prevImagesRef = useRef<string[]>(images || []);

  // No need to initialize Cloudinary widget here anymore
  // The script is now loaded in the layout.tsx file

  // Funkcia na filtrovanie placeholder obrázkov
  const filterPlaceholderImages = (imageList: string[]) => {
    return (imageList || []).filter(img => !img.includes('/toys/placeholder.jpg') && !img.includes('placeholder.jpg'));
  };

  // Update internal state when images prop changes from parent
  useEffect(() => {
    if (JSON.stringify(images) !== JSON.stringify(uploadedImages)) {
      // Filtrujeme placeholder obrázky
      const filteredImages = filterPlaceholderImages(images);
      setUploadedImages(filteredImages);
      prevImagesRef.current = filteredImages;
    }
  }, [images]);

  // Update parent component when images change, but avoid the infinite loop
  useEffect(() => {
    // Only call onChange if the images array actually changed
    if (JSON.stringify(prevImagesRef.current) !== JSON.stringify(uploadedImages)) {
      prevImagesRef.current = [...uploadedImages];
      onChange(uploadedImages);
    }
  }, [uploadedImages, onChange]);

  // Enhanced client-side validation for HEIC files
  const isValidImageFile = (file: File): boolean => {
    const allowedExtensions = ['jpg', 'jpeg', 'png', 'gif', 'webp', 'heic', 'heif'];
    const allowedMimeTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp', 'image/heic', 'image/heif', 'application/octet-stream'];

    const extension = file.name.split('.').pop()?.toLowerCase();
    const isValidExtension = extension && allowedExtensions.includes(extension);
    const isValidMimeType = allowedMimeTypes.includes(file.type);

    // Special handling for HEIC files which often have wrong MIME types
    const isHeicFile = extension === 'heic' || extension === 'heif';

    return isValidExtension || isValidMimeType || isHeicFile;
  };

  // Handle file upload through our backend API
  const uploadFile = async (file: File) => {
    if (uploadedImages.length >= MAX_IMAGES) {
      setError(`Maximum ${MAX_IMAGES} images allowed.`);
      return;
    }

    if (!user || (!user.hashedUserId && !user.dbUserId)) {
      setError('Používateľ nie je prihlásený alebo chýba ID používateľa.');
      return;
    }

    // Debug logging for file details
    console.log('Frontend: Uploading file:', {
      name: file.name,
      type: file.type,
      size: file.size,
      lastModified: file.lastModified
    });

    // Enhanced client-side validation using both functions
    if (!isValidImageFile(file) && !clientIsValidImageFile(file)) {
      setError('Neplatný typ súboru. Povolené typy sú: JPG, JPEG, PNG, GIF, WebP, HEIC, HEIF.');
      return;
    }

    if (file.size < 1024) {
      setError('Súbor je príliš malý. Minimálna veľkosť je 1KB.');
      return;
    }

    setError(null);
    setCompressionInfo(null);
    setProcessingInfo(null);

    let fileToUpload = file;

    // Client-side compression if needed
    if (needsCompression(file)) {
      setIsCompressing(true);
      setCompressionInfo(`Komprimujem obrázok (${formatFileSize(file.size)})...`);

      try {
        const compressionResult = await progressiveCompress(file);
        fileToUpload = compressionResult.file;

        setCompressionInfo(
          `Kompresované: ${formatFileSize(compressionResult.originalSize)} → ${formatFileSize(compressionResult.compressedSize)} ` +
          `(${compressionResult.compressionRatio}% úspora), rozlíšenie: ${compressionResult.dimensions.width}x${compressionResult.dimensions.height}`
        );

        console.log('Client-side compression completed:', compressionResult);

        // Clear compression info after 5 seconds
        setTimeout(() => setCompressionInfo(null), 5000);
      } catch (compressionError) {
        console.error('Client-side compression failed:', compressionError);
        setCompressionInfo('Kompresia zlyhala, pokúšam sa nahrať pôvodný súbor...');
        // Continue with original file if compression fails
      } finally {
        setIsCompressing(false);
      }
    }

    setIsUploading(true);
    setProcessingInfo('Nahrávam na server...');

    try {
      // Získanie Firebase tokenu pre autentifikáciu
      let token = '';
      if (user.getIdToken) {
        token = await user.getIdToken();
      }

      // Vytvorenie FormData pre upload súboru (použijeme komprimovaný súbor)
      const formData = new FormData();
      formData.append('file', fileToUpload);

      // Pridanie ID hračky, ak existuje
      if (toyId) {
        formData.append('toyId', toyId);
      }

      // Pripravenie hlavičiek s autentifikačnými údajmi
      const headers: Record<string, string> = {};

      // Pridanie autorizačného tokenu, ak je k dispozícii
      if (token) {
        headers['Authorization'] = `Bearer ${token}`;
      }

      // Nahratie súboru na náš backend endpoint
      const response = await fetch('/api/cloudinary/upload', {
        method: 'POST',
        headers: headers,
        body: formData,
      });

      if (!response.ok) {
        const errorData = await response.json();
        console.error('Upload failed:', {
          status: response.status,
          statusText: response.statusText,
          errorData
        });

        // Show detailed error information
        let errorMessage = errorData.error || 'Failed to upload image';
        if (errorData.details && Array.isArray(errorData.details)) {
          errorMessage += ': ' + errorData.details.join(', ');
        }

        throw new Error(errorMessage);
      }

      const data = await response.json();
      const imageUrl = data.imageUrl;

      // Show processing information if available
      if (data.processing) {
        const proc = data.processing;
        setProcessingInfo(
          `Spracované: ${proc.originalFormat} → ${proc.finalFormat}, ` +
          `${proc.originalSize}KB → ${proc.finalSize}KB (${proc.compressionRatio}% kompresie), ` +
          `rozlíšenie: ${proc.dimensions}`
        );

        // Clear processing info after 5 seconds
        setTimeout(() => setProcessingInfo(null), 5000);
      }

      // Add the new image URL to the list
      setUploadedImages(prev => {
        // Pridáme nový obrázok a filtrujeme placeholder obrázky
        const newImages = filterPlaceholderImages([...prev, imageUrl]);
        // Limit to MAX_IMAGES
        return newImages.slice(0, MAX_IMAGES);
      });
    } catch (error) {
      console.error('Error uploading image:', error);
      setError('Failed to upload image. Please try again.');
    } finally {
      setIsUploading(false);
    }
  };

  // Setup dropzone for file uploads with expanded format support
  const { getRootProps, getInputProps, isDragActive, open } = useDropzone({
    accept: {
      'image/*': ['.jpeg', '.jpg', '.png', '.gif', '.webp', '.heic', '.heif'],
      'application/octet-stream': ['.heic', '.heif'] // HEIC files often have this MIME type
    },
    maxSize: 5 * 1024 * 1024, // 5MB
    maxFiles: 1,
    multiple: false,
    disabled: isCompressing || isUploading || uploadedImages.length >= MAX_IMAGES,
    noClick: true, // Disable click to open file dialog (we'll use our own button)
    noKeyboard: true,
    validator: (file) => {
      // Custom validator that's more permissive for HEIC files
      if (!isValidImageFile(file)) {
        return {
          code: 'file-invalid-type',
          message: 'Neplatný typ súboru. Povolené typy sú: JPG, JPEG, PNG, GIF, WebP, HEIC, HEIF.'
        };
      }
      return null;
    },
    onDrop: async (acceptedFiles, rejectedFiles) => {
      // Handle accepted files
      if (acceptedFiles.length > 0) {
        await uploadFile(acceptedFiles[0]);
      }
      // Handle rejected files with custom validation
      else if (rejectedFiles.length > 0) {
        const rejection = rejectedFiles[0];
        if (rejection.file && isValidImageFile(rejection.file)) {
          // File is actually valid (HEIC with wrong MIME type), try to upload anyway
          await uploadFile(rejection.file);
        }
      }
    },
    onDropRejected: (fileRejections) => {
      const rejection = fileRejections[0];
      if (rejection) {
        if (rejection.errors[0].code === 'file-too-large') {
          setError('Súbor je príliš veľký. Maximálna veľkosť je 5MB.');
        } else if (rejection.errors[0].code === 'file-invalid-type') {
          setError('Neplatný typ súboru. Povolené typy sú: JPG, JPEG, PNG, GIF, WebP, HEIC, HEIF.');
        } else {
          setError(`Chyba pri nahrávaní: ${rejection.errors[0].message}`);
        }
      }
    }
  });

  // Remove an image from the list
  const removeImage = (index: number) => {
    setUploadedImages(prev => {
      // Odstránime obrázok na danom indexe
      const newImages = prev.filter((_, i) => i !== index);
      // Ešte raz filtrujeme placeholder obrázky pre istotu
      return filterPlaceholderImages(newImages);
    });
  };

  // Handle camera access with proper error handling
  const handleCameraCapture = async () => {
    // Clear previous camera errors and general errors
    setCameraError(null);
    setError(null);
    setCameraStatus('loading');

    try {
      // Check if we can upload more images
      if (uploadedImages.length >= MAX_IMAGES) {
        setCameraError(`Dosiahnutý maximálny počet fotografií (${MAX_IMAGES})`);
        setCameraStatus('error');
        // Auto-clear error after 5 seconds
        setTimeout(() => {
          setCameraError(null);
          setCameraStatus('idle');
        }, 5000);
        return;
      }

      // Check if user is authenticated
      if (!user || !user.hashedUserId) {
        setCameraError('Používateľ nie je prihlásený alebo chýba ID používateľa.');
        setCameraStatus('error');
        // Auto-clear error after 5 seconds
        setTimeout(() => {
          setCameraError(null);
          setCameraStatus('idle');
        }, 5000);
        return;
      }

      // Check if the device supports file input (much more compatible than getUserMedia)
      // This approach works with HTML5 file input + capture attribute, which is supported
      // by virtually all modern browsers including mobile browsers and WebViews
      // Unlike navigator.mediaDevices.getUserMedia, this doesn't require special permissions
      const supportsFileInput = typeof document !== 'undefined' && 'createElement' in document;

      if (!supportsFileInput) {
        setCameraError('Váš prehliadač nepodporuje nahrávanie súborov.');
        setCameraStatus('error');
        // Auto-clear error after 8 seconds for this type of error
        setTimeout(() => {
          setCameraError(null);
          setCameraStatus('idle');
        }, 8000);
        return;
      }

      // Create a file input element with camera capture
      const input = document.createElement('input');
      input.type = 'file';
      input.accept = 'image/jpeg,image/jpg,image/png,image/gif,image/webp,image/heic,image/heif';
      input.capture = 'environment'; // Use the back camera

      // Handle file selection
      input.onchange = async (e) => {
        try {
          const files = (e.target as HTMLInputElement).files;
          if (files && files.length > 0) {
            setCameraStatus('loading');
            await uploadFile(files[0]);
            setCameraStatus('idle');
            // Clear any previous camera errors on successful upload
            setCameraError(null);
          } else {
            setCameraStatus('idle');
          }
        } catch (error) {
          console.error('Chyba pri spracovaní fotografie z fotoaparátu:', error);
          setCameraError(error instanceof Error ? error.message : 'Nepodarilo sa spracovať fotografiu z fotoaparátu');
          setCameraStatus('error');
          // Auto-clear error after 6 seconds
          setTimeout(() => {
            setCameraError(null);
            setCameraStatus('idle');
          }, 6000);
        }
      };

      // Handle cancellation (when user closes camera without taking photo)
      input.oncancel = () => {
        setCameraStatus('idle');
      };

      // Reset status when starting
      setCameraStatus('idle');

      // Trigger the file input
      input.click();
    } catch (error) {
      console.error('Chyba pri prístupe k fotoaparátu:', error);
      setCameraError(error instanceof Error ? error.message : 'Nepodarilo sa získať prístup k fotoaparátu');
      setCameraStatus('error');
      // Auto-clear error after 6 seconds
      setTimeout(() => {
        setCameraError(null);
        setCameraStatus('idle');
      }, 6000);
    }
  };

  // Reorder images by dragging (first image is the thumbnail)
  const moveImage = (fromIndex: number, toIndex: number) => {
    setUploadedImages(prev => {
      // Presunieme obrázok
      const newImages = [...prev];
      const [movedImage] = newImages.splice(fromIndex, 1);
      newImages.splice(toIndex, 0, movedImage);
      // Ešte raz filtrujeme placeholder obrázky pre istotu
      return filterPlaceholderImages(newImages);
    });
  };

  return (
    <div className="space-y-4">
      <div className="flex flex-col">
        <label className="block text-sm font-medium text-neutral-700 mb-1">
          Fotografie hračky {uploadedImages.length > 0 && `(${uploadedImages.length}/${MAX_IMAGES})`}
        </label>
        <p className="text-sm text-neutral-500 mb-2">
          Prvá fotografia bude použitá ako náhľadová. Môžete pridať maximálne {MAX_IMAGES} fotografií.
        </p>
      </div>

      {/* Display uploaded images */}
      {uploadedImages.length > 0 && (
        <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-5 gap-4 mb-4">
          {uploadedImages.map((image, index) => (
            <div
              key={index}
              className={`relative group rounded-lg overflow-hidden border-2 ${index === 0 ? 'border-primary' : 'border-neutral-200'}`}
            >
              <img
                src={image}
                alt={`Uploaded image ${index + 1}`}
                className="w-full h-32 object-cover"
              />
              <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-40 transition-all flex items-center justify-center opacity-0 group-hover:opacity-100">
                <div className="flex space-x-2">
                  {/* Move left button */}
                  {index > 0 && (
                    <button
                      type="button"
                      onClick={() => moveImage(index, index - 1)}
                      className="p-1 bg-white rounded-full text-neutral-700 hover:text-primary"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                      </svg>
                    </button>
                  )}

                  {/* Remove button */}
                  <button
                    type="button"
                    onClick={() => removeImage(index)}
                    className="p-1 bg-white rounded-full text-red-500 hover:text-red-700"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                    </svg>
                  </button>

                  {/* Move right button */}
                  {index < uploadedImages.length - 1 && (
                    <button
                      type="button"
                      onClick={() => moveImage(index, index + 1)}
                      className="p-1 bg-white rounded-full text-neutral-700 hover:text-primary"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                      </svg>
                    </button>
                  )}
                </div>
              </div>
              {index === 0 && (
                <div className="absolute top-0 left-0 bg-primary text-white text-xs px-2 py-1 rounded-br">
                  Náhľad
                </div>
              )}
            </div>
          ))}
        </div>
      )}

      {/* Dropzone for file uploads */}
      <div
        {...getRootProps()}
        className={`border-2 border-dashed rounded-lg p-6 transition-colors ${
          isDragActive
            ? 'border-primary bg-primary-light'
            : isCompressing || isUploading || uploadedImages.length >= MAX_IMAGES
              ? 'border-neutral-300 bg-neutral-50 cursor-not-allowed'
              : 'border-neutral-300 hover:border-primary'
        }`}
      >
        <input {...getInputProps()} />

        <div className="flex flex-col items-center justify-center text-center">
          {isCompressing ? (
            <>
              <svg className="animate-spin mb-3 h-10 w-10 text-orange-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              <p className="text-orange-600">Komprimujem obrázok...</p>
            </>
          ) : isUploading ? (
            <>
              <svg className="animate-spin mb-3 h-10 w-10 text-primary" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              <p className="text-neutral-600">Nahrávam...</p>
            </>
          ) : uploadedImages.length >= MAX_IMAGES ? (
            <>
              <svg xmlns="http://www.w3.org/2000/svg" className="h-10 w-10 mb-3 text-neutral-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
              </svg>
              <p className="text-neutral-600">Dosiahnutý maximálny počet fotografií ({MAX_IMAGES})</p>
            </>
          ) : isDragActive ? (
            <>
              <svg xmlns="http://www.w3.org/2000/svg" className="h-10 w-10 mb-3 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10" />
              </svg>
              <p className="text-primary font-medium">Pustite súbor pre nahratie</p>
            </>
          ) : (
            <>
              <svg xmlns="http://www.w3.org/2000/svg" className="h-10 w-10 mb-3 text-neutral-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
              </svg>
              <p className="text-neutral-600 mb-2">Pretiahnite sem fotografie alebo</p>
              <button
                type="button"
                onClick={open}
                disabled={isCompressing || isUploading || uploadedImages.length >= MAX_IMAGES}
                className={`px-4 py-2 rounded-md ${
                  isCompressing || isUploading || uploadedImages.length >= MAX_IMAGES
                    ? 'bg-neutral-200 text-neutral-500 cursor-not-allowed'
                    : 'bg-primary text-white hover:bg-primary-dark'
                }`}
              >
                Vyberte súbor
              </button>
              <p className="text-xs text-neutral-500 mt-2">
                Podporované formáty: JPG, PNG, GIF, WebP, HEIC, HEIF
              </p>
              <p className="text-xs text-neutral-400 mt-1">
                Veľké obrázky sa automaticky komprimujú pred nahrávaním na server
              </p>
            </>
          )}
        </div>
      </div>

      {/* Improved camera button */}
      {!isCompressing && !isUploading && uploadedImages.length < MAX_IMAGES && (
        <button
          type="button"
          onClick={handleCameraCapture}
          disabled={cameraStatus === 'loading'}
          className={`mt-3 btn w-full ${
            cameraStatus === 'loading'
              ? 'bg-neutral-200 text-neutral-500 cursor-not-allowed'
              : 'btn-primary'
          }`}
          aria-label="Otvoriť fotoaparát pre zachytenie fotografie"
        >
          {cameraStatus === 'loading' ? (
            <>
              <svg className="animate-spin h-5 w-5 mr-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              Spracovávam...
            </>
          ) : (
            <>
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z" />
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 13a3 3 0 11-6 0 3 3 0 016 0z" />
              </svg>
              Použiť fotoaparát
            </>
          )}
        </button>
      )}

      {/* Compression information */}
      {compressionInfo && (
        <div className="mt-4 p-3 bg-orange-50 border border-orange-200 rounded-md">
          <p className="text-sm text-orange-700">{compressionInfo}</p>
        </div>
      )}

      {/* Processing information */}
      {processingInfo && (
        <div className="mt-4 p-3 bg-green-50 border border-green-200 rounded-md">
          <p className="text-sm text-green-700">{processingInfo}</p>
        </div>
      )}

      {/* Camera error message */}
      {cameraError && (
        <div className="mt-3 p-3 bg-red-50 border border-red-200 rounded-md">
          <div className="flex items-center">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-red-400 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
            </svg>
            <p className="text-sm text-red-700">{cameraError}</p>
          </div>
        </div>
      )}

      {/* General error message */}
      {error && <p className="text-red-500 text-sm mt-3">{error}</p>}
    </div>
  );
}


