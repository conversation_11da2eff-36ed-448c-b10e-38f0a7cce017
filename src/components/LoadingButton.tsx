'use client';

import React from 'react';

interface LoadingButtonProps {
  isLoading: boolean;
  loadingText?: string;
  children: React.ReactNode;
  className?: string;
  type?: 'button' | 'submit' | 'reset';
  disabled?: boolean;
  onClick?: () => void;
}

/**
 * LoadingButton component displays a button with loading spinner and text
 * Used for form submissions and async operations
 */
export default function LoadingButton({
  isLoading,
  loadingText = 'Načítavam...',
  children,
  className = 'btn btn-primary',
  type = 'button',
  disabled = false,
  onClick
}: LoadingButtonProps) {
  return (
    <button
      type={type}
      className={className}
      disabled={isLoading || disabled}
      onClick={onClick}
    >
      {isLoading ? (
        <div className="flex items-center justify-center">
          {/* Loading Spinner */}
          <svg 
            className="animate-spin h-4 w-4 mr-2" 
            xmlns="http://www.w3.org/2000/svg" 
            fill="none" 
            viewBox="0 0 24 24"
          >
            <circle 
              className="opacity-25" 
              cx="12" 
              cy="12" 
              r="10" 
              stroke="currentColor" 
              strokeWidth="4"
            />
            <path 
              className="opacity-75" 
              fill="currentColor" 
              d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
            />
          </svg>
          <span>{loadingText}</span>
        </div>
      ) : (
        children
      )}
    </button>
  );
}
