'use client';

import { useState } from 'react';

interface DeleteToyModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => Promise<void>;
  toyName: string;
  toyDescription: string;
  isDeleting: boolean;
}

export default function DeleteToyModal({
  isOpen,
  onClose,
  onConfirm,
  toyName,
  toyDescription,
  isDeleting
}: DeleteToyModalProps) {
  if (!isOpen) return null;

  const handleConfirm = async () => {
    await onConfirm();
    onClose();
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 max-w-md w-full">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-bold text-red-600">Ods<PERSON><PERSON><PERSON><PERSON></h2>
          <button
            onClick={onClose}
            className="text-neutral-500 hover:text-neutral-700"
            disabled={isDeleting}
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-6 w-6"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M6 18L18 6M6 6l12 12"
              />
            </svg>
          </button>
        </div>

        <div className="mb-6">
          <div className="bg-red-50 p-4 rounded-lg mb-4">
            <h3 className="font-semibold mb-2">{toyName}</h3>
            <p className="text-sm text-neutral-600 line-clamp-3">{toyDescription}</p>
          </div>
          <p className="text-neutral-700">
            Naozaj chcete odstrániť túto hračku? Táto akcia je nevratná.
          </p>
        </div>

        <div className="flex justify-end space-x-3">
          <button
            onClick={onClose}
            className="btn btn-outline"
            disabled={isDeleting}
          >
            Zrušiť
          </button>
          <button
            onClick={handleConfirm}
            className="btn bg-red-600 hover:bg-red-700 text-white"
            disabled={isDeleting}
          >
            {isDeleting ? (
              <>
                <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Odstraňujem...
              </>
            ) : (
              'Odstrániť'
            )}
          </button>
        </div>
      </div>
    </div>
  );
}
