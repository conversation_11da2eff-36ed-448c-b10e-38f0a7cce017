'use client';

import React, { ReactNode, useEffect, useState } from 'react';
import { useAuth } from '../contexts/AuthContext';
import LoadingSpinner from './LoadingSpinner';

interface AuthLoadingProviderProps {
  children: ReactNode;
}

/**
 * Component that prevents content from flashing before authentication state is known
 * Shows a loading spinner with rotating playful messages until authentication state is definitively determined
 */
export default function AuthLoadingProvider({ children }: AuthLoadingProviderProps) {
  const { loading: authLoading } = useAuth();
  const [authStateKnown, setAuthStateKnown] = useState(false);
  const [currentMessageIndex, setCurrentMessageIndex] = useState(0);

  // Playful loading messages in Slovak
  const loadingMessages = [
    "Hľadám všetky dostupné hračky...",
    "Čistím hračky...",
    "Ukladám ich na svoje miesta...",
    "Chvíľku strpenia...",
    "Pripravujem najlepšie hračky...",
    "Kontrolujem kvalitu hračiek...",
    "Organizujem hračky podľa veku...",
    "Balím hračky s láskou...",
    "Hľadám tie najzábavnejšie...",
    "Testujam všetky funkcie...",
    "Nastavujem bezpečnosť...",
    "Pripájam sa k databáze...",
    "Už to bude hotové...",
    "Posledné úpravy..."
  ];

  // Rotate messages every 2 seconds for better engagement
  useEffect(() => {
    const messageTimer = setInterval(() => {
      setCurrentMessageIndex((prevIndex) =>
        (prevIndex + 1) % loadingMessages.length
      );
    }, 2000);

    return () => clearInterval(messageTimer);
  }, [loadingMessages.length]);

  // Wait for authentication state to be known
  useEffect(() => {
    if (!authLoading) {
      // Add a small delay to ensure all auth-related operations are complete
      const timer = setTimeout(() => {
        setAuthStateKnown(true);
      }, 100);

      return () => clearTimeout(timer);
    }
  }, [authLoading]);

  if (!authStateKnown) {
    return (
      <div className="fixed inset-0 flex flex-col items-center justify-center bg-white z-50 px-4">
        <div className="text-center">
          <LoadingSpinner size="large" text="Načítavam..." />

          {/* Rotating playful messages */}
          <div className="mt-8 h-16 flex items-center justify-center px-2">
            <p
              key={currentMessageIndex}
              className="text-neutral-600 text-sm sm:text-base md:text-lg font-medium animate-fade-in max-w-xs sm:max-w-sm md:max-w-md text-center leading-relaxed"
            >
              {loadingMessages[currentMessageIndex]}
            </p>
          </div>
        </div>
      </div>
    );
  }

  return <>{children}</>;
}
