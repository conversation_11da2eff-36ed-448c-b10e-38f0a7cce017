import React from 'react';

interface EmailTypeStats {
  type: string;
  sent: number;
  failed: number;
}

interface EmailTypeBreakdownProps {
  emailTypeBreakdown: EmailTypeStats[];
}

export default function EmailTypeBreakdown({ emailTypeBreakdown }: EmailTypeBreakdownProps) {
  const formatEmailType = (type: string) => {
    switch (type) {
      case 'RESERVATION_CREATED':
        return 'Rezervácia vytvorená';
      case 'RESERVATION_APPROVED':
        return 'Rezervácia schválená';
      default:
        return type;
    }
  };

  if (emailTypeBreakdown.length === 0) {
    return (
      <div className="bg-white p-6 rounded-lg shadow">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Rozdelenie podľa typu</h3>
        <p className="text-gray-500">Žiadne údaje nie sú k dispozícii</p>
      </div>
    );
  }

  return (
    <div className="bg-white p-6 rounded-lg shadow">
      <h3 className="text-lg font-medium text-gray-900 mb-4">Rozdelenie podľa typu</h3>
      <div className="space-y-3">
        {emailTypeBreakdown.map((typeStats) => {
          const total = typeStats.sent + typeStats.failed;
          const successRate = total > 0 ? (typeStats.sent / total) * 100 : 0;
          
          return (
            <div key={typeStats.type} className="border-b border-gray-100 pb-3 last:border-b-0">
              <div className="flex justify-between items-center mb-2">
                <span className="font-medium text-gray-900">{formatEmailType(typeStats.type)}</span>
                <span className="text-sm text-gray-500">
                  {successRate.toFixed(1)}% úspešnosť
                </span>
              </div>
              <div className="flex justify-between items-center text-sm">
                <div className="flex space-x-4">
                  <span className="text-green-600 flex items-center">
                    <span className="w-2 h-2 bg-green-600 rounded-full mr-1"></span>
                    Odoslané: {typeStats.sent}
                  </span>
                  <span className="text-red-600 flex items-center">
                    <span className="w-2 h-2 bg-red-600 rounded-full mr-1"></span>
                    Neúspešné: {typeStats.failed}
                  </span>
                </div>
                <span className="text-gray-600">Celkom: {total}</span>
              </div>
              {/* Progress bar */}
              <div className="mt-2 w-full bg-gray-200 rounded-full h-2">
                <div 
                  className="bg-green-600 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${successRate}%` }}
                ></div>
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
}
