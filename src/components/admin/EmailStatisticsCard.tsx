import React from 'react';

interface EmailStatisticsCardProps {
  title: string;
  value: string | number;
  color?: 'blue' | 'green' | 'red' | 'orange' | 'gray';
  subtitle?: string;
}

export default function EmailStatisticsCard({ 
  title, 
  value, 
  color = 'gray',
  subtitle 
}: EmailStatisticsCardProps) {
  const getColorClasses = (color: string) => {
    switch (color) {
      case 'blue':
        return 'text-blue-600';
      case 'green':
        return 'text-green-600';
      case 'red':
        return 'text-red-600';
      case 'orange':
        return 'text-orange-600';
      case 'gray':
      default:
        return 'text-gray-600';
    }
  };

  return (
    <div className="bg-white p-6 rounded-lg shadow">
      <h3 className="text-lg font-medium text-gray-900 mb-2">{title}</h3>
      <p className={`text-3xl font-bold ${getColorClasses(color)}`}>
        {value}
      </p>
      {subtitle && (
        <p className="text-sm text-gray-500 mt-1">{subtitle}</p>
      )}
    </div>
  );
}
