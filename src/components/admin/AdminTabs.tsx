'use client';

import React from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';

interface AdminTab {
  id: string;
  label: string;
  path: string;
}

const tabs: AdminTab[] = [
  { id: 'users', label: 'Používatelia', path: '/admin' },
  { id: 'toys', label: '<PERSON><PERSON><PERSON><PERSON>', path: '/admin/toys' },
  { id: 'toy-types', label: 'Typy hračiek', path: '/admin/toy-types' },
  { id: 'toy-statuses', label: 'Statusy hračiek', path: '/admin/toy-statuses' },
  { id: 'reservation-days', label: 'Rezervačné dni', path: '/admin/reservation-days' },
  { id: 'email-monitoring', label: 'Email monitoring', path: '/admin/email-monitoring' },
  { id: 'sitemap', label: 'Sitemap', path: '/admin/sitemap' },
];

export default function AdminTabs() {
  const pathname = usePathname();

  // Určenie aktívnej <PERSON>ž<PERSON> podľa aktuálnej cesty
  const getActiveTab = (): string => {
    if (pathname === '/admin') return 'users';
    if (pathname.includes('/admin/toys')) return 'toys';
    if (pathname.includes('/admin/toy-types')) return 'toy-types';
    if (pathname.includes('/admin/toy-statuses')) return 'toy-statuses';
    if (pathname.includes('/admin/reservation-days')) return 'reservation-days';
    if (pathname.includes('/admin/email-monitoring')) return 'email-monitoring';
    if (pathname.includes('/admin/sitemap')) return 'sitemap';
    return 'users'; // Predvolená záložka
  };

  const activeTab = getActiveTab();

  return (
    <div className="mb-8">
      <div className="border-b border-neutral-200">
        <nav className="-mb-px flex space-x-8" aria-label="Tabs">
          {tabs.map((tab) => (
            <Link
              key={tab.id}
              href={tab.path}
              className={`
                whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm
                ${activeTab === tab.id
                  ? 'border-primary text-primary'
                  : 'border-transparent text-neutral-500 hover:text-neutral-700 hover:border-neutral-300'}
              `}
              aria-current={activeTab === tab.id ? 'page' : undefined}
            >
              {tab.label}
            </Link>
          ))}
        </nav>
      </div>
    </div>
  );
}
