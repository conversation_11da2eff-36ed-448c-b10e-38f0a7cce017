import React from 'react';
import { useToyStatuses } from '../hooks/useToyStatuses';
import CustomSelect from './CustomSelect';

interface ToyStatusSelectProps {
  id: string;
  name: string;
  value: string;
  onChange: (e: React.ChangeEvent<HTMLSelectElement>) => void;
  className?: string;
  required?: boolean;
  disabled?: boolean;
}

/**
 * Reusable component for toy status selection
 * Dynamically fetches status options from the database
 */
export default function ToyStatusSelect({
  id,
  name,
  value,
  onChange,
  className = 'w-full',
  required = false,
  disabled = false,
}: ToyStatusSelectProps) {
  const { statuses, loading, error } = useToyStatuses();

  // Helper function to convert CustomSelect onChange to HTMLSelectElement onChange
  const handleCustomSelectChange = (newValue: string) => {
    // Create a synthetic event that matches the expected interface
    const syntheticEvent = {
      target: {
        name,
        value: newValue
      }
    } as React.ChangeEvent<HTMLSelectElement>;
    onChange(syntheticEvent);
  };

  if (loading) {
    return (
      <CustomSelect
        id={id}
        options={[{ value: "", label: "Načítavam statusy..." }]}
        value={value}
        onChange={handleCustomSelectChange}
        placeholder="Načítavam statusy..."
        disabled={true}
      />
    );
  }

  if (error && statuses.length === 0) {
    return (
      <div className="text-sm text-red-600 mb-2">
        ⚠️ {error}
        <br />
        <span className="text-xs text-neutral-500">
          Používajú sa predvolené hodnoty.
        </span>
      </div>
    );
  }

  return (
    <>
      {error && (
        <div className="text-sm text-amber-600 mb-2">
          ⚠️ {error}
          <br />
          <span className="text-xs text-neutral-500">
            Používajú sa predvolené hodnoty.
          </span>
        </div>
      )}
      <CustomSelect
        id={id}
        options={statuses}
        value={value}
        onChange={handleCustomSelectChange}
        placeholder="Vyberte status hračky"
        disabled={disabled}
      />
    </>
  );
}
