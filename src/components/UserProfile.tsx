'use client';

import { useState, useRef, useEffect } from 'react';
import { useAuth } from '../contexts/AuthContext';
import Link from 'next/link';

export default function UserProfile() {
  const { user, signOut } = useAuth();
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  // Zatvorenie dropdown menu pri kliknutí mimo
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsDropdownOpen(false);
      }
    }

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  if (!user) return null;

  // Získanie iniciálok používateľa pre avatar
  const getInitials = () => {
    if (user.displayName) {
      const names = user.displayName.split(' ');
      if (names.length >= 2) {
        return `${names[0][0]}${names[1][0]}`.toUpperCase();
      }
      return user.displayName[0].toUpperCase();
    }
    return user.email ? user.email[0].toUpperCase() : '?';
  };

  // Získanie iniciálok pre zobrazenie v hlavičke
  const getDisplayInitials = () => {
    if (user.displayName) {
      const names = user.displayName.trim().split(/\s+/); // Rozdelenie podľa medzier
      if (names.length >= 2) {
        // Prvé písmeno mena a prvé písmeno priezviska
        return `${names[0][0]}${names[names.length - 1][0]}`.toUpperCase();
      }
      // Ak je len jedno meno, zobraz prvé písmeno
      return user.displayName[0].toUpperCase();
    }
    // Fallback na email ak nie je displayName
    return user.email ? user.email[0].toUpperCase() : '?';
  };

  // Získanie farby avatara na základe role
  const getAvatarColor = () => {
    switch (user.role) {
      case 'admin':
        return 'bg-red-500';
      case 'classic':
        return 'bg-blue-500';
      case 'none':
        return 'bg-gray-500';
      default:
        return 'bg-green-500';
    }
  };

  // Získanie názvu poskytovateľa autentifikácie
  const getProviderName = (providerId: string) => {
    switch (providerId) {
      case 'google.com':
        return 'Google';
      case 'password':
        return 'Email/Heslo';
      default:
        return providerId.replace('.com', '');
    }
  };

  return (
    <div className="relative z-50" ref={dropdownRef}>
      {/* Avatar a meno používateľa */}
      <button
        onClick={() => setIsDropdownOpen(!isDropdownOpen)}
        className="flex items-center space-x-2 focus:outline-none"
      >
        <span className="text-neutral-700 text-sm md:text-base truncate max-w-32 md:max-w-none">
          Tvoj profil
        </span>
        {user.role === 'admin' && (
          <span className="text-xs bg-red-500 text-white px-2 py-0.5 rounded-full">
            A
          </span>
        )}
        <svg
          xmlns="http://www.w3.org/2000/svg"
          className={`h-4 w-4 text-neutral-500 transition-transform ${isDropdownOpen ? 'rotate-180' : ''}`}
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
        </svg>
      </button>

      {/* Dropdown menu */}
      {isDropdownOpen && (
        <div className="absolute mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-[60] border border-gray-200 top-full right-0">
          <div className="px-4 py-2 border-b border-gray-100">
            <p className="text-sm font-medium text-gray-900">{user.displayName || 'Používateľ'}</p>
            <p className="text-xs text-gray-500 truncate">{user.email}</p>
          </div>

          <div className="py-1">
            <div className="px-4 py-2 text-xs text-gray-500">
              <p>Prihlásený cez: {getProviderName(user.providerData && user.providerData[0]?.providerId || '')}</p>
              <p>Rola: {user.role === 'admin' ? 'Administrátor' : user.role === 'classic' ? 'Používateľ' : 'Žiadna'}</p>
            </div>
          </div>

          {/* Odkazy na profil a nastavenia */}
          <Link
            href="/profil"
            className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
            onClick={() => setIsDropdownOpen(false)}
          >
            Môj profil
          </Link>
          <Link
            href="/moje-hracky"
            className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
            onClick={() => setIsDropdownOpen(false)}
          >
            Moje hračky
          </Link>
          <Link
            href="/rezervacie"
            className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
            onClick={() => setIsDropdownOpen(false)}
          >
            Rezervácie
          </Link>
          {user.role === 'admin' && (
            <Link
              href="/admin"
              className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
              onClick={() => setIsDropdownOpen(false)}
            >
              Administrácia
            </Link>
          )}

          {/* Odhlásenie */}
          <button
            onClick={() => {
              setIsDropdownOpen(false);
              signOut();
            }}
            className="block w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-gray-100"
          >
            Odhlásiť sa
          </button>
        </div>
      )}
    </div>
  );
}
