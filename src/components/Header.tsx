'use client';

import Link from 'next/link';
import React, { useState, useEffect } from 'react';
import { useAuth } from '../contexts/AuthContext';
import LoginModal from './LoginModal';
import UserProfile from './UserProfile';
import { usePathname } from 'next/navigation';

export default function Header() {
  const [isLoginModalOpen, setIsLoginModalOpen] = useState(false);
  const [initialView, setInitialView] = useState<'login' | 'register'>('login');
  const { user } = useAuth();
  const pathname = usePathname();

  // Funkcia na kontrolu, či je položka menu aktívna
  const isActive = (path: string) => {
    if (path === '/domov') {
      return pathname === '/domov';
    }
    if (path === '/') {
      return pathname === '/';
    }
    return pathname.startsWith(path);
  };

  // Funkcia na generovanie tried pre položky menu
  const getLinkClassName = (path: string, mobile = false) => {
    if (isActive(path)) {
      return mobile
        ? "text-primary font-medium border-b-2 border-primary transition-colors text-sm"
        : "text-primary font-medium border-b-2 border-primary transition-colors";
    }
    return mobile
      ? "text-neutral-700 hover:text-primary transition-colors text-sm"
      : "text-neutral-700 hover:text-primary transition-colors";
  };

  const handleLoginClick = (viewOrEvent: 'login' | 'register' | React.MouseEvent = 'login') => {
    // If it's a string (view type), use it directly
    if (typeof viewOrEvent === 'string') {
      setInitialView(viewOrEvent);
    } else {
      // If it's an event (from button click), use default 'login'
      setInitialView('login');
    }
    setIsLoginModalOpen(true);
  };

  return (
    <header className="bg-white shadow-sm mb-4">
      <div className="container mx-auto px-4 py-4">
        {/* Desktop Layout */}
        <div className="hidden md:flex justify-between items-center">
          {/* Logo */}
          <Link href="/" className="flex items-center">
            <span className="text-2xl font-bold text-primary">
              🎠 Swapka
            </span>
          </Link>

          {/* Desktop Navigation */}
          <nav className="flex space-x-8">
            <Link href="/domov" className={getLinkClassName('/domov')}>
              Domov
            </Link>
            <Link href="/hracky" className={getLinkClassName('/hracky')}>
              Hračky
            </Link>
            <Link href="/ako-to-funguje" className={getLinkClassName('/ako-to-funguje')}>
              Ako to funguje
            </Link>
          </nav>

          {/* User Actions - Desktop */}
          <div className="flex">
            {user ? (
              <UserProfile />
            ) : (
              <button
                onClick={handleLoginClick}
                className="text-primary hover:text-primary-dark font-medium transition-colors flex items-center border-b-2 border-transparent hover:border-primary"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                </svg>
                Prihlásiť sa
              </button>
            )}
          </div>
        </div>

        {/* Mobile Layout */}
        <div className="md:hidden mobile-header-container">
          {/* Mobile Header Row - Logo and User Actions in single horizontal row */}
          <div className="flex flex-row items-center justify-between w-full mb-3 min-h-[40px] mobile-header-row">
            {/* Logo - Left side */}
            <div className="flex-shrink-0">
              <Link href="/" className="flex items-center">
                <span className="text-xl font-bold text-primary">
                  🎠 Swapka
                </span>
              </Link>
            </div>

            {/* User Actions - Right side */}
            <div className="flex-shrink-0 ml-4">
              {user ? (
                <UserProfile />
              ) : (
                <button
                  onClick={handleLoginClick}
                  className="text-primary hover:text-primary-dark font-medium transition-colors flex items-center text-sm whitespace-nowrap"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                  </svg>
                  Prihlásiť sa
                </button>
              )}
            </div>
          </div>

          {/* Mobile Navigation Row */}
          <nav className="flex flex-wrap justify-center gap-x-6 gap-y-2 border-t border-gray-100 pt-3">
            <Link href="/domov" className={getLinkClassName('/domov', true)}>
              Domov
            </Link>
            <Link href="/hracky" className={getLinkClassName('/hracky', true)}>
              Hračky
            </Link>
            <Link href="/ako-to-funguje" className={getLinkClassName('/ako-to-funguje', true)}>
              Ako to funguje
            </Link>
          </nav>
        </div>
      </div>

      {/* Login Modal */}
      <LoginModal
        isOpen={isLoginModalOpen}
        onClose={() => setIsLoginModalOpen(false)}
        initialView={initialView}
      />
    </header>
  );
}
