'use client';

import { useState } from 'react';
import Link from 'next/link';

interface TermsConfirmationModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => Promise<void>;
  email: string;
}

export default function TermsConfirmationModal({
  isOpen,
  onClose,
  onConfirm,
  email
}: TermsConfirmationModalProps) {
  const [termsAccepted, setTermsAccepted] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  if (!isOpen) return null;

  const handleConfirm = async () => {
    if (!termsAccepted) {
      setError('Pre pokračovanie musíte súhlasiť s podmienkami používania');
      return;
    }

    try {
      setLoading(true);
      setError(null);
      await onConfirm();
      onClose();
    } catch (error) {
      console.error('Chyba pri potvrdení podmienok:', error);
      setError('Nastala chyba pri potvrdení podmienok. Skúste to znova.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-8 max-w-md w-full">
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-2xl font-bold">
            Potvrdenie podmienok používania
          </h2>
          <button
            onClick={onClose}
            className="text-neutral-500 hover:text-neutral-700"
            disabled={loading}
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-6 w-6"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M6 18L18 6M6 6l12 12"
              />
            </svg>
          </button>
        </div>

        {error && (
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
            {error}
          </div>
        )}

        <div className="space-y-4">
          <p className="text-gray-700">
            Vitajte v aplikácii Swapka! Prihlásili ste sa ako <strong>{email}</strong>.
          </p>

          <p className="text-gray-700">
            Pred pokračovaním je potrebné potvrdiť súhlas s podmienkami používania našej aplikácie.
          </p>

          <div className="flex items-start mt-4">
            <div className="flex items-center h-5">
              <input
                id="terms"
                type="checkbox"
                checked={termsAccepted}
                onChange={(e) => setTermsAccepted(e.target.checked)}
                className="w-4 h-4 border border-gray-300 rounded bg-gray-50 focus:ring-3 focus:ring-primary"
                disabled={loading}
              />
            </div>
            <label htmlFor="terms" className="ml-2 text-sm text-gray-700">
              Súhlasím so <Link href="/podmienky-pouzitia" className="text-primary hover:underline" target="_blank">všeobecnými podmienkami používania</Link> a <Link href="/ochrana-osobnych-udajov" className="text-primary hover:underline" target="_blank">ochranou osobných údajov</Link>
            </label>
          </div>

          <button
            onClick={handleConfirm}
            disabled={loading || !termsAccepted}
            className={`w-full bg-primary text-white rounded-lg py-2.5 px-4 hover:bg-primary-dark transition-colors mt-4 ${!termsAccepted ? 'opacity-50 cursor-not-allowed' : ''}`}
          >
            {loading ? 'Spracovanie...' : 'Potvrdiť a pokračovať'}
          </button>
        </div>
      </div>
    </div>
  );
}
