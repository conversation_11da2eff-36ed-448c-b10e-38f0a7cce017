'use client';

import React from 'react';
import Link from 'next/link';
import { useCookieConsent, CookieType } from '../contexts/CookieConsentContext';

export default function CookieBanner() {
  const {
    consent,
    hasConsented,
    updateConsent,
    acceptAll,
    rejectAll,
    savePreferences,
    isPreferencesOpen,
    openPreferences,
    closePreferences,
  } = useCookieConsent();

  // Ak používateľ už dal súhlas, nezobrazujeme banner
  if (hasConsented && !isPreferencesOpen) {
    return null;
  }

  // Komponent pre zobrazenie nastavení cookies
  const CookiePreferences = () => (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        <div className="p-6">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-2xl font-semibold text-neutral-800">Nastavenia cookies</h2>
            <button
              onClick={closePreferences}
              className="text-neutral-500 hover:text-neutral-700"
              aria-label="Zavrieť"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>

          <p className="text-neutral-600 mb-6">
            Používame cookies na zabezpečenie základných funkcií webovej stránky a na zlepšenie vášho online zážitku. 
            Pre každú kategóriu si môžete vybrať, či chcete cookies povoliť alebo zakázať. 
            Viac informácií o tom, čo sú cookies a aké typy cookies používame, nájdete v našich{' '}
            <Link href="/cookies" className="text-primary hover:underline">
              Zásadách používania cookies
            </Link>.
          </p>

          <div className="space-y-4 mb-6">
            {/* Nevyhnutné cookies - vždy povolené */}
            <div className="p-4 border border-neutral-200 rounded-lg">
              <div className="flex items-center justify-between mb-2">
                <h3 className="font-semibold text-neutral-800">Nevyhnutné cookies</h3>
                <div className="relative inline-block w-12 h-6 bg-neutral-200 rounded-full cursor-not-allowed">
                  <div className="absolute left-1 top-1 bg-primary w-4 h-4 rounded-full transition-transform transform translate-x-6"></div>
                </div>
              </div>
              <p className="text-sm text-neutral-600">
                Tieto cookies sú potrebné na správne fungovanie webovej stránky a nemôžu byť vypnuté. 
                Zvyčajne sa nastavujú len v reakcii na akcie, ktoré vykonáte, ako napríklad nastavenie 
                vašich preferencií ochrany osobných údajov, prihlásenie alebo vyplnenie formulárov.
              </p>
            </div>

            {/* Funkčné cookies */}
            <div className="p-4 border border-neutral-200 rounded-lg">
              <div className="flex items-center justify-between mb-2">
                <h3 className="font-semibold text-neutral-800">Funkčné cookies</h3>
                <label className="relative inline-block w-12 h-6 bg-neutral-200 rounded-full cursor-pointer">
                  <input
                    type="checkbox"
                    className="sr-only"
                    checked={consent.functional}
                    onChange={(e) => updateConsent({ functional: e.target.checked })}
                  />
                  <div className={`absolute left-1 top-1 w-4 h-4 rounded-full transition-transform transform ${consent.functional ? 'bg-primary translate-x-6' : 'bg-white'}`}></div>
                </label>
              </div>
              <p className="text-sm text-neutral-600">
                Tieto cookies umožňujú webovej stránke poskytovať vylepšené funkcie a personalizáciu. 
                Môžu byť nastavené nami alebo poskytovateľmi tretích strán, ktorých služby sme pridali 
                na naše stránky. Ak tieto cookies nepovolíte, niektoré alebo všetky tieto služby nemusia 
                fungovať správne.
              </p>
            </div>

            {/* Analytické cookies */}
            <div className="p-4 border border-neutral-200 rounded-lg">
              <div className="flex items-center justify-between mb-2">
                <h3 className="font-semibold text-neutral-800">Analytické cookies</h3>
                <label className="relative inline-block w-12 h-6 bg-neutral-200 rounded-full cursor-pointer">
                  <input
                    type="checkbox"
                    className="sr-only"
                    checked={consent.analytics}
                    onChange={(e) => updateConsent({ analytics: e.target.checked })}
                  />
                  <div className={`absolute left-1 top-1 w-4 h-4 rounded-full transition-transform transform ${consent.analytics ? 'bg-primary translate-x-6' : 'bg-white'}`}></div>
                </label>
              </div>
              <p className="text-sm text-neutral-600">
                Tieto cookies nám umožňujú počítať návštevy a zdroje návštevnosti, aby sme mohli merať 
                a zlepšovať výkon našej stránky. Pomáhajú nám zistiť, ktoré stránky sú najviac a najmenej 
                populárne, a vidieť, ako sa návštevníci pohybujú po stránke. Všetky informácie, ktoré tieto 
                cookies zhromažďujú, sú agregované, a teda anonymné.
              </p>
            </div>

            {/* Marketingové cookies */}
            <div className="p-4 border border-neutral-200 rounded-lg">
              <div className="flex items-center justify-between mb-2">
                <h3 className="font-semibold text-neutral-800">Marketingové cookies</h3>
                <label className="relative inline-block w-12 h-6 bg-neutral-200 rounded-full cursor-pointer">
                  <input
                    type="checkbox"
                    className="sr-only"
                    checked={consent.marketing}
                    onChange={(e) => updateConsent({ marketing: e.target.checked })}
                  />
                  <div className={`absolute left-1 top-1 w-4 h-4 rounded-full transition-transform transform ${consent.marketing ? 'bg-primary translate-x-6' : 'bg-white'}`}></div>
                </label>
              </div>
              <p className="text-sm text-neutral-600">
                Tieto cookies môžu byť nastavené prostredníctvom našej stránky našimi reklamnými partnermi. 
                Môžu byť použité týmito spoločnosťami na vytvorenie profilu vašich záujmov a zobrazenie 
                relevantných reklám na iných stránkach. Neukladajú priamo osobné údaje, ale sú založené 
                na jedinečnej identifikácii vášho prehliadača a internetového zariadenia.
              </p>
            </div>
          </div>

          <div className="flex flex-col sm:flex-row justify-end space-y-2 sm:space-y-0 sm:space-x-2">
            <button
              onClick={rejectAll}
              className="btn btn-outline"
            >
              Odmietnuť všetko
            </button>
            <button
              onClick={acceptAll}
              className="btn btn-outline"
            >
              Prijať všetko
            </button>
            <button
              onClick={savePreferences}
              className="btn btn-primary"
            >
              Uložiť nastavenia
            </button>
          </div>
        </div>
      </div>
    </div>
  );

  // Ak sú otvorené nastavenia cookies, zobrazíme ich
  if (isPreferencesOpen) {
    return <CookiePreferences />;
  }

  // Hlavný banner s cookies
  return (
    <div className="fixed bottom-0 left-0 right-0 bg-white border-t border-neutral-200 shadow-lg z-50">
      <div className="container mx-auto p-4">
        <div className="flex flex-col md:flex-row md:items-center justify-between gap-4">
          <div className="flex-1">
            <h2 className="text-lg font-semibold text-neutral-800 mb-2">Táto stránka používa cookies</h2>
            <p className="text-neutral-600 text-sm">
              Používame cookies na zabezpečenie základných funkcií webovej stránky a na zlepšenie vášho online zážitku. 
              Môžete si vybrať, ktoré cookies povolíte. Viac informácií nájdete v našich{' '}
              <Link href="/cookies" className="text-primary hover:underline">
                Zásadách používania cookies
              </Link>.
            </p>
          </div>
          <div className="flex flex-col sm:flex-row gap-2">
            <button
              onClick={openPreferences}
              className="btn btn-outline whitespace-nowrap"
            >
              Nastavenia cookies
            </button>
            <button
              onClick={rejectAll}
              className="btn btn-outline whitespace-nowrap"
            >
              Odmietnuť všetko
            </button>
            <button
              onClick={acceptAll}
              className="btn btn-primary whitespace-nowrap"
            >
              Prijať všetko
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
