'use client';

import { useEffect, useState } from 'react';

/**
 * Client-side cache management component
 * Handles cache invalidation and version checking for mobile Safari compatibility
 */

interface CacheInfo {
  buildId: string;
  timestamp: number;
  version: string;
  environment: string;
  cacheBustParam: string;
}

export default function CacheManager() {
  const [cacheInfo, setCacheInfo] = useState<CacheInfo | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  // Check for version changes on component mount
  useEffect(() => {
    checkVersionAndCache();
  }, []);

  /**
   * Check current version and cache status
   */
  const checkVersionAndCache = async () => {
    try {
      // Get stored version from localStorage
      const storedVersion = localStorage.getItem('app-version');
      const storedBuildId = localStorage.getItem('app-build-id');
      
      // Get current version from server
      const response = await fetch('/api/cache-bust', {
        method: 'GET',
        headers: {
          'Cache-Control': 'no-cache',
        },
      });

      if (response.ok) {
        const info: CacheInfo = await response.json();
        setCacheInfo(info);

        // Check if version has changed
        if (storedVersion && storedVersion !== info.version) {
          console.log('App version changed, clearing caches...');
          await clearBrowserCaches();
        }

        // Check if build ID has changed
        if (storedBuildId && storedBuildId !== info.buildId) {
          console.log('Build ID changed, clearing caches...');
          await clearBrowserCaches();
        }

        // Update stored version and build ID
        localStorage.setItem('app-version', info.version);
        localStorage.setItem('app-build-id', info.buildId);
      }
    } catch (error) {
      console.error('Error checking version and cache:', error);
    }
  };

  /**
   * Clear browser caches
   */
  const clearBrowserCaches = async () => {
    try {
      // Clear localStorage cache entries
      const keysToRemove = [];
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i);
        if (key && (key.startsWith('cache-') || key.startsWith('temp-'))) {
          keysToRemove.push(key);
        }
      }
      keysToRemove.forEach(key => localStorage.removeItem(key));

      // Clear sessionStorage cache entries
      const sessionKeysToRemove = [];
      for (let i = 0; i < sessionStorage.length; i++) {
        const key = sessionStorage.key(i);
        if (key && (key.startsWith('cache-') || key.startsWith('temp-'))) {
          sessionKeysToRemove.push(key);
        }
      }
      sessionKeysToRemove.forEach(key => sessionStorage.removeItem(key));

      // Force reload with cache bypass for mobile Safari
      if (navigator.userAgent.includes('Safari') && navigator.userAgent.includes('Mobile')) {
        // Mobile Safari specific cache clearing
        window.location.href = window.location.href + (window.location.href.includes('?') ? '&' : '?') + 'cache-bust=' + Date.now();
      }

      console.log('Browser caches cleared');
    } catch (error) {
      console.error('Error clearing browser caches:', error);
    }
  };

  /**
   * Force cache invalidation
   */
  const forceCacheInvalidation = async () => {
    setIsLoading(true);
    try {
      const response = await fetch('/api/cache-bust', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Cache-Control': 'no-cache',
        },
        body: JSON.stringify({
          action: 'invalidate-all',
          target: 'client',
        }),
      });

      if (response.ok) {
        await clearBrowserCaches();
        await checkVersionAndCache();
        
        // Show success message
        if (typeof window !== 'undefined' && window.location.reload) {
          setTimeout(() => {
            window.location.reload();
          }, 1000);
        }
      }
    } catch (error) {
      console.error('Error forcing cache invalidation:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // Only render in development or for debugging
  if (process.env.NODE_ENV === 'production') {
    return null;
  }

  return (
    <div className="fixed bottom-4 right-4 bg-gray-800 text-white p-4 rounded-lg shadow-lg text-xs max-w-xs z-50">
      <div className="mb-2">
        <strong>Cache Manager</strong>
      </div>
      
      {cacheInfo && (
        <div className="mb-2 space-y-1">
          <div>Version: {cacheInfo.version}</div>
          <div>Build: {cacheInfo.buildId.substring(0, 12)}...</div>
          <div>Env: {cacheInfo.environment}</div>
        </div>
      )}

      <button
        onClick={forceCacheInvalidation}
        disabled={isLoading}
        className="bg-blue-600 hover:bg-blue-700 disabled:bg-gray-600 px-3 py-1 rounded text-xs"
      >
        {isLoading ? 'Clearing...' : 'Clear Cache'}
      </button>
    </div>
  );
}

/**
 * Hook for cache management in other components
 */
export function useCacheManager() {
  const [isOnline, setIsOnline] = useState(true);

  useEffect(() => {
    const handleOnline = () => setIsOnline(true);
    const handleOffline = () => setIsOnline(false);

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  const addCacheBust = (url: string): string => {
    const separator = url.includes('?') ? '&' : '?';
    return `${url}${separator}v=${Date.now()}`;
  };

  const clearLocalCache = () => {
    try {
      // Clear specific cache entries
      const keysToRemove = [];
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i);
        if (key && key.startsWith('cache-')) {
          keysToRemove.push(key);
        }
      }
      keysToRemove.forEach(key => localStorage.removeItem(key));
    } catch (error) {
      console.error('Error clearing local cache:', error);
    }
  };

  return {
    isOnline,
    addCacheBust,
    clearLocalCache,
  };
}
