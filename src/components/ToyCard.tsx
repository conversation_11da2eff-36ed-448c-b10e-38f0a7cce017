'use client';

import Link from 'next/link';
import { translateToyStatus, getToyStatusClasses } from '../lib/constants';
import { formatToyPricePerDay } from '../lib/priceUtils';
import { generateToyUrl } from '../lib/seoUtils';

// Typy pre dáta
interface ToyOwner {
  hashedId: string;
  name: string;
  city: string | null;
}

interface Toy {
  id: string; // Hashované ID
  name: string;
  description: string;
  image: string;
  type: string;
  typeLabel?: string; // Popisný label pre typ hračky
  location: string;
  price: number;
  deposit: number;
  priceType?: string; // Typ ceny (PER_DAY alebo PER_RENTAL)
  status: string;
  statusLabel?: string; // Popisný label pre status hračky
  distance?: number; // Vzdialenosť od používateľa v km (voliteľné)
  owner: ToyOwner; // Informácie o vlastníkovi hračky
}

interface ToyCardProps {
  toy: Toy;
  viewMode: 'grid' | 'list';
}

export default function ToyCard({ toy, viewMode }: ToyCardProps) {
  if (viewMode === 'list') {
    return (
      <div className="bg-white rounded-lg shadow-sm overflow-hidden">
        <div className="flex flex-col md:flex-row md:items-center">
          {/* Image section */}
          <div className="relative w-full md:w-48 h-48 md:h-32 bg-neutral-200 flex-shrink-0">
            {toy.image && !toy.image.includes('placeholder.jpg') ? (
              <div className="w-full h-full">
                <img
                  src={toy.image}
                  alt={toy.name}
                  className="w-full h-full object-cover"
                />
              </div>
            ) : (
              <div className="w-full h-full flex items-center justify-center text-neutral-400">
                {/* Placeholder pre obrázok */}
                <span className="text-3xl">🧸</span>
              </div>
            )}
            {/* Status badge */}
            <div className="absolute top-2 right-2">
              <span className={`inline-block px-2 py-1 text-xs rounded-full font-medium ${getToyStatusClasses(toy.status)}`}>
                {toy.statusLabel || translateToyStatus(toy.status)}
              </span>
            </div>
          </div>

          {/* Content section */}
          <div className="flex-1 p-4">
            <div className="flex flex-col md:flex-row md:justify-between md:items-start gap-2">
              <div className="flex-1">
                <div className="flex flex-col md:flex-row md:justify-between md:items-start mb-2">
                  <h2 className="text-xl font-semibold">{toy.name}</h2>
                  <span className="text-primary font-bold text-lg">{formatToyPricePerDay(toy.price, toy.priceType)}</span>
                </div>
                <div className="flex justify-between items-center mb-2">
                  <span className="toy-category text-sm text-neutral-500">🏷️ {toy.typeLabel || toy.type}</span>
                  {toy.deposit === 0 ? (
                    <span className="text-sm text-green-600 bg-green-100 px-2 py-1 rounded-full">bez zálohy</span>
                  ) : (
                    <span className="text-sm text-neutral-500">Záloha: {toy.deposit}€</span>
                  )}
                </div>
                <p className="text-neutral-600 text-sm mb-3 overflow-hidden" style={{
                  display: '-webkit-box',
                  WebkitLineClamp: 2,
                  WebkitBoxOrient: 'vertical'
                }}>{toy.description}</p>

                <div className="flex flex-col md:flex-row md:items-center gap-2 md:gap-4 text-sm text-neutral-500 mb-3">
                  <span className="toy-location">📍 {toy.location}
                    {toy.distance !== undefined && (
                      <span className="text-primary font-medium ml-2">📏 {toy.distance.toFixed(1)} km</span>
                    )}
                  </span>
                </div>

                <div className="flex flex-col md:flex-row md:justify-between md:items-center gap-2">
                  <div></div>
                  <Link href={generateToyUrl(toy.name, toy.id)} className="btn btn-primary text-sm py-1 self-start md:self-auto">
                    Zobraziť detail
                  </Link>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Grid view (default)
  return (
    <div className="bg-white rounded-lg shadow-sm overflow-hidden">
      <div className="relative h-48 bg-neutral-200">
        {toy.image && !toy.image.includes('placeholder.jpg') ? (
          <div className="w-full h-full">
            <img
              src={toy.image}
              alt={toy.name}
              className="w-full h-full object-cover"
            />
          </div>
        ) : (
          <div className="w-full h-full flex items-center justify-center text-neutral-400">
            {/* Placeholder pre obrázok */}
            <span className="text-5xl">🧸</span>
          </div>
        )}
        {/* Status badge */}
        <div className="absolute top-2 right-2">
          <span className={`inline-block px-2 py-1 text-xs rounded-full font-medium ${getToyStatusClasses(toy.status)}`}>
            {toy.statusLabel || translateToyStatus(toy.status)}
          </span>
        </div>
      </div>
      <div className="p-4">
        <div className="flex justify-between items-start">
          <h2 className="text-xl font-semibold">{toy.name}</h2>
          <span className="text-primary font-bold">{formatToyPricePerDay(toy.price, toy.priceType)}</span>
        </div>
        <div className="flex justify-between items-center mt-2">
          <span className="toy-category text-sm text-neutral-500">🏷️ {toy.typeLabel || toy.type}</span>
          {toy.deposit === 0 ? (
            <span className="text-sm text-green-600 bg-green-100 px-2 py-1 rounded-full">bez zálohy</span>
          ) : (
            <span className="text-sm text-neutral-500">Záloha: {toy.deposit}€</span>
          )}
        </div>
        <p className="text-neutral-600 text-sm mt-2">{toy.description}</p>
        <div className="flex items-center justify-between mt-3 text-sm text-neutral-500">
          <span className="toy-location">📍 {toy.location}
            {toy.distance !== undefined && (
              <span className="text-primary font-medium">📏 {toy.distance.toFixed(1)} km</span>
            )}
          </span>
        </div>
        <div className="mt-4 pt-4 border-t border-neutral-100 flex justify-between items-center">

          <Link href={generateToyUrl(toy.name, toy.id)} className="btn btn-primary text-sm py-1">
            Zobraziť detail
          </Link>
        </div>
      </div>
    </div>
  );
}
