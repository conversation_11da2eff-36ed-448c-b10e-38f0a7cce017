'use client';

import { useState, useEffect, useRef } from 'react';
import { searchAddress, NominatimSearchResult, formatAddress, reverseGeocode } from '../lib/nominatim';

interface AddressSearchProps {
  onAddressSelect: (
    address: string,
    latitude: number,
    longitude: number,
    city?: string,
    postalCode?: string
  ) => void;
  initialAddress?: string;
  placeholder?: string;
  className?: string;
  countryCode?: string;
}

export default function AddressSearch({
  onAddressSelect,
  initialAddress = '',
  placeholder = 'Zadajte adresu...',
  className = '',
  countryCode = 'sk'
}: AddressSearchProps) {
  const [query, setQuery] = useState(initialAddress);
  const [results, setResults] = useState<NominatimSearchResult[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [showResults, setShowResults] = useState(false);
  const [selectedAddress, setSelectedAddress] = useState(initialAddress);
  const [isGettingLocation, setIsGettingLocation] = useState(false);
  const [locationError, setLocationError] = useState<string | null>(null);
  const searchTimeout = useRef<NodeJS.Timeout | null>(null);
  const resultsRef = useRef<HTMLDivElement>(null);

  // Vyhľadávanie adries s oneskorením (debounce)
  useEffect(() => {
    if (query.trim().length < 3) {
      setResults([]);
      return;
    }

    // Zrušenie predchádzajúceho časovača
    if (searchTimeout.current) {
      clearTimeout(searchTimeout.current);
    }

    // Nastavenie nového časovača
    searchTimeout.current = setTimeout(async () => {
      setIsLoading(true);
      try {
        const searchResults = await searchAddress(query, 5, countryCode);
        setResults(searchResults);
        setShowResults(true);
      } catch (error) {
        console.error('Chyba pri vyhľadávaní adresy:', error);
      } finally {
        setIsLoading(false);
      }
    }, 300);

    return () => {
      if (searchTimeout.current) {
        clearTimeout(searchTimeout.current);
      }
    };
  }, [query, countryCode]);

  // Zatvorenie výsledkov pri kliknutí mimo
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (resultsRef.current && !resultsRef.current.contains(event.target as Node)) {
        setShowResults(false);
      }
    }

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Spracovanie výberu adresy
  const handleAddressSelect = (result: NominatimSearchResult) => {
    const formattedAddress = formatAddress(result);
    setSelectedAddress(formattedAddress);
    setQuery(formattedAddress);
    setShowResults(false);

    // Extrakcia mesta a PSČ z výsledku
    const city = result.address?.city ||
                 result.address?.town ||
                 result.address?.village ||
                 result.address?.suburb ||
                 '';
    const postalCode = result.address?.postcode || '';

    // Logovanie pre debugovanie
    console.log('Vybraná adresa:', result);
    console.log('Extrahované mesto:', city);
    console.log('Extrahované PSČ:', postalCode);

    onAddressSelect(
      formattedAddress,
      parseFloat(result.lat),
      parseFloat(result.lon),
      city,
      postalCode
    );
  };

  // Získanie aktuálnej polohy používateľa
  const getCurrentLocation = () => {
    // Resetovanie chybového stavu
    setLocationError(null);

    // Kontrola, či prehliadač podporuje geolokáciu
    if (!navigator.geolocation) {
      setLocationError('Váš prehliadač nepodporuje geolokáciu.');
      return;
    }

    setIsGettingLocation(true);

    navigator.geolocation.getCurrentPosition(
      async (position) => {
        try {
          const { latitude, longitude } = position.coords;

          // Získanie adresy zo súradníc pomocou reverzného geocodingu
          const result = await reverseGeocode(latitude, longitude);

          if (result) {
            const formattedAddress = formatAddress(result);
            setQuery(formattedAddress);
            setSelectedAddress(formattedAddress);

            // Extrakcia mesta a PSČ z výsledku
            const city = result.address?.city ||
                         result.address?.town ||
                         result.address?.village ||
                         result.address?.suburb ||
                         '';
            const postalCode = result.address?.postcode || '';

            // Logovanie pre debugovanie
            console.log('Nájdená adresa z aktuálnej polohy:', result);
            console.log('Extrahované mesto:', city);
            console.log('Extrahované PSČ:', postalCode);

            onAddressSelect(
              formattedAddress,
              latitude,
              longitude,
              city,
              postalCode
            );
          } else {
            setLocationError('Nepodarilo sa nájsť adresu pre vašu aktuálnu polohu.');
          }
        } catch (error) {
          console.error('Chyba pri získavaní adresy z aktuálnej polohy:', error);
          setLocationError('Nastala chyba pri získavaní adresy z vašej polohy.');
        } finally {
          setIsGettingLocation(false);
        }
      },
      (error) => {
        console.error('Chyba pri získavaní aktuálnej polohy:', error);

        let errorMessage = 'Nastala chyba pri získavaní vašej polohy.';
        switch (error.code) {
          case error.PERMISSION_DENIED:
            errorMessage = 'Prístup k polohe bol zamietnutý. Povoľte prístup k polohe vo vašom prehliadači.';
            break;
          case error.POSITION_UNAVAILABLE:
            errorMessage = 'Informácie o polohe nie sú dostupné.';
            break;
          case error.TIMEOUT:
            errorMessage = 'Vypršal časový limit pre získanie polohy.';
            break;
        }

        setLocationError(errorMessage);
        setIsGettingLocation(false);
      },
      {
        enableHighAccuracy: true,
        timeout: 10000,
        maximumAge: 0
      }
    );
  };

  return (
    <div className="relative w-full" ref={resultsRef}>
      {/* Vstupné pole pre vyhľadávanie */}
      <div className="relative">
        <div className="flex">
          <input
            type="text"
            value={query}
            onChange={(e) => setQuery(e.target.value)}
            onFocus={() => query.trim().length >= 3 && setShowResults(true)}
            placeholder={placeholder}
            className={`w-full p-2 border rounded-l-md ${className}`}
          />
          <button
            type="button"
            onClick={getCurrentLocation}
            className="bg-primary text-white px-3 py-2 rounded-r-md hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-primary"
            title="Použiť moju aktuálnu polohu"
            disabled={isGettingLocation}
          >
            {isGettingLocation ? (
              <div className="animate-spin h-5 w-5 border-2 border-white border-t-transparent rounded-full"></div>
            ) : (
              "📍"
            )}
          </button>
        </div>
        {isLoading && !isGettingLocation && (
          <div className="absolute right-12 top-1/2 transform -translate-y-1/2">
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary"></div>
          </div>
        )}
      </div>

      {/* Chybová hláška pri získavaní polohy */}
      {locationError && (
        <div className="mt-1 text-sm text-red-600">
          {locationError}
        </div>
      )}

      {/* Výsledky vyhľadávania */}
      {showResults && results.length > 0 && (
        <div className="absolute z-10 w-full mt-1 bg-white border rounded-md shadow-lg max-h-60 overflow-y-auto">
          <ul>
            {results.map((result) => (
              <li
                key={result.place_id}
                onClick={() => handleAddressSelect(result)}
                className="p-2 hover:bg-gray-100 cursor-pointer"
              >
                {result.display_name}
              </li>
            ))}
          </ul>
        </div>
      )}

      {/* Žiadne výsledky */}
      {showResults && query.trim().length >= 3 && results.length === 0 && !isLoading && (
        <div className="absolute z-10 w-full mt-1 bg-white border rounded-md shadow-lg p-2 text-center text-gray-500">
          Neboli nájdené žiadne adresy
        </div>
      )}
    </div>
  );
}
