'use client';

import { useState } from 'react';
import Link from 'next/link';

interface TermsAcceptanceModalProps {
  isOpen: boolean;
  onAccept: () => Promise<void>;
  onSignOut: () => Promise<void>;
  email: string | null;
}

export default function TermsAcceptanceModal({
  isOpen,
  onAccept,
  onSignOut,
  email
}: TermsAcceptanceModalProps) {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  if (!isOpen) return null;

  const handleAccept = async () => {
    try {
      setLoading(true);
      setError(null);
      await onAccept();
    } catch (error) {
      console.error('Chyba pri potvrdení podmienok:', error);
      setError('Nastala chyba pri potvrdení podmienok. Skúste to znova.');
    } finally {
      setLoading(false);
    }
  };

  const handleSignOut = async () => {
    try {
      setLoading(true);
      setError(null);
      await onSignOut();
    } catch (error) {
      console.error('Chyba pri odhlásení:', error);
      setError('Nastala chyba pri odhlásení. Skúste to znova.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-8 max-w-md w-full">
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-2xl font-bold">
            Podmienky používania
          </h2>
        </div>

        {error && (
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
            {error}
          </div>
        )}

        <div className="space-y-4">
          <p className="text-gray-700">
            Vitajte v aplikácii Swapka! Prihlásili ste sa ako <strong>{email}</strong>.
          </p>

          <p className="text-gray-700">
            Pre pokračovanie v používaní aplikácie je potrebné potvrdiť súhlas s podmienkami používania.
          </p>

          <p className="text-gray-700">
            Prečítajte si naše <Link href="/podmienky-pouzitia" className="text-primary hover:underline" target="_blank">všeobecné podmienky používania</Link> a <Link href="/ochrana-osobnych-udajov" className="text-primary hover:underline" target="_blank">ochranu osobných údajov</Link>.
          </p>

          <div className="flex flex-col space-y-3 mt-6">
            <button
              onClick={handleAccept}
              disabled={loading}
              className="w-full bg-primary text-white rounded-lg py-2.5 px-4 hover:bg-primary-dark transition-colors"
            >
              {loading ? 'Spracovanie...' : '✅ Súhlasím s podmienkami'}
            </button>
            
            <button
              onClick={handleSignOut}
              disabled={loading}
              className="w-full bg-white text-red-600 border border-red-600 rounded-lg py-2.5 px-4 hover:bg-red-50 transition-colors"
            >
              {loading ? 'Spracovanie...' : '❌ Odhlásiť sa'}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
