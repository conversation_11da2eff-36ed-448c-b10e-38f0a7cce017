'use client';

import { useState } from 'react';
import { useAuth } from '../contexts/AuthContext';
import Link from 'next/link';

interface LoginModalProps {
  isOpen: boolean;
  onClose: () => void;
  initialView?: ModalView;
}

type ModalView = 'login' | 'register';

export default function LoginModal({ isOpen, onClose, initialView = 'login' }: LoginModalProps) {
  const {
    signInWithGoogle,
    signInWithEmailAndPassword,
    registerWithEmailAndPassword,
    loading,
    error: authError,
    clearError
  } = useAuth();

  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [name, setName] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [termsAccepted, setTermsAccepted] = useState(false);
  const [localError, setLocalError] = useState<string | null>(null);
  const [currentView, setCurrentView] = useState<ModalView>(initialView);

  if (!isOpen) return null;

  const handleGoogleSignIn = async () => {
    try {
      setLocalError(null);
      clearError();
      await signInWithGoogle();
      onClose();
    } catch (error) {
      setLocalError('Prihlásenie cez Google zlyhalo. Skúste to znova.');
      console.error('Chyba pri prihlásení cez Google:', error);
    }
  };

  const handleEmailPasswordSignIn = async () => {
    if (!email || !password) {
      setLocalError('Vyplňte email a heslo');
      return;
    }

    try {
      setLocalError(null);
      clearError();
      await signInWithEmailAndPassword(email, password);
      onClose();
    } catch (error) {
      // Chyba je už nastavená v AuthContext
    }
  };

  const handleRegister = async () => {
    if (!email || !password || !name) {
      setLocalError('Vyplňte všetky povinné polia');
      return;
    }

    if (password !== confirmPassword) {
      setLocalError('Heslá sa nezhodujú');
      return;
    }

    if (password.length < 6) {
      setLocalError('Heslo musí mať aspoň 6 znakov');
      return;
    }

    // Kontrola súhlasu s podmienkami
    if (!termsAccepted) {
      setLocalError('Pre registráciu je potrebné súhlasiť s podmienkami používania');
      return;
    }

    try {
      setLocalError(null);
      clearError();
      await registerWithEmailAndPassword(email, password, name, termsAccepted);
      onClose();
    } catch (error) {
      // Chyba je už nastavená v AuthContext
    }
  };

  const switchView = (view: ModalView) => {
    setCurrentView(view);
    setLocalError(null);
    clearError();
  };

  const error = localError || authError;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-8 max-w-md w-full">
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-2xl font-bold">
            {currentView === 'login' ? 'Prihlásenie' : 'Registrácia'}
          </h2>
          <button
            onClick={onClose}
            className="text-neutral-500 hover:text-neutral-700"
            disabled={loading}
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-6 w-6"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M6 18L18 6M6 6l12 12"
              />
            </svg>
          </button>
        </div>

        {error && (
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
            {error}
          </div>
        )}

        {/* Formulár pre prihlásenie/registráciu */}
        <div className="space-y-4">
          {currentView === 'register' && (
            <div>
              <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">
                Meno a priezvisko
              </label>
              <input
                type="text"
                id="name"
                value={name}
                onChange={(e) => setName(e.target.value)}
                className="w-full"
                placeholder="Zadajte vaše meno"
                disabled={loading}
              />
            </div>
          )}

          <div>
            <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
              Email
            </label>
            <input
              type="email"
              id="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              className="w-full"
              placeholder="<EMAIL>"
              disabled={loading}
            />
          </div>

          <div>
            <label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-1">
              Heslo
            </label>
            <input
              type="password"
              id="password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              className="w-full"
              placeholder={currentView === 'register' ? 'Minimálne 6 znakov' : 'Zadajte heslo'}
              disabled={loading}
            />
          </div>

          {currentView === 'register' && (
            <div>
              <label htmlFor="confirmPassword" className="block text-sm font-medium text-gray-700 mb-1">
                Potvrdenie hesla
              </label>
              <input
                type="password"
                id="confirmPassword"
                value={confirmPassword}
                onChange={(e) => setConfirmPassword(e.target.value)}
                className="w-full"
                placeholder="Zopakujte heslo"
                disabled={loading}
              />
            </div>
          )}

          {currentView === 'register' && (
            <div className="mt-4">
              <div className="flex items-start">
                <div className="flex items-center h-5">
                  <input
                    id="terms"
                    type="checkbox"
                    checked={termsAccepted}
                    onChange={(e) => setTermsAccepted(e.target.checked)}
                    className="w-4 h-4 border border-gray-300 rounded bg-gray-50 focus:ring-3 focus:ring-primary"
                    disabled={loading}
                  />
                </div>
                <label htmlFor="terms" className="ml-2 text-sm text-gray-700">
                  Súhlasím s <Link href="/podmienky-pouzitia" className="text-primary hover:underline" target="_blank">podmienkami používania</Link> a <Link href="/ochrana-osobnych-udajov" className="text-primary hover:underline" target="_blank">ochranou osobných údajov</Link>.
                </label>
              </div>
            </div>
          )}

          {currentView === 'login' ? (
            <button
              onClick={handleEmailPasswordSignIn}
              disabled={loading}
              className="w-full bg-primary text-white rounded-lg py-2.5 px-4 hover:bg-primary-dark transition-colors mt-4"
            >
              Prihlásiť sa
            </button>
          ) : (
            <button
              onClick={handleRegister}
              disabled={loading}
              className="w-full bg-primary text-white rounded-lg py-2.5 px-4 hover:bg-primary-dark transition-colors mt-4"
            >
              Registrovať sa
            </button>
          )}

          <div className="relative flex items-center justify-center">
            <div className="border-t border-gray-300 flex-grow"></div>
            <span className="px-3 text-sm text-gray-500 bg-white">alebo</span>
            <div className="border-t border-gray-300 flex-grow"></div>
          </div>

          <button
            onClick={handleGoogleSignIn}
            disabled={loading}
            className="w-full flex items-center justify-center gap-2 bg-white border border-gray-300 rounded-lg py-2.5 px-4 text-gray-800 hover:bg-gray-50 transition-colors"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 48 48"
              width="24px"
              height="24px"
            >
              <path
                fill="#FFC107"
                d="M43.611,20.083H42V20H24v8h11.303c-1.649,4.657-6.08,8-11.303,8c-6.627,0-12-5.373-12-12c0-6.627,5.373-12,12-12c3.059,0,5.842,1.154,7.961,3.039l5.657-5.657C34.046,6.053,29.268,4,24,4C12.955,4,4,12.955,4,24c0,11.045,8.955,20,20,20c11.045,0,20-8.955,20-20C44,22.659,43.862,21.35,43.611,20.083z"
              />
              <path
                fill="#FF3D00"
                d="M6.306,14.691l6.571,4.819C14.655,15.108,18.961,12,24,12c3.059,0,5.842,1.154,7.961,3.039l5.657-5.657C34.046,6.053,29.268,4,24,4C16.318,4,9.656,8.337,6.306,14.691z"
              />
              <path
                fill="#4CAF50"
                d="M24,44c5.166,0,9.86-1.977,13.409-5.192l-6.19-5.238C29.211,35.091,26.715,36,24,36c-5.202,0-9.619-3.317-11.283-7.946l-6.522,5.025C9.505,39.556,16.227,44,24,44z"
              />
              <path
                fill="#1976D2"
                d="M43.611,20.083H42V20H24v8h11.303c-0.792,2.237-2.231,4.166-4.087,5.571c0.001-0.001,0.002-0.001,0.003-0.002l6.19,5.238C36.971,39.205,44,34,44,24C44,22.659,43.862,21.35,43.611,20.083z"
              />
            </svg>
            <span>Pokračovať s Google</span>
          </button>
        </div>

        {loading && (
          <div className="mt-4 text-center text-neutral-500">
            Prebieha spracovanie...
          </div>
        )}

        <div className="mt-6 text-center">
          {currentView === 'login' ? (
            <p className="text-sm text-neutral-600">
              Nemáte účet?{' '}
              <button
                onClick={() => switchView('register')}
                className="text-primary hover:underline"
                disabled={loading}
              >
                Registrujte sa
              </button>
            </p>
          ) : (
            <p className="text-sm text-neutral-600">
              Už máte účet?{' '}
              <button
                onClick={() => switchView('login')}
                className="text-primary hover:underline"
                disabled={loading}
              >
                Prihláste sa
              </button>
            </p>
          )}
        </div>

        {currentView === 'login' && (
          <div className="mt-4 text-center text-xs text-neutral-500">
            Prihlásením súhlasíte s našimi <Link href="/podmienky-pouzitia" className="text-primary hover:underline" target="_blank">podmienkami používania</Link> a <Link href="/ochrana-osobnych-udajov" className="text-primary hover:underline" target="_blank">ochranou osobných údajov</Link>.
          </div>
        )}
      </div>
    </div>
  );
}
