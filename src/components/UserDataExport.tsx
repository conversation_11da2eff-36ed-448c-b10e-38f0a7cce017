'use client';

import { useState } from 'react';
import { useAuth } from '../contexts/AuthContext';

/**
 * Component for exporting user data in compliance with GDPR
 * Allows users to download all their personal data in either XML or JSON format
 */
export default function UserDataExport() {
  const { user } = useAuth();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [format, setFormat] = useState<'json' | 'xml'>('json');

  // Function to handle the export request
  const handleExport = async () => {
    if (!user || !user.hashedUserId) {
      setError('Mus<PERSON>te byť prihlásený pre export dát');
      return;
    }

    try {
      setIsLoading(true);
      setError(null);

      // Prepare headers
      const headers: Record<string, string> = {};

      // Add authorization token if available
      if (typeof user.getIdToken === 'function') {
        headers['Authorization'] = `Bearer ${await user.getIdToken()}`;
      }

      // Create the URL with the format parameter
      const url = `/api/users/${user.hashedUserId}/export?format=${format}`;

      // Use fetch to get the file as a blob
      const response = await fetch(url, {
        method: 'GET',
        headers,
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Nepodarilo sa exportovať dáta');
      }

      // Get the blob from the response
      const blob = await response.blob();

      // Create a URL for the blob
      const downloadUrl = window.URL.createObjectURL(blob);

      // Create a temporary link element to trigger the download
      const link = document.createElement('a');
      link.href = downloadUrl;
      link.download = `user_data_${format}.zip`;
      document.body.appendChild(link);
      link.click();

      // Clean up
      document.body.removeChild(link);
      window.URL.revokeObjectURL(downloadUrl);
    } catch (err) {
      console.error('Error exporting user data:', err);
      setError(err instanceof Error ? err.message : 'Nastala chyba pri exporte dát');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="bg-white shadow-md rounded-lg p-4 mb-4">
      <h2 className="text-xl font-semibold mb-4">Export osobných údajov (GDPR)</h2>
      <p className="mb-4">
        V súlade s GDPR si môžete stiahnuť všetky vaše osobné údaje, ktoré o vás uchovávame.
        Dáta budú exportované vo formáte ZIP, ktorý obsahuje vaše údaje a všetky súvisiace obrázky.
      </p>

      <div className="mb-4">
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Formát dát:
        </label>
        <div className="flex space-x-4">
          <label className="inline-flex items-center">
            <input
              type="radio"
              className="form-radio"
              name="format"
              value="json"
              checked={format === 'json'}
              onChange={() => setFormat('json')}
            />
            <span className="ml-2">JSON</span>
          </label>
          <label className="inline-flex items-center">
            <input
              type="radio"
              className="form-radio"
              name="format"
              value="xml"
              checked={format === 'xml'}
              onChange={() => setFormat('xml')}
            />
            <span className="ml-2">XML</span>
          </label>
        </div>
      </div>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          {error}
        </div>
      )}

      <button
        onClick={handleExport}
        disabled={isLoading || !user}
        className={`w-full py-2 px-4 rounded font-medium ${
          isLoading || !user
            ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
            : 'bg-blue-600 text-white hover:bg-blue-700'
        }`}
      >
        {isLoading ? 'Exportujem...' : 'Exportovať moje dáta'}
      </button>
    </div>
  );
}
