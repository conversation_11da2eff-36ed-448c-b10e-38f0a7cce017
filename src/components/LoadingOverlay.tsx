'use client';

import React from 'react';

interface LoadingOverlayProps {
  isVisible: boolean;
  message?: string;
  className?: string;
}

/**
 * LoadingOverlay component provides a full-screen overlay with loading spinner
 * Used to block UI interactions during async operations like saving
 */
export default function LoadingOverlay({ 
  isVisible, 
  message = 'Načítavam...', 
  className = '' 
}: LoadingOverlayProps) {
  if (!isVisible) return null;

  return (
    <div 
      className={`fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 ${className}`}
      role="dialog"
      aria-modal="true"
      aria-label="Loading"
    >
      <div className="bg-white rounded-lg p-6 shadow-xl flex flex-col items-center justify-center min-w-[280px] max-w-[400px] mx-4">
        {/* Loading Spinner */}
        <div className="flex justify-center mb-4 w-full">
          <svg
            className="animate-spin h-10 w-10 text-primary"
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
          >
            <circle
              className="opacity-25"
              cx="12"
              cy="12"
              r="10"
              stroke="currentColor"
              strokeWidth="4"
            />
            <path
              className="opacity-75"
              fill="currentColor"
              d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
            />
          </svg>
        </div>

        {/* Loading Message */}
        <p className="text-neutral-700 text-lg font-medium text-center w-full flex justify-center">
          <span className="block">{message}</span>
        </p>
      </div>
    </div>
  );
}
