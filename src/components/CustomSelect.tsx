'use client';

import { useState, useRef, useEffect } from 'react';

interface Option {
  value: string;
  label: string;
}

interface CustomSelectProps {
  options: Option[] | string[];
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  className?: string;
  id?: string;
  disabled?: boolean;
  disabledTooltip?: string;
}

export default function CustomSelect({
  options,
  value,
  onChange,
  placeholder = 'Vyberte možnosť...',
  className = '',
  id = 'custom-select',
  disabled = false,
  disabledTooltip = 'Táto mož<PERSON>ť je momentálne nedostupná'
}: CustomSelectProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [isFocused, setIsFocused] = useState(false);
  const containerRef = useRef<HTMLDivElement>(null);
  
  // Convert string[] to Option[] if needed
  const normalizedOptions: Option[] = options.map(option => 
    typeof option === 'string' ? { value: option, label: option } : option as Option
  );

  // Find the label for the current value
  const getSelectedLabel = () => {
    const option = normalizedOptions.find(opt => opt.value === value);
    return option ? option.label : placeholder;
  };
  
  // Close dropdown when clicking outside
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (containerRef.current && !containerRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    }
    
    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Handle option selection
  const handleOptionClick = (optionValue: string) => {
    if (disabled) return;
    onChange(optionValue);
    setIsOpen(false);
  };

  return (
    <div className={`relative ${className}`} ref={containerRef}>
      {/* Selected value display area */}
      <div
        className={`w-full min-h-[42px] px-3 py-1.5 border rounded-md flex items-center justify-between shadow-sm transition-all duration-200 ${
          disabled
            ? 'bg-neutral-100 border-neutral-200 cursor-not-allowed opacity-60'
            : isFocused
            ? 'bg-white border-primary outline-none ring-2 ring-primary ring-opacity-20 cursor-pointer'
            : 'bg-white border-neutral-300 cursor-pointer'
        }`}
        onClick={() => !disabled && setIsOpen(!isOpen)}
        onFocus={() => !disabled && setIsFocused(true)}
        onBlur={() => setIsFocused(false)}
        tabIndex={disabled ? -1 : 0}
        aria-expanded={isOpen}
        aria-haspopup="listbox"
        role="combobox"
        title={disabled ? disabledTooltip : undefined}
        onKeyDown={(e) => {
          if (disabled) return;
          if (e.key === 'Enter' || e.key === ' ') {
            e.preventDefault();
            setIsOpen(!isOpen);
          } else if (e.key === 'Escape') {
            setIsOpen(false);
          }
        }}
      >
        <span className={value ? 'text-neutral-700' : 'text-neutral-500'}>
          {getSelectedLabel()}
        </span>
        
        {/* Dropdown indicator */}
        <div>
          <svg 
            xmlns="http://www.w3.org/2000/svg" 
            className={`h-4 w-4 text-neutral-500 transition-transform ${isOpen ? 'rotate-180' : ''}`} 
            fill="none" 
            viewBox="0 0 24 24" 
            stroke="currentColor"
          >
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
          </svg>
        </div>
      </div>

      {/* Dropdown */}
      {isOpen && !disabled && (
        <div 
          className="absolute z-10 mt-1 w-full bg-white border border-neutral-300 rounded-md shadow-lg max-h-60 overflow-auto"
          role="listbox"
        >
          {normalizedOptions.map(option => (
            <div
              key={option.value}
              className={`px-4 py-2 cursor-pointer hover:bg-neutral-100 ${
                option.value === value 
                  ? 'bg-primary bg-opacity-20 text-primary-dark font-medium border-l-4 border-primary' 
                  : 'text-neutral-700'
              }`}
              onClick={() => handleOptionClick(option.value)}
              role="option"
              aria-selected={option.value === value}
            >
              <div className="flex items-center">
                {/* Checkmark for selected item */}
                <div className="mr-2 w-5 flex-shrink-0">
                  {option.value === value && (
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={2.5}>
                      <path strokeLinecap="round" strokeLinejoin="round" d="M5 13l4 4L19 7" />
                    </svg>
                  )}
                </div>
                <span>{option.label}</span>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
}
