'use client';

import { useAuth } from '../contexts/AuthContext';
import { useReservationNotificationContext } from '../contexts/ReservationNotificationContext';
import Link from 'next/link';
import { useState } from 'react';

export default function ReservationNotification() {
  const { user } = useAuth();
  const { counts, hasActiveReservations, loading } = useReservationNotificationContext();
  const [isHovered, setIsHovered] = useState(false);

  // Nezobrazovať komponentu ak:
  // - používateľ nie je prihlásený
  // - nemá žiadne aktívne rezervácie
  // - práve sa načítavajú údaje
  if (!user || !hasActiveReservations || loading) {
    return null;
  }

  const getMessage = () => {
    const messages: string[] = [];

    // Moje rezervácie (rezervácie, ktoré si používateľ vytvoril) - len čaka<PERSON>
    if (counts.myPending > 0) {
      messages.push(`${counts.myPending} čakajúcich rezervácií`);
    }

    // Rezervácie mojich hračiek (požiadavky na moje hračky) - len čakajúce
    if (counts.ownerPending > 0) {
      messages.push(`${counts.ownerPending} dopytov na moje hračky`);
    }

    // Spojenie správ
    if (messages.length === 0) {
      return '';
    } else if (messages.length === 1) {
      return messages[0];
    } else if (messages.length === 2) {
      return messages.join(', ');
    } else {
      // Pre viac ako 2 správy, skrátime na celkový počet
      return `${counts.total} čakajúcich rezervácií`;
    }
  };

  // Skrátená verzia správy pre mobilné zariadenia
  const getMobileMessage = () => {
    // Pre mobilné zariadenia použijeme kratšie správy
    if (counts.myPending > 0 && counts.ownerPending > 0) {
      return `${counts.total} dopytov na hračky`;
    } else if (counts.myPending > 0) {
      return `${counts.myPending} čakajúcich rezervácií`;
    } else if (counts.ownerPending > 0) {
      return `${counts.ownerPending} dopytov na hračky`;
    }
    return '';
  };

  const getIcon = () => {
    // Zobrazíme hodiny pre čakajúce rezervácie
    return (
      <svg
        xmlns="http://www.w3.org/2000/svg"
        className="h-5 w-5"
        fill="none"
        viewBox="0 0 24 24"
        stroke="currentColor"
      >
        <path
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth={2}
          d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
        />
      </svg>
    );
  };

  const getBadgeColor = () => {
    return 'text-neutral-800'; // Jednotná farba textu
  };

  const getBadgeStyle = () => {
    // Vždy žltá pre čakajúce rezervácie
    return { backgroundColor: 'var(--warning)' };
  };

  return (
    <div className="fixed bottom-4 right-4 z-40">
      <Link href="/rezervacie">
        <div
          className={`
            flex  gap-2 md:gap-3 px-3 md:px-4 py-2 md:py-3 rounded-lg shadow-lg cursor-pointer
            transition-all duration-300 ease-in-out transform
            ${getBadgeColor()}
            ${isHovered ? 'scale-105 shadow-xl' : 'hover:scale-105 hover:shadow-xl'}
            animate-fade-in
            max-w-[280px] md:max-w-sm
            whitespace-nowrap overflow-hidden
          `}
          style={getBadgeStyle()}
          onMouseEnter={() => setIsHovered(true)}
          onMouseLeave={() => setIsHovered(false)}
        >
          {/* Ikona */}
          <div className="flex-shrink-0">
            <div className="w-4 h-4 md:w-5 md:h-5">
              {getIcon()}
            </div>
          </div>

          {/* Text - použije mobilnú verziu na malých obrazovkách */}
          <div className="flex-1 min-w-0 overflow-hidden">
            <p className="text-xs md:text-sm font-medium truncate">
              <span className="md:hidden">{getMobileMessage()}</span>
              <span className="hidden md:inline">{getMessage()}</span>
            </p>
          </div>

          {/* Badge s počtom */}
          {counts.total > 0 && (
            <div className="flex-shrink-0">
              <span
                className="inline-flex items-center justify-center w-4 h-4 md:w-5 md:h-5 text-xs font-bold text-white rounded-full"
                style={{ backgroundColor: 'var(--primary)' }}
              >
                {counts.total}
              </span>
            </div>
          )}

          {/* Šípka */}
          <div className="flex-shrink-0">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className={`w-3 h-3 md:w-4 md:h-4 transition-transform duration-200 ${isHovered ? 'translate-x-1' : ''}`}
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M9 5l7 7-7 7"
              />
            </svg>
          </div>
        </div>
      </Link>
    </div>
  );
}
