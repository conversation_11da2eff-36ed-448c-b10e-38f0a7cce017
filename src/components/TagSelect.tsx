'use client';

import { useState, useRef, useEffect } from 'react';

interface Option {
  value: string;
  label: string;
}

interface TagSelectProps {
  options: Option[];
  value: string[];
  onChange: (value: string[]) => void;
  placeholder?: string;
  className?: string;
  id?: string;
  allOptionValue?: string;
}

export default function TagSelect({
  options,
  value,
  onChange,
  placeholder = 'Vyberte možnosti...',
  className = '',
  id = 'tag-select',
  allOptionValue = 'Všetky typy'
}: TagSelectProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [isFocused, setIsFocused] = useState(false);
  const containerRef = useRef<HTMLDivElement>(null);

  // Close dropdown when clicking outside
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (containerRef.current && !containerRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    }

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Handle option selection
  const handleOptionClick = (optionValue: string) => {
    if (optionValue === allOptionValue) {
      // If "All types" is selected, only select that option
      onChange([allOptionValue]);
    } else {
      let newValue: string[];

      if (value.includes(allOptionValue)) {
        // If "All types" is currently selected, replace it with the new selection
        newValue = [optionValue];
      } else if (value.includes(optionValue)) {
        // If the option is already selected, remove it
        newValue = value.filter(v => v !== optionValue);
      } else {
        // Otherwise, add the new option to the selection
        newValue = [...value, optionValue];
      }

      // If no options are selected, default to "All types"
      onChange(newValue.length === 0 ? [allOptionValue] : newValue);
    }

    // Keep the dropdown open for multiple selections
    // setIsOpen(false);
  };

  // Remove a selected tag
  const removeTag = (optionValue: string, e: React.MouseEvent) => {
    e.stopPropagation(); // Prevent the dropdown from opening

    const newValue = value.filter(v => v !== optionValue);

    // If no options are selected, default to "All types"
    onChange(newValue.length === 0 ? [allOptionValue] : newValue);
  };

  // Find the label for a value
  const getLabelForValue = (optionValue: string) => {
    const option = options.find(opt => opt.value === optionValue);
    return option ? option.label : optionValue;
  };

  return (
    <div className={`relative ${className}`} ref={containerRef}>
      {/* Selected tags display area */}
      <div
        className={`w-full min-h-[42px] px-3 py-1.5 border rounded-md bg-white flex flex-wrap items-center gap-2 cursor-pointer shadow-sm ${
          isFocused
            ? 'border-primary outline-none ring-2 ring-primary ring-opacity-20'
            : 'border-neutral-300'
        }`}
        onClick={() => setIsOpen(!isOpen)}
        onFocus={() => setIsFocused(true)}
        onBlur={() => setIsFocused(false)}
        tabIndex={0}
        aria-expanded={isOpen}
        aria-haspopup="listbox"
        role="combobox"
        onKeyDown={(e) => {
          if (e.key === 'Enter' || e.key === ' ') {
            e.preventDefault();
            setIsOpen(!isOpen);
          } else if (e.key === 'Escape') {
            setIsOpen(false);
          }
        }}
      >
        {value.length > 0 && !value.includes(allOptionValue) ? (
          <>
            {value.map(val => (
              <div
                key={val}
                className="bg-primary bg-opacity-20 text-primary-dark px-3 py-1 rounded-md text-sm font-medium flex items-center shadow-sm border border-primary border-opacity-20"
                role="option"
                aria-selected="true"
              >
                {getLabelForValue(val)}
                <button
                  type="button"
                  className="ml-2 text-primary hover:text-primary-dark focus:outline-none"
                  onClick={(e) => removeTag(val, e)}
                  aria-label={`Odstrániť ${getLabelForValue(val)}`}
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-3.5 w-3.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>
            ))}
          </>
        ) : (
          <span className="text-neutral-500">{placeholder}</span>
        )}

        {/* Dropdown indicator */}
        <div className="ml-auto">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className={`h-4 w-4 text-neutral-500 transition-transform ${isOpen ? 'rotate-180' : ''}`}
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
          </svg>
        </div>
      </div>

      {/* Dropdown */}
      {isOpen && (
        <div
          className="absolute z-10 mt-1 w-full bg-white border border-neutral-300 rounded-md shadow-lg max-h-60 overflow-auto"
          role="listbox"
        >
          {options.map(option => (
            <div
              key={option.value}
              className={`px-4 py-2 cursor-pointer hover:bg-neutral-100 ${
                (option.value === allOptionValue && value.includes(allOptionValue)) ||
                (option.value !== allOptionValue && value.includes(option.value))
                  ? 'bg-primary bg-opacity-20 text-primary-dark font-medium border-l-4 border-primary'
                  : 'text-neutral-700'
              }`}
              onClick={() => handleOptionClick(option.value)}
              role="option"
              aria-selected={
                (option.value === allOptionValue && value.includes(allOptionValue)) ||
                (option.value !== allOptionValue && value.includes(option.value))
              }
            >
              <div className="flex items-center">
                {/* Checkmark for selected items */}
                <div className="mr-2 w-5 flex-shrink-0">
                  {((option.value === allOptionValue && value.includes(allOptionValue)) ||
                    (option.value !== allOptionValue && value.includes(option.value))) && (
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={2.5}>
                      <path strokeLinecap="round" strokeLinejoin="round" d="M5 13l4 4L19 7" />
                    </svg>
                  )}
                </div>
                <span>{option.label}</span>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
}
