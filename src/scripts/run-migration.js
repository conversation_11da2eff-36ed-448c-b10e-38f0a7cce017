const mysql = require('mysql2/promise');
const fs = require('fs');
const path = require('path');
require('dotenv').config();

async function main() {
  console.log('Pripájam sa k databáze...');

  // Extrakcia údajov z DATABASE_URL
  const url = new URL(process.env.DATABASE_URL.replace(/^mysql:\/\//, 'http://'));
  const config = {
    host: url.hostname,
    user: url.username,
    password: decodeURIComponent(url.password),
    database: url.pathname.substring(1),
    multipleStatements: true
  };

  console.log(`Pripájam sa k databáze ${config.database} na serveri ${config.host}...`);

  try {
    // Vytvorenie pripojenia k databáze
    const connection = await mysql.createConnection(config);

    console.log('Pripojenie úspešné!');

    // Načítanie a spustenie SQL skriptu
    console.log('Spúšťam migráciu...');
    const migrationSQL = fs.readFileSync(path.join(__dirname, '../src/prisma/migrations/add-terms-accepted.sql'), 'utf8');
    await connection.query(migrationSQL);

    console.log('Migrácia úspešne dokončená!');

    // Ukončenie pripojenia
    await connection.end();

  } catch (error) {
    console.error('Chyba pri spúšťaní migrácie:', error);
    process.exit(1);
  }
}

main();
