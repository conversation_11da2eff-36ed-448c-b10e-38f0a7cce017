/**
 * Jednoduchý test script na odoslanie emailov
 * Používa priamo Resend API bez závislostí na Next.js moduloch
 */

const { Resend } = require('resend');
require('dotenv').config();

// Inicializácia Resend
const resend = new Resend(process.env.RESEND_API_KEY);

// Email konfigurácia
const EMAIL_FROM = process.env.EMAIL_FROM || '<EMAIL>';
const EMAIL_FROM_NAME = process.env.EMAIL_FROM_NAME || 'Swapka - Zdieľanie hračiek';

async function sendTestReservationCreatedEmail() {
  console.log('📧 Odosielam test email - Vytvorenie rezervácie...');
  
  const subject = 'TEST - Potvrdenie rezervácie - LEGO Technic Bugatti Chiron';
  
  const htmlContent = `
    <!DOCTYPE html>
    <html lang="sk">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>${subject}</title>
    </head>
    <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">
      <div style="background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
        <h1 style="color: #2563eb; margin: 0 0 10px 0; font-size: 24px;">Swapka - Zdieľanie hračiek</h1>
        <h2 style="color: #1f2937; margin: 0; font-size: 20px;">🧪 TEST - Potvrdenie rezervácie</h2>
      </div>
      
      <div style="background-color: white; padding: 20px; border-radius: 8px; border: 1px solid #e5e7eb;">
        <p style="margin: 0 0 16px 0; font-size: 16px;">Dobrý deň <strong>Michal Gašparík</strong>,</p>
        
        <p style="margin: 0 0 16px 0;">Vaša rezervácia bola úspešne vytvorená!</p>
        
        <div style="background-color: #f3f4f6; padding: 16px; border-radius: 6px; margin: 16px 0;">
          <h3 style="margin: 0 0 8px 0; color: #1f2937; font-size: 18px;">Detaily rezervácie:</h3>
          <p style="margin: 0 0 8px 0;"><strong>Hračka:</strong> LEGO Technic Bugatti Chiron</p>
          <p style="margin: 0;"><strong>ID rezervácie:</strong> hash_test_123456</p>
        </div>
        
        <p style="margin: 16px 0;">Teraz čakáme na schválenie od vlastníka hračky. Keď bude vaša rezervácia schválená, pošleme vám ďalší email s kontaktnými údajmi.</p>
        
        <div style="text-align: center; margin: 24px 0;">
          <a href="https://swapka.sk/rezervacie" 
             style="background-color: #2563eb; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; font-weight: 600; display: inline-block;">
            Zobraziť moje rezervácie
          </a>
        </div>
        
        <p style="margin: 16px 0 0 0; font-size: 14px; color: #6b7280;">
          Ďakujeme, že používate Swapka!<br>
          Tím Swapka
        </p>
      </div>
      
      <div style="margin-top: 20px; padding: 16px; background-color: #f9fafb; border-radius: 6px; font-size: 12px; color: #6b7280; text-align: center;">
        <p style="margin: 0 0 8px 0;">Toto je testovací email. Prosím, neodpovedajte na túto správu.</p>
        <p style="margin: 0;"><a href="https://swapka.sk/api/email/unsubscribe?token=test_token" style="color: #6b7280; text-decoration: underline;">Odhlásiť sa z emailových notifikácií</a></p>
      </div>
    </body>
    </html>
  `;

  const textContent = `
Dobrý deň Michal Gašparík,

Vaša rezervácia bola úspešne vytvorená!

Detaily rezervácie:
- Hračka: LEGO Technic Bugatti Chiron
- ID rezervácie: hash_test_123456

Teraz čakáme na schválenie od vlastníka hračky. Keď bude vaša rezervácia schválená, pošleme vám ďalší email s kontaktnými údajmi.

Zobraziť moje rezervácie: https://swapka.sk/rezervacie

Ďakujeme, že používate Swapka!
Tím Swapka

---
Toto je testovací email. Prosím, neodpovedajte na túto správu.
Odhlásiť sa z emailových notifikácií: https://swapka.sk/api/email/unsubscribe?token=test_token
  `;

  try {
    const result = await resend.emails.send({
      from: `${EMAIL_FROM_NAME} <${EMAIL_FROM}>`,
      to: ['<EMAIL>'],
      subject: subject,
      html: htmlContent,
      text: textContent,
    });

    console.log('✅ Email o vytvorení rezervácie odoslaný úspešne!');
    console.log('   Message ID:', result.data?.id);
    return result;
  } catch (error) {
    console.error('❌ Chyba pri odosielaní emailu o vytvorení rezervácie:', error);
    throw error;
  }
}

async function sendTestReservationApprovedEmail() {
  console.log('📧 Odosielam test email - Schválenie rezervácie...');
  
  const subject = 'TEST - Rezervácia schválená - LEGO Technic Bugatti Chiron';
  
  const htmlContent = `
    <!DOCTYPE html>
    <html lang="sk">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>${subject}</title>
    </head>
    <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">
      <div style="background-color: #f0fdf4; padding: 20px; border-radius: 8px; margin-bottom: 20px; border-left: 4px solid #22c55e;">
        <h1 style="color: #2563eb; margin: 0 0 10px 0; font-size: 24px;">Swapka - Zdieľanie hračiek</h1>
        <h2 style="color: #16a34a; margin: 0; font-size: 20px;">🎉 TEST - Rezervácia schválená!</h2>
      </div>
      
      <div style="background-color: white; padding: 20px; border-radius: 8px; border: 1px solid #e5e7eb;">
        <p style="margin: 0 0 16px 0; font-size: 16px;">Dobrý deň <strong>Michal Gašparík</strong>,</p>
        
        <p style="margin: 0 0 16px 0; color: #16a34a; font-weight: 600;">Skvelé správy! Vaša rezervácia bola schválená!</p>
        
        <div style="background-color: #f3f4f6; padding: 16px; border-radius: 6px; margin: 16px 0;">
          <h3 style="margin: 0 0 8px 0; color: #1f2937; font-size: 18px;">Detaily rezervácie:</h3>
          <p style="margin: 0 0 8px 0;"><strong>Hračka:</strong> LEGO Technic Bugatti Chiron</p>
          <p style="margin: 0;"><strong>ID rezervácie:</strong> hash_test_123456</p>
        </div>
        
        <div style="background-color: #fef3c7; padding: 16px; border-radius: 6px; margin: 16px 0; border-left: 4px solid #f59e0b;">
          <h3 style="margin: 0 0 12px 0; color: #92400e; font-size: 18px;">Kontaktné údaje vlastníka:</h3>
          <div style="color: #92400e;">
            <p><strong>Meno:</strong> Ján Novák</p>
            <p><strong>Email:</strong> <a href="mailto:<EMAIL>"><EMAIL></a></p>
            <p><strong>Telefón:</strong> <a href="tel:+421901234567">+421901234567</a></p>
            <p><strong>Mesto:</strong> Bratislava</p>
          </div>
        </div>
        
        <div style="background-color: #e0f2fe; padding: 16px; border-radius: 6px; margin: 16px 0;">
          <h3 style="margin: 0 0 8px 0; color: #0369a1; font-size: 16px;">Ďalšie kroky:</h3>
          <ol style="margin: 0; padding-left: 20px; color: #0369a1;">
            <li>Kontaktujte vlastníka hračky pomocou uvedených kontaktných údajov</li>
            <li>Dohodnite si čas a miesto prevzatia hračky</li>
            <li>Užite si zábavu s hračkou!</li>
          </ol>
        </div>
        
        <div style="text-align: center; margin: 24px 0;">
          <a href="https://swapka.sk/rezervacie" 
             style="background-color: #2563eb; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; font-weight: 600; display: inline-block;">
            Zobraziť moje rezervácie
          </a>
        </div>
        
        <p style="margin: 16px 0 0 0; font-size: 14px; color: #6b7280;">
          Ďakujeme, že používate Swapka!<br>
          Tím Swapka
        </p>
      </div>
      
      <div style="margin-top: 20px; padding: 16px; background-color: #f9fafb; border-radius: 6px; font-size: 12px; color: #6b7280; text-align: center;">
        <p style="margin: 0 0 8px 0;">Toto je testovací email. Prosím, neodpovedajte na túto správu.</p>
        <p style="margin: 0;"><a href="https://swapka.sk/api/email/unsubscribe?token=test_token" style="color: #6b7280; text-decoration: underline;">Odhlásiť sa z emailových notifikácií</a></p>
      </div>
    </body>
    </html>
  `;

  const textContent = `
Dobrý deň Michal Gašparík,

🎉 Skvelé správy! Vaša rezervácia bola schválená!

Detaily rezervácie:
- Hračka: LEGO Technic Bugatti Chiron
- ID rezervácie: hash_test_123456

Kontaktné údaje vlastníka:
Meno: Ján Novák
Email: <EMAIL>
Telefón: +421901234567
Mesto: Bratislava

Ďalšie kroky:
1. Kontaktujte vlastníka hračky pomocou uvedených kontaktných údajov
2. Dohodnite si čas a miesto prevzatia hračky
3. Užite si zábavu s hračkou!

Zobraziť moje rezervácie: https://swapka.sk/rezervacie

Ďakujeme, že používate Swapka!
Tím Swapka

---
Toto je testovací email. Prosím, neodpovedajte na túto správu.
Odhlásiť sa z emailových notifikácií: https://swapka.sk/api/email/unsubscribe?token=test_token
  `;

  try {
    const result = await resend.emails.send({
      from: `${EMAIL_FROM_NAME} <${EMAIL_FROM}>`,
      to: ['<EMAIL>'],
      subject: subject,
      html: htmlContent,
      text: textContent,
    });

    console.log('✅ Email o schválení rezervácie odoslaný úspešne!');
    console.log('   Message ID:', result.data?.id);
    return result;
  } catch (error) {
    console.error('❌ Chyba pri odosielaní emailu o schválení rezervácie:', error);
    throw error;
  }
}

async function main() {
  console.log('🧪 Spúšťam test odosielania <NAME_EMAIL>\n');

  // Kontrola konfigurácie
  if (!process.env.RESEND_API_KEY) {
    console.error('❌ RESEND_API_KEY nie je nastavený v .env súbore');
    return;
  }

  console.log('✅ RESEND_API_KEY je nastavený');
  console.log(`📧 Email FROM: ${EMAIL_FROM_NAME} <${EMAIL_FROM}>\n`);

  try {
    // Test 1: Email o vytvorení rezervácie
    await sendTestReservationCreatedEmail();
    
    // Krátka pauza medzi emailmi
    console.log('\n⏳ Čakám 2 sekundy pred odoslaním druhého emailu...\n');
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // Test 2: Email o schválení rezervácie
    await sendTestReservationApprovedEmail();
    
    console.log('\n🎉 Všetky testovacie emaily boli úspešne odoslané!');
    console.log('📬 Skontrolujte svoju emailovú schránku: <EMAIL>');
    
  } catch (error) {
    console.error('\n❌ Chyba pri testovaní emailov:', error);
  }
}

// Spustenie testu
if (require.main === module) {
  main();
}

module.exports = {
  sendTestReservationCreatedEmail,
  sendTestReservationApprovedEmail
};
