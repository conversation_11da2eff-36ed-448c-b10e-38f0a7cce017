const mysql = require('mysql2/promise');
const fs = require('fs');
const path = require('path');
require('dotenv').config();

async function main() {
  console.log('Pripájam sa k databáze...');
  
  // Extrakcia údajov z DATABASE_URL
  const url = new URL(process.env.DATABASE_URL.replace(/^mysql:\/\//, 'http://'));
  const config = {
    host: url.hostname,
    user: url.username,
    password: decodeURIComponent(url.password),
    database: url.pathname.substring(1),
    multipleStatements: true
  };
  
  console.log(`Pripájam sa k databáze ${config.database} na serveri ${config.host}...`);
  
  try {
    // Vytvorenie pripojenia k databáze
    const connection = await mysql.createConnection(config);
    
    console.log('Pripojenie úspešné!');
    
    // Načítanie a spustenie SQL skriptov
    console.log('Vytv<PERSON><PERSON> tabuľky...');
    const schemaSQL = fs.readFileSync(path.join(__dirname, '../prisma/schema.sql'), 'utf8');
    await connection.query(schemaSQL);
    
    console.log('Tabuľky úspešne vytvorené!');
    
    console.log('Napĺňam databázu ukážkovými dátami...');
    const seedSQL = fs.readFileSync(path.join(__dirname, '../prisma/seed.sql'), 'utf8');
    await connection.query(seedSQL);
    
    console.log('Databáza úspešne naplnená ukážkovými dátami!');
    
    // Ukončenie pripojenia
    await connection.end();
    
    console.log('Databáza bola úspešne nastavená!');
  } catch (error) {
    console.error('Chyba pri nastavovaní databázy:', error);
    process.exit(1);
  }
}

main();
