/**
 * Skript pre nastavenie Firebase Admin SDK
 * 
 * Tento skript pomáha s nastavením Firebase Admin SDK pre server-side autentifikáciu.
 * Vysvetľuje, ako získať a nastaviť service account credentials pre Firebase Admin SDK.
 */

console.log(`
=======================================================
Nastavenie Firebase Admin SDK pre server-side autentifikáciu
=======================================================

Pre správne fungovanie Firebase Admin SDK potrebujete nastaviť service account credentials.
Nasledujte tieto kroky:

1. Prejdite do Firebase konzoly: https://console.firebase.google.com/
2. Vyberte váš projekt (${process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID || 'swapka-b9171'})
3. Prejdite do Project settings > Service accounts
4. Kliknite na "Generate new private key"
5. Stiahnite a uložte JSON súbor s credentials

Potom máte dve možnosti:

A) Použiť Google Application Default Credentials (ADC)
   - Nastavte premennú prostredia GOOGLE_APPLICATION_CREDENTIALS na cestu k JSON súboru
   - Napríklad: export GOOGLE_APPLICATION_CREDENTIALS="/path/to/service-account.json"

B) Nastaviť credentials priamo v .env súbore
   - Otvorte stiahnutý JSON súbor
   - Pridajte nasledujúce premenné do .env súboru:

FIREBASE_PROJECT_ID=project-id-z-json-suboru
FIREBASE_CLIENT_EMAIL=client-email-z-json-suboru
FIREBASE_PRIVATE_KEY="private-key-z-json-suboru"

Poznámka: FIREBASE_PRIVATE_KEY musí byť v úvodzovkách a zachovať všetky znaky nového riadku (\\n).

=======================================================
`);

// Kontrola, či sú nastavené potrebné premenné prostredia
if (process.env.GOOGLE_APPLICATION_CREDENTIALS) {
  console.log('✅ GOOGLE_APPLICATION_CREDENTIALS je nastavená na:', process.env.GOOGLE_APPLICATION_CREDENTIALS);
} else if (
  process.env.FIREBASE_PROJECT_ID &&
  process.env.FIREBASE_CLIENT_EMAIL &&
  process.env.FIREBASE_PRIVATE_KEY
) {
  console.log('✅ FIREBASE_PROJECT_ID, FIREBASE_CLIENT_EMAIL a FIREBASE_PRIVATE_KEY sú nastavené');
} else {
  console.log('❌ Nie sú nastavené potrebné premenné prostredia pre Firebase Admin SDK');
  console.log('   Prosím, nasledujte pokyny vyššie pre nastavenie credentials');
}

console.log('\nPo nastavení credentials upravte src/lib/firebaseAdmin.ts podľa potreby.');
