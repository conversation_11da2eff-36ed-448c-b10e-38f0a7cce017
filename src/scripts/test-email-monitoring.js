#!/usr/bin/env node

/**
 * Test script pre email monitoring systém
 * Testuje základné funkcie email monitoring API
 */

const { prisma } = require('../lib/db');
const { 
  getEmailQueueStatus, 
  getEmailStatistics, 
  getRecentEmailLogs 
} = require('../lib/emailNotificationService');

async function testEmailMonitoring() {
  console.log('🧪 Testovanie email monitoring systému...\n');

  try {
    // Test 1: Databázové pripojenie
    console.log('1️⃣ Testovanie databázového pripojenia...');
    const emailLogCount = await prisma.emailLog.count();
    console.log(`   ✅ Pripojenie úspešné. Počet záznamov v EmailLog: ${emailLogCount}\n`);

    // Test 2: Email queue status
    console.log('2️⃣ Testovanie email queue status...');
    const queueStatus = getEmailQueueStatus();
    console.log(`   📊 Dĺžka fronty: ${queueStatus.queueLength}`);
    console.log(`   🔄 Spracováva sa: ${queueStatus.isProcessing ? 'Áno' : 'Nie'}`);
    console.log(`   📧 Čakajúce emaile: ${queueStatus.pendingEmails.length}\n`);

    // Test 3: Email statistics
    console.log('3️⃣ Testovanie email štatistík...');
    const statistics = await getEmailStatistics();
    console.log(`   📈 Celkom emailov: ${statistics.totalEmails}`);
    console.log(`   ✅ Odoslané: ${statistics.sentEmails}`);
    console.log(`   ❌ Neúspešné: ${statistics.failedEmails}`);
    console.log(`   📊 Úspešnosť: ${statistics.successRate.toFixed(1)}%`);
    console.log(`   📋 Typy emailov: ${statistics.emailTypeBreakdown.length}\n`);

    // Test 4: Recent email logs
    console.log('4️⃣ Testovanie posledných email logov...');
    const recentLogs = await getRecentEmailLogs(10);
    console.log(`   📝 Počet posledných logov: ${recentLogs.length}`);
    
    if (recentLogs.length > 0) {
      const latestLog = recentLogs[0];
      console.log(`   🕐 Posledný log: ${latestLog.emailType} - ${latestLog.status} (${latestLog.createdAt})`);
    }
    console.log();

    // Test 5: Vytvorenie testovacieho logu
    console.log('5️⃣ Vytvorenie testovacieho email logu...');
    const testLog = await prisma.emailLog.create({
      data: {
        emailId: `test_${Date.now()}`,
        emailType: 'RESERVATION_CREATED',
        recipientEmail: 'te***@example.com',
        recipientUserId: 1,
        subject: 'Test email monitoring',
        status: 'SENT',
        attempt: 1,
        maxAttempts: 3,
        messageId: `test_msg_${Date.now()}`,
        sentAt: new Date()
      }
    });
    console.log(`   ✅ Testovací log vytvorený s ID: ${testLog.id}\n`);

    // Test 6: Overenie štatistík po pridaní testovacieho logu
    console.log('6️⃣ Overenie aktualizovaných štatistík...');
    const updatedStatistics = await getEmailStatistics();
    console.log(`   📈 Nový počet emailov: ${updatedStatistics.totalEmails}`);
    console.log(`   ✅ Nový počet odoslaných: ${updatedStatistics.sentEmails}\n`);

    // Test 7: Vymazanie testovacieho logu
    console.log('7️⃣ Vymazanie testovacieho logu...');
    await prisma.emailLog.delete({
      where: { id: testLog.id }
    });
    console.log(`   🗑️ Testovací log vymazaný\n`);

    console.log('🎉 Všetky testy úspešne dokončené!');
    console.log('\n📝 Email monitoring systém je pripravený na použitie:');
    console.log('   1. Prihláste sa ako admin (<EMAIL>)');
    console.log('   2. Prejdite do Admin sekcie');
    console.log('   3. Kliknite na záložku "Email monitoring"');
    console.log('   4. Sledujte real-time štatistiky a logy');

  } catch (error) {
    console.error('\n❌ Chyba pri testovaní:', error.message);
    console.error('\n🔧 Možné riešenia:');
    console.error('   1. Skontrolujte, či je databáza dostupná');
    console.error('   2. Overte, že migrácia bola úspešne vykonaná');
    console.error('   3. Skontrolujte Prisma konfiguráciu');
    process.exit(1);
  } finally {
    await prisma.$disconnect();
    console.log('\n🔌 Databázové pripojenie ukončené');
  }
}

// Spustenie testov
if (require.main === module) {
  testEmailMonitoring();
}

module.exports = { testEmailMonitoring };
