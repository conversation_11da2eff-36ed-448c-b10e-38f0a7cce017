// Skript pre zobrazenie informácií o verzii aplikácie

require('dotenv').config();
const fs = require('fs');
const path = require('path');

const packageJson = require('../package.json');

// Generate build ID if not exists
const buildId = process.env.BUILD_ID || `build-${Date.now()}-${Math.random().toString(36).substring(2, 15)}`;
process.env.BUILD_ID = buildId;

function displayVersionInfo() {
  console.log('\n=== SWAPKA APPLICATION INFO ===');
  console.log(`Name: Swapka`);
  console.log(`Version: ${packageJson.version}`);
  console.log(`Description: Aplikácia na zdieľanie a požičiavanie detských hračiek`);
  console.log(`Running in ${process.env.NODE_ENV || 'development'} mode`);
  console.log(`Build ID: ${buildId}`);
  console.log(`Cache busting: ${process.env.NODE_ENV === 'production' ? 'ENABLED' : 'DISABLED'}`);
  console.log('===============================\n');
}

// Write version info to a file for runtime access
function writeVersionInfo() {
  const versionInfo = {
    version: packageJson.version,
    buildId: buildId,
    timestamp: Date.now(),
    environment: process.env.NODE_ENV || 'development',
    buildDate: new Date().toISOString(),
    cacheBusting: process.env.NODE_ENV === 'production',
  };

  const versionInfoPath = path.join(__dirname, '..', 'version-info.json');
  fs.writeFileSync(versionInfoPath, JSON.stringify(versionInfo, null, 2));

  if (process.env.NODE_ENV !== 'production') {
    console.log(`📝 Version info saved to ${versionInfoPath}`);
  }
}

// Zobrazenie informácií
displayVersionInfo();
writeVersionInfo();

module.exports = { displayVersionInfo, writeVersionInfo };
