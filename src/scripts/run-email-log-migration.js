#!/usr/bin/env node

/**
 * Script na spustenie migrácie pre EmailLog tabuľku
 * Tento script pridá novú tabuľku EmailLog do databázy pre email monitoring
 */

const mysql = require('mysql2/promise');
const fs = require('fs');
const path = require('path');

// Načítanie environment premenných
require('dotenv').config();

async function runEmailLogMigration() {
  let connection;

  try {
    console.log('🚀 Spúšťam migráciu pre EmailLog tabuľku...\n');

    // Parsovanie DATABASE_URL
    const databaseUrl = process.env.DATABASE_URL;
    if (!databaseUrl) {
      throw new Error('DATABASE_URL nie je nastavená v .env súbore');
    }

    // Extrahovanie údajov z DATABASE_URL
    // Podporuje formáty: mysql://user:pass@host:port/db a mysql://user:pass@host/db
    let urlParts = databaseUrl.match(/mysql:\/\/([^:]+):([^@]+)@([^:\/]+):(\d+)\/(.+)/);
    let username, password, host, port, database;

    if (urlParts) {
      // Formát s portom: mysql://user:pass@host:port/db
      [, username, password, host, port, database] = urlParts;
      port = parseInt(port);
    } else {
      // Formát bez portu: mysql://user:pass@host/db
      urlParts = databaseUrl.match(/mysql:\/\/([^:]+):([^@]+)@([^\/]+)\/(.+)/);
      if (!urlParts) {
        throw new Error('Neplatný formát DATABASE_URL. Očakávaný formát: mysql://username:password@host[:port]/database');
      }
      [, username, password, host, database] = urlParts;
      port = 3306; // Predvolený MySQL port
    }

    // Dekódovanie URL-encoded znakov v hesle
    password = decodeURIComponent(password);

    console.log(`📊 Pripájam sa k databáze: ${database} na ${host}:${port}`);

    // Vytvorenie pripojenia
    connection = await mysql.createConnection({
      host,
      port,
      user: username,
      password,
      database,
      multipleStatements: true
    });

    console.log('✅ Pripojenie k databáze úspešné\n');

    // Načítanie SQL migračného súboru
    const migrationPath = path.join(__dirname, '..', 'prisma', 'migrations', 'add-email-log-table.sql');
    
    if (!fs.existsSync(migrationPath)) {
      throw new Error(`Migračný súbor nebol nájdený: ${migrationPath}`);
    }

    const migrationSql = fs.readFileSync(migrationPath, 'utf8');
    console.log('📄 Načítaný migračný súbor');

    // Rozdelenie SQL príkazov
    // Odstránenie komentárov a prázdnych riadkov
    const cleanedSql = migrationSql
      .split('\n')
      .filter(line => {
        const trimmed = line.trim();
        return trimmed.length > 0 && !trimmed.startsWith('--');
      })
      .join('\n');

    // Rozdelenie podľa semicolonov, ale zachovanie multi-line príkazov
    const sqlCommands = cleanedSql
      .split(';')
      .map(cmd => cmd.trim())
      .filter(cmd => cmd.length > 0);

    console.log(`📝 Spúšťam ${sqlCommands.length} SQL príkazov...\n`);

    // Spustenie každého SQL príkazu
    for (let i = 0; i < sqlCommands.length; i++) {
      const command = sqlCommands[i];
      console.log(`   ${i + 1}/${sqlCommands.length}: ${command.substring(0, 50)}...`);
      
      try {
        await connection.execute(command);
        console.log('   ✅ Úspešne vykonané');
      } catch (error) {
        if (error.code === 'ER_TABLE_EXISTS_ERROR' || error.message.includes('already exists')) {
          console.log('   ⚠️  Tabuľka už existuje, preskakujem');
        } else {
          console.error(`   ❌ Chyba: ${error.message}`);
          throw error;
        }
      }
    }

    console.log('\n✅ Migrácia úspešne dokončená!');
    
    // Overenie, že tabuľka bola vytvorená
    console.log('\n🔍 Overujem vytvorenú tabuľku...');
    
    const [tables] = await connection.execute(`
      SELECT TABLE_NAME 
      FROM INFORMATION_SCHEMA.TABLES 
      WHERE TABLE_SCHEMA = ? AND TABLE_NAME = 'EmailLog'
    `, [database]);

    if (tables.length > 0) {
      console.log('   ✅ Tabuľka EmailLog bola úspešne vytvorená');
      
      // Zobrazenie štruktúry tabuľky
      const [columns] = await connection.execute(`
        SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_DEFAULT
        FROM INFORMATION_SCHEMA.COLUMNS 
        WHERE TABLE_SCHEMA = ? AND TABLE_NAME = 'EmailLog'
        ORDER BY ORDINAL_POSITION
      `, [database]);

      console.log('\n📋 Štruktúra tabuľky EmailLog:');
      columns.forEach(col => {
        console.log(`   - ${col.COLUMN_NAME}: ${col.DATA_TYPE} ${col.IS_NULLABLE === 'NO' ? 'NOT NULL' : 'NULL'} ${col.COLUMN_DEFAULT ? `DEFAULT ${col.COLUMN_DEFAULT}` : ''}`);
      });
    } else {
      console.log('   ❌ Tabuľka EmailLog nebola nájdená');
    }

    // Zobrazenie indexov
    const [indexes] = await connection.execute(`
      SELECT INDEX_NAME, COLUMN_NAME
      FROM INFORMATION_SCHEMA.STATISTICS 
      WHERE TABLE_SCHEMA = ? AND TABLE_NAME = 'EmailLog'
      ORDER BY INDEX_NAME, SEQ_IN_INDEX
    `, [database]);

    if (indexes.length > 0) {
      console.log('\n🔍 Indexy tabuľky EmailLog:');
      const indexGroups = {};
      indexes.forEach(idx => {
        if (!indexGroups[idx.INDEX_NAME]) {
          indexGroups[idx.INDEX_NAME] = [];
        }
        indexGroups[idx.INDEX_NAME].push(idx.COLUMN_NAME);
      });
      
      Object.entries(indexGroups).forEach(([indexName, columns]) => {
        console.log(`   - ${indexName}: (${columns.join(', ')})`);
      });
    }

    console.log('\n🎉 Migrácia pre EmailLog tabuľku bola úspešne dokončená!');
    console.log('\n📝 Ďalšie kroky:');
    console.log('   1. Spustite: npx prisma generate');
    console.log('   2. Reštartujte aplikáciu: pm2 restart swapka');
    console.log('   3. Otestujte email monitoring v admin sekcii');

  } catch (error) {
    console.error('\n❌ Chyba pri migrácii:', error.message);
    console.error('\n🔧 Riešenie problémov:');
    console.error('   1. Skontrolujte DATABASE_URL v .env súbore');
    console.error('   2. Overte, že máte oprávnenia na vytvorenie tabuliek');
    console.error('   3. Skontrolujte, či je databázový server dostupný');
    process.exit(1);
  } finally {
    if (connection) {
      await connection.end();
      console.log('\n🔌 Pripojenie k databáze ukončené');
    }
  }
}

// Spustenie migrácie
if (require.main === module) {
  runEmailLogMigration();
}

module.exports = { runEmailLogMigration };
