// Skript pre testovanie Firebase Admin SDK
// Tento skript overí, či je Firebase Admin SDK správne inicializovaný

// Načítanie premenných prostredia z .env súboru
require('dotenv').config({ path: '../../.env' });

// Import Firebase Admin SDK
const admin = require('firebase-admin');

// Výpis premenných prostredia pre debugovanie
console.log('Premenné prostredia:');
console.log('FIREBASE_PROJECT_ID:', process.env.FIREBASE_PROJECT_ID);
console.log('FIREBASE_CLIENT_EMAIL:', process.env.FIREBASE_CLIENT_EMAIL);
console.log('FIREBASE_PRIVATE_KEY je nastavený:', !!process.env.FIREBASE_PRIVATE_KEY);
console.log('GOOGLE_APPLICATION_CREDENTIALS:', process.env.GOOGLE_APPLICATION_CREDENTIALS);

// Funkcia pre inicializáciu Firebase Admin SDK
async function initializeFirebaseAdmin() {
  try {
    // Inicializácia Firebase Admin SDK
    const firebaseConfig = {
      projectId: process.env.FIREBASE_PROJECT_ID,
    };

    // Ak máme nastavené service account credentials v .env, použijeme ich
    if (
      process.env.FIREBASE_PROJECT_ID &&
      process.env.FIREBASE_CLIENT_EMAIL &&
      process.env.FIREBASE_PRIVATE_KEY
    ) {
      console.log('Používam service account credentials z .env');
      
      // Vytvorenie credentials
      try {
        firebaseConfig.credential = admin.credential.cert({
          projectId: process.env.FIREBASE_PROJECT_ID,
          clientEmail: process.env.FIREBASE_CLIENT_EMAIL,
          // Uistíme sa, že privátny kľúč je správne formátovaný
          privateKey: process.env.FIREBASE_PRIVATE_KEY.replace(/\\n/g, '\n'),
        });
        console.log('Credentials boli úspešne vytvorené');
      } catch (credError) {
        console.error('Chyba pri vytváraní credentials:', credError);
        throw credError;
      }
    } else {
      // Inak sa spolieha na Google Application Default Credentials (ADC)
      console.log('Používam Google Application Default Credentials');
    }

    // Inicializácia Firebase Admin SDK
    console.log('Inicializujem Firebase Admin SDK s konfiguráciou:', JSON.stringify(firebaseConfig, (key, value) => {
      // Skrytie citlivých údajov
      if (key === 'privateKey') return '***HIDDEN***';
      return value;
    }, 2));
    
    const app = admin.initializeApp(firebaseConfig);
    console.log('Firebase Admin SDK bol úspešne inicializovaný');

    // Testovanie funkčnosti Firebase Admin SDK
    console.log('Testujem funkčnosť Firebase Admin SDK...');
    const listUsers = await app.auth().listUsers(1);
    console.log('Počet používateľov:', listUsers.users.length);
    console.log('Test bol úspešný!');

    return app;
  } catch (error) {
    console.error('Chyba pri inicializácii Firebase Admin SDK:', error);
    throw error;
  }
}

// Spustenie testu
initializeFirebaseAdmin()
  .then(() => {
    console.log('Test Firebase Admin SDK bol úspešne dokončený');
    process.exit(0);
  })
  .catch((error) => {
    console.error('Test Firebase Admin SDK zlyhal:', error);
    process.exit(1);
  });
