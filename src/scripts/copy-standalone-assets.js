#!/usr/bin/env node

/**
 * Copy Static Assets for Next.js Standalone Mode
 * 
 * This script copies static files and public assets to the standalone directory
 * to fix 404 errors in production deployment.
 * 
 * In Next.js standalone mode, static files are not automatically copied,
 * which causes 404 errors for CSS, JS chunks, and other assets.
 */

const fs = require('fs');
const path = require('path');

// ANSI color codes for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function log(message, color = colors.reset) {
  console.log(`${color}${message}${colors.reset}`);
}

function logSuccess(message) {
  log(`✅ ${message}`, colors.green);
}

function logError(message) {
  log(`❌ ${message}`, colors.red);
}

function logWarning(message) {
  log(`⚠️  ${message}`, colors.yellow);
}

function logInfo(message) {
  log(`ℹ️  ${message}`, colors.blue);
}

/**
 * Recursively copy directory contents
 */
function copyDirectory(src, dest) {
  if (!fs.existsSync(src)) {
    logWarning(`Source directory does not exist: ${src}`);
    return false;
  }

  // Create destination directory if it doesn't exist
  if (!fs.existsSync(dest)) {
    fs.mkdirSync(dest, { recursive: true });
  }

  const entries = fs.readdirSync(src, { withFileTypes: true });
  let copiedFiles = 0;

  for (const entry of entries) {
    const srcPath = path.join(src, entry.name);
    const destPath = path.join(dest, entry.name);

    if (entry.isDirectory()) {
      const subResult = copyDirectory(srcPath, destPath);
      if (subResult !== false) {
        copiedFiles += subResult;
      }
    } else {
      try {
        fs.copyFileSync(srcPath, destPath);
        copiedFiles++;
      } catch (error) {
        logError(`Failed to copy file ${srcPath}: ${error.message}`);
      }
    }
  }

  return copiedFiles;
}

/**
 * Get file size in human readable format
 */
function getFileSize(filePath) {
  try {
    const stats = fs.statSync(filePath);
    const bytes = stats.size;
    
    if (bytes === 0) return '0 B';
    
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  } catch (error) {
    return 'Unknown';
  }
}

/**
 * Main function to copy standalone assets
 */
function copyStandaloneAssets() {
  log('\n🚀 Starting Next.js Standalone Assets Copy Process...', colors.bright);
  
  const projectRoot = process.cwd();
  const nextDir = path.join(projectRoot, '.next');
  const standaloneDir = path.join(nextDir, 'standalone');
  const staticDir = path.join(nextDir, 'static');
  const publicDir = path.join(projectRoot, 'public');
  
  // Check if standalone directory exists
  if (!fs.existsSync(standaloneDir)) {
    logError('Standalone directory not found. Make sure you have run "next build" first.');
    logError(`Expected location: ${standaloneDir}`);
    process.exit(1);
  }

  logInfo(`Project root: ${projectRoot}`);
  logInfo(`Standalone directory: ${standaloneDir}`);
  
  let totalCopiedFiles = 0;
  
  // 1. Copy static files (.next/static -> .next/standalone/.next/static)
  if (fs.existsSync(staticDir)) {
    logInfo('\n📁 Copying Next.js static files...');
    
    const standaloneNextDir = path.join(standaloneDir, '.next');
    const standaloneStaticDir = path.join(standaloneNextDir, 'static');
    
    // Create .next directory in standalone if it doesn't exist
    if (!fs.existsSync(standaloneNextDir)) {
      fs.mkdirSync(standaloneNextDir, { recursive: true });
    }
    
    const staticFilesCopied = copyDirectory(staticDir, standaloneStaticDir);
    if (staticFilesCopied !== false) {
      totalCopiedFiles += staticFilesCopied;
      logSuccess(`Copied ${staticFilesCopied} static files to standalone directory`);
    }
  } else {
    logWarning('Static directory not found. This might be normal for some builds.');
  }
  
  // 2. Copy public files (public -> .next/standalone/public)
  if (fs.existsSync(publicDir)) {
    logInfo('\n📁 Copying public assets...');

    const standalonePublicDir = path.join(standaloneDir, 'public');
    const publicFilesCopied = copyDirectory(publicDir, standalonePublicDir);
    if (publicFilesCopied !== false) {
      totalCopiedFiles += publicFilesCopied;
      logSuccess(`Copied ${publicFilesCopied} public files to standalone directory`);
    }
  } else {
    logWarning('Public directory not found.');
  }

  // 3. Copy Prisma client binaries (app/generated/prisma -> .next/standalone/app/generated/prisma)
  const prismaDir = path.join(projectRoot, 'app', 'generated', 'prisma');
  if (fs.existsSync(prismaDir)) {
    logInfo('\n📁 Copying Prisma client binaries...');

    const standalonePrismaDir = path.join(standaloneDir, 'app', 'generated', 'prisma');
    const prismaFilesCopied = copyDirectory(prismaDir, standalonePrismaDir);
    if (prismaFilesCopied !== false) {
      totalCopiedFiles += prismaFilesCopied;
      logSuccess(`Copied ${prismaFilesCopied} Prisma files to standalone directory`);
    }
  } else {
    logWarning('Prisma generated directory not found. Run "npx prisma generate" first.');
  }
  
  // 4. Verify critical files exist
  logInfo('\n🔍 Verifying critical files...');

  const criticalFiles = [
    path.join(standaloneDir, 'server.js'),
    path.join(standaloneDir, '.next', 'static'),
    path.join(standaloneDir, 'public'),
    path.join(standaloneDir, 'app', 'generated', 'prisma')
  ];
  
  let allCriticalFilesExist = true;
  
  for (const filePath of criticalFiles) {
    if (fs.existsSync(filePath)) {
      const size = fs.statSync(filePath).isDirectory() ? 'Directory' : getFileSize(filePath);
      logSuccess(`${path.relative(standaloneDir, filePath)} - ${size}`);
    } else {
      logError(`Missing: ${path.relative(standaloneDir, filePath)}`);
      allCriticalFilesExist = false;
    }
  }
  
  // 4. Summary
  log('\n📊 Copy Summary:', colors.bright);
  log(`Total files copied: ${totalCopiedFiles}`, colors.cyan);
  log(`Standalone directory size: ${getFileSize(standaloneDir)}`, colors.cyan);
  
  if (allCriticalFilesExist && totalCopiedFiles > 0) {
    logSuccess('\n✅ Standalone assets copy completed successfully!');
    logInfo('Your Next.js standalone build is ready for production deployment.');
    logInfo('Make sure to restart your production server to pick up the new assets.');
  } else {
    logError('\n❌ Standalone assets copy completed with issues.');
    logError('Please check the errors above and ensure your build completed successfully.');
    process.exit(1);
  }
}

// Run the script
if (require.main === module) {
  copyStandaloneAssets();
}

module.exports = { copyStandaloneAssets };
