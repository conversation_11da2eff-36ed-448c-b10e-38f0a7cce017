/**
 * Script na spustenie migrácie pre emailové preferencie
 * Tento script sa pokúsi pripojiť k databáze a spustiť migráciu
 */

const mysql = require('mysql2/promise');
const fs = require('fs');
const path = require('path');

// Načítanie environment variables
require('dotenv').config();

async function runEmailMigration() {
  console.log('🔄 Spúšťam migráciu emailových preferencií...\n');

  // Parse DATABASE_URL
  const databaseUrl = process.env.DATABASE_URL;
  if (!databaseUrl) {
    console.error('❌ DATABASE_URL nie je nastavená v .env súbore');
    return false;
  }

  console.log('📊 Parsovanie DATABASE_URL...');
  
  // Parse MySQL connection string
  // Format: mysql://username:password@host/database
  const urlMatch = databaseUrl.match(/mysql:\/\/([^:]+):([^@]+)@([^\/]+)\/(.+)/);
  if (!urlMatch) {
    console.error('❌ Neplatný formát DATABASE_URL');
    return false;
  }

  const [, username, password, host, database] = urlMatch;
  
  // Decode URL-encoded password
  const decodedPassword = decodeURIComponent(password);
  
  console.log(`   Host: ${host}`);
  console.log(`   Database: ${database}`);
  console.log(`   Username: ${username}`);
  console.log('   Password: [SKRYTÉ]\n');

  // Načítanie SQL migrácie
  const migrationPath = path.join(__dirname, '../prisma/migrations/add-email-preferences.sql');
  
  if (!fs.existsSync(migrationPath)) {
    console.error(`❌ Migračný súbor nebol nájdený: ${migrationPath}`);
    return false;
  }

  const migrationSQL = fs.readFileSync(migrationPath, 'utf8');
  console.log('📄 Migračný súbor načítaný');
  console.log(`   Veľkosť: ${migrationSQL.length} znakov\n`);

  // Pripojenie k databáze
  let connection;
  try {
    console.log('🔌 Pripájam sa k databáze...');
    
    connection = await mysql.createConnection({
      host: host,
      user: username,
      password: decodedPassword,
      database: database,
      ssl: false // Vypnutie SSL
    });

    console.log('✅ Pripojenie k databáze úspešné\n');

    // Rozdelenie SQL na jednotlivé príkazy
    const sqlCommands = migrationSQL
      .split(';')
      .map(cmd => cmd.trim())
      .filter(cmd => cmd.length > 0);

    console.log(`📝 Spúšťam ${sqlCommands.length} SQL príkazov...\n`);

    // Spustenie každého SQL príkazu
    for (let i = 0; i < sqlCommands.length; i++) {
      const command = sqlCommands[i];
      console.log(`   ${i + 1}/${sqlCommands.length}: ${command.substring(0, 50)}...`);
      
      try {
        await connection.execute(command);
        console.log('   ✅ Úspešne vykonané');
      } catch (error) {
        if (error.code === 'ER_DUP_FIELDNAME' || error.message.includes('Duplicate column name')) {
          console.log('   ⚠️  Stĺpec už existuje, preskakujem');
        } else if (error.code === 'ER_TABLE_EXISTS_ERROR' || error.message.includes('already exists')) {
          console.log('   ⚠️  Tabuľka už existuje, preskakujem');
        } else {
          console.error(`   ❌ Chyba: ${error.message}`);
          throw error;
        }
      }
    }

    console.log('\n✅ Migrácia úspešne dokončená!');
    
    // Overenie, že stĺpce boli pridané
    console.log('\n🔍 Overujem pridané stĺpce...');
    
    const [rows] = await connection.execute(`
      SELECT COLUMN_NAME 
      FROM INFORMATION_SCHEMA.COLUMNS 
      WHERE TABLE_SCHEMA = ? AND TABLE_NAME = 'User' 
      AND COLUMN_NAME IN ('emailNotificationsEnabled', 'emailReservationCreated', 'emailReservationApproved', 'emailUnsubscribeToken')
    `, [database]);

    console.log('   Nájdené stĺpce:');
    rows.forEach(row => {
      console.log(`   ✅ ${row.COLUMN_NAME}`);
    });

    // Overenie EmailUnsubscribe tabuľky
    const [tableRows] = await connection.execute(`
      SELECT TABLE_NAME 
      FROM INFORMATION_SCHEMA.TABLES 
      WHERE TABLE_SCHEMA = ? AND TABLE_NAME = 'EmailUnsubscribe'
    `, [database]);

    if (tableRows.length > 0) {
      console.log('   ✅ EmailUnsubscribe tabuľka existuje');
    } else {
      console.log('   ❌ EmailUnsubscribe tabuľka neexistuje');
    }

    return true;

  } catch (error) {
    console.error('\n❌ Chyba pri migrácii:', error.message);
    return false;
  } finally {
    if (connection) {
      await connection.end();
      console.log('\n🔌 Pripojenie k databáze ukončené');
    }
  }
}

// Spustenie migrácie ak je script volaný priamo
if (require.main === module) {
  runEmailMigration()
    .then(success => {
      if (success) {
        console.log('\n🎉 Migrácia dokončená! Teraz môžete spustiť:');
        console.log('   npx prisma generate');
        console.log('   node tests/email-notification.test.js');
      } else {
        console.log('\n💡 Ak migrácia zlyhala, môžete spustiť SQL príkazy manuálne:');
        console.log('   1. Pripojte sa k databáze pomocou vášho MySQL klienta');
        console.log('   2. Spustite obsah súboru: prisma/migrations/add-email-preferences.sql');
      }
      process.exit(success ? 0 : 1);
    })
    .catch(error => {
      console.error('❌ Neočakávaná chyba:', error);
      process.exit(1);
    });
}

module.exports = { runEmailMigration };
