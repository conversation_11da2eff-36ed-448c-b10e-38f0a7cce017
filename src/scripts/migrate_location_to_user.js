/**
 * <PERSON><PERSON><PERSON><PERSON><PERSON> skript pre presun lokality z hračiek k používateľom
 * 
 * Tento skript vykoná nasledujúce kroky:
 * 1. Pridá stĺpce latitude a longitude do tabuľky User
 * 2. Aktualizuje používateľov - nastaví lokalitu podľa lokality ich hračiek
 * 3. Odstráni stĺpec locationId z tabuľky Toy
 * 
 * Spustenie: node src/scripts/migrate_location_to_user.js
 */

const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function main() {
  console.log('Začínam migráciu lokality z hračiek k používateľom...');

  try {
    // 1. Pridanie stĺpcov latitude a longitude do tabuľky User
    console.log('Pridávam stĺpce latitude a longitude do tabuľky User...');
    
    // Kontrola, či stĺpce už existujú
    const checkColumns = await prisma.$queryRaw`
      SELECT COLUMN_NAME 
      FROM INFORMATION_SCHEMA.COLUMNS 
      WHERE TABLE_NAME = 'User' 
      AND COLUMN_NAME IN ('latitude', 'longitude')
    `;
    
    if (checkColumns.length < 2) {
      // Pridanie stĺpcov, ak neexistujú
      await prisma.$executeRaw`
        ALTER TABLE User 
        ADD COLUMN IF NOT EXISTS latitude FLOAT NULL,
        ADD COLUMN IF NOT EXISTS longitude FLOAT NULL
      `;
      console.log('Stĺpce latitude a longitude boli pridané do tabuľky User');
    } else {
      console.log('Stĺpce latitude a longitude už existujú v tabuľke User');
    }

    // 2. Aktualizácia používateľov - nastavenie lokality podľa lokality ich hračiek
    console.log('Aktualizujem používateľov - nastavujem lokalitu podľa lokality ich hračiek...');
    
    // Získanie všetkých používateľov, ktorí majú hračky
    const usersWithToys = await prisma.user.findMany({
      where: {
        toys: {
          some: {}
        }
      },
      include: {
        toys: {
          include: {
            location: true
          }
        }
      }
    });

    console.log(`Nájdených ${usersWithToys.length} používateľov s hračkami`);

    // Aktualizácia každého používateľa
    for (const user of usersWithToys) {
      // Ak používateľ nemá nastavenú lokalitu, použijeme lokalitu jeho prvej hračky
      if ((!user.city || !user.postalCode) && user.toys.length > 0 && user.toys[0].location) {
        const firstToyLocation = user.toys[0].location;
        
        await prisma.user.update({
          where: { id: user.id },
          data: {
            city: user.city || firstToyLocation.city,
            postalCode: user.postalCode || firstToyLocation.postalCode
          }
        });
        
        console.log(`Aktualizovaný používateľ ${user.id} (${user.name}) - nastavená lokalita z hračky`);
      }
    }

    console.log('Migrácia bola úspešne dokončená');
  } catch (error) {
    console.error('Chyba pri migrácii:', error);
  } finally {
    await prisma.$disconnect();
  }
}

main()
  .then(() => {
    console.log('Migrácia bola úspešne dokončená');
    process.exit(0);
  })
  .catch((error) => {
    console.error('Chyba pri migrácii:', error);
    process.exit(1);
  });
