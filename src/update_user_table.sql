-- Skript na aktualizáciu tabuľky User
-- Pridanie stĺpca firebaseUid, ak neexistuje
ALTER TABLE User ADD COLUMN IF NOT EXISTS firebaseUid VARCHAR(255) NULL;

-- Pridanie stĺpca address, ak neexistuje
ALTER TABLE User ADD COLUMN IF NOT EXISTS address VARCHAR(255) NULL;

-- Pridanie indexu na firebaseUid pre rýchlejšie vyhľadávanie
CREATE INDEX IF NOT EXISTS idx_user_firebase_uid ON User(firebaseUid);

-- Nastavenie unikátneho obmedzenia pre firebaseUid
ALTER TABLE User ADD CONSTRAINT IF NOT EXISTS unique_firebase_uid UNIQUE (firebaseUid);
