import { NextRequest, NextResponse } from 'next/server';
import { processUnsubscribeRequest } from '../../../../lib/emailPreferences';

/**
 * Handles email unsubscribe requests
 * GET /api/email/unsubscribe?token=<unsubscribe_token>
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const token = searchParams.get('token');

    if (!token) {
      return NextResponse.json(
        { error: 'Chýba token pre odhlásenie' },
        { status: 400 }
      );
    }

    // Get client IP and user agent for GDPR compliance logging
    const clientIP = request.headers.get('x-forwarded-for') || 
                     request.headers.get('x-real-ip') || 
                     'unknown';
    const userAgent = request.headers.get('user-agent') || 'unknown';

    const result = await processUnsubscribeRequest(token, {
      ipAddress: clientIP,
      userAgent: userAgent,
      reason: 'User clicked unsubscribe link'
    });

    if (result.success) {
      // Return a simple HTML page confirming the unsubscribe
      const htmlResponse = `
        <!DOCTYPE html>
        <html lang="sk">
        <head>
          <meta charset="UTF-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>Odhlásenie z emailových notifikácií - Swapka</title>
          <style>
            body {
              font-family: Arial, sans-serif;
              line-height: 1.6;
              color: #333;
              max-width: 600px;
              margin: 0 auto;
              padding: 20px;
              background-color: #f8f9fa;
            }
            .container {
              background-color: white;
              padding: 30px;
              border-radius: 8px;
              box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            }
            .success {
              color: #16a34a;
              background-color: #f0fdf4;
              padding: 16px;
              border-radius: 6px;
              border-left: 4px solid #22c55e;
              margin-bottom: 20px;
            }
            .logo {
              color: #2563eb;
              font-size: 24px;
              font-weight: bold;
              margin-bottom: 20px;
            }
            .button {
              display: inline-block;
              background-color: #2563eb;
              color: white;
              padding: 12px 24px;
              text-decoration: none;
              border-radius: 6px;
              font-weight: 600;
              margin-top: 20px;
            }
            .button:hover {
              background-color: #1d4ed8;
            }
          </style>
        </head>
        <body>
          <div class="container">
            <div class="logo">Swapka - Zdieľanie hračiek</div>
            
            <div class="success">
              <h2 style="margin: 0 0 10px 0;">✅ Úspešne odhlásené</h2>
              <p style="margin: 0;">${result.message}</p>
            </div>
            
            <div style="background-color: #fef3c7; padding: 16px; border-radius: 6px; margin: 16px 0; border-left: 4px solid #f59e0b;">
              <h3 style="margin: 0 0 8px 0; color: #92400e; font-size: 16px;">Dôležité upozornenie</h3>
              <p style="margin: 0; color: #92400e;">
                Emailové notifikácie o rezerváciách (potvrdenia a schválenia) zostávajú aktívne,
                pretože sú nevyhnutné pre správne fungovanie služby. Odhlásili ste sa len z marketingových emailov.
              </p>
            </div>

            <p>Ak si to rozmyslíte, môžete sa znovu prihlásiť k odberaniu marketingových emailov kontaktovaním našej podpory.</p>
            
            <a href="${process.env.NEXT_PUBLIC_BASE_URL || 'https://swapka.sk'}" class="button">
              Návrat na Swapka.sk
            </a>
            
            <hr style="margin: 30px 0; border: none; border-top: 1px solid #e5e7eb;">
            
            <p style="font-size: 14px; color: #6b7280;">
              Ak máte akékoľvek otázky, kontaktujte nás na 
              <a href="mailto:<EMAIL>" style="color: #2563eb;"><EMAIL></a>
            </p>
          </div>
        </body>
        </html>
      `;

      return new NextResponse(htmlResponse, {
        status: 200,
        headers: {
          'Content-Type': 'text/html; charset=utf-8',
        },
      });
    } else {
      // Return error page
      const errorHtml = `
        <!DOCTYPE html>
        <html lang="sk">
        <head>
          <meta charset="UTF-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>Chyba pri odhlásení - Swapka</title>
          <style>
            body {
              font-family: Arial, sans-serif;
              line-height: 1.6;
              color: #333;
              max-width: 600px;
              margin: 0 auto;
              padding: 20px;
              background-color: #f8f9fa;
            }
            .container {
              background-color: white;
              padding: 30px;
              border-radius: 8px;
              box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            }
            .error {
              color: #dc2626;
              background-color: #fef2f2;
              padding: 16px;
              border-radius: 6px;
              border-left: 4px solid #ef4444;
              margin-bottom: 20px;
            }
            .logo {
              color: #2563eb;
              font-size: 24px;
              font-weight: bold;
              margin-bottom: 20px;
            }
            .button {
              display: inline-block;
              background-color: #2563eb;
              color: white;
              padding: 12px 24px;
              text-decoration: none;
              border-radius: 6px;
              font-weight: 600;
              margin-top: 20px;
            }
          </style>
        </head>
        <body>
          <div class="container">
            <div class="logo">Swapka - Zdieľanie hračiek</div>
            
            <div class="error">
              <h2 style="margin: 0 0 10px 0;">❌ Chyba pri odhlásení</h2>
              <p style="margin: 0;">${result.message}</p>
            </div>
            
            <p>Ak problém pretrváva, kontaktujte nás prosí<NAME_EMAIL></p>
            
            <a href="${process.env.NEXT_PUBLIC_BASE_URL || 'https://swapka.sk'}" class="button">
              Návrat na Swapka.sk
            </a>
          </div>
        </body>
        </html>
      `;

      return new NextResponse(errorHtml, {
        status: 400,
        headers: {
          'Content-Type': 'text/html; charset=utf-8',
        },
      });
    }
  } catch (error) {
    console.error('Error processing unsubscribe request:', error);
    
    const errorHtml = `
      <!DOCTYPE html>
      <html lang="sk">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Chyba servera - Swapka</title>
        <style>
          body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8f9fa;
          }
          .container {
            background-color: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
          }
          .error {
            color: #dc2626;
            background-color: #fef2f2;
            padding: 16px;
            border-radius: 6px;
            border-left: 4px solid #ef4444;
            margin-bottom: 20px;
          }
          .logo {
            color: #2563eb;
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 20px;
          }
          .button {
            display: inline-block;
            background-color: #2563eb;
            color: white;
            padding: 12px 24px;
            text-decoration: none;
            border-radius: 6px;
            font-weight: 600;
            margin-top: 20px;
          }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="logo">Swapka - Zdieľanie hračiek</div>
          
          <div class="error">
            <h2 style="margin: 0 0 10px 0;">❌ Chyba servera</h2>
            <p style="margin: 0;">Nastala neočakávaná chyba pri spracovaní vašej požiadavky.</p>
          </div>
          
          <p>Prosím, skúste to neskôr alebo nás <NAME_EMAIL></p>
          
          <a href="${process.env.NEXT_PUBLIC_BASE_URL || 'https://swapka.sk'}" class="button">
            Návrat na Swapka.sk
          </a>
        </div>
      </body>
      </html>
    `;

    return new NextResponse(errorHtml, {
      status: 500,
      headers: {
        'Content-Type': 'text/html; charset=utf-8',
      },
    });
  }
}
