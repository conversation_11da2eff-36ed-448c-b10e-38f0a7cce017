/**
 * Cache invalidation API endpoint
 * Provides cache-busting utilities and version information
 */

import { NextRequest, NextResponse } from 'next/server';
import { withAdminAuth } from '../../../lib/auth';
import { getCacheHeaders, getMobileSafariHeaders } from '../../../lib/cacheHeaders';

/**
 * GET handler - Get current cache information and version
 */
async function handleGetCacheInfo(request: NextRequest, user: any) {
  try {
    const buildId = process.env.BUILD_ID || 'dev';
    const timestamp = Date.now();
    
    const cacheInfo = {
      buildId,
      timestamp,
      version: process.env.npm_package_version || '0.10',
      environment: process.env.NODE_ENV,
      cacheBustParam: `v=${timestamp}`,
      headers: {
        standard: getCacheHeaders('NO_CACHE'),
        mobileSafari: getMobileSafariHeaders(),
      },
    };

    return NextResponse.json(cacheInfo, {
      headers: getCacheHeaders('API'),
    });
  } catch (error) {
    console.error('Error getting cache info:', error);
    return NextResponse.json(
      { error: 'Failed to get cache information' },
      { status: 500, headers: getCacheHeaders('API') }
    );
  }
}

/**
 * POST handler - Force cache invalidation
 */
async function handleCacheInvalidation(request: NextRequest, user: any) {
  try {
    const body = await request.json();
    const { action, target } = body;

    const timestamp = Date.now();
    const result: any = {
      timestamp,
      action,
      target,
      success: true,
    };

    switch (action) {
      case 'invalidate-all':
        // Simulate cache invalidation by updating timestamp
        result.message = 'All caches invalidated successfully';
        result.newCacheBust = `v=${timestamp}`;
        break;

      case 'invalidate-js':
        // Invalidate JavaScript chunks specifically
        result.message = 'JavaScript chunks cache invalidated';
        result.newCacheBust = `js-v=${timestamp}`;
        break;

      case 'invalidate-assets':
        // Invalidate static assets
        result.message = 'Static assets cache invalidated';
        result.newCacheBust = `assets-v=${timestamp}`;
        break;

      case 'get-version':
        // Return current version information
        result.message = 'Version information retrieved';
        result.buildId = process.env.BUILD_ID || 'dev';
        result.version = process.env.npm_package_version || '0.10';
        break;

      default:
        return NextResponse.json(
          { error: 'Invalid action. Supported actions: invalidate-all, invalidate-js, invalidate-assets, get-version' },
          { status: 400, headers: getCacheHeaders('API') }
        );
    }

    return NextResponse.json(result, {
      headers: {
        ...getCacheHeaders('API'),
        'X-Cache-Invalidation': timestamp.toString(),
      },
    });
  } catch (error) {
    console.error('Error handling cache invalidation:', error);
    return NextResponse.json(
      { error: 'Failed to invalidate cache' },
      { status: 500, headers: getCacheHeaders('API') }
    );
  }
}

/**
 * DELETE handler - Clear all caches (legacy support)
 */
async function handleClearAllCaches(request: NextRequest, user: any) {
  try {
    const timestamp = Date.now();

    return NextResponse.json({
      message: 'All caches cleared successfully',
      timestamp,
      cacheBustParam: `v=${timestamp}`,
    }, {
      headers: {
        ...getCacheHeaders('API'),
        'X-Cache-Cleared': timestamp.toString(),
      },
    });
  } catch (error) {
    console.error('Error clearing caches:', error);
    return NextResponse.json(
      { error: 'Failed to clear caches' },
      { status: 500, headers: getCacheHeaders('API') }
    );
  }
}

// Export functions with admin authentication
export const GET = withAdminAuth(handleGetCacheInfo);
export const POST = withAdminAuth(handleCacheInvalidation);
export const DELETE = withAdminAuth(handleClearAllCaches);
