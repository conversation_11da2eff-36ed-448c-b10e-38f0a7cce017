import { NextRequest, NextResponse } from 'next/server';
import { withAdminAuth } from '../../../../lib/auth';
import { getRateLimitStats, clearRateLimitForIP, clearAllRateLimits } from '../../../../middleware/rateLimit';
import { rateLimitConfig } from '../../../../lib/rateLimiting';

/**
 * Získanie štatistík rate limiting
 * GET /api/admin/rate-limit
 */
async function handleGetRateLimitStats(request: NextRequest, user: any) {
  try {
    const stats = getRateLimitStats();
    
    return NextResponse.json({
      config: {
        enabled: rateLimitConfig.enabled,
        auth: rateLimitConfig.auth,
        general: rateLimitConfig.general,
        admin: rateLimitConfig.admin,
        upload: rateLimitConfig.upload,
        whitelistIPs: rateLimitConfig.whitelistIPs,
      },
      stats,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error('Chyba pri získavaní rate limit štatistík:', error);
    return NextResponse.json(
      { error: 'Nastala chyba pri získavaní štatistík' },
      { status: 500 }
    );
  }
}

/**
 * Vyčistenie rate limit záznamov
 * DELETE /api/admin/rate-limit
 * 
 * Query parameters:
 * - ip: Vyčistiť záznamy pre konkrétnu IP adresu
 * - all: Vyčistiť všetky záznamy (all=true)
 */
async function handleClearRateLimit(request: NextRequest, user: any) {
  try {
    const searchParams = request.nextUrl.searchParams;
    const ip = searchParams.get('ip');
    const all = searchParams.get('all') === 'true';
    
    if (all) {
      const clearedCount = clearAllRateLimits();
      
      console.log(`Admin ${user.email} cleared all rate limit records`, {
        clearedCount,
        timestamp: new Date().toISOString(),
      });
      
      return NextResponse.json({
        message: `Vyčistených ${clearedCount} rate limit záznamov`,
        clearedCount,
      });
    } else if (ip) {
      const cleared = clearRateLimitForIP(ip);
      
      if (cleared) {
        console.log(`Admin ${user.email} cleared rate limit for IP`, {
          ip: ip.substring(0, 8) + '***',
          timestamp: new Date().toISOString(),
        });
        
        return NextResponse.json({
          message: `Rate limit záznamy pre IP ${ip.substring(0, 8)}*** boli vyčistené`,
          ip: ip.substring(0, 8) + '***',
        });
      } else {
        return NextResponse.json({
          message: `Žiadne rate limit záznamy pre IP ${ip.substring(0, 8)}*** neboli nájdené`,
          ip: ip.substring(0, 8) + '***',
        });
      }
    } else {
      return NextResponse.json(
        { error: 'Musíte špecifikovať parameter "ip" alebo "all=true"' },
        { status: 400 }
      );
    }
  } catch (error) {
    console.error('Chyba pri čistení rate limit záznamov:', error);
    return NextResponse.json(
      { error: 'Nastala chyba pri čistení záznamov' },
      { status: 500 }
    );
  }
}

/**
 * Aktualizácia rate limit konfigurácie
 * PUT /api/admin/rate-limit
 * 
 * Body:
 * {
 *   "enabled": boolean,
 *   "whitelistIPs": string[] (optional)
 * }
 */
async function handleUpdateRateLimit(request: NextRequest, user: any) {
  try {
    const body = await request.json();
    const { enabled, whitelistIPs } = body;
    
    // Validácia vstupov
    if (typeof enabled !== 'boolean') {
      return NextResponse.json(
        { error: 'Parameter "enabled" musí byť boolean' },
        { status: 400 }
      );
    }
    
    if (whitelistIPs && !Array.isArray(whitelistIPs)) {
      return NextResponse.json(
        { error: 'Parameter "whitelistIPs" musí byť array' },
        { status: 400 }
      );
    }
    
    // Poznámka: V production prostredí by sme aktualizovali environment variables
    // alebo databázovú konfiguráciu. Pre túto implementáciu vrátime informáciu
    // o tom, že zmeny vyžadujú reštart aplikácie.
    
    console.log(`Admin ${user.email} requested rate limit config update`, {
      enabled,
      whitelistIPs: whitelistIPs?.map((ip: string) => ip.substring(0, 8) + '***'),
      timestamp: new Date().toISOString(),
    });
    
    return NextResponse.json({
      message: 'Konfigurácia rate limiting bola prijatá',
      note: 'Zmeny sa prejavia po reštarte aplikácie alebo aktualizácii environment variables',
      currentConfig: {
        enabled: rateLimitConfig.enabled,
        whitelistIPs: rateLimitConfig.whitelistIPs.map(ip => ip.substring(0, 8) + '***'), // Skryť IP adresy
      },
      requestedConfig: {
        enabled,
        whitelistIPs: (whitelistIPs || rateLimitConfig.whitelistIPs).map((ip: string) => ip.substring(0, 8) + '***'),
      },
    });
  } catch (error) {
    console.error('Chyba pri aktualizácii rate limit konfigurácie:', error);
    return NextResponse.json(
      { error: 'Nastala chyba pri aktualizácii konfigurácie' },
      { status: 500 }
    );
  }
}

// Export funkcií s overením autentifikácie a role admin
export const GET = withAdminAuth(handleGetRateLimitStats);
export const DELETE = withAdminAuth(handleClearRateLimit);
export const PUT = withAdminAuth(handleUpdateRateLimit);
