/**
 * Admin API endpoint for sitemap management
 * Provides statistics and cache management for sitemap
 */

import { NextRequest, NextResponse } from 'next/server';
import { withAdminAuth } from '../../../../lib/auth';
import { getSitemapStats, clearSitemapCache, generateSitemap } from '../../../../lib/sitemapGenerator';
import { sitemapCache } from '../../../../lib/sitemapCache';

/**
 * GET handler - Get sitemap statistics
 */
async function handleGetSitemapStats(request: NextRequest, user: any) {
  try {
    const stats = await getSitemapStats();
    const cacheStats = sitemapCache.getStats();

    return NextResponse.json({
      sitemap: stats,
      cache: cacheStats,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Error getting sitemap stats:', error);
    return NextResponse.json(
      { error: 'Failed to get sitemap statistics' },
      { status: 500 }
    );
  }
}

/**
 * POST handler - Handle cache management actions
 */
async function handleSitemapActions(request: NextRequest, user: any) {
  try {
    const body = await request.json();
    const { action } = body;

    switch (action) {
      case 'clear':
        clearSitemapCache();
        return NextResponse.json({
          message: 'Sitemap cache cleared successfully',
          timestamp: new Date().toISOString()
        });

      case 'warmup':
        // Generate sitemap to warm up cache
        await generateSitemap();
        return NextResponse.json({
          message: 'Sitemap cache warmed up successfully',
          timestamp: new Date().toISOString()
        });

      default:
        return NextResponse.json(
          { error: 'Invalid action. Supported actions: clear, warmup' },
          { status: 400 }
        );
    }
  } catch (error) {
    console.error('Error handling sitemap action:', error);
    return NextResponse.json(
      { error: 'Failed to execute sitemap action' },
      { status: 500 }
    );
  }
}

/**
 * DELETE handler - Clear sitemap cache (legacy support)
 */
async function handleClearSitemapCache(request: NextRequest, user: any) {
  try {
    clearSitemapCache();

    return NextResponse.json({
      message: 'Sitemap cache cleared successfully',
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Error clearing sitemap cache:', error);
    return NextResponse.json(
      { error: 'Failed to clear sitemap cache' },
      { status: 500 }
    );
  }
}

// Export functions with admin authentication
export const GET = withAdminAuth(handleGetSitemapStats);
export const POST = withAdminAuth(handleSitemapActions);
export const DELETE = withAdminAuth(handleClearSitemapCache);
