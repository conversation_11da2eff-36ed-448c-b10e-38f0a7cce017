import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '../../../../../../lib/db';
import { withAdminAuth } from '../../../../../../lib/auth';

// Funkcia pre nastavenie predvolenej hodnoty rezervačných dní
async function handleSetDefaultReservationDaySetting(
  request: NextRequest,
  user: any,
  params: { id: string }
) {
  try {
    const id = parseInt(params.id);

    if (isNaN(id)) {
      return NextResponse.json(
        { error: 'Neplatné ID nastavenia' },
        { status: 400 }
      );
    }

    // V skutočnej implementácii by sme tu aktualizovali záznamy v databáze
    // Keďže nemáme samostatnú tabuľku, vrátime simulovaný výsledok

    return NextResponse.json({ message: 'Predvolená hodnota bola úspešne nastavená' });
  } catch (error) {
    console.error('Chyba pri nastavovaní predvolenej hodnoty rezervačných dní:', error);
    return NextResponse.json(
      { error: 'Nastala chyba pri nastavovaní predvolenej hodnoty rezervačných dní' },
      { status: 500 }
    );
  }
}

// Export funkcie s overením autentifikácie a role admin
export async function POST(request: NextRequest) {
  // Získanie parametrov z URL
  const url = new URL(request.url);
  const pathParts = url.pathname.split('/');
  const id = pathParts[pathParts.length - 2]; // -2 because the last part is 'default'

  // Použitie withAdminAuth middleware
  return withAdminAuth(async (req, user) => {
    return handleSetDefaultReservationDaySetting(req, user, { id });
  })(request);
}
