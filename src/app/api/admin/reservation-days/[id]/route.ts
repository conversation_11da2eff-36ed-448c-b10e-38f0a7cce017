import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '../../../../../lib/db';
import { withAdminAuth } from '../../../../../lib/auth';

// Funkcia pre odstránenie nastavenia rezervačných dní
async function handleDeleteReservationDaySetting(
  request: NextRequest,
  user: any,
  params: { id: string }
) {
  try {
    const id = parseInt(params.id);

    if (isNaN(id)) {
      return NextResponse.json(
        { error: 'Neplatné ID nastavenia' },
        { status: 400 }
      );
    }

    // V skutočnej implementácii by sme tu odstránili z<PERSON>znam z databázy
    // Keďže nemáme samostatnú tabuľku, vrátime simulovaný výsledok

    // Kontrola, či nejde o predvolené nastavenie (ID 1)
    if (id === 1) {
      return NextResponse.json(
        { error: 'Predvolené nastavenie nie je možné o<PERSON>' },
        { status: 400 }
      );
    }

    return NextResponse.json({ message: 'Nastavenie bolo úspešne odstránené' });
  } catch (error) {
    console.error('Chyba pri odstraňovaní nastavenia rezervačných dní:', error);
    return NextResponse.json(
      { error: 'Nastala chyba pri odstraňovaní nastavenia rezervačných dní' },
      { status: 500 }
    );
  }
}

// Export funkcie s overením autentifikácie a role admin
export async function DELETE(request: NextRequest) {
  // Získanie parametrov z URL
  const url = new URL(request.url);
  const pathParts = url.pathname.split('/');
  const id = pathParts[pathParts.length - 1];

  // Použitie withAdminAuth middleware
  return withAdminAuth(async (req, user) => {
    return handleDeleteReservationDaySetting(req, user, { id });
  })(request);
}
