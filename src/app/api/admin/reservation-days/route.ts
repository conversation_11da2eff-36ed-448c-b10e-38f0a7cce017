import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '../../../../lib/db';
import { withAdminAuth } from '../../../../lib/auth';

// Funkcia pre získanie všetkých nastavení rezervačných dní
async function handleGetAllReservationDays(request: NextRequest, user: any) {
  try {
    // Získanie všetkých nastavení z databázy
    // Keďže nemáme samostatnú tabuľku pre rezervačné dni, vrátime predvolené hodnoty
    // V budúcnosti by sa tu mohla implementovať skutočná databázová tabuľka
    
    // Predvolené hodnoty
    const defaultSettings = [
      { id: 1, days: 7, description: 'Týždeň', isDefault: true },
      { id: 2, days: 14, description: 'Dva týždne', isDefault: false },
      { id: 3, days: 30, description: 'Mesia<PERSON>', isDefault: false }
    ];

    return NextResponse.json(defaultSettings);
  } catch (error) {
    console.error('Chyba pri získavaní nastavení rezervačných dní:', error);
    return NextResponse.json(
      { error: 'Nastala chyba pri získavaní nastavení rezervačných dní' },
      { status: 500 }
    );
  }
}

// Funkcia pre pridanie nového nastavenia rezervačných dní
async function handleAddReservationDaySetting(request: NextRequest, user: any) {
  try {
    const { days, description, isDefault } = await request.json();

    // Kontrola povinných údajov
    if (!days || !description) {
      return NextResponse.json(
        { error: 'Chýbajú povinné údaje' },
        { status: 400 }
      );
    }

    // Kontrola, či je počet dní kladné číslo
    if (days <= 0) {
      return NextResponse.json(
        { error: 'Počet dní musí byť kladné číslo' },
        { status: 400 }
      );
    }

    // V skutočnej implementácii by sme tu pridali záznam do databázy
    // Keďže nemáme samostatnú tabuľku, vrátime simulovaný výsledok
    
    // Simulácia pridania nového nastavenia
    const newSetting = {
      id: Math.floor(Math.random() * 1000) + 10, // Náhodné ID
      days,
      description,
      isDefault: Boolean(isDefault)
    };

    return NextResponse.json(newSetting);
  } catch (error) {
    console.error('Chyba pri pridávaní nastavenia rezervačných dní:', error);
    return NextResponse.json(
      { error: 'Nastala chyba pri pridávaní nastavenia rezervačných dní' },
      { status: 500 }
    );
  }
}

// Export funkcií s overením autentifikácie a role admin
export const GET = withAdminAuth(handleGetAllReservationDays);
export const POST = withAdminAuth(handleAddReservationDaySetting);
