import { NextRequest, NextResponse } from 'next/server';
import { withAdminAuth } from '../../../../lib/auth';
import { 
  getEmailQueueStatus, 
  getEmailStatistics, 
  getRecentEmailLogs,
  clearEmailQueue 
} from '../../../../lib/emailNotificationService';

/**
 * GET handler - Get email monitoring data
 */
async function handleGetEmailMonitoring(request: NextRequest, user: any) {
  try {
    const url = new URL(request.url);
    const type = url.searchParams.get('type') || 'all';
    const limit = parseInt(url.searchParams.get('limit') || '50');

    switch (type) {
      case 'queue':
        // Get current queue status
        const queueStatus = getEmailQueueStatus();
        return NextResponse.json({
          success: true,
          data: queueStatus
        });

      case 'statistics':
        // Get email statistics
        const statistics = await getEmailStatistics();
        return NextResponse.json({
          success: true,
          data: statistics
        });

      case 'logs':
        // Get recent email logs
        const logs = await getRecentEmailLogs(limit);
        return NextResponse.json({
          success: true,
          data: logs
        });

      case 'all':
      default:
        // Get all monitoring data
        const [queueData, statsData, logsData] = await Promise.all([
          Promise.resolve(getEmailQueueStatus()),
          getEmailStatistics(),
          getRecentEmailLogs(limit)
        ]);

        return NextResponse.json({
          success: true,
          data: {
            queue: queueData,
            statistics: statsData,
            recentLogs: logsData
          }
        });
    }
  } catch (error) {
    console.error('Error getting email monitoring data:', error);
    return NextResponse.json(
      { 
        success: false,
        error: 'Nastala chyba pri získavaní údajov o emailovom monitoringu' 
      },
      { status: 500 }
    );
  }
}

/**
 * POST handler - Perform email monitoring actions
 */
async function handleEmailMonitoringActions(request: NextRequest, user: any) {
  try {
    const body = await request.json();
    const { action } = body;

    switch (action) {
      case 'clear-queue':
        // Clear email queue (for testing/emergency purposes)
        clearEmailQueue();
        return NextResponse.json({
          success: true,
          message: 'Email fronta bola vymazaná'
        });

      case 'refresh-stats':
        // Refresh statistics (just return fresh data)
        const freshStats = await getEmailStatistics();
        return NextResponse.json({
          success: true,
          data: freshStats
        });

      default:
        return NextResponse.json(
          { 
            success: false,
            error: 'Neznáma akcia' 
          },
          { status: 400 }
        );
    }
  } catch (error) {
    console.error('Error performing email monitoring action:', error);
    return NextResponse.json(
      { 
        success: false,
        error: 'Nastala chyba pri vykonávaní akcie' 
      },
      { status: 500 }
    );
  }
}

// Export functions with admin authentication
export const GET = withAdminAuth(handleGetEmailMonitoring);
export const POST = withAdminAuth(handleEmailMonitoringActions);
