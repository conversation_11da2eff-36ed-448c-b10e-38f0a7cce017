import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '../../../../../lib/db';
import { withAdminAuth } from '../../../../../lib/auth';
import { toNumericId, hashId } from '../../../../../lib/hashUtils';
import { deleteToyImages, deleteCloudinaryImage } from '../../../../../lib/cloudinary';

// Funkcia pre odstránenie hračky administrátorom
async function handleDeleteToy(request: NextRequest, user: any) {
  try {
    const data = await request.json();

    // Kontrola, či je poskytnuté ID hračky
    if (!data.id) {
      return NextResponse.json(
        { error: 'Chýba ID hračky' },
        { status: 400 }
      );
    }

    // Konverzia hashu na číselné ID
    const toyId = toNumericId(data.id);

    if (toyId === null) {
      return NextResponse.json(
        { error: 'Neplatné ID hračky' },
        { status: 400 }
      );
    }

    // Kontrola, či hračka existuje
    const existingToy = await prisma.toy.findUnique({
      where: { id: toyId },
    });

    if (!existingToy) {
      return NextResponse.json(
        { error: 'Hračka nebola nájdená' },
        { status: 404 }
      );
    }

    try {
      // Najprv získame všetky obrázky hračky, aby sme ich mohli odstrániť z Cloudinary
      console.log(`Získavam obrázky hračky ${toyId} pre odstránenie z Cloudinary`);
      const toyImages = await prisma.toyImage.findMany({
        where: { toyId },
        select: {
          id: true,
          url: true,
          hashedFilename: true,
          originalFilename: true
        }
      });

      console.log(`Nájdených ${toyImages.length} obrázkov hračky ${toyId} na odstránenie`);

      // Uložíme informácie o obrázkoch pre neskoršie odstránenie z Cloudinary
      const imageData = toyImages.map(img => ({
        url: img.url,
        hashedFilename: img.hashedFilename,
        originalFilename: img.originalFilename
      }));

      // Odstránime všetky súvisiace záznamy v databáze v transakčnom bloku
      console.log(`Začínam transakciu pre odstránenie hračky ${toyId} a súvisiacich záznamov`);

      await prisma.$transaction(async (tx) => {
        // 1. Odstránime všetky rezervácie hračky
        console.log(`Odstraňujem rezervácie hračky ${toyId}`);
        const deletedReservations = await tx.reservation.deleteMany({
          where: { toyId },
        });
        console.log(`Odstránených ${deletedReservations.count} rezervácií hračky ${toyId}`);

        // 2. Odstránime všetky záznamy o skrytých hračkách
        console.log(`Odstraňujem záznamy o skrytých hračkách pre hračku ${toyId}`);
        const deletedHiddenToys = await tx.hiddenToy.deleteMany({
          where: { toyId },
        });
        console.log(`Odstránených ${deletedHiddenToys.count} záznamov o skrytých hračkách pre hračku ${toyId}`);

        // 3. Odstránime záznamy o obrázkoch z databázy
        console.log(`Odstraňujem záznamy o obrázkoch hračky ${toyId}`);
        const deletedImages = await tx.toyImage.deleteMany({
          where: { toyId },
        });
        console.log(`Odstránených ${deletedImages.count} záznamov o obrázkoch hračky ${toyId}`);

        // 4. Nakoniec odstránime hračku
        console.log(`Odstraňujem hračku ${toyId}`);
        await tx.toy.delete({
          where: { id: toyId },
        });
        console.log(`Hračka ${toyId} bola úspešne odstránená`);
      });

      console.log(`Transakcia pre odstránenie hračky ${toyId} bola úspešne dokončená`);

      // Po úspešnom odstránení záznamov z databázy odstránime obrázky z Cloudinary
      if (imageData.length > 0) {
        console.log(`Odstraňujem ${imageData.length} obrázkov hračky ${toyId} z Cloudinary`);

        // Odstránenie každého obrázku z Cloudinary
        for (const imageInfo of imageData) {
          try {
            console.log(`Odstraňujem obrázok z Cloudinary:`, {
              url: imageInfo.url,
              hashedFilename: imageInfo.hashedFilename,
              originalFilename: imageInfo.originalFilename
            });

            // Použijeme hashed filename ak je k dispozícii, inak fallback na URL
            const result = await deleteCloudinaryImage(imageInfo.url, imageInfo.hashedFilename || undefined);
            console.log(`Výsledok odstránenia obrázku: ${result ? 'úspešné' : 'neúspešné'}`);
          } catch (imageError) {
            console.error(`Chyba pri odstraňovaní obrázku z Cloudinary:`, imageError);
          }
        }
      } else {
        console.log(`Hračka ${toyId} nemá žiadne obrázky na odstránenie z Cloudinary`);
      }
    } catch (deleteError) {
      console.error(`Chyba pri odstraňovaní hračky ${toyId} alebo súvisiacich záznamov:`, deleteError);
      throw deleteError; // Preposielame chybu na spracovanie v hlavnom try-catch bloku
    }

    return NextResponse.json({
      message: 'Hračka bola úspešne odstránená',
      id: hashId(toyId)
    });
  } catch (error: any) {
    console.error('Chyba pri odstraňovaní hračky:', error);

    // Podrobnejšia chybová správa pre lepšiu diagnostiku
    let errorMessage = 'Nastala chyba pri odstraňovaní hračky';

    // Ak je to Prisma chyba, získame podrobnejšie informácie
    if (error && typeof error === 'object' && 'code' in error) {
      console.error('Prisma error code:', error.code);
      console.error('Prisma error message:', error.message);

      // Špecifické spracovanie pre chybu cudzieho kľúča
      if (error.code === 'P2003') {
        errorMessage = `Hračku nie je možné odstrániť, pretože existujú súvisiace záznamy. Detaily: ${error.meta?.field_name || 'neznáme pole'}`;
      }
    }

    return NextResponse.json(
      { error: errorMessage, details: error?.message || String(error) },
      { status: 500 }
    );
  }
}

// Export funkcie s overením autentifikácie a role admin
export const POST = withAdminAuth(handleDeleteToy);
