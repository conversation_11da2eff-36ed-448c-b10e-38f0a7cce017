import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '../../../../../lib/db';
import { ToyType } from '../../../../../lib/constants';
import { withAdminAuth } from '../../../../../lib/auth';
import { toNumericId, hashId, isHashedId } from '../../../../../lib/hashUtils';
import { deleteCloudinaryImage } from '../../../../../lib/cloudinary';
import { isValidToyStatus } from '../../../../../lib/toyStatusUtils';

// Funkcia pre aktualizáciu existujúcej hračky administrátorom
async function handleUpdateToy(request: NextRequest, user: any) {
  try {
    const data = await request.json();

    // Kontrola, či je poskytnuté ID hračky
    if (!data.id) {
      return NextResponse.json(
        { error: '<PERSON>ý<PERSON> ID hračky' },
        { status: 400 }
      );
    }

    // Kontrola, či id je hashované
    if (!isHashedId(data.id)) {
      return NextResponse.json(
        { error: 'Neplatné ID hračky. Očakáva sa hashované ID.' },
        { status: 400 }
      );
    }

    // Konverzia hashovaného ID na číselné ID
    const toyId = toNumericId(data.id);

    if (toyId === null) {
      return NextResponse.json(
        { error: 'Neplatné ID hračky' },
        { status: 400 }
      );
    }

    // Kontrola, či hračka existuje
    const existingToy = await prisma.toy.findUnique({
      where: { id: toyId },
      include: {
        user: true
      }
    });

    if (!existingToy) {
      return NextResponse.json(
        { error: 'Hračka nebola nájdená' },
        { status: 404 }
      );
    }

    // Kontrola povinných údajov
    if (!data.name || !data.description || !data.type || !data.price || !data.deposit || !data.priceType) {
      return NextResponse.json(
        { error: 'Chýbajú povinné údaje' },
        { status: 400 }
      );
    }

    // Kontrola typu ceny
    if (!['PER_DAY', 'PER_RENTAL'].includes(data.priceType)) {
      return NextResponse.json(
        { error: 'Neplatný typ ceny' },
        { status: 400 }
      );
    }

    // Kontrola typu hračky - fetch valid types from database
    const validTypes = await prisma.toyType.findMany();
    if (!validTypes.map(t => t.name).includes(data.type)) {
      return NextResponse.json(
        { error: 'Neplatný typ hračky' },
        { status: 400 }
      );
    }

    // Kontrola statusu hračky (ak je poskytnutý) - overenie, či status existuje v databáze
    if (data.status) {
      const isValid = await isValidToyStatus(data.status);
      if (!isValid) {
        return NextResponse.json(
          { error: 'Neplatný status hračky' },
          { status: 400 }
        );
      }
    }

    // Aktualizácia hračky
    const updatedToy = await prisma.toy.update({
      where: { id: toyId },
      data: {
        name: data.name,
        description: data.description,
        type: data.type,
        price: parseFloat(data.price),
        deposit: parseFloat(data.deposit),
        priceType: data.priceType,
        status: data.status || existingToy.status,
        maxReservationDays: data.maxReservationDays || existingToy.maxReservationDays,
        // locationId už nie je potrebné, používame lokalitu používateľa
      },
      include: {
        user: true
      },
    });

    // Aktualizácia obrázkov (ak sú poskytnuté)
    if (data.images && Array.isArray(data.images)) {
      console.log(`Admin: Aktualizujem obrázky hračky ${toyId}`);

      // Filtrujeme placeholder obrázky z nových obrázkov
      const newImages = data.images.filter((img: string) => !img.includes('/toys/placeholder.jpg'));
      console.log(`Admin: Nové obrázky (bez placeholder): ${newImages.length}`);

      // Získame existujúce obrázky z databázy
      const existingImages = await prisma.toyImage.findMany({
        where: { toyId },
        select: {
          id: true,
          url: true,
          hashedFilename: true,
          originalFilename: true
        }
      });

      console.log(`Admin: Nájdených ${existingImages.length} existujúcich obrázkov hračky ${toyId}`);

      // Identifikujeme obrázky na odstránenie (existujúce, ktoré nie sú v nových)
      const imagesToDelete = existingImages.filter(existing =>
        !newImages.includes(existing.url)
      );

      console.log(`Admin: Obrázky na odstránenie: ${imagesToDelete.length}`);

      // Odstránime len obrázky, ktoré sa skutočne odstránili
      if (imagesToDelete.length > 0) {
        console.log(`Admin: Odstraňujem ${imagesToDelete.length} obrázkov z databázy pre hračku ${toyId}`);

        // Odstránime z databázy
        await prisma.toyImage.deleteMany({
          where: {
            toyId,
            url: {
              in: imagesToDelete.map(img => img.url)
            }
          },
        });

        // Odstránime z Cloudinary
        console.log(`Admin: Odstraňujem ${imagesToDelete.length} obrázkov z Cloudinary pre hračku ${toyId}`);

        for (const image of imagesToDelete) {
          try {
            console.log(`Admin: Odstraňujem obrázok z Cloudinary:`, {
              url: image.url,
              hashedFilename: image.hashedFilename,
              originalFilename: image.originalFilename
            });

            const result = await deleteCloudinaryImage(image.url, image.hashedFilename || undefined);
            console.log(`Admin: Výsledok odstránenia obrázku: ${result ? 'úspešné' : 'neúspešné'}`);
          } catch (imageError) {
            console.error(`Admin: Chyba pri odstraňovaní obrázku z Cloudinary:`, imageError);
          }
        }
      } else {
        console.log(`Admin: Žiadne obrázky na odstránenie pre hračku ${toyId}`);
      }

      // Identifikujeme nové obrázky na pridanie (nové, ktoré nie sú v existujúcich)
      const existingUrls = existingImages.map(img => img.url);
      const imagesToAdd = newImages.filter((newUrl: string) =>
        !existingUrls.includes(newUrl)
      );

      console.log(`Admin: Nové obrázky na pridanie: ${imagesToAdd.length}`);

      // Pridáme len skutočne nové obrázky
      if (imagesToAdd.length > 0) {
        console.log(`Admin: Pridávam ${imagesToAdd.length} nových obrázkov pre hračku ${toyId}`);
        for (const imageUrl of imagesToAdd) {
          await prisma.toyImage.create({
            data: {
              url: imageUrl,
              toyId,
            },
          });
        }
        console.log(`Admin: Úspešne pridaných ${imagesToAdd.length} nových obrázkov pre hračku ${toyId}`);
      } else {
        console.log(`Admin: Žiadne nové obrázky na pridanie pre hračku ${toyId}`);
      }
    }

    // Transformácia dát - pridanie hashovaných ID
    const transformedToy = {
      ...updatedToy,
      id: updatedToy.id,
      hashedId: hashId(updatedToy.id),
      userId: updatedToy.userId,
      hashedUserId: hashId(updatedToy.userId),
      user: updatedToy.user ? {
        ...updatedToy.user,
        id: updatedToy.user.id,
        hashedId: hashId(updatedToy.user.id)
      } : null
    };

    return NextResponse.json(transformedToy);
  } catch (error) {
    console.error('Chyba pri aktualizácii hračky:', error);
    return NextResponse.json(
      { error: 'Nastala chyba pri aktualizácii hračky' },
      { status: 500 }
    );
  }
}

// Export funkcie s overením autentifikácie a role admin
export const POST = withAdminAuth(handleUpdateToy);
