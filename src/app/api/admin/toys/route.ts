import { NextRequest, NextResponse } from 'next/server';
import { getAllToysForAdmin } from '../../../../lib/db';
import { prisma } from '../../../../lib/db';
import { withAdminAuth } from '../../../../lib/auth';
import { hashId } from '../../../../lib/hashUtils';

// Funkcia pre získanie všetkých hračiek pre administrátora
async function handleGetAllToys(request: NextRequest, user: any) {
  try {
    // Získanie všetkých hračiek bez filtrovania
    const toys = await getAllToysForAdmin();

    // Get toy type labels from database
    const toyTypes = await prisma.toyType.findMany({
      select: {
        name: true,
        label: true
      }
    });

    // Get toy status labels from database
    const toyStatuses = await prisma.toyStatus.findMany({
      select: {
        name: true,
        label: true
      }
    });

    // Create a map of toy type names to labels
    const toyTypeLabels: { [key: string]: string } = {};
    toyTypes.forEach(type => {
      toyTypeLabels[type.name] = type.label || type.name;
    });

    // Create a map of toy status names to labels
    const toyStatusLabels: { [key: string]: string } = {};
    toyStatuses.forEach(status => {
      toyStatusLabels[status.name] = status.label || status.name;
    });

    // Transformácia dát do formátu, ktorý očakáva frontend
    const transformedToys = await Promise.all(toys.map(async (toy) => {
      // Základné informácie o hračke
      const transformedToy = {
        id: hashId(toy.id), // Používame hashované ID ako primárne ID
        name: toy.name,
        description: toy.description,
        image: toy.images && toy.images.length > 0 ? toy.images[0].url : '/toys/placeholder.jpg',
        type: toy.type,
        typeLabel: toyTypeLabels[toy.type] || toy.type, // Add the type label
        location: toy.user?.city || 'Neuvedené',
        price: toy.price,
        deposit: toy.deposit,
        status: toy.status,
        statusLabel: toyStatusLabels[toy.status] || toy.status, // Add the status label
        owner: undefined as any // Pridáme owner property, ktorá bude nastavená neskôr
      };

      // Získanie informácií o vlastníkovi
      try {
        // Získanie informácií o používateľovi z databázy
        const user = await prisma.user.findUnique({
          where: { id: toy.userId },
          select: {
            id: true,
            name: true,
            email: true,
            status: true
          }
        });

        if (user) {
          transformedToy.owner = {
            hashedId: hashId(user.id),
            name: user.name,
            email: user.email,
            status: user.status
          };
        }
      } catch (error) {
        console.error(`Chyba pri získavaní informácií o vlastníkovi hračky ${toy.id}:`, error);
      }

      return transformedToy;
    }));

    return NextResponse.json(transformedToys);
  } catch (error) {
    console.error('Chyba pri získavaní hračiek:', error);
    return NextResponse.json(
      { error: 'Nastala chyba pri získavaní hračiek' },
      { status: 500 }
    );
  }
}

// Export funkcie s overením autentifikácie a role admin
export const GET = withAdminAuth(handleGetAllToys);
