import { NextResponse } from 'next/server';
import { getAllToyTypes } from '../../../lib/db';
import { prisma } from '../../../lib/db';

export async function GET() {
  try {
    // Získanie unikátnych miest z používateľov
    const users = await prisma.user.findMany({
      select: {
        city: true
      },
      where: {
        city: {
          not: null
        }
      },
      distinct: ['city']
    });

    const toyTypesRaw = await getAllToyTypes() as { name: string, label: string }[];

    // Transformácia dát do formátu, ktor<PERSON> očakáva frontend
    const toyTypes = toyTypesRaw.map(type => ({
      value: type.name,
      label: type.label || type.name
    }));

    const locationNames = users
      .map(user => user.city)
      .filter(city => city !== null && city !== '') as string[];

    // Zoradenie lokalít abecedne
    locationNames.sort((a, b) => a.localeCompare(b, 'sk'));

    // <PERSON><PERSON><PERSON> "Všetky" možností
    const allToyTypes = [{ value: 'Všetky typy', label: 'Všetky typy' }, ...toyTypes];
    const allLocations = ['Všetky lokality', ...locationNames];

    return NextResponse.json({
      toyTypes: allToyTypes,
      locations: allLocations
    });
  } catch (error) {
    console.error('Chyba pri získavaní filtrov:', error);
    return NextResponse.json(
      { error: 'Nastala chyba pri získavaní filtrov' },
      { status: 500 }
    );
  }
}
