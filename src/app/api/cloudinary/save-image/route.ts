import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '../../../../lib/db';
import { withAuth } from '../../../../lib/auth';
import { toNumericId } from '../../../../lib/hashUtils';

/**
 * Handler pre okamžité uloženie fotografie po nahratí do Cloudinary
 * Táto funkcia zabezpečí, že každá fotografia bude mať väzbu na konkrétnu hračku
 */
async function handleSaveImage(request: NextRequest, user: any) {
  try {
    const data = await request.json();

    // Kontrola povinných údajov
    if (!data.imageUrl || !data.toyId) {
      return NextResponse.json(
        { error: 'Chýba URL obrázku alebo ID hračky' },
        { status: 400 }
      );
    }

    // Konverzia hashu na číselné ID, ak je to potrebné
    const toyId = typeof data.toyId === 'string' ? toNumericId(data.toyId) : data.toyId;

    if (toyId === null) {
      return NextResponse.json(
        { error: 'Neplatné ID hračky' },
        { status: 400 }
      );
    }

    // Kontrola, či hračka existuje
    const existingToy = await prisma.toy.findUnique({
      where: { id: toyId },
    });

    if (!existingToy) {
      return NextResponse.json(
        { error: 'Hračka nebola nájdená' },
        { status: 404 }
      );
    }

    // Kontrola, či používateľ je vlastníkom hračky alebo má rolu ADMIN
    if (existingToy.userId !== user.id && user.role !== 'ADMIN') {
      return NextResponse.json(
        { error: 'Nemáte oprávnenie pridať obrázok k tejto hračke' },
        { status: 403 }
      );
    }

    // Uloženie obrázku do databázy
    const toyImage = await prisma.toyImage.create({
      data: {
        url: data.imageUrl,
        toyId,
      },
    });

    return NextResponse.json({
      message: 'Obrázok bol úspešne uložený',
      toyImage,
    });
  } catch (error) {
    console.error('Chyba pri ukladaní obrázku:', error);
    return NextResponse.json(
      { error: 'Nastala chyba pri ukladaní obrázku' },
      { status: 500 }
    );
  }
}

// Export funkcie s overením autentifikácie
export const POST = withAuth(handleSaveImage);
