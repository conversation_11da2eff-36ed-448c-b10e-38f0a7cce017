import { NextResponse } from 'next/server';
import { getAllToyStatuses } from '../../../../lib/toyStatusUtils';

// Public endpoint for fetching toy statuses (no authentication required)
// This endpoint is used by forms to populate status dropdowns
export async function GET() {
  try {
    const statuses = await getAllToyStatuses();
    return NextResponse.json(statuses);
  } catch (error) {
    console.error('Chyba pri získavaní statusov hračiek:', error);
    return NextResponse.json(
      { error: 'Nastala chyba pri získavaní statusov hračiek' },
      { status: 500 }
    );
  }
}
