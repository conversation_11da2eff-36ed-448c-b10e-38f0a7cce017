import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '../../../lib/db';
import { withAdminAuth } from '../../../lib/auth';
import { sanitizeObject, sanitizeText } from '../../../lib/inputSanitization';

// Funkcia pre získanie všetkých statusov hračiek s počtom hračiek
async function handleGetAllToyStatuses(request: NextRequest, user: any) {
  try {
    // Získanie všetkých statusov hračiek vrátane labelov
    const toyStatuses = await prisma.$queryRaw`SELECT name, label FROM ToyStatus`;

    // Získanie počtu hračiek pre každý status
    const statusCounts = await prisma.$queryRaw`
      SELECT status, COUNT(*) as count
      FROM Toy
      GROUP BY status
    `;

    // Vytvorenie mapy počtu hračiek podľa statusu
    const countMap: { [key: string]: number } = {};
    for (const row of statusCounts as any[]) {
      countMap[row.status] = parseInt(row.count.toString());
    }

    // Pridanie počtu hračiek k statusom
    const result = (toyStatuses as any[]).map(status => ({
      name: status.name,
      label: status.label || status.name, // Použijeme label, ak existuje, inak name
      count: countMap[status.name] || 0
    }));

    return NextResponse.json(result);
  } catch (error) {
    console.error('Chyba pri získavaní statusov hračiek:', error);
    return NextResponse.json(
      { error: 'Nastala chyba pri získavaní statusov hračiek' },
      { status: 500 }
    );
  }
}

// Funkcia pre pridanie nového statusu hračky
async function handleAddToyStatus(request: NextRequest, user: any) {
  try {
    const rawData = await request.json();

    // Sanitizácia vstupných údajov
    const sanitizationResult = sanitizeObject(rawData, {
      name: (value) => sanitizeText(value, { maxLength: 50, allowSpecialChars: false, allowSlovakDiacritics: true }),
      label: (value) => value ? sanitizeText(value, { maxLength: 100, allowSpecialChars: true }) : { isValid: true, sanitizedValue: '', errors: [] }
    });

    if (!sanitizationResult.isValid) {
      return NextResponse.json(
        {
          error: 'Neplatné vstupné údaje',
          details: sanitizationResult.errors
        },
        { status: 400 }
      );
    }

    const { name, label } = sanitizationResult.sanitizedData;

    // Kontrola, či status už existuje
    const existingStatus = await prisma.$queryRaw`
      SELECT name FROM ToyStatus WHERE name = ${name}
    `;

    if ((existingStatus as any[]).length > 0) {
      return NextResponse.json(
        { error: 'Status hračky s týmto názvom už existuje' },
        { status: 400 }
      );
    }

    // Pridanie nového statusu s labelom - používame template literal syntax
    await prisma.$executeRaw`INSERT INTO ToyStatus (name, label) VALUES (${name}, ${label || name})`;

    return NextResponse.json({ name, label: label || name, count: 0 });
  } catch (error) {
    console.error('Chyba pri pridávaní statusu hračky:', error);
    return NextResponse.json(
      { error: 'Nastala chyba pri pridávaní statusu hračky' },
      { status: 500 }
    );
  }
}

// Export funkcií s overením autentifikácie a role admin
export const GET = withAdminAuth(handleGetAllToyStatuses);
export const POST = withAdminAuth(handleAddToyStatus);
