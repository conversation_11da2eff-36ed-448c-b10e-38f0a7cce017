import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '../../../../lib/db';
import { withAdminAuth } from '../../../../lib/auth';

// Funkcia pre odstránenie statusu hračky
async function handleDeleteToyStatus(
  request: NextRequest,
  user: any,
  params: { name: string }
) {
  try {
    const name = decodeURIComponent(params.name);

    // Kontrola, či status existuje
    const existingStatus = await prisma.$queryRaw`
      SELECT name FROM ToyStatus WHERE name = ${name}
    `;

    if ((existingStatus as any[]).length === 0) {
      return NextResponse.json(
        { error: 'Status hračky nebol nájdený' },
        { status: 404 }
      );
    }

    // Kontrola, či status nie je používaný
    const toyCount = await prisma.toy.count({
      where: { status: name as any }
    });

    if (toyCount > 0) {
      return NextResponse.json(
        { error: 'Nie je možné o<PERSON> status hračky, ktorý je používan<PERSON>' },
        { status: 400 }
      );
    }

    // Odstránenie statusu
    await prisma.$executeRaw`DELETE FROM ToyStatus WHERE name = ${name}`;

    return NextResponse.json({
      message: 'Status hračky bol úspešne odstránený'
    });
  } catch (error) {
    console.error('Chyba pri odstraňovaní statusu hračky:', error);
    return NextResponse.json(
      { error: 'Nastala chyba pri odstraňovaní statusu hračky' },
      { status: 500 }
    );
  }
}

// Funkcia pre aktualizáciu labelu statusu hračky
async function handleUpdateToyStatus(
  request: NextRequest,
  user: any,
  params: { name: string }
) {
  try {
    const name = decodeURIComponent(params.name);
    const { label } = await request.json();

    // Kontrola, či status existuje
    const existingStatus = await prisma.$queryRaw`
      SELECT name FROM ToyStatus WHERE name = ${name}
    `;

    if ((existingStatus as any[]).length === 0) {
      return NextResponse.json(
        { error: 'Status hračky nebol nájdený' },
        { status: 404 }
      );
    }

    // Aktualizácia labelu
    await prisma.$executeRaw`UPDATE ToyStatus SET label = ${label} WHERE name = ${name}`;

    return NextResponse.json({
      name,
      label,
      message: 'Label statusu hračky bol úspešne aktualizovaný'
    });
  } catch (error) {
    console.error('Chyba pri aktualizácii statusu hračky:', error);
    return NextResponse.json(
      { error: 'Nastala chyba pri aktualizácii statusu hračky' },
      { status: 500 }
    );
  }
}

// Export funkcií s overením autentifikácie a role admin
export async function DELETE(request: NextRequest, context: any) {
  const params = await context.params;
  return withAdminAuth((req, user) => handleDeleteToyStatus(req, user, params))(request);
}

export async function PUT(request: NextRequest, context: any) {
  const params = await context.params;
  return withAdminAuth((req, user) => handleUpdateToyStatus(req, user, params))(request);
}
