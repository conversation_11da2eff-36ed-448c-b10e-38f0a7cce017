import { NextRequest, NextResponse } from 'next/server';
import { generateCsrfTokenForCookie } from '../../../lib/csrf';

/**
 * API endpoint pre získanie CSRF tokenu
 * @param request NextRequest objekt
 * @returns NextResponse objekt s CSRF tokenom
 */
export async function GET(request: NextRequest) {
  try {
    // Generovanie CSRF tokenu
    const token = generateCsrfTokenForCookie();
    console.log(`Generovaný nový CSRF token: ${token.substring(0, 20)}...`);

    // Vytvorenie odpovede s tokenom
    const response = NextResponse.json({ csrfToken: token });

    // Nastavenie CSRF tokenu do cookies v odpovedi
    response.cookies.set({
      name: 'swapka_csrf',
      value: token,
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax', // Zmenené z 'strict' na 'lax' pre lepšiu kompatibilitu
      path: '/',
      maxAge: 60 * 60 * 24, // 24 hod<PERSON> v se<PERSON><PERSON><PERSON>
    });

    console.log('CSRF token bol nastavený do cookies');

    // Vrátenie odpovede s tokenom a cookie
    return response;
  } catch (error) {
    console.error('Chyba pri generovaní CSRF tokenu:', error);
    return NextResponse.json(
      { error: 'Nastala chyba pri generovaní CSRF tokenu' },
      { status: 500 }
    );
  }
}
