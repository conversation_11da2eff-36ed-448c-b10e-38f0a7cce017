import { NextResponse } from 'next/server';
import fs from 'fs';
import path from 'path';

export async function GET() {
  try {
    // SECURITY: Only allow version endpoint in development environment
    // In production, this endpoint should not expose detailed system information
    const isProduction = process.env.NODE_ENV === 'production';

    if (isProduction) {
      // In production, only return minimal version information
      return NextResponse.json({
        version: process.env.npm_package_version || '0.22',
        status: 'running'
      });
    }

    // Development/testing environment - full version information
    const versionInfoPath = path.join(process.cwd(), 'version-info.json');

    if (fs.existsSync(versionInfoPath)) {
      const versionInfo = JSON.parse(fs.readFileSync(versionInfoPath, 'utf8'));
      return NextResponse.json({
        version: versionInfo.version,
        buildId: versionInfo.buildId,
        environment: versionInfo.environment,
        buildDate: versionInfo.buildDate
      });
    }

    // Fallback - načítanie z package.json
    const packageJsonPath = path.join(process.cwd(), 'package.json');
    if (fs.existsSync(packageJsonPath)) {
      const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
      return NextResponse.json({
        version: packageJson.version,
        environment: process.env.NODE_ENV || 'development'
      });
    }

    // Posledný fallback
    return NextResponse.json({
      version: '0.22',
      environment: process.env.NODE_ENV || 'development'
    });

  } catch (error) {
    console.error('Chyba pri načítavaní verzie:', error);

    // In production, don't expose error details
    if (process.env.NODE_ENV === 'production') {
      return NextResponse.json({
        version: '0.22',
        status: 'error'
      }, { status: 500 });
    }

    return NextResponse.json({
      version: '0.22',
      environment: process.env.NODE_ENV || 'development',
      error: 'Failed to load version information'
    }, { status: 500 });
  }
}
