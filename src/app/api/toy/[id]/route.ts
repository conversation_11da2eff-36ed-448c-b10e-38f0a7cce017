import { NextResponse } from 'next/server';
import { getToyById, prisma } from '../../../../lib/db';
import { toNumericId, hashId, isHashedId } from '../../../../lib/hashUtils';
import { anonymizeName } from '../../../../lib/nameUtils';
import { parseToyUrl } from '../../../../lib/seoUtils';

export async function GET(request: Request) {
  // Získanie ID z URL
  const url = new URL(request.url);
  const segments = url.pathname.split('/');
  const idParam = segments[segments.length - 1];

  // Pokus o parsovanie SEF URL alebo legacy hashovaného ID
  let hashedId: string | null = null;

  // Najprv skúsime parsovať ako SEF URL
  hashedId = parseToyUrl(url.pathname);

  // Ak to nie je SEF URL, skúsime či je to priamo hashované ID
  if (!hashedId && isHashedId(idParam)) {
    hashedId = idParam;
  }

  if (!hashedId) {
    return NextResponse.json(
      { error: 'Neplatné ID hračky. Očakáva sa SEF URL alebo hashované ID.' },
      { status: 400 }
    );
  }

  // Konverzia hashovaného ID na číselné ID
  const id = toNumericId(hashedId);

  if (id === null) {
    return NextResponse.json(
      { error: 'Neplatné ID hračky' },
      { status: 400 }
    );
  }

  try {
    const toy = await getToyById(id);

    if (!toy) {
      return NextResponse.json(
        { error: 'Hračka nebola nájdená' },
        { status: 404 }
      );
    }

    // Získanie informácie o autentifikovanom používateľovi z hlavičky
    const url = new URL(request.url);
    const authHeader = url.searchParams.get('userId');

    let userId: number | null = null;

    // Kontrola, či je ID hashované
    if (authHeader) {
      if (isHashedId(authHeader)) {
        // Konverzia hashovaného ID na číselné
        userId = toNumericId(authHeader);
      } else {
        console.error('Neplatný formát ID v parametri userId');
        userId = null;
      }
    }

    const isOwner = userId !== null && userId === toy.user.id;

    // Kontrola, či vlastník hračky nie je zablokovaný (okrem prípadu, keď je to vlastník)
    if (toy.user.status === 'BLOCKED' && !isOwner) {
      return NextResponse.json(
        { error: 'Hračka nie je dostupná' },
        { status: 404 }
      );
    }

    // Kontrola, či hračka nie je v stave DRAFT (okrem prípadu, keď je to vlastník)
    if (toy.status === 'DRAFT' && !isOwner) {
      return NextResponse.json(
        { error: 'Hračka nie je dostupná' },
        { status: 404 }
      );
    }

    // Ak je používateľ prihlásený, skontrolujeme, či má rezerváciu na túto hračku
    let userReservation = null;
    let waitingListInfo = null;

    // Získanie všetkých aktívnych rezervácií pre túto hračku
    const allReservations = await prisma.reservation.findMany({
      where: {
        toyId: toy.id,
        status: { in: ['PENDING', 'CONFIRMED', 'ACTIVE'] }
      },
      orderBy: {
        position: 'asc'
      }
    });

    // Celkový počet rezervácií v čakacom zozname
    const totalReservations = allReservations.length;

    if (userId !== null) {
      // Získanie rezervácie používateľa pre túto hračku
      const reservation = await prisma.reservation.findFirst({
        where: {
          toyId: toy.id,
          userId: userId,
          status: { in: ['PENDING', 'CONFIRMED', 'ACTIVE'] }
        },
        orderBy: {
          createdAt: 'desc'
        }
      });

      if (reservation) {
        userReservation = {
          id: reservation.id,
          hashedId: hashId(reservation.id),
          status: reservation.status,
          position: reservation.position,
          userId: reservation.userId,
          hashedUserId: hashId(reservation.userId),
          toyId: reservation.toyId,
          hashedToyId: hashId(reservation.toyId),
          ownerId: reservation.ownerId,
          hashedOwnerId: hashId(reservation.ownerId)
        };

        // Informácie o pozícii v čakacom zozname
        waitingListInfo = {
          position: reservation.position,
          totalReservations: totalReservations
        };
      }
    }

    // Get toy status label from database
    const toyStatus = await prisma.toyStatus.findUnique({
      where: { name: toy.status },
      select: { label: true }
    });

    // Get toy type label from database
    const toyType = await prisma.toyType.findUnique({
      where: { name: toy.type },
      select: { label: true }
    });

    // Transformácia dát do formátu, ktorý očakáva frontend
    const transformedToy = {
      id: hashId(toy.id), // Hashujeme ID pre frontend
      name: toy.name,
      description: toy.description,
      images: toy.images.length > 0
        ? toy.images.map(img => img.url)
        : ['/toys/placeholder.jpg'],
      type: toy.type,
      typeLabel: toyType?.label || toy.type, // Add the type label
      location: {
        city: toy.user.city || 'Neuvedené',
        postalCode: toy.user.postalCode || ''
      },
      price: toy.price,
      deposit: toy.deposit,
      priceType: (toy as any).priceType || 'PER_DAY', // Add priceType field
      status: toy.status,
      statusLabel: toyStatus?.label || toy.status, // Add the status label
      createdAt: toy.createdAt,
      updatedAt: toy.updatedAt,
      owner: {
        hashedId: toy.user?.id ? hashId(toy.user.id) : '',
        name: anonymizeName(toy.user?.name || 'Neznámy používateľ'),
        city: toy.user?.city || null
      },
      maxReservationDays: (toy as any).maxReservationDays || 7, // Pridanie maxReservationDays s predvolenou hodnotou 7
      reservation: userReservation, // Pridanie informácie o rezervácii používateľa
      waitingList: {
        totalReservations: totalReservations,
        userPosition: waitingListInfo?.position || -1
      }
    };

    return NextResponse.json(transformedToy);
  } catch (error) {
    console.error('Chyba pri získavaní detailu hračky:', error);
    return NextResponse.json(
      { error: 'Nastala chyba pri získavaní detailu hračky' },
      { status: 500 }
    );
  }
}
