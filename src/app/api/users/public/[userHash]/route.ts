import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '../../../../../lib/db';
import { toNumericId, toNumericIdAsync, hashId, isHashedId } from '../../../../../lib/hashUtils';
import { anonymizeName } from '../../../../../lib/nameUtils';

// Funkcia pre získanie verejných údajov o používateľovi podľa hashedId
export async function GET(request: NextRequest) {
  try {
    // Získanie userHash z URL
    const url = new URL(request.url);
    const pathParts = url.pathname.split('/');
    const userHash = pathParts[pathParts.length - 1];

    if (!userHash || !isHashedId(userHash)) {
      return NextResponse.json(
        { error: 'Neplatné ID používateľa' },
        { status: 400 }
      );
    }

    // Konverzia hashovaného ID na číselné ID s databázovou optimalizáciou
    const numericId = await toNumericIdAsync(userHash, prisma);

    if (numericId === null) {
      return NextResponse.json(
        { error: 'Neplatné ID používateľa' },
        { status: 400 }
      );
    }

    // Hľadáme podľa číselného ID
    const user = await prisma.user.findUnique({
      where: { id: numericId },
      select: {
        id: true,
        name: true,
        city: true,
        status: true,
      },
    });

    if (!user) {
      return NextResponse.json(
        { error: 'Používateľ nebol nájdený' },
        { status: 404 }
      );
    }

    // Kontrola, či používateľ nie je blokovaný
    if (user.status === 'BLOCKED') {
      return NextResponse.json(
        { error: 'Používateľ nie je dostupný' },
        { status: 403 }
      );
    }

    // Transformácia dát - vrátime len verejné údaje s anonymizovaným menom
    const publicUser = {
      hashedId: hashId(user.id),
      name: anonymizeName(user.name),
      city: user.city,
    };

    return NextResponse.json(publicUser);
  } catch (error) {
    console.error('Chyba pri získavaní používateľa:', error);
    return NextResponse.json(
      { error: 'Nastala chyba pri získavaní používateľa' },
      { status: 500 }
    );
  }
}
