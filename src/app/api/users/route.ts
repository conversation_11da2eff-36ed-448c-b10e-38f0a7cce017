import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '../../../lib/db';
import { withAuth, withAdminAuth } from '../../../lib/auth';
import { hashId } from '../../../lib/hashUtils';
import {
  sanitizeText,
  sanitizeEmail,
  sanitizeObject
} from '../../../lib/inputSanitization';

// Vytvorenie nového používateľa
export async function POST(request: NextRequest) {
  try {
    const rawData = await request.json();

    // Sanitizácia vstupných údajov
    const sanitizationResult = sanitizeObject(rawData, {
      email: (value) => sanitizeEmail(value),
      name: (value) => sanitizeText(value, { maxLength: 100, allowSpecialChars: false, allowSlovakDiacritics: true }),
      role: (value) => sanitizeText(value, { maxLength: 20, allowSpecialChars: false }),
      password: (value) => sanitizeText(value, { maxLength: 255 }),
      firebaseUid: (value) => value ? sanitizeText(value, { maxLength: 128, allowSpecialChars: false }) : { isValid: true, sanitizedValue: null, errors: [] },
      termsAccepted: (value) => ({ isValid: true, sanitizedValue: Boolean(value), errors: [] })
    });

    if (!sanitizationResult.isValid) {
      return NextResponse.json(
        {
          error: 'Neplatné vstupné údaje',
          details: sanitizationResult.errors
        },
        { status: 400 }
      );
    }

    const { email, name, role, password = '', firebaseUid = null, termsAccepted = false } = sanitizationResult.sanitizedData;

    // Kontrola povinných údajov
    if (!email || !name || !role) {
      return NextResponse.json(
        { error: 'Chýbajú povinné údaje' },
        { status: 400 }
      );
    }

    // Vytvorenie používateľa v databáze
    const user = await prisma.user.create({
      data: {
        email,
        name,
        role, // Používame existujúci enum (USER alebo ADMIN)
        password, // Prázdne heslo pre Firebase používateľov
        firebaseUid, // Firebase UID pre prepojenie s Firebase autentifikáciou
        termsAccepted, // Informácia o súhlase s podmienkami
      },
    });

    return NextResponse.json(user);
  } catch (error) {
    console.error('Chyba pri vytváraní používateľa:', error);
    return NextResponse.json(
      { error: 'Nastala chyba pri vytváraní používateľa' },
      { status: 500 }
    );
  }
}

// Funkcia pre získanie všetkých používateľov (len pre administrátorov)
async function handleGetAllUsers(request: NextRequest, user: any) {
  try {
    // Overenie, či má používateľ rolu admin
    if (user.role !== 'ADMIN') {
      return NextResponse.json(
        { error: 'Nedostatočné oprávnenia' },
        { status: 403 }
      );
    }

    const users = await prisma.user.findMany({
      select: {
        id: true,
        email: true,
        name: true,
        role: true,
        status: true,
        phone: true,
        city: true,
        postalCode: true,
        isAnonymized: true,
        createdAt: true,
        updatedAt: true,
      },
    });

    // Transform users to exclude raw numeric IDs and include only hashed IDs for security
    const transformedUsers = users.map(user => ({
      // Security: Use hashed ID as the primary identifier - no raw numeric IDs exposed
      id: hashId(user.id), // Use hashed ID as the main ID for frontend
      hashedId: hashId(user.id), // Maintain backward compatibility
      email: user.email,
      name: user.name,
      role: user.role,
      status: user.status,
      phone: user.phone,
      city: user.city,
      postalCode: user.postalCode,
      isAnonymized: user.isAnonymized,
      createdAt: user.createdAt,
      updatedAt: user.updatedAt,
    }));

    return NextResponse.json(transformedUsers);
  } catch (error) {
    console.error('Chyba pri získavaní používateľov:', error);
    return NextResponse.json(
      { error: 'Nastala chyba pri získavaní používateľov' },
      { status: 500 }
    );
  }
}

// Export funkcie s overením autentifikácie a role admin
export const GET = withAdminAuth(handleGetAllUsers);
