import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '../../../../../lib/db';
import { withAuth } from '../../../../../lib/auth';

// Funkcia pre získanie používateľa podľa emailu
async function handleGetUserByEmail(
  request: NextRequest,
  authenticatedUser: any,
  params: { email: string }
) {
  try {
    const { email } = params;

    // Špeciálna výnimka pre autentifikačný proces
    // Ak je to požiadavka z autentifikačného procesu, povolíme ju
    const isAuthRequest = request.headers.get('x-auth-request') === 'true';

    const user = await prisma.user.findUnique({
      where: { email },
      select: {
        id: true,
        email: true,
        name: true,
        role: true,
        status: true,
        phone: true,
        city: true,
        postalCode: true,
        termsAccepted: true,
        createdAt: true,
        updatedAt: true,
      },
    });

    if (!user) {
      return NextResponse.json(
        { error: 'Používateľ nebol nájdený' },
        { status: 404 }
      );
    }

    // Ak to nie je autentifikačná požiadavka, overíme oprávnenia
    if (!isAuthRequest) {
      // Overenie, či používateľ pristupuje k vlastnému profilu alebo je admin
      if (user.id !== authenticatedUser.id && authenticatedUser.role !== 'ADMIN') {
        return NextResponse.json(
          { error: 'Nemáte oprávnenie na prístup k tomuto profilu' },
          { status: 403 }
        );
      }
    }

    return NextResponse.json(user);
  } catch (error) {
    console.error('Chyba pri získavaní používateľa:', error);
    return NextResponse.json(
      { error: 'Nastala chyba pri získavaní používateľa' },
      { status: 500 }
    );
  }
}

// Špeciálna funkcia pre autentifikačný proces
export async function GET(request: NextRequest) {
  // Získanie parametrov z URL
  const url = new URL(request.url);
  const pathParts = url.pathname.split('/');
  const emailEncoded = pathParts[pathParts.length - 1];
  const email = decodeURIComponent(emailEncoded);

  // Ak je to požiadavka z autentifikačného procesu, povolíme ju bez overenia
  const isAuthRequest = request.headers.get('x-auth-request') === 'true';

  if (isAuthRequest) {
    return handleGetUserByEmail(request, null, { email });
  }

  // Inak vyžadujeme autentifikáciu
  return withAuth((req, user) => handleGetUserByEmail(req, user, { email }))(request);
}