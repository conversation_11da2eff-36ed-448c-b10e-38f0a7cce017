import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '../../../../../lib/db';
import { withAdminAuth } from '../../../../../lib/auth';
import { deleteUserImages } from '../../../../../lib/cloudinary';
import { toNumericId, toNumericIdAsync, hashId, isHashedId } from '../../../../../lib/hashUtils';

// Funkcia pre aktualizáciu stavu používateľa a jeho hračiek
async function handleUpdateUserStatus(
  request: NextRequest,
  authenticatedUser: any,
  params: { id: string }
) {
  try {
    const idParam = params.id;
    const { status } = await request.json();

    // Kontrola, či id je hashované
    if (!isHashedId(idParam)) {
      return NextResponse.json(
        { error: 'Neplatné ID používateľa. Očakáva sa hashované ID.' },
        { status: 400 }
      );
    }

    // Konverzia hashovaného ID na číselné ID s databázovou optimalizáciou
    const userId = await toNumericIdAsync(idParam, prisma);

    if (userId === null) {
      return NextResponse.json(
        { error: 'Neplatné ID používateľa' },
        { status: 400 }
      );
    }

    // Kontrola, či používateľ existuje
    const existingUser = await prisma.user.findUnique({
      where: { id: userId },
    });

    if (!existingUser) {
      return NextResponse.json(
        { error: 'Používateľ nebol nájdený' },
        { status: 404 }
      );
    }

    // Kontrola, či sa nepokúšame zablokovať administrátora
    if (existingUser.role === 'ADMIN') {
      return NextResponse.json(
        { error: 'Administrátora nie je možné zablokovať' },
        { status: 403 }
      );
    }

    // Aktualizácia stavu používateľa
    const updatedUser = await prisma.user.update({
      where: { id: userId },
      data: { status },
    });

    // Ak je používateľ blokovaný, deaktivujeme všetky jeho hračky a odstránime fotografie
    if (status === 'BLOCKED') {
      // Najprv fyzicky odstránime všetky obrázky používateľa z Cloudinary
      await deleteUserImages(userId);

      // Potom odstránime záznamy o obrázkoch z databázy
      const userToys = await prisma.toy.findMany({
        where: { userId },
        select: { id: true },
      });

      for (const toy of userToys) {
        await prisma.toyImage.deleteMany({
          where: { toyId: toy.id },
        });
      }

      // Nakoniec deaktivujeme všetky hračky používateľa
      await prisma.toy.updateMany({
        where: { userId },
        data: { status: 'UNAVAILABLE' },
      });
    } else if (status === 'ACTIVE') {
      // Ak je používateľ odblokovaný, aktivujeme všetky jeho hračky
      await prisma.toy.updateMany({
        where: { userId },
        data: { status: 'AVAILABLE' },
      });
    }

    // Transformácia dát - pridanie hashovaného ID
    const transformedUser = {
      ...updatedUser,
      hashedId: hashId(updatedUser.id),
    };

    return NextResponse.json({
      message: `Stav používateľa bol úspešne aktualizovaný na ${status}`,
      user: transformedUser,
    });
  } catch (error) {
    console.error('Chyba pri aktualizácii stavu používateľa:', error);
    return NextResponse.json(
      { error: 'Nastala chyba pri aktualizácii stavu používateľa' },
      { status: 500 }
    );
  }
}

// Export funkcie s overením autentifikácie a role admin
export async function PUT(request: NextRequest) {
  // Získanie parametrov z URL
  const url = new URL(request.url);
  const pathParts = url.pathname.split('/');
  const id = pathParts[pathParts.length - 2]; // -2 because the last part is 'status'

  // Použitie withAdminAuth middleware
  return withAdminAuth(async (req, user) => {
    return handleUpdateUserStatus(req, user, { id });
  })(request);
}
