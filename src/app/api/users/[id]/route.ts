/**
 * User Profile API Endpoint
 *
 * Security Features:
 * - Role modification prevention: Users cannot modify their role field via API
 * - Sensitive data protection: Numeric database IDs are excluded from responses
 * - Only hashed IDs and safe user data are returned to frontend
 */

import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '../../../../lib/db';
import { withAuth } from '../../../../lib/auth';
import { toNumericId, toNumericIdAsync, hashId, isHashedId } from '../../../../lib/hashUtils';
import {
  sanitizeText,
  sanitizeEmail,
  sanitizePhoneNumber,
  sanitizeNumericInput,
  sanitizeObject
} from '../../../../lib/inputSanitization';

// Funkcia pre získanie používateľa podľa hashovaného ID
async function handleGetUser(
  request: NextRequest,
  authenticatedUser: any,
  params: { id: string }
) {
  try {
    const idParam = params.id;

    let user;

    // Kontrol<PERSON>, či id je hashované
    if (!isHashedId(idParam)) {
      return NextResponse.json(
        { error: 'Neplatné ID používateľa. Očakáva sa hashované ID.' },
        { status: 400 }
      );
    }

    // Konverzia hashovaného ID na číselné ID s databázovou optimalizáciou
    const numericId = await toNumericIdAsync(idParam, prisma);

    if (numericId === null) {
      return NextResponse.json(
        { error: 'Neplatné ID používateľa' },
        { status: 400 }
      );
    }

    // Hľadáme podľa číselného ID
    user = await prisma.user.findUnique({
      where: { id: numericId },
      select: {
        id: true,
        firebaseUid: true,
        email: true,
        name: true,
        role: true,
        status: true,
        phone: true,
        address: true,
        city: true,
        postalCode: true,
        latitude: true,
        longitude: true,
        createdAt: true,
        updatedAt: true,
      },
    });

    if (!user) {
      return NextResponse.json(
        { error: 'Používateľ nebol nájdený' },
        { status: 404 }
      );
    }

    // Overenie, či používateľ pristupuje k vlastnému profilu alebo je admin
    if (user.id !== authenticatedUser.id && authenticatedUser.role !== 'ADMIN') {
      return NextResponse.json(
        { error: 'Nemáte oprávnenie na prístup k tomuto profilu' },
        { status: 403 }
      );
    }

    // Transformácia dát - pridanie hashovaného ID a odstránenie citlivých údajov
    const { id, ...safeUserData } = user;
    const transformedUser = {
      ...safeUserData,
      hashedId: hashId(id), // Pridanie hashovaného ID, ale odstránenie číselného ID
    };

    return NextResponse.json(transformedUser);
  } catch (error) {
    console.error('Chyba pri získavaní používateľa:', error);
    return NextResponse.json(
      { error: 'Nastala chyba pri získavaní používateľa' },
      { status: 500 }
    );
  }
}

// Export funkcie s overením autentifikácie
export async function GET(request: NextRequest) {
  // Získanie parametrov z URL
  const url = new URL(request.url);
  const pathParts = url.pathname.split('/');
  const id = pathParts[pathParts.length - 1];

  return withAuth(async (req, user) => {
    return handleGetUser(req, user, { id });
  })(request);
}

// Funkcia pre aktualizáciu používateľa
async function handleUpdateUser(
  request: NextRequest,
  authenticatedUser: any,
  params: { id: string }
) {
  try {
    const idParam = params.id;
    const rawData = await request.json();

    let existingUser;
    let numericId: number | null = null;

    // Kontrola, či id je hashované
    if (!isHashedId(idParam)) {
      return NextResponse.json(
        { error: 'Neplatné ID používateľa. Očakáva sa hashované ID.' },
        { status: 400 }
      );
    }

    // Konverzia hashovaného ID na číselné ID s databázovou optimalizáciou
    numericId = await toNumericIdAsync(idParam, prisma);

    if (numericId === null) {
      return NextResponse.json(
        { error: 'Neplatné ID používateľa' },
        { status: 400 }
      );
    }

    // Hľadáme podľa číselného ID
    existingUser = await prisma.user.findUnique({
      where: { id: numericId },
    });

    if (!existingUser) {
      return NextResponse.json(
        { error: 'Používateľ nebol nájdený' },
        { status: 404 }
      );
    }

    // Overenie, či používateľ aktualizuje vlastný profil alebo je admin
    if (existingUser.id !== authenticatedUser.id && authenticatedUser.role !== 'ADMIN') {
      return NextResponse.json(
        { error: 'Nemáte oprávnenie na aktualizáciu tohto profilu' },
        { status: 403 }
      );
    }

    // Definícia sanitizačných schém pre rôzne typy používateľov
    const userSanitizationSchema = {
      phone: (value: any) => sanitizePhoneNumber(value),
      address: (value: any) => sanitizeText(value, { maxLength: 200, allowSpecialChars: true }),
      city: (value: any) => sanitizeText(value, { maxLength: 100, allowSpecialChars: false, allowSlovakDiacritics: true }),
      postalCode: (value: any) => sanitizeText(value, { maxLength: 20, allowSpecialChars: false }),
      latitude: (value: any) => {
        if (value === null || value === undefined || value === '') {
          return { isValid: true, sanitizedValue: null, errors: [] };
        }
        const result = sanitizeNumericInput(value, { min: -90, max: 90, decimals: 6 });
        return result;
      },
      longitude: (value: any) => {
        if (value === null || value === undefined || value === '') {
          return { isValid: true, sanitizedValue: null, errors: [] };
        }
        const result = sanitizeNumericInput(value, { min: -180, max: 180, decimals: 6 });
        return result;
      },
      termsAccepted: (value: any) => ({ isValid: true, sanitizedValue: Boolean(value), errors: [] })
    };

    const adminSanitizationSchema = {
      ...userSanitizationSchema,
      // Note: role field is intentionally excluded for security - users cannot modify their role
      status: (value: any) => {
        if (value === null || value === undefined || value === '') {
          return { isValid: true, sanitizedValue: value, errors: [] };
        }
        return sanitizeText(value, { maxLength: 20, allowSpecialChars: false });
      },
      name: (value: any) => {
        if (value === null || value === undefined || value === '') {
          return { isValid: true, sanitizedValue: value, errors: [] };
        }
        return sanitizeText(value, { maxLength: 100, allowSpecialChars: false, allowSlovakDiacritics: true });
      },
      email: (value: any) => {
        if (value === null || value === undefined || value === '') {
          return { isValid: true, sanitizedValue: value, errors: [] };
        }
        return sanitizeEmail(value);
      }
    };

    // Výber správnej sanitizačnej schémy
    const sanitizationSchema = (authenticatedUser.role === 'ADMIN' || authenticatedUser.role === 'admin') ? adminSanitizationSchema : userSanitizationSchema;

    // Filtrovanie len povolených polí - len tie, ktoré sú skutočne poslané
    const allowedFields = Object.keys(sanitizationSchema);
    const filteredRawData: Record<string, any> = {};

    for (const field of allowedFields) {
      if (rawData.hasOwnProperty(field)) {
        filteredRawData[field] = rawData[field];
      }
    }

    // Vytvorenie sanitizačnej schémy len pre polia, ktoré sú skutočne poslané
    const activeSanitizationSchema: Record<string, any> = {};
    for (const field of Object.keys(filteredRawData)) {
      if ((sanitizationSchema as any)[field]) {
        activeSanitizationSchema[field] = (sanitizationSchema as any)[field];
      }
    }

    // Sanitizácia dát
    const sanitizationResult = sanitizeObject(filteredRawData, activeSanitizationSchema);

    if (!sanitizationResult.isValid) {
      return NextResponse.json(
        {
          error: 'Neplatné vstupné údaje',
          details: sanitizationResult.errors
        },
        { status: 400 }
      );
    }

    const filteredData = sanitizationResult.sanitizedData;

    // Logovanie pre debugovanie (bez citlivých údajov)
    if (process.env.NODE_ENV === 'development') {
      console.log('Profile update - fields being updated:', Object.keys(filteredData));
    }

    // Security note: Role field is intentionally excluded from updates for security

    // Kontrola, či máme nejaké dáta na aktualizáciu
    if (Object.keys(filteredData).length === 0) {
      return NextResponse.json(
        { error: 'Neboli poskytnuté žiadne platné údaje na aktualizáciu' },
        { status: 400 }
      );
    }

    // Aktualizácia používateľa
    let updatedUser;

    const selectFields = {
      id: true, // Required for internal operations but will be excluded from response
      firebaseUid: true,
      email: true,
      name: true,
      role: true,
      status: true,
      phone: true,
      address: true,
      city: true,
      postalCode: true,
      latitude: true,
      longitude: true,
      termsAccepted: true,
      createdAt: true,
      updatedAt: true,
    };

    // Aktualizácia používateľa podľa číselného ID (konvertovaného z hashu)
    updatedUser = await prisma.user.update({
      where: { id: numericId },
      data: filteredData,
      select: selectFields,
    });

    // Transformácia dát - pridanie hashovaného ID a odstránenie citlivých údajov
    const { id, ...safeUserData } = updatedUser;
    const transformedUser = {
      ...safeUserData,
      hashedId: hashId(id), // Pridanie hashovaného ID, ale odstránenie číselného ID
    };

    return NextResponse.json(transformedUser);
  } catch (error) {
    console.error('Chyba pri aktualizácii používateľa:', error);

    // Poskytnutie špecifickejšej chybovej správy
    let errorMessage = 'Nastala chyba pri aktualizácii používateľa';
    if (error instanceof Error) {
      if (error.message.includes('Record to update not found')) {
        errorMessage = 'Používateľ nebol nájdený';
      } else if (error.message.includes('Unique constraint')) {
        errorMessage = 'Údaje už existujú v systéme';
      }
    }

    return NextResponse.json(
      { error: errorMessage },
      { status: 500 }
    );
  }
}

// Export funkcie s overením autentifikácie
export async function PUT(request: NextRequest) {
  // Získanie parametrov z URL
  const url = new URL(request.url);
  const pathParts = url.pathname.split('/');
  const id = pathParts[pathParts.length - 1];

  return withAuth(async (req, user) => {
    return handleUpdateUser(req, user, { id });
  })(request);
}
