import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '../../../../../lib/db';
import { withAuth } from '../../../../../lib/auth';
import { toNumericId, isHashedId, hashId } from '../../../../../lib/hashUtils';
import { fetchUserData, createUserDataZip } from '../../../../../lib/dataExport';
import { Readable } from 'stream';

// Custom function to convert Readable to Web ReadableStream
function readableToWeb(nodeStream: Readable): ReadableStream<any> {
  return new ReadableStream({
    start(controller) {
      nodeStream.on('data', chunk => {
        controller.enqueue(chunk);
      });
      nodeStream.on('end', () => {
        controller.close();
      });
      nodeStream.on('error', err => {
        controller.error(err);
      });
    },
    cancel() {
      nodeStream.destroy();
    }
  });
}

/**
 * Handler for exporting a user's personal data (GDPR compliance)
 * This endpoint allows users to download all their personal data
 * in either XML or JSON format, packaged in a ZIP file.
 */
async function handleExportUserData(
  request: NextRequest,
  authenticatedUser: any,
  params: { id: string }
) {
  try {
    const { id } = params;
    const url = new URL(request.url);
    const format = url.searchParams.get('format') || 'json';

    if (format !== 'json' && format !== 'xml') {
      return NextResponse.json(
        { error: 'Neplatný formát. Podporované formáty sú "json" a "xml".' },
        { status: 400 }
      );
    }

    console.log(`Používateľ ${authenticatedUser.id} (${authenticatedUser.email}) požiadal o export dát používateľa s ID ${id} vo formáte ${format}`);

    // Convert ID to numeric ID if it's a hashed ID
    let numericId: number | null = null;
    if (isHashedId(id)) {
      numericId = toNumericId(id);
      console.log(`Konverzia hashovaného ID ${id} na číselné ID ${numericId}`);
    } else {
      numericId = parseInt(id);
      console.log(`Použitie číselného ID ${numericId}`);
    }

    if (!numericId) {
      console.error(`Neplatné ID používateľa: ${id}`);
      return NextResponse.json(
        { error: 'Neplatné ID používateľa' },
        { status: 400 }
      );
    }

    // Check if the user exists
    const existingUser = await prisma.user.findUnique({
      where: { id: numericId },
      select: {
        id: true,
        email: true,
        name: true,
        role: true,
        status: true,
      }
    });

    if (!existingUser) {
      console.error(`Používateľ s ID ${numericId} nebol nájdený`);
      return NextResponse.json(
        { error: 'Používateľ nebol nájdený' },
        { status: 404 }
      );
    }

    // Security check: Only allow users to export their own data or admins to export any data
    if (authenticatedUser.id !== numericId && authenticatedUser.role !== 'ADMIN') {
      console.error(`Používateľ ${authenticatedUser.id} sa pokúsil exportovať dáta používateľa ${numericId} bez oprávnenia`);
      return NextResponse.json(
        { error: 'Nemáte oprávnenie exportovať dáta tohto používateľa' },
        { status: 403 }
      );
    }

    // Fetch all user data
    console.log(`Začiatok exportu dát používateľa s ID ${numericId}`);
    const userData = await fetchUserData(numericId);

    if (!userData) {
      console.error(`Nepodarilo sa načítať dáta používateľa s ID ${numericId}`);
      return NextResponse.json(
        { error: 'Nepodarilo sa načítať dáta používateľa' },
        { status: 500 }
      );
    }

    // Create ZIP file with user data
    const zipStream = await createUserDataZip(userData, format as 'json' | 'xml');

    // For Next.js API routes, we need to convert the stream to a Response
    // First, convert the Readable stream to a Web ReadableStream
    const readableWebStream = readableToWeb(zipStream);

    // Create a response with the ZIP file
    const response = new NextResponse(readableWebStream);

    // Set appropriate headers for file download
    response.headers.set('Content-Type', 'application/zip');
    response.headers.set('Content-Disposition', `attachment; filename="user_data_${numericId}_${format}.zip"`);

    console.log(`Export dát používateľa s ID ${numericId} bol úspešne dokončený`);

    return response;
  } catch (error) {
    console.error('Chyba pri exporte dát používateľa:', error);
    return NextResponse.json(
      { error: 'Nastala chyba pri exporte dát používateľa' },
      { status: 500 }
    );
  }
}

/**
 * Endpoint pre export dát používateľa (GDPR)
 * Tento endpoint je prístupný pre používateľa, ktorého dáta sa exportujú, alebo pre administrátorov
 * Používa withAuth middleware pre overenie oprávnení
 *
 * Parametre:
 * - format: Formát exportu (json alebo xml), predvolene json
 *
 * Zabezpečenie:
 * - Vyžaduje platný Bearer token v Authorization hlavičke
 * - Overuje, či je používateľ autentifikovaný pomocou verifyAuth funkcie
 * - Kontroluje, či používateľ exportuje vlastné dáta alebo má rolu ADMIN
 * - Vracia 401 Unauthorized ak používateľ nie je autentifikovaný
 * - Vracia 403 Forbidden ak používateľ nemá oprávnenie exportovať dáta
 */
export async function GET(request: NextRequest) {
  // Get ID from URL
  const url = new URL(request.url);
  const pathParts = url.pathname.split('/');
  const id = pathParts[pathParts.length - 1];

  // Use withAuth middleware to ensure the user is authenticated
  return withAuth(async (req, user) => {
    return handleExportUserData(req, user, { id });
  })(request);
}
