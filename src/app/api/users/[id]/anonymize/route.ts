import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '../../../../../lib/db';
import { withAdminAuth } from '../../../../../lib/auth';
import { toNumericId, toNumericIdAsync, isHashedId, hashId } from '../../../../../lib/hashUtils';
import { anonymizeUser } from '../../../../../lib/anonymization';

/**
 * Handler for anonymizing a user's personal data (GDPR compliance)
 * This endpoint is only accessible to administrators
 */
async function handleAnonymizeUser(
  request: NextRequest,
  authenticatedUser: any,
  params: { id: string }
) {
  try {
    // The admin role check is already handled by the withAdminAuth middleware
    // so we don't need to check it again here

    const { id } = params;
    console.log(`Admin ${authenticatedUser.id} (${authenticatedUser.email}) požiadal o anonymizáciu používateľa s ID ${id}`);

    // Convert ID to numeric ID if it's a hashed ID
    let numericId: number | null = null;
    if (isHashedId(id)) {
      numericId = await toNumericIdAsync(id, prisma);
      console.log(`Konverzia hashovaného ID ${id} na číselné ID ${numericId}`);
    } else {
      numericId = parseInt(id);
      console.log(`Použitie číselného ID ${numericId}`);
    }

    if (!numericId) {
      console.error(`Neplatné ID používateľa: ${id}`);
      return NextResponse.json(
        { error: 'Neplatné ID používateľa' },
        { status: 400 }
      );
    }

    // Check if the user exists
    const existingUser = await prisma.user.findUnique({
      where: { id: numericId },
      select: {
        id: true,
        email: true,
        name: true,
        role: true,
        status: true,
        isAnonymized: true,
      }
    });

    if (!existingUser) {
      console.error(`Používateľ s ID ${numericId} nebol nájdený`);
      return NextResponse.json(
        { error: 'Používateľ nebol nájdený' },
        { status: 404 }
      );
    }

    // Check if the user is already anonymized
    if (existingUser.isAnonymized) {
      console.warn(`Používateľ s ID ${numericId} je už anonymizovaný`);
      return NextResponse.json(
        { error: 'Používateľ je už anonymizovaný' },
        { status: 400 }
      );
    }

    // Check if the user is an admin (prevent anonymizing admins)
    if (existingUser.role === 'ADMIN') {
      console.error(`Pokus o anonymizáciu admin účtu s ID ${numericId}`);
      return NextResponse.json(
        { error: 'Administrátorské účty nemôžu byť anonymizované' },
        { status: 403 }
      );
    }

    // Check if the user is blocked
    if (existingUser.status === 'BLOCKED') {
      console.warn(`Anonymizácia blokovaného používateľa s ID ${numericId}`);
    }

    // Anonymize the user
    console.log(`Začiatok anonymizácie používateľa s ID ${numericId}`);
    const anonymizedUser = await anonymizeUser(numericId, authenticatedUser.id);
    console.log(`Používateľ s ID ${numericId} bol úspešne anonymizovaný`);

    return NextResponse.json({
      message: 'Používateľ bol úspešne anonymizovaný',
      user: {
        id: anonymizedUser.id,
        hashedId: hashId(anonymizedUser.id),
        email: anonymizedUser.email,
        name: anonymizedUser.name,
        isAnonymized: anonymizedUser.isAnonymized,
      },
    });
  } catch (error) {
    console.error('Chyba pri anonymizácii používateľa:', error);
    return NextResponse.json(
      { error: 'Nastala chyba pri anonymizácii používateľa' },
      { status: 500 }
    );
  }
}

/**
 * Endpoint pre anonymizáciu používateľa (GDPR)
 * Tento endpoint je prístupný len pre administrátorov
 * Používa withAdminAuth middleware pre overenie oprávnení
 *
 * Zabezpečenie:
 * - Vyžaduje platný Bearer token v Authorization hlavičke
 * - Overuje, či je používateľ autentifikovaný pomocou verifyAuth funkcie
 * - Kontroluje, či má používateľ rolu ADMIN
 * - Vracia 401 Unauthorized ak používateľ nie je autentifikovaný
 * - Vracia 403 Forbidden ak používateľ nemá rolu ADMIN
 */
export async function POST(request: NextRequest) {
  // Získanie parametrov z URL
  const url = new URL(request.url);
  const pathParts = url.pathname.split('/');
  const id = pathParts[pathParts.length - 2]; // -2 pretože posledná časť je 'anonymize'

  // Použitie withAdminAuth middleware pre zabezpečenie, že len admini môžu pristupovať k tomuto endpointu
  // withAdminAuth vykoná nasledujúce kontroly:
  // 1. Overí, či je používateľ autentifikovaný (má platný token)
  // 2. Overí, či má používateľ rolu ADMIN
  // 3. Ak niektorá z kontrol zlyhá, vráti chybovú odpoveď (401 alebo 403)
  return withAdminAuth(async (req, user) => {
    return handleAnonymizeUser(req, user, { id });
  })(request);
}
