import { NextResponse } from 'next/server';
import { prisma } from '../../../../lib/db';

// Získanie počtu používateľov
export async function GET() {
  try {
    const count = await prisma.user.count();
    return NextResponse.json({ count });
  } catch (error) {
    console.error('Chyba pri získavaní počtu používateľov:', error);
    return NextResponse.json(
      { error: 'Nastala chyba pri získavaní počtu používateľov' },
      { status: 500 }
    );
  }
}
