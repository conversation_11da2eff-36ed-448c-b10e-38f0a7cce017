import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '../../../../../lib/db';
import { withAuth } from '../../../../../lib/auth';
import { toNumericId, toNumericIdAsync, hashId, isHashedId } from '../../../../../lib/hashUtils';

// Funkcia pre získanie kontaktných údajov používateľa
async function handleGetUserContact(
  request: NextRequest,
  authenticatedUser: any,
  params: { id: string }
) {
  try {
    const idParam = params.id;

    // Kontrola, či id je hashované
    if (!isHashedId(idParam)) {
      return NextResponse.json(
        { error: 'Neplatné ID používateľa. Očakáva sa hashované ID.' },
        { status: 400 }
      );
    }

    // Konverzia hashovaného ID na číselné ID s databázovou optimalizáciou
    const userId = await toNumericIdAsync(idParam, prisma);

    if (userId === null) {
      return NextResponse.json(
        { error: 'Neplatné ID používateľa' },
        { status: 400 }
      );
    }

    // Kontrola, či používateľ existuje
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: {
        id: true,
        name: true,
        email: true,
        phone: true,
        address: true,
        city: true,
        postalCode: true,
        status: true,
      },
    });

    if (!user) {
      return NextResponse.json(
        { error: 'Používateľ nebol nájdený' },
        { status: 404 }
      );
    }

    // Kontrola, či používateľ nie je blokovaný
    if (user.status === 'BLOCKED') {
      return NextResponse.json(
        { error: 'Používateľ nie je dostupný' },
        { status: 403 }
      );
    }

    // Kontrola, či autentifikovaný používateľ má právo vidieť kontaktné údaje
    // 1. Používateľ môže vidieť svoje vlastné údaje
    // 2. Používateľ môže vidieť údaje vlastníka hračky, ktorú má potvrdenú rezerváciu
    // 3. Vlastník hračky môže vidieť údaje používateľa, ktorý má potvrdenú rezerváciu jeho hračky
    let hasAccess = false;

    // Prípad 1: Vlastné údaje
    if (authenticatedUser.id === userId) {
      hasAccess = true;
    } else {
      // Prípad 2: Používateľ má potvrdenú rezerváciu hračky vlastníka
      const userReservations = await prisma.reservation.findMany({
        where: {
          userId: authenticatedUser.id,
          ownerId: userId,
          status: { in: ['CONFIRMED', 'ACTIVE'] },
        },
      });

      if (userReservations.length > 0) {
        hasAccess = true;
      }

      // Prípad 3: Vlastník hračky má potvrdenú rezerváciu od používateľa
      if (!hasAccess) {
        const ownerReservations = await prisma.reservation.findMany({
          where: {
            userId: userId,
            ownerId: authenticatedUser.id,
            status: { in: ['CONFIRMED', 'ACTIVE'] },
          },
        });

        if (ownerReservations.length > 0) {
          hasAccess = true;
        }
      }
    }

    if (!hasAccess) {
      return NextResponse.json(
        { error: 'Nemáte oprávnenie na prístup ku kontaktným údajom tohto používateľa' },
        { status: 403 }
      );
    }

    // Transformácia dát - vrátime len kontaktné údaje s hashovaným ID
    const contactInfo = {
      id: hashId(user.id), // Hashované ID ako primárne ID
      name: user.name,
      email: user.email,
      phone: user.phone || null,
      address: user.address || null,
      city: user.city || null,
      postalCode: user.postalCode || null,
    };

    return NextResponse.json(contactInfo);
  } catch (error) {
    console.error('Chyba pri získavaní kontaktných údajov používateľa:', error);
    return NextResponse.json(
      { error: 'Nastala chyba pri získavaní kontaktných údajov používateľa' },
      { status: 500 }
    );
  }
}

// Export funkcie s overením autentifikácie
export async function GET(request: NextRequest, context: any) {
  const params = context.params;
  return withAuth((req, user) => handleGetUserContact(req, user, params))(request);
}
