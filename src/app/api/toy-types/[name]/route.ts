import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '../../../../lib/db';
import { withAdminAuth } from '../../../../lib/auth';

// Funkcia pre odstránenie typu hračky
async function handleDeleteToyType(
  request: NextRequest,
  user: any,
  params: { name: string }
) {
  try {
    const name = decodeURIComponent(params.name);

    // Kontrola, či typ existuje
    const existingType = await prisma.$queryRaw`
      SELECT name FROM ToyType WHERE name = ${name}
    `;

    if ((existingType as any[]).length === 0) {
      return NextResponse.json(
        { error: 'Typ hračky nebol nájdený' },
        { status: 404 }
      );
    }

    // Kontrola, či typ nie je použ<PERSON>vaný
    const toyCount = await prisma.toy.count({
      where: { type: name as any }
    });

    if (toyCount > 0) {
      return NextResponse.json(
        { error: 'Nie je mož<PERSON> o<PERSON> typ h<PERSON>, k<PERSON><PERSON> je použ<PERSON>' },
        { status: 400 }
      );
    }

    // Odstránenie typu
    await prisma.$executeRaw`DELETE FROM ToyType WHERE name = ${name}`;

    return NextResponse.json({ message: 'Typ hračky bol úspešne odstránený' });
  } catch (error) {
    console.error('Chyba pri odstraňovaní typu hračky:', error);
    return NextResponse.json(
      { error: 'Nastala chyba pri odstraňovaní typu hračky' },
      { status: 500 }
    );
  }
}

// Funkcia pre aktualizáciu labelu typu hračky
async function handleUpdateToyType(
  request: NextRequest,
  user: any,
  params: { name: string }
) {
  try {
    const name = decodeURIComponent(params.name);
    const { label } = await request.json();

    // Kontrola, či typ existuje
    const existingType = await prisma.$queryRaw`
      SELECT name FROM ToyType WHERE name = ${name}
    `;

    if ((existingType as any[]).length === 0) {
      return NextResponse.json(
        { error: 'Typ hračky nebol nájdený' },
        { status: 404 }
      );
    }

    // Aktualizácia labelu
    await prisma.$executeRaw`UPDATE ToyType SET label = ${label} WHERE name = ${name}`;

    return NextResponse.json({
      name,
      label,
      message: 'Label typu hračky bol úspešne aktualizovaný'
    });
  } catch (error) {
    console.error('Chyba pri aktualizácii typu hračky:', error);
    return NextResponse.json(
      { error: 'Nastala chyba pri aktualizácii typu hračky' },
      { status: 500 }
    );
  }
}

// Export funkcií s overením autentifikácie a role admin
export async function DELETE(request: NextRequest, context: any) {
  const params = await context.params;
  return withAdminAuth((req, user) => handleDeleteToyType(req, user, params))(request);
}

export async function PUT(request: NextRequest, context: any) {
  const params = await context.params;
  return withAdminAuth((req, user) => handleUpdateToyType(req, user, params))(request);
}
