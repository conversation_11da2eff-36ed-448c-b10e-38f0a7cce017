import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '../../../lib/db';
import { withAdminAuth } from '../../../lib/auth';
import { sanitizeText, sanitizeObject } from '../../../lib/inputSanitization';

// Funkcia pre získanie všetkých typov hračiek s počtom hračiek
async function handleGetAllToyTypes(request: NextRequest, user: any) {
  try {
    // Získanie všetkých typov hračiek vrátane labelov
    const toyTypes = await prisma.$queryRaw`SELECT name, label FROM ToyType`;

    // Získanie počtu hračiek pre každý typ
    const typeCounts = await prisma.$queryRaw`
      SELECT type, COUNT(*) as count
      FROM Toy
      GROUP BY type
    `;

    // Vytvorenie mapy počtu hračiek podľa typu
    const countMap: { [key: string]: number } = {};
    for (const row of typeCounts as any[]) {
      countMap[row.type] = parseInt(row.count.toString());
    }

    // Pridanie počtu hračiek k typom
    const result = (toyTypes as any[]).map(type => ({
      name: type.name,
      label: type.label || type.name, // Použijeme label, ak existuje, inak name
      count: countMap[type.name] || 0
    }));

    return NextResponse.json(result);
  } catch (error) {
    console.error('Chyba pri získavaní typov hračiek:', error);
    return NextResponse.json(
      { error: 'Nastala chyba pri získavaní typov hračiek' },
      { status: 500 }
    );
  }
}

// Funkcia pre pridanie nového typu hračky
async function handleAddToyType(request: NextRequest, user: any) {
  try {
    const rawData = await request.json();

    // Sanitizácia vstupných údajov
    const sanitizationResult = sanitizeObject(rawData, {
      name: (value) => sanitizeText(value, { maxLength: 50, allowSpecialChars: false, allowSlovakDiacritics: true }),
      label: (value) => value ? sanitizeText(value, { maxLength: 100, allowSpecialChars: true }) : { isValid: true, sanitizedValue: '', errors: [] }
    });

    if (!sanitizationResult.isValid) {
      return NextResponse.json(
        {
          error: 'Neplatné vstupné údaje',
          details: sanitizationResult.errors
        },
        { status: 400 }
      );
    }

    const { name, label } = sanitizationResult.sanitizedData;

    if (!name) {
      return NextResponse.json(
        { error: 'Názov typu hračky je povinný' },
        { status: 400 }
      );
    }

    // Kontrola, či typ už existuje - používame template literal syntax
    const existingType = await prisma.$queryRaw`
      SELECT name FROM ToyType WHERE name = ${name}
    `;

    if ((existingType as any[]).length > 0) {
      return NextResponse.json(
        { error: 'Typ hračky s týmto názvom už existuje' },
        { status: 400 }
      );
    }

    // Pridanie nového typu s labelom - používame template literal syntax
    await prisma.$executeRaw`INSERT INTO ToyType (name, label) VALUES (${name}, ${label || name})`;

    return NextResponse.json({ name, label: label || name, count: 0 });
  } catch (error) {
    console.error('Chyba pri pridávaní typu hračky:', error);
    return NextResponse.json(
      { error: 'Nastala chyba pri pridávaní typu hračky' },
      { status: 500 }
    );
  }
}

// Export funkcií s overením autentifikácie a role admin
export const GET = withAdminAuth(handleGetAllToyTypes);
export const POST = withAdminAuth(handleAddToyType);
