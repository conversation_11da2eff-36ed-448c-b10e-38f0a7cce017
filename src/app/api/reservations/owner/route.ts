import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '../../../../lib/db';
import { withAuth } from '../../../../lib/auth';
import { hashId } from '../../../../lib/hashUtils';

// Funkcia pre získanie rezervácií, kde je používateľ vlastníkom hračky
async function handleGetOwnerReservations(request: NextRequest, user: any) {
  try {
    // Získanie rezervácií, kde je používateľ vlastníkom hračky - vylúčenie zrušených/odmietnutých rezervácií
    const reservations = await prisma.reservation.findMany({
      where: {
        ownerId: user.id,
        status: { in: ['PENDING', 'CONFIRMED', 'ACTIVE', 'COMPLETED'] }, // Vylúčenie CANCELLED rezervácií
      },
      include: {
        toy: {
          include: {
            images: true,
          },
        },
        user: {
          select: {
            id: true,
            name: true,
            city: true,
          },
        },
      },
      orderBy: {
        createdAt: 'desc',
      },
    });

    // Transformácia dát - bezpečné hashované ID (odstránenie raw numeric IDs)
    const transformedReservations = reservations.map(reservation => {
      // Transformácia hračky
      const transformedToy = reservation.toy ? {
        ...reservation.toy,
        id: hashId(reservation.toy.id), // Použitie hashovaného ID ako primárne ID
        hashedId: hashId(reservation.toy.id), // Backward compatibility
        userId: hashId(reservation.toy.userId), // Hashované user ID
        hashedUserId: hashId(reservation.toy.userId), // Backward compatibility
        // Transformácia obrázkov hračky
        images: reservation.toy.images ? reservation.toy.images.map(image => ({
          ...image,
          id: hashId(image.id), // Hashované image ID
          hashedId: hashId(image.id), // Backward compatibility
          toyId: hashId(image.toyId), // Hashované toy ID
          hashedToyId: hashId(image.toyId), // Backward compatibility
        })) : [],
      } : null;

      // Transformácia používateľa, ktorý si rezervoval hračku
      const transformedUser = reservation.user ? {
        ...reservation.user,
        id: hashId(reservation.user.id), // Použitie hashovaného ID ako primárne ID
        hashedId: hashId(reservation.user.id), // Backward compatibility
      } : null;

      // Transformácia rezervácie - bezpečné hashované IDs
      return {
        ...reservation,
        id: hashId(reservation.id), // Použitie hashovaného ID ako primárne ID
        hashedId: hashId(reservation.id), // Backward compatibility
        userId: hashId(reservation.userId), // Hashované user ID
        hashedUserId: hashId(reservation.userId), // Backward compatibility
        toyId: hashId(reservation.toyId), // Hashované toy ID
        hashedToyId: hashId(reservation.toyId), // Backward compatibility
        ownerId: hashId(reservation.ownerId), // Hashované owner ID
        hashedOwnerId: hashId(reservation.ownerId), // Backward compatibility
        toy: transformedToy,
        user: transformedUser,
      };
    });

    return NextResponse.json(transformedReservations);
  } catch (error) {
    console.error('Chyba pri získavaní rezervácií vlastníka:', error);
    return NextResponse.json(
      { error: 'Nastala chyba pri získavaní rezervácií vlastníka' },
      { status: 500 }
    );
  }
}

// Export funkcie s overením autentifikácie
export const GET = withAuth(handleGetOwnerReservations);
