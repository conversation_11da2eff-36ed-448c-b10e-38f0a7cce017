import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '../../../lib/db';
import { withAuth } from '../../../lib/auth';
import { toNumericId, hashId, isHashedId } from '../../../lib/hashUtils';
import { sanitizeText, sanitizeObject } from '../../../lib/inputSanitization';
import { queueReservationCreatedEmail, queueReservationRequestEmail } from '../../../lib/emailNotificationService';

// Funkcia pre vytvorenie novej rezervácie
async function handleCreateReservation(request: NextRequest, user: any) {
  try {
    const rawData = await request.json();

    // Sanitizácia vstupných údajov
    const sanitizationResult = sanitizeObject(rawData, {
      toyId: (value) => sanitizeText(value, { maxLength: 20, allowSpecialChars: false })
    });

    if (!sanitizationResult.isValid) {
      return NextResponse.json(
        {
          error: 'Neplatné vstupné údaje',
          details: sanitizationResult.errors
        },
        { status: 400 }
      );
    }

    const { toyId } = sanitizationResult.sanitizedData;

    // Kontrola povinných údajov
    if (!toyId) {
      return NextResponse.json(
        { error: 'Chýba ID hračky' },
        { status: 400 }
      );
    }

    // Kontrola, či id je hashované
    if (!isHashedId(toyId)) {
      return NextResponse.json(
        { error: 'Neplatné ID hračky. Očakáva sa hashované ID.' },
        { status: 400 }
      );
    }

    // Konverzia hashovaného ID na číselné ID
    const numericToyId = toNumericId(toyId);

    if (numericToyId === null) {
      return NextResponse.json(
        { error: 'Neplatné ID hračky' },
        { status: 400 }
      );
    }

    // Získanie hračky z databázy
    const toy = await prisma.toy.findUnique({
      where: { id: numericToyId },
      include: {
        user: true,
      },
    });

    if (!toy) {
      return NextResponse.json(
        { error: 'Hračka nebola nájdená' },
        { status: 404 }
      );
    }

    // Kontrola, či je hračka úplne nedostupná
    if (toy.status === 'UNAVAILABLE' || toy.status === 'DRAFT') {
      return NextResponse.json(
        { error: 'Hračka nie je dostupná na rezerváciu' },
        { status: 400 }
      );
    }

    // Kontrola, či používateľ už nemá existujúcu aktívnu rezerváciu pre túto hračku
    const existingReservation = await prisma.reservation.findFirst({
      where: {
        toyId: toy.id,
        userId: user.id,
        status: { in: ['PENDING', 'CONFIRMED', 'ACTIVE'] }
      }
    });

    if (existingReservation) {
      return NextResponse.json(
        { error: 'Už máte aktívnu rezerváciu pre túto hračku' },
        { status: 400 }
      );
    }

    // Kontrola, či používateľ nerezervuje vlastnú hračku
    if (toy.userId === user.id) {
      return NextResponse.json(
        { error: 'Nemôžete si rezervovať vlastnú hračku' },
        { status: 400 }
      );
    }

    // Vytvorenie rezervácie
    const startDate = new Date();
    const endDate = new Date();

    // Použitie maxReservationDays z hračky alebo predvolenej hodnoty 7 dní
    let reservationDays = 7; // Predvolená hodnota
    try {
      // Skúsime pristúpiť k maxReservationDays, ak existuje v schéme
      if (toy.maxReservationDays !== undefined) {
        reservationDays = toy.maxReservationDays;
      }
    } catch (e) {
      console.log('maxReservationDays nie je podporovaný v aktuálnej schéme databázy, použitá predvolená hodnota 7 dní');
    }

    endDate.setDate(endDate.getDate() + reservationDays);

    // Zistenie počtu existujúcich aktívnych rezervácií pre túto hračku
    const activeReservations = await prisma.reservation.findMany({
      where: {
        toyId: toy.id,
        status: { in: ['PENDING', 'CONFIRMED', 'ACTIVE'] }
      },
      orderBy: {
        createdAt: 'asc'
      }
    });

    // Určenie pozície v čakacom zozname
    const position = activeReservations.length;

    // Vytvorenie rezervácie s pozíciou v čakacom zozname
    const reservation = await prisma.reservation.create({
      data: {
        startDate,
        endDate,
        status: 'PENDING',
        position: position,
        userId: user.id,
        toyId: toy.id,
        ownerId: toy.userId,
      },
    });

    // Aktualizácia statusu hračky na RESERVED len ak je to prvá rezervácia
    if (position === 0) {
      await prisma.toy.update({
        where: { id: toy.id },
        data: {
          status: 'RESERVED',
        },
      });
    }

    // Transformácia dát - bezpečné hashované ID (odstránenie raw numeric IDs)
    const transformedReservation = {
      ...reservation,
      id: hashId(reservation.id), // Použitie hashovaného ID ako primárne ID
      hashedId: hashId(reservation.id), // Backward compatibility
      userId: hashId(reservation.userId), // Hashované user ID
      hashedUserId: hashId(reservation.userId), // Backward compatibility
      toyId: hashId(reservation.toyId), // Hashované toy ID
      hashedToyId: hashId(reservation.toyId), // Backward compatibility
      ownerId: hashId(reservation.ownerId), // Hashované owner ID
      hashedOwnerId: hashId(reservation.ownerId), // Backward compatibility
    };

    // Vytvorenie správy na základe pozície v čakacom zozname
    let message = 'Rezervácia bola úspešne vytvorená';
    if (position > 0) {
      message = `Rezervácia bola úspešne vytvorená. Ste ${position + 1}. v poradí pre túto hračku.`;
    }

    // Odoslanie emailového potvrdenia používateľovi (asynchronne)
    try {
      const emailData = {
        userId: user.id,
        userName: user.name,
        userEmail: user.email,
        toyName: toy.name,
        reservationId: transformedReservation.hashedId,
        position: position,
        queueLength: activeReservations.length + 1
      };

      const emailId = queueReservationCreatedEmail(emailData);
      console.log(`Email notification queued for reservation ${transformedReservation.hashedId}: ${emailId}`);
    } catch (emailError) {
      // Email chyba by nemala ovplyvniť úspešnosť vytvorenia rezervácie
      console.error('Chyba pri odosielaní emailového potvrdenia:', emailError);
    }

    // Odoslanie notifikácie vlastníkovi hračky (asynchronne)
    try {
      if (toy.user && toy.user.email) {
        const ownerEmailData = {
          ownerId: toy.user.id,
          ownerName: toy.user.name,
          ownerEmail: toy.user.email,
          toyName: toy.name,
          reservationId: transformedReservation.hashedId,
          requesterName: user.name,
          requesterEmail: user.email,
          requesterPhone: user.phone,
          requesterCity: user.city,
          position: position,
          queueLength: activeReservations.length + 1
        };

        const ownerEmailId = queueReservationRequestEmail(ownerEmailData);
        console.log(`Owner notification queued for reservation ${transformedReservation.hashedId}: ${ownerEmailId}`);
      } else {
        console.warn(`Toy owner ${toy.userId} has no email address, skipping owner notification`);
      }
    } catch (emailError) {
      // Email chyba by nemala ovplyvniť úspešnosť vytvorenia rezervácie
      console.error('Chyba pri odosielaní notifikácie vlastníkovi:', emailError);
    }

    return NextResponse.json({
      message: message,
      reservation: transformedReservation,
      position: position,
      queueLength: activeReservations.length + 1
    });
  } catch (error) {
    console.error('Chyba pri vytváraní rezervácie:', error);
    return NextResponse.json(
      { error: 'Nastala chyba pri vytváraní rezervácie' },
      { status: 500 }
    );
  }
}

// Export funkcie s overením autentifikácie
export const POST = withAuth(handleCreateReservation);

// Funkcia pre získanie rezervácií používateľa
async function handleGetUserReservations(request: NextRequest, user: any) {
  try {
    // Získanie rezervácií používateľa - vylúčenie zrušených/odmietnutých rezervácií
    const reservations = await prisma.reservation.findMany({
      where: {
        userId: user.id,
        status: { in: ['PENDING', 'CONFIRMED', 'ACTIVE', 'COMPLETED'] }, // Vylúčenie CANCELLED rezervácií
      },
      include: {
        toy: {
          include: {
            images: true,
            user: true,
          },
        },
        owner: {
          select: {
            id: true,
            name: true,
            city: true,
          },
        },
      },
      orderBy: {
        createdAt: 'desc',
      },
    });

    // Transformácia dát - bezpečné hashované ID (odstránenie raw numeric IDs)
    const transformedReservations = reservations.map(reservation => {
      // Transformácia hračky
      const transformedToy = reservation.toy ? {
        ...reservation.toy,
        id: hashId(reservation.toy.id), // Použitie hashovaného ID ako primárne ID
        hashedId: hashId(reservation.toy.id), // Backward compatibility
        userId: hashId(reservation.toy.userId), // Hashované user ID
        hashedUserId: hashId(reservation.toy.userId), // Backward compatibility
        // Transformácia používateľa hračky
        user: reservation.toy.user ? {
          ...reservation.toy.user,
          id: hashId(reservation.toy.user.id), // Hashované user ID
          hashedId: hashId(reservation.toy.user.id), // Backward compatibility
        } : null,
        // Transformácia obrázkov hračky
        images: reservation.toy.images ? reservation.toy.images.map(image => ({
          ...image,
          id: hashId(image.id), // Hashované image ID
          hashedId: hashId(image.id), // Backward compatibility
          toyId: hashId(image.toyId), // Hashované toy ID
          hashedToyId: hashId(image.toyId), // Backward compatibility
        })) : [],
      } : null;

      // Transformácia vlastníka
      const transformedOwner = reservation.owner ? {
        ...reservation.owner,
        id: hashId(reservation.owner.id), // Hashované owner ID
        hashedId: hashId(reservation.owner.id), // Backward compatibility
      } : null;

      // Transformácia rezervácie - bezpečné hashované IDs
      return {
        ...reservation,
        id: hashId(reservation.id), // Použitie hashovaného ID ako primárne ID
        hashedId: hashId(reservation.id), // Backward compatibility
        userId: hashId(reservation.userId), // Hashované user ID
        hashedUserId: hashId(reservation.userId), // Backward compatibility
        toyId: hashId(reservation.toyId), // Hashované toy ID
        hashedToyId: hashId(reservation.toyId), // Backward compatibility
        ownerId: hashId(reservation.ownerId), // Hashované owner ID
        hashedOwnerId: hashId(reservation.ownerId), // Backward compatibility
        toy: transformedToy,
        owner: transformedOwner,
      };
    });

    return NextResponse.json(transformedReservations);
  } catch (error) {
    console.error('Chyba pri získavaní rezervácií:', error);
    return NextResponse.json(
      { error: 'Nastala chyba pri získavaní rezervácií' },
      { status: 500 }
    );
  }
}

// Export funkcie s overením autentifikácie
export const GET = withAuth(handleGetUserReservations);
