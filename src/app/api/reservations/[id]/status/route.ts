import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '../../../../../lib/db';
import { withAuth } from '../../../../../lib/auth';
import { toNumericId, hashId, isHashedId } from '../../../../../lib/hashUtils';
import { ReservationStatus } from '../../../../../lib/constants';
import { queueReservationApprovedEmail } from '../../../../../lib/emailNotificationService';

// Funkcia pre aktualizáciu stavu rezervácie
async function handleUpdateReservationStatus(
  request: NextRequest,
  authenticatedUser: any,
  params: { id: string }
) {
  try {
    const idParam = params.id;
    const { status } = await request.json();

    // Kontrola, či id je hashované
    if (!isHashedId(idParam)) {
      return NextResponse.json(
        { error: 'Neplatné ID rezervácie. Očakáva sa hashované ID.' },
        { status: 400 }
      );
    }

    // Konverzia hashovaného ID na číselné ID
    const reservationId = toNumericId(idParam);

    if (reservationId === null) {
      return NextResponse.json(
        { error: 'Neplatné ID rezervácie' },
        { status: 400 }
      );
    }

    // Kontrola, či je poskytnutý status
    if (!status) {
      return NextResponse.json(
        { error: 'Chýba status rezervácie' },
        { status: 400 }
      );
    }

    // Kontrola, či je status platný
    if (!Object.values(ReservationStatus).includes(status)) {
      return NextResponse.json(
        { error: 'Neplatný status rezervácie' },
        { status: 400 }
      );
    }

    // Získanie rezervácie z databázy
    const reservation = await prisma.reservation.findUnique({
      where: { id: reservationId },
      include: {
        toy: true,
      },
    });

    if (!reservation) {
      return NextResponse.json(
        { error: 'Rezervácia nebola nájdená' },
        { status: 404 }
      );
    }

    // Kontrola oprávnení:
    // - Vlastník hračky môže meniť status rezervácie (potvrdiť/odmietnuť)
    // - Používateľ môže zrušiť svoju vlastnú rezerváciu (len na CANCELLED)
    const isOwner = reservation.ownerId === authenticatedUser.id;
    const isReservationUser = reservation.userId === authenticatedUser.id;
    const isCancellation = status === 'CANCELLED';

    if (!isOwner && !(isReservationUser && isCancellation)) {
      return NextResponse.json(
        { error: 'Nemáte oprávnenie upraviť túto rezerváciu' },
        { status: 403 }
      );
    }

    // Aktualizácia stavu rezervácie
    const updatedReservation = await prisma.reservation.update({
      where: { id: reservationId },
      data: { status },
      include: {
        toy: true,
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            city: true,
          },
        },
        owner: {
          select: {
            id: true,
            name: true,
            email: true,
            phone: true,
            city: true,
          },
        },
      },
    });

    // Aktualizácia statusu hračky a pozícií v čakacom zozname, ak je to potrebné
    if (status === 'CONFIRMED') {
      await prisma.toy.update({
        where: { id: reservation.toyId },
        data: { status: 'RESERVED' },
      });
    } else if (status === 'ACTIVE') {
      await prisma.toy.update({
        where: { id: reservation.toyId },
        data: { status: 'RESERVED' },
      });
    } else if (status === 'COMPLETED' || status === 'CANCELLED') {
      // Získanie všetkých aktívnych rezervácií pre túto hračku zoradených podľa pozície
      const activeReservations = await prisma.reservation.findMany({
        where: {
          toyId: reservation.toyId,
          status: { in: ['PENDING', 'CONFIRMED', 'ACTIVE'] },
          id: { not: reservationId } // Vylúčime aktuálnu rezerváciu, ktorá sa práve ruší
        },
        orderBy: {
          position: 'asc'
        }
      });

      // Ak existujú ďalšie rezervácie, posunieme ich v poradí
      if (activeReservations.length > 0) {
        // Aktualizácia pozícií všetkých rezervácií
        for (let i = 0; i < activeReservations.length; i++) {
          await prisma.reservation.update({
            where: { id: activeReservations[i].id },
            data: { position: i }
          });
        }

        // Ak existuje rezervácia na pozícii 0, nastavíme status hračky na RESERVED
        if (activeReservations.length > 0) {
          await prisma.toy.update({
            where: { id: reservation.toyId },
            data: { status: 'RESERVED' },
          });
        }
      } else {
        // Ak neexistujú ďalšie rezervácie, nastavíme status hračky na AVAILABLE
        await prisma.toy.update({
          where: { id: reservation.toyId },
          data: { status: 'AVAILABLE' },
        });
      }
    }

    // Transformácia dát - bezpečné hashované ID (odstránenie raw numeric IDs)
    const transformedReservation = {
      ...updatedReservation,
      id: hashId(updatedReservation.id), // Použitie hashovaného ID ako primárne ID
      hashedId: hashId(updatedReservation.id), // Backward compatibility
      userId: hashId(updatedReservation.userId), // Hashované user ID
      hashedUserId: hashId(updatedReservation.userId), // Backward compatibility
      toyId: hashId(updatedReservation.toyId), // Hashované toy ID
      hashedToyId: hashId(updatedReservation.toyId), // Backward compatibility
      ownerId: hashId(updatedReservation.ownerId), // Hashované owner ID
      hashedOwnerId: hashId(updatedReservation.ownerId), // Backward compatibility
      toy: updatedReservation.toy ? {
        ...updatedReservation.toy,
        id: hashId(updatedReservation.toy.id), // Hashované toy ID
        hashedId: hashId(updatedReservation.toy.id), // Backward compatibility
      } : null,
      user: updatedReservation.user ? {
        ...updatedReservation.user,
        id: hashId(updatedReservation.user.id), // Hashované user ID
        hashedId: hashId(updatedReservation.user.id), // Backward compatibility
      } : null,
      owner: updatedReservation.owner ? {
        ...updatedReservation.owner,
        id: hashId(updatedReservation.owner.id), // Hashované owner ID
        hashedId: hashId(updatedReservation.owner.id), // Backward compatibility
      } : null,
    };

    // Odoslanie emailového potvrdenia pri schválení rezervácie (asynchronne)
    if (status === 'CONFIRMED' && updatedReservation.user && updatedReservation.toy && updatedReservation.owner) {
      try {
        const emailData = {
          userId: updatedReservation.user.id,
          userName: updatedReservation.user.name,
          userEmail: updatedReservation.user.email,
          toyName: updatedReservation.toy.name,
          reservationId: transformedReservation.hashedId,
          ownerName: updatedReservation.owner.name,
          ownerEmail: updatedReservation.owner.email,
          ownerPhone: updatedReservation.owner.phone,
          ownerCity: updatedReservation.owner.city,
        };

        const emailId = queueReservationApprovedEmail(emailData);
        console.log(`Approval email notification queued for reservation ${transformedReservation.hashedId}: ${emailId}`);
      } catch (emailError) {
        // Email chyba by nemala ovplyvniť úspešnosť aktualizácie rezervácie
        console.error('Chyba pri odosielaní emailového potvrdenia o schválení:', emailError);
      }
    }

    return NextResponse.json({
      message: `Status rezervácie bol úspešne aktualizovaný na ${status}`,
      reservation: transformedReservation,
    });
  } catch (error) {
    console.error('Chyba pri aktualizácii stavu rezervácie:', error);
    return NextResponse.json(
      { error: 'Nastala chyba pri aktualizácii stavu rezervácie' },
      { status: 500 }
    );
  }
}

// Export funkcie s overením autentifikácie
export async function PUT(request: NextRequest, context: any) {
  const params = context.params;
  return withAuth((req, user) => handleUpdateReservationStatus(req, user, params))(request);
}
