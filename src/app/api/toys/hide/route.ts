import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '../../../../lib/db';
import { withAuth } from '../../../../lib/auth';
import { toNumericId, isHashedId, hashId } from '../../../../lib/hashUtils';

// Funkcia pre skrytie hračky pre prihláseného používateľa
async function handleHideToy(request: NextRequest, user: any) {
  try {
    const data = await request.json();
    const toyId = data.toyId;

    // Kontrola povinných údajov
    if (!toyId) {
      return NextResponse.json(
        { error: 'Chýba ID hračky' },
        { status: 400 }
      );
    }

    // Kontrola, či id je hashované
    if (!isHashedId(toyId)) {
      return NextResponse.json(
        { error: 'Neplatné ID hračky. Očakáva sa hashované ID.' },
        { status: 400 }
      );
    }

    // Konverzia hashovaného ID na číselné ID
    const numericToyId = toNumericId(toyId);

    if (numericToyId === null) {
      return NextResponse.json(
        { error: 'Neplatné ID hračky' },
        { status: 400 }
      );
    }

    // Kontrola, či hračka existuje
    const toy = await prisma.toy.findUnique({
      where: { id: numericToyId },
    });

    if (!toy) {
      return NextResponse.json(
        { error: 'Hračka nebola nájdená' },
        { status: 404 }
      );
    }

    // Kontrola, či používateľ neskrýva vlastnú hračku
    if (toy.userId === user.id) {
      return NextResponse.json(
        { error: 'Nemôžete skryť vlastnú hračku' },
        { status: 400 }
      );
    }

    // Kontrola, či už hračka nie je skrytá
    const existingHiddenToy = await prisma.hiddenToy.findFirst({
      where: {
        userId: user.id,
        toyId: numericToyId,
      },
    });

    if (existingHiddenToy) {
      return NextResponse.json(
        { error: 'Hračka je už skrytá' },
        { status: 400 }
      );
    }

    // Skrytie hračky
    const hiddenToy = await prisma.hiddenToy.create({
      data: {
        userId: user.id,
        toyId: numericToyId,
      },
    });

    // Transformácia dát - pridanie hashovaných ID
    const transformedHiddenToy = {
      ...hiddenToy,
      id: hiddenToy.id,
      hashedId: hashId(hiddenToy.id),
      userId: hiddenToy.userId,
      hashedUserId: hashId(hiddenToy.userId),
      toyId: hiddenToy.toyId,
      hashedToyId: hashId(hiddenToy.toyId)
    };

    return NextResponse.json({
      message: 'Hračka bola úspešne skrytá',
      hiddenToy: transformedHiddenToy,
    });
  } catch (error) {
    console.error('Chyba pri skrývaní hračky:', error);
    return NextResponse.json(
      { error: 'Nastala chyba pri skrývaní hračky' },
      { status: 500 }
    );
  }
}

// Funkcia pre zobrazenie skrytej hračky
async function handleUnhideToy(request: NextRequest, user: any) {
  try {
    const data = await request.json();
    const toyId = data.toyId;

    // Kontrola povinných údajov
    if (!toyId) {
      return NextResponse.json(
        { error: 'Chýba ID hračky' },
        { status: 400 }
      );
    }

    // Kontrola, či id je hashované
    if (!isHashedId(toyId)) {
      return NextResponse.json(
        { error: 'Neplatné ID hračky. Očakáva sa hashované ID.' },
        { status: 400 }
      );
    }

    // Konverzia hashovaného ID na číselné ID
    const numericToyId = toNumericId(toyId);

    if (numericToyId === null) {
      return NextResponse.json(
        { error: 'Neplatné ID hračky' },
        { status: 400 }
      );
    }

    // Odstránenie skrytej hračky
    const deletedHiddenToy = await prisma.hiddenToy.deleteMany({
      where: {
        userId: user.id,
        toyId: numericToyId,
      },
    });

    if (deletedHiddenToy.count === 0) {
      return NextResponse.json(
        { error: 'Hračka nebola skrytá alebo neexistuje' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      message: 'Hračka bola úspešne zobrazená',
      count: deletedHiddenToy.count,
    });
  } catch (error) {
    console.error('Chyba pri zobrazovaní hračky:', error);
    return NextResponse.json(
      { error: 'Nastala chyba pri zobrazovaní hračky' },
      { status: 500 }
    );
  }
}

// Export funkcií s overením autentifikácie
export const POST = withAuth(handleHideToy);
export const DELETE = withAuth(handleUnhideToy);
