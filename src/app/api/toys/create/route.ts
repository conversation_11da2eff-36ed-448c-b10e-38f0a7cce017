import { NextRequest, NextResponse } from 'next/server';
import { createToy, createToyImage } from '../../../../lib/db';
import { prisma } from '../../../../lib/db';
import { withAuthAndCsrf } from '../../../../lib/auth';
import { isValidToyStatus } from '../../../../lib/toyStatusUtils';

// Funkcia pre spracovanie požiadavky s overením autentifikácie
async function handleCreateToy(request: NextRequest, user: any) {
  try {
    const data = await request.json();

    // Kontrola povinných údajov
    if (!data.name || !data.description || !data.type || !data.price || !data.deposit || !data.priceType) {
      return NextResponse.json(
        { error: 'Chýbajú povinné údaje' },
        { status: 400 }
      );
    }

    // Kontrola typu ceny
    if (!['PER_DAY', 'PER_RENTAL'].includes(data.priceType)) {
      return NextResponse.json(
        { error: 'Neplatný typ ceny' },
        { status: 400 }
      );
    }

    // Overenie, či používateľ vytvára hračku pre seba
    // userId musí byť ID prihláseného používateľa
    if (data.userId && parseInt(data.userId) !== user.id) {
      return NextResponse.json(
        { error: 'Nemáte oprávnenie vytvoriť hračku pre iného používateľa' },
        { status: 403 }
      );
    }

    // Nastavenie userId na ID prihláseného používateľa
    data.userId = user.id;

    // Kontrola typu hračky - fetch valid types from database
    const validTypes = await prisma.toyType.findMany();
    if (!validTypes.map(t => t.name).includes(data.type)) {
      return NextResponse.json(
        { error: 'Neplatný typ hračky' },
        { status: 400 }
      );
    }

    // Kontrola statusu hračky (ak je poskytnutý) - overenie, či status existuje v databáze
    if (data.status) {
      const isValid = await isValidToyStatus(data.status);
      if (!isValid) {
        return NextResponse.json(
          { error: 'Neplatný status hračky' },
          { status: 400 }
        );
      }
    }

    // Vytvorenie hračky
    const toy = await createToy({
      name: data.name,
      description: data.description,
      type: data.type,
      price: parseFloat(data.price),
      deposit: parseFloat(data.deposit),
      priceType: data.priceType,
      status: data.status || 'AVAILABLE',
      maxReservationDays: data.maxReservationDays || 7,
      userId: parseInt(data.userId),
      // locationId už nie je potrebné, používame lokalitu používateľa
    });

    // Pridanie obrázkov (ak sú poskytnuté)
    if (data.images && Array.isArray(data.images) && data.images.length > 0) {
      for (const imageUrl of data.images) {
        await createToyImage(toy.id, imageUrl);
      }
    }

    return NextResponse.json(toy);
  } catch (error) {
    console.error('Chyba pri vytváraní hračky:', error);
    return NextResponse.json(
      { error: 'Nastala chyba pri vytváraní hračky' },
      { status: 500 }
    );
  }
}

// Export funkcie s overením autentifikácie a CSRF ochranou
export const POST = withAuthAndCsrf(handleCreateToy);
