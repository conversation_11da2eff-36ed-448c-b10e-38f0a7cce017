import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '../../../../lib/db';
import { withAuth } from '../../../../lib/auth';

// Funkcia pre publikovanie hračky (zmena statusu z DRAFT na AVAILABLE)
async function handlePublishToy(request: NextRequest, user: any) {
  try {
    const data = await request.json();

    // Kontrola, či je poskytnuté ID hračky
    if (!data.id) {
      return NextResponse.json(
        { error: 'Chýba ID hračky' },
        { status: 400 }
      );
    }

    const toyId = parseInt(data.id);

    // Kontrola, či hračka existuje
    const existingToy = await prisma.toy.findUnique({
      where: { id: toyId },
    });

    if (!existingToy) {
      return NextResponse.json(
        { error: 'Hračka nebola nájdená' },
        { status: 404 }
      );
    }

    // <PERSON><PERSON><PERSON><PERSON>, či p<PERSON> je vlastníkom hračky
    if (existingToy.userId !== user.id) {
      return NextResponse.json(
        { error: 'Nemáte oprávnenie publikovať túto hračku' },
        { status: 403 }
      );
    }

    // Kontrola, či je hračka v stave DRAFT
    if (existingToy.status !== 'DRAFT') {
      return NextResponse.json(
        { error: 'Hračku je možné publikovať iba ak je v stave Nepublikovaná' },
        { status: 400 }
      );
    }

    // Aktualizácia statusu hračky na AVAILABLE
    const updatedToy = await prisma.toy.update({
      where: { id: toyId },
      data: {
        status: 'AVAILABLE',
      },
      include: {
        user: true,
      },
    });

    return NextResponse.json({
      message: 'Hračka bola úspešne publikovaná',
      toy: updatedToy
    });
  } catch (error) {
    console.error('Chyba pri publikovaní hračky:', error);
    return NextResponse.json(
      { error: 'Nastala chyba pri publikovaní hračky' },
      { status: 500 }
    );
  }
}

// Export funkcie s overením autentifikácie
export const POST = withAuth(handlePublishToy);
