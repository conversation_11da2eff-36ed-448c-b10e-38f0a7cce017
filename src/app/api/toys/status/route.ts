import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '../../../../lib/db';
import { withAuth } from '../../../../lib/auth';
import { isValidToyStatus } from '../../../../lib/toyStatusUtils';

// Funkcia pre aktualizáciu statusu hračky
async function handleUpdateToyStatus(request: NextRequest, user: any) {
  try {
    const data = await request.json();

    // Kontrola, či je poskytnuté ID hračky
    if (!data.id) {
      return NextResponse.json(
        { error: 'Chýba ID hračky' },
        { status: 400 }
      );
    }

    // Kontrola, či je poskytnutý status
    if (!data.status) {
      return NextResponse.json(
        { error: 'Chýba status hračky' },
        { status: 400 }
      );
    }

    const toyId = parseInt(data.id);

    // Ko<PERSON><PERSON><PERSON>, či hračka existuje
    const existingToy = await prisma.toy.findUnique({
      where: { id: toyId },
    });

    if (!existingToy) {
      return NextResponse.json(
        { error: 'Hračka nebola nájdená' },
        { status: 404 }
      );
    }

    // Kontrola, či používateľ je vlastníkom hračky
    if (existingToy.userId !== user.id) {
      return NextResponse.json(
        { error: 'Nemáte oprávnenie upraviť túto hračku' },
        { status: 403 }
      );
    }

    // Kontrola statusu hračky - overenie, či status existuje v databáze
    const isValid = await isValidToyStatus(data.status);
    if (!isValid) {
      return NextResponse.json(
        { error: 'Neplatný status hračky' },
        { status: 400 }
      );
    }

    // Aktualizácia statusu hračky
    const updatedToy = await prisma.toy.update({
      where: { id: toyId },
      data: {
        status: data.status,
      },
      include: {
        user: true,
      },
    });

    return NextResponse.json(updatedToy);
  } catch (error) {
    console.error('Chyba pri aktualizácii statusu hračky:', error);
    return NextResponse.json(
      { error: 'Nastala chyba pri aktualizácii statusu hračky' },
      { status: 500 }
    );
  }
}

// Export funkcie s overením autentifikácie
export const POST = withAuth(handleUpdateToyStatus);
