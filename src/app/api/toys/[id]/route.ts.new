import { NextRequest, NextResponse } from 'next/server';
import { getToyById } from '../../../../lib/db';

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  // Bezpečné získanie ID
  const idParam = params?.id;
  if (!idParam) {
    return NextResponse.json(
      { error: 'Chýba ID hračky' },
      { status: 400 }
    );
  }

  const id = parseInt(idParam);

  try {
    if (isNaN(id)) {
      return NextResponse.json(
        { error: 'Neplatné ID hračky' },
        { status: 400 }
      );
    }

    const toy = await getToyById(id);

    if (!toy) {
      return NextResponse.json(
        { error: 'Hračka nebola nájdená' },
        { status: 404 }
      );
    }

    // Transformácia dát do formátu, ktorý očaká<PERSON> frontend
    const transformedToy = {
      id: toy.id,
      name: toy.name,
      description: toy.description,
      images: toy.images.length > 0
        ? toy.images.map(img => img.url)
        : ['/toys/placeholder.jpg'],
      type: toy.type,
      location: {
        city: toy.location.city,
        postalCode: toy.location.postalCode
      },
      price: toy.price,
      deposit: toy.deposit,
      status: toy.status,
      createdAt: toy.createdAt,
      updatedAt: toy.updatedAt,
      owner: {
        id: toy.user.id,
        name: toy.user.name,
        email: toy.user.email,
        phone: toy.user.phone,
        city: toy.user.city,
        postalCode: toy.user.postalCode
      }
    };

    return NextResponse.json(transformedToy);
  } catch (error) {
    console.error('Chyba pri získavaní detailu hračky:', error);
    return NextResponse.json(
      { error: 'Nastala chyba pri získavaní detailu hračky' },
      { status: 500 }
    );
  }
}
