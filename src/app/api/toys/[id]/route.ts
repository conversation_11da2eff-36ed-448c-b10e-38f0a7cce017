import { NextRequest, NextResponse } from 'next/server';
import { getToyById, prisma } from '../../../../lib/db';
import { toNumericId, hashId, isHashedId } from '../../../../lib/hashUtils';
import { verifyAuth } from '../../../../lib/auth';
import { anonymizeName } from '../../../../lib/nameUtils';

export async function GET(request: NextRequest) {
  // Bezpečné získanie ID
  const url = new URL(request.url);
  const pathParts = url.pathname.split('/');
  const idParam = pathParts[pathParts.length - 1];
  if (!idParam) {
    return NextResponse.json(
      { error: 'Chýba ID hračky' },
      { status: 400 }
    );
  }

  // Kontrola, či id je hashované
  if (!isHashedId(idParam)) {
    return NextResponse.json(
      { error: 'Neplatné ID hračky. Očakáva sa hashované ID.' },
      { status: 400 }
    );
  }

  // Konverzia hashovaného ID na číselné ID
  const id = toNumericId(idParam);

  try {
    if (id === null) {
      return NextResponse.json(
        { error: 'Neplatné ID hračky' },
        { status: 400 }
      );
    }

    const toy = await getToyById(id);

    if (!toy) {
      return NextResponse.json(
        { error: 'Hračka nebola nájdená' },
        { status: 404 }
      );
    }

    // Overenie autentifikácie používateľa
    const authenticatedUser = await verifyAuth(request);
    const isAdmin = authenticatedUser ? authenticatedUser.role === 'ADMIN' : false;

    const isOwner = authenticatedUser && authenticatedUser.id === toy.user.id;

    // Kontrola, či vlastník hračky nie je zablokovaný (okrem prípadu, keď je to vlastník alebo admin)
    if (toy.user.status === 'BLOCKED' && !isOwner && !isAdmin) {
      return NextResponse.json(
        { error: 'Hračka nie je dostupná' },
        { status: 404 }
      );
    }

    // Kontrola, či hračka nie je v stave DRAFT (okrem prípadu, keď je to vlastník alebo admin)
    if (toy.status === 'DRAFT' && !isOwner && !isAdmin) {
      return NextResponse.json(
        { error: 'Hračka nie je dostupná' },
        { status: 404 }
      );
    }

    // Debug logging pre server-side data
    console.log('=== SERVER-SIDE TOY DATA DEBUG ===');
    console.log('toy.id:', toy.id);
    console.log('toy.user:', toy.user);
    console.log('toy.user.id:', toy.user?.id);
    console.log('hashId(toy.user.id):', toy.user?.id ? hashId(toy.user.id) : 'undefined');
    console.log('===================================');

    // Transformácia dát do formátu, ktorý očakáva frontend
    const transformedToy: any = {
      id: hashId(toy.id), // Hashujeme ID pre frontend
      name: toy.name,
      description: toy.description,
      images: toy.images.length > 0
        ? toy.images.map(img => img.url)
        : ['/toys/placeholder.jpg'],
      type: toy.type,
      location: {
        city: toy.user.city || 'Neuvedené',
        postalCode: toy.user.postalCode || ''
      },
      price: toy.price,
      deposit: toy.deposit,
      status: toy.status,
      createdAt: toy.createdAt,
      updatedAt: toy.updatedAt,
      owner: {
        hashedId: toy.user?.id ? hashId(toy.user.id) : null,
        name: toy.user?.name ? anonymizeName(toy.user.name) : 'Neznámy používateľ',
        city: toy.user?.city || null
      }
    };

    // Debug logging pre transformed data
    console.log('=== TRANSFORMED TOY DATA DEBUG ===');
    console.log('transformedToy.owner:', transformedToy.owner);
    console.log('===================================');

    // Pridáme maxReservationDays, ak existuje v schéme
    try {
      if ((toy as any).maxReservationDays !== undefined) {
        transformedToy.maxReservationDays = (toy as any).maxReservationDays;
      } else {
        transformedToy.maxReservationDays = 7; // Predvolená hodnota
      }
    } catch (e) {
      transformedToy.maxReservationDays = 7; // Predvolená hodnota
      console.log('maxReservationDays nie je podporovaný v aktuálnej schéme databázy, použitá predvolená hodnota 7 dní');
    }

    return NextResponse.json(transformedToy);
  } catch (error) {
    console.error('Chyba pri získavaní detailu hračky:', error);
    return NextResponse.json(
      { error: 'Nastala chyba pri získavaní detailu hračky' },
      { status: 500 }
    );
  }
}
