'use client';

import { useEffect, useState } from 'react';
import { useAuth } from '../../contexts/AuthContext';
import { useReservationNotificationContext } from '../../contexts/ReservationNotificationContext';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import LoadingSpinner from '../../components/LoadingSpinner';
import { formatToyPricePerDay } from '../../lib/priceUtils';
import { generateToyUrl } from '../../lib/seoUtils';

// Typy pre rezervácie
interface ToyImage {
  id: string; // Now hashed ID as primary ID
  hashedId: string; // Backward compatibility
  url: string;
  toyId: string; // Now hashed ID as primary ID
  hashedToyId: string; // Backward compatibility
}

interface ToyOwner {
  id: string; // Now hashed ID as primary ID
  hashedId: string; // Backward compatibility
  name: string;
  city?: string;
}

interface Toy {
  id: string; // Now hashed ID as primary ID
  hashedId: string; // Backward compatibility
  name: string;
  description: string;
  type: string;
  price: number;
  deposit: number;
  priceType?: string; // Typ ceny (PER_DAY alebo PER_RENTAL)
  status: string;
  images: ToyImage[];
  userId: string; // Now hashed ID as primary ID
  hashedUserId: string; // Backward compatibility
  user?: ToyOwner;
}

interface ReservationUser {
  id: string; // Now hashed ID as primary ID
  hashedId: string; // Backward compatibility
  name: string;
  city?: string;
}

interface UserContact {
  id: string; // Hashed ID as primary ID
  name: string;
  email: string;
  phone: string | null;
  address: string | null;
  city: string | null;
  postalCode: string | null;
}

interface Reservation {
  id: string; // Now hashed ID as primary ID
  hashedId: string; // Backward compatibility
  startDate: string;
  endDate: string;
  status: string;
  position: number; // Pozícia v čakacom zozname
  createdAt: string;
  updatedAt: string;
  userId: string; // Now hashed ID as primary ID
  hashedUserId: string; // Backward compatibility
  toyId: string; // Now hashed ID as primary ID
  hashedToyId: string; // Backward compatibility
  ownerId: string; // Now hashed ID as primary ID
  hashedOwnerId: string; // Backward compatibility
  toy: Toy;
  user?: ReservationUser;
  owner?: ToyOwner;
}

export default function ReservationsPage() {
  const { user } = useAuth();
  const { refresh: refreshNotifications } = useReservationNotificationContext();
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(true);
  const [myReservations, setMyReservations] = useState<Reservation[]>([]);
  const [ownerReservations, setOwnerReservations] = useState<Reservation[]>([]);
  const [error, setError] = useState<string | null>(null);
  const [userContacts, setUserContacts] = useState<Record<string, UserContact>>({});
  const [contactsLoading, setContactsLoading] = useState<Record<string, boolean>>({});
  const [contactsError, setContactsError] = useState<Record<string, string>>({});
  const [confirmLoading, setConfirmLoading] = useState<Record<string, boolean>>({});
  const [rejectLoading, setRejectLoading] = useState<Record<string, boolean>>({});
  const [cancelLoading, setCancelLoading] = useState<Record<string, boolean>>({});

  // Presmerovanie na domovskú stránku, ak používateľ nie je prihlásený
  useEffect(() => {
    if (!user && !isLoading) {
      router.push('/');
    }
  }, [user, router, isLoading]);

  // Načítanie rezervácií používateľa
  useEffect(() => {
    async function loadReservations() {
      if (!user || !user.hashedUserId) {
        setIsLoading(false);
        return;
      }

      try {
        // Získanie Firebase tokenu pre autentifikáciu
        if (!user.getIdToken) {
          throw new Error('Chyba pri získavaní autentifikačného tokenu');
        }
        const token = await user.getIdToken();

        // Načítanie rezervácií používateľa
        const myReservationsResponse = await fetch('/api/reservations', {
          headers: {
            'Authorization': `Bearer ${token}`,
          },
        });

        if (!myReservationsResponse.ok) {
          throw new Error('Nepodarilo sa načítať moje rezervácie');
        }

        const myReservationsData = await myReservationsResponse.json();
        setMyReservations(myReservationsData);

        // Načítanie rezervácií, kde je používateľ vlastníkom hračky
        const ownerReservationsResponse = await fetch('/api/reservations/owner', {
          headers: {
            'Authorization': `Bearer ${token}`,
          },
        });

        if (!ownerReservationsResponse.ok) {
          throw new Error('Nepodarilo sa načítať rezervácie mojich hračiek');
        }

        const ownerReservationsData = await ownerReservationsResponse.json();
        setOwnerReservations(ownerReservationsData);
      } catch (err) {
        setError('Nastala chyba pri načítaní rezervácií');
        console.error('Chyba pri načítaní rezervácií:', err);
      } finally {
        setIsLoading(false);
      }
    }

    if (user) {
      loadReservations();
    } else {
      setIsLoading(false);
    }
  }, [user]);

  // Funkcia pre potvrdenie rezervácie
  const confirmReservation = async (reservationId: string) => {
    if (!user || !user.hashedUserId) {
      return;
    }

    // Nastavenie stavu načítavania pre konkrétnu rezerváciu
    setConfirmLoading(prev => ({ ...prev, [reservationId]: true }));

    try {
      // Prepare headers - using only Bearer token authentication
      const headers: Record<string, string> = {
        'Content-Type': 'application/json',
      };

      // Add authorization token if available
      if (typeof user.getIdToken === 'function') {
        headers['Authorization'] = `Bearer ${await user.getIdToken()}`;
      }

      const response = await fetch(`/api/reservations/${reservationId}/status`, {
        method: 'PUT',
        headers,
        body: JSON.stringify({
          status: 'CONFIRMED',
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Nepodarilo sa potvrdiť rezerváciu');
      }

      // Aktualizácia zoznamu rezervácií
      const updatedReservation = await response.json();

      // Aktualizácia zoznamu rezervácií vlastníka
      setOwnerReservations(prev =>
        prev.map(res => res.hashedId === reservationId ? { ...res, status: 'CONFIRMED' } : res)
      );

      // Obnovenie notifikácií pre okamžitú aktualizáciu UI
      refreshNotifications();

    } catch (err) {
      console.error('Chyba pri potvrdzovaní rezervácie:', err);
      alert(err instanceof Error ? err.message : 'Nepodarilo sa potvrdiť rezerváciu');
    } finally {
      setConfirmLoading(prev => ({ ...prev, [reservationId]: false }));
    }
  };

  // Funkcia pre odmietnutie rezervácie
  const rejectReservation = async (reservationId: string) => {
    if (!user || !user.hashedUserId) {
      return;
    }

    // Nastavenie stavu načítavania pre konkrétnu rezerváciu
    setRejectLoading(prev => ({ ...prev, [reservationId]: true }));

    try {
      // Prepare headers
      const headers: Record<string, string> = {
        'Content-Type': 'application/json',
      };

      // Add authorization token if available
      if (typeof user.getIdToken === 'function') {
        headers['Authorization'] = `Bearer ${await user.getIdToken()}`;
      }

      const response = await fetch(`/api/reservations/${reservationId}/status`, {
        method: 'PUT',
        headers,
        body: JSON.stringify({
          status: 'CANCELLED',
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Nepodarilo sa odmietnuť rezerváciu');
      }

      // Aktualizácia zoznamu rezervácií
      const updatedReservation = await response.json();

      // Aktualizácia zoznamu rezervácií vlastníka
      setOwnerReservations(prev =>
        prev.map(res => res.hashedId === reservationId ? { ...res, status: 'CANCELLED' } : res)
      );

      // Odstránenie rezervácie zo zoznamu používateľa, ak je to jeho rezervácia
      setMyReservations(prev =>
        prev.filter(res => res.hashedId !== reservationId)
      );

      // Obnovenie notifikácií pre okamžitú aktualizáciu UI
      refreshNotifications();

    } catch (err) {
      console.error('Chyba pri odmietaní rezervácie:', err);
      alert(err instanceof Error ? err.message : 'Nepodarilo sa odmietnuť rezerváciu');
    } finally {
      setRejectLoading(prev => ({ ...prev, [reservationId]: false }));
    }
  };

  // Funkcia pre zrušenie potvrdenej rezervácie
  const cancelReservation = async (reservationId: string) => {
    if (!user || !user.hashedUserId) {
      return;
    }

    // Nastavenie stavu načítavania pre konkrétnu rezerváciu
    setCancelLoading(prev => ({ ...prev, [reservationId]: true }));

    try {
      // Prepare headers
      const headers: Record<string, string> = {
        'Content-Type': 'application/json',
      };

      // Add authorization token if available
      if (typeof user.getIdToken === 'function') {
        headers['Authorization'] = `Bearer ${await user.getIdToken()}`;
      }

      const response = await fetch(`/api/reservations/${reservationId}/status`, {
        method: 'PUT',
        headers,
        body: JSON.stringify({
          status: 'CANCELLED',
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Nepodarilo sa zrušiť rezerváciu');
      }

      // Aktualizácia zoznamu rezervácií
      const updatedReservation = await response.json();

      // Aktualizácia zoznamu rezervácií vlastníka
      setOwnerReservations(prev =>
        prev.map(res => res.hashedId === reservationId ? { ...res, status: 'CANCELLED' } : res)
      );

      // Odstránenie rezervácie zo zoznamu používateľa
      setMyReservations(prev =>
        prev.filter(res => res.hashedId !== reservationId)
      );

      // Obnovenie notifikácií pre okamžitú aktualizáciu UI
      refreshNotifications();

    } catch (err) {
      console.error('Chyba pri rušení rezervácie:', err);
      alert(err instanceof Error ? err.message : 'Nepodarilo sa zrušiť rezerváciu');
    } finally {
      setCancelLoading(prev => ({ ...prev, [reservationId]: false }));
    }
  };

  // Funkcia pre načítanie kontaktných údajov používateľa
  const loadUserContact = async (userId: string) => {
    if (!user || !user.hashedUserId) {
      return;
    }

    // Ak už máme kontaktné údaje, nemusíme ich znova načítavať
    if (userContacts[userId]) {
      return;
    }

    // Nastavenie stavu načítavania pre konkrétneho používateľa
    setContactsLoading(prev => ({ ...prev, [userId]: true }));
    setContactsError(prev => ({ ...prev, [userId]: '' }));

    try {
      // Prepare headers
      const headers: Record<string, string> = {
        'Content-Type': 'application/json',
      };

      // Add authorization token if available
      if (typeof user.getIdToken === 'function') {
        headers['Authorization'] = `Bearer ${await user.getIdToken()}`;
      }

      const response = await fetch(`/api/users/contact/${userId}`, {
        headers,
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Nepodarilo sa načítať kontaktné údaje');
      }

      const contactData: UserContact = await response.json();
      setUserContacts(prev => ({ ...prev, [userId]: contactData }));
    } catch (err) {
      console.error('Chyba pri načítaní kontaktných údajov:', err);
      setContactsError(prev => ({
        ...prev,
        [userId]: err instanceof Error ? err.message : 'Nepodarilo sa načítať kontaktné údaje'
      }));
    } finally {
      setContactsLoading(prev => ({ ...prev, [userId]: false }));
    }
  };

  // Funkcia pre preklad statusu rezervácie
  const translateStatus = (status: string): string => {
    switch (status) {
      case 'PENDING':
        return 'Čakajúca';
      case 'CONFIRMED':
        return 'Potvrdená';
      case 'ACTIVE':
        return 'Aktívna';
      case 'COMPLETED':
        return 'Ukončená';
      case 'CANCELLED':
        return 'Zrušená';
      default:
        return status;
    }
  };

  // Funkcia pre formátovanie dátumu
  const formatDate = (dateString: string): string => {
    const date = new Date(dateString);
    return date.toLocaleDateString('sk-SK', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    });
  };

  if (isLoading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">
          <LoadingSpinner size="large" text="Načítavam rezervácie..." />
        </div>
      </div>
    );
  }

  if (!user) {
    return null; // Toto sa nezobrazí, pretože useEffect presmeruje používateľa
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-3xl font-bold mb-8">Rezervácie</h1>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          {error}
        </div>
      )}

      {/* Sekcia: Moje aktuálne / čakajúce rezervácie hračiek */}
      <div className="mb-12">
        <h2 className="text-2xl font-semibold mb-4">Moje aktuálne / čakajúce rezervácie hračiek</h2>

        {myReservations.length > 0 ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {myReservations.map((reservation) => (
              <div key={reservation.id} className="bg-white rounded-lg shadow-sm overflow-hidden">
                <div className="relative h-48 bg-neutral-200">
                  {reservation.toy.images && reservation.toy.images.length > 0 ? (
                    <div className="w-full h-full">
                      <img
                        src={reservation.toy.images[0].url}
                        alt={reservation.toy.name}
                        className="w-full h-full object-cover"
                      />
                    </div>
                  ) : (
                    <div className="w-full h-full flex items-center justify-center text-neutral-400">
                      <span className="text-5xl">🧸</span>
                    </div>
                  )}
                  <div className="absolute top-2 right-2 flex flex-col items-end gap-1">
                    <span className={`inline-block px-2 py-1 text-xs rounded-full ${
                      reservation.status === 'PENDING' ? 'bg-yellow-100 text-yellow-800' :
                      reservation.status === 'CONFIRMED' ? 'bg-green-100 text-green-800' :
                      reservation.status === 'ACTIVE' ? 'bg-blue-100 text-blue-800' :
                      reservation.status === 'COMPLETED' ? 'bg-gray-100 text-gray-800' :
                      'bg-red-100 text-red-800'
                    }`}>
                      {translateStatus(reservation.status)}
                    </span>
                    {reservation.status === 'PENDING' && reservation.position > 0 && (
                      <span className="inline-block px-2 py-1 text-xs rounded-full bg-blue-100 text-blue-800">
                        {reservation.position + 1}. v poradí
                      </span>
                    )}
                  </div>
                </div>
                <div className="p-4">
                  <h3 className="text-xl font-semibold mb-2">{reservation.toy.name}</h3>
                  <div className="text-sm text-neutral-600 mb-3">
                    <p>Rezervované od: <span className="font-medium">{formatDate(reservation.startDate)}</span></p>
                    <p>Rezervované do: <span className="font-medium">{formatDate(reservation.endDate)}</span></p>
                    <p>Vlastník: <span className="font-medium">{reservation.owner?.name || 'Neznámy'}</span></p>
                    <p>Lokalita: <span className="font-medium">{reservation.owner?.city || 'Neuvedená'}</span></p>
                  </div>

                  {/* Kontaktné údaje vlastníka - zobrazí sa len ak je rezervácia potvrdená */}
                  {(reservation.status === 'CONFIRMED' || reservation.status === 'ACTIVE') && reservation.owner?.hashedId && (
                    <div className="mt-3 bg-green-50 p-3 rounded-lg">
                      <h4 className="font-semibold text-green-800 text-sm mb-2">Kontaktné údaje vlastníka</h4>

                      <button
                        className="text-xs text-primary underline mb-2"
                        onClick={() => loadUserContact(reservation.owner!.hashedId)}
                      >
                        {userContacts[reservation.owner.hashedId] ? 'Obnoviť kontaktné údaje' : 'Zobraziť kontaktné údaje'}
                      </button>

                      {contactsLoading[reservation.owner.hashedId] && (
                        <p className="text-xs text-neutral-500">Načítavam kontaktné údaje...</p>
                      )}

                      {contactsError[reservation.owner.hashedId] && (
                        <p className="text-xs text-red-600">{contactsError[reservation.owner.hashedId]}</p>
                      )}

                      {userContacts[reservation.owner.hashedId] && (
                        <div className="text-xs">
                          <p><span className="font-medium">Meno:</span> {userContacts[reservation.owner.hashedId].name}</p>
                          <p><span className="font-medium">Email:</span> {userContacts[reservation.owner.hashedId].email}</p>
                          {userContacts[reservation.owner.hashedId].phone && (
                            <p><span className="font-medium">Telefón:</span> {userContacts[reservation.owner.hashedId].phone}</p>
                          )}
                          {userContacts[reservation.owner.hashedId].address && (
                            <p><span className="font-medium">Adresa:</span> {userContacts[reservation.owner.hashedId].address}</p>
                          )}
                          {userContacts[reservation.owner.hashedId].city && (
                            <p><span className="font-medium">Mesto:</span> {userContacts[reservation.owner.hashedId].city}</p>
                          )}
                          {userContacts[reservation.owner.hashedId].postalCode && (
                            <p><span className="font-medium">PSČ:</span> {userContacts[reservation.owner.hashedId].postalCode}</p>
                          )}
                        </div>
                      )}
                    </div>
                  )}

                  <div className="mt-4 pt-4 border-t border-neutral-100 flex justify-between items-center">
                    <span className="text-sm text-neutral-500">Cena: {formatToyPricePerDay(reservation.toy.price, reservation.toy.priceType)}</span>
                    <Link href={generateToyUrl(reservation.toy.name, reservation.toy.hashedId)} className="btn btn-primary text-sm py-1 px-2">
                      Detail hračky
                    </Link>
                  </div>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="bg-white rounded-lg shadow-sm p-8 text-center">
            <div className="text-5xl mb-4">🧸</div>
            <h3 className="text-xl font-semibold mb-2">Zatiaľ nemáte žiadne rezervácie</h3>
            <p className="text-neutral-500 mb-4">
              Prehliadnite si dostupné hračky a rezervujte si tie, ktoré vás zaujímajú.
            </p>
            <Link href="/hracky" className="btn btn-primary">
              Prehliadať hračky
            </Link>
          </div>
        )}
      </div>

      {/* Sekcia: Dopyty na rezervácie mojich hračiek */}
      <div>
        <h2 className="text-2xl font-semibold mb-4">Dopyty na rezervácie mojich hračiek</h2>

        {ownerReservations.length > 0 ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {ownerReservations.map((reservation) => (
              <div key={reservation.id} className="bg-white rounded-lg shadow-sm overflow-hidden">
                <div className="relative h-48 bg-neutral-200">
                  {reservation.toy.images && reservation.toy.images.length > 0 ? (
                    <div className="w-full h-full">
                      <img
                        src={reservation.toy.images[0].url}
                        alt={reservation.toy.name}
                        className="w-full h-full object-cover"
                      />
                    </div>
                  ) : (
                    <div className="w-full h-full flex items-center justify-center text-neutral-400">
                      <span className="text-5xl">🧸</span>
                    </div>
                  )}
                  <div className="absolute top-2 right-2 flex flex-col items-end gap-1">
                    <span className={`inline-block px-2 py-1 text-xs rounded-full ${
                      reservation.status === 'PENDING' ? 'bg-yellow-100 text-yellow-800' :
                      reservation.status === 'CONFIRMED' ? 'bg-green-100 text-green-800' :
                      reservation.status === 'ACTIVE' ? 'bg-blue-100 text-blue-800' :
                      reservation.status === 'COMPLETED' ? 'bg-gray-100 text-gray-800' :
                      'bg-red-100 text-red-800'
                    }`}>
                      {translateStatus(reservation.status)}
                    </span>
                    {reservation.status === 'PENDING' && (
                      <span className="inline-block px-2 py-1 text-xs rounded-full bg-blue-100 text-blue-800">
                        {reservation.position === 0 ? 'Prvý v poradí' : `${reservation.position + 1}. v poradí`}
                      </span>
                    )}
                  </div>
                </div>
                <div className="p-4">
                  <h3 className="text-xl font-semibold mb-2">{reservation.toy.name}</h3>
                  <div className="text-sm text-neutral-600 mb-3">
                    <p>Rezervované od: <span className="font-medium">{formatDate(reservation.startDate)}</span></p>
                    <p>Rezervované do: <span className="font-medium">{formatDate(reservation.endDate)}</span></p>
                    <p>Záujemca: <span className="font-medium">{reservation.user?.name || 'Neznámy'}</span></p>
                    <p>Lokalita: <span className="font-medium">{reservation.user?.city || 'Neuvedená'}</span></p>
                  </div>

                  {/* Tlačidlá pre správu rezervácie */}
                  {reservation.status === 'PENDING' && (
                    <div className="mt-3 flex space-x-2">
                      <button
                        className="btn btn-primary flex-1 text-sm"
                        disabled={confirmLoading[reservation.hashedId]}
                        onClick={() => confirmReservation(reservation.hashedId)}
                      >
                        {confirmLoading[reservation.hashedId] ? (
                          <span className="flex items-center justify-center">
                            <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                            </svg>
                            Potvrdzujem...
                          </span>
                        ) : 'Potvrdiť'}
                      </button>
                      <button
                        className="btn btn-outline btn-error flex-1 text-sm"
                        disabled={rejectLoading[reservation.hashedId]}
                        onClick={() => rejectReservation(reservation.hashedId)}
                      >
                        {rejectLoading[reservation.hashedId] ? (
                          <span className="flex items-center justify-center">
                            <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-error" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                            </svg>
                            Odmietam...
                          </span>
                        ) : 'Odmietnuť'}
                      </button>
                    </div>
                  )}

                  {/* Tlačidlo pre zrušenie potvrdenej rezervácie */}
                  {reservation.status === 'CONFIRMED' && (
                    <div className="mt-3">
                      <button
                        className="btn btn-outline btn-error w-full text-sm"
                        disabled={cancelLoading[reservation.hashedId]}
                        onClick={() => cancelReservation(reservation.hashedId)}
                      >
                        {cancelLoading[reservation.hashedId] ? (
                          <span className="flex items-center justify-center">
                            <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-error" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                            </svg>
                            Ruším...
                          </span>
                        ) : 'Zrušiť rezerváciu'}
                      </button>
                    </div>
                  )}

                  {/* Kontaktné údaje záujemcu - zobrazí sa len ak je rezervácia potvrdená */}
                  {(reservation.status === 'CONFIRMED' || reservation.status === 'ACTIVE') && reservation.user?.hashedId && (
                    <div className="mt-3 bg-green-50 p-3 rounded-lg">
                      <h4 className="font-semibold text-green-800 text-sm mb-2">Kontaktné údaje záujemcu</h4>

                      <button
                        className="text-xs text-primary underline mb-2"
                        onClick={() => loadUserContact(reservation.user!.hashedId)}
                      >
                        {userContacts[reservation.user.hashedId] ? 'Obnoviť kontaktné údaje' : 'Zobraziť kontaktné údaje'}
                      </button>

                      {contactsLoading[reservation.user.hashedId] && (
                        <p className="text-xs text-neutral-500">Načítavam kontaktné údaje...</p>
                      )}

                      {contactsError[reservation.user.hashedId] && (
                        <p className="text-xs text-red-600">{contactsError[reservation.user.hashedId]}</p>
                      )}

                      {userContacts[reservation.user.hashedId] && (
                        <div className="text-xs">
                          <p><span className="font-medium">Meno:</span> {userContacts[reservation.user.hashedId].name}</p>
                          <p><span className="font-medium">Email:</span> {userContacts[reservation.user.hashedId].email}</p>
                          {userContacts[reservation.user.hashedId].phone && (
                            <p><span className="font-medium">Telefón:</span> {userContacts[reservation.user.hashedId].phone}</p>
                          )}
                          {userContacts[reservation.user.hashedId].address && (
                            <p><span className="font-medium">Adresa:</span> {userContacts[reservation.user.hashedId].address}</p>
                          )}
                          {userContacts[reservation.user.hashedId].city && (
                            <p><span className="font-medium">Mesto:</span> {userContacts[reservation.user.hashedId].city}</p>
                          )}
                          {userContacts[reservation.user.hashedId].postalCode && (
                            <p><span className="font-medium">PSČ:</span> {userContacts[reservation.user.hashedId].postalCode}</p>
                          )}
                        </div>
                      )}
                    </div>
                  )}

                  <div className="mt-4 pt-4 border-t border-neutral-100 flex justify-between items-center">
                    <span className="text-sm text-neutral-500">Cena: {formatToyPricePerDay(reservation.toy.price, reservation.toy.priceType)}</span>
                    <div className="flex space-x-2">
                      <Link href={generateToyUrl(reservation.toy.name, reservation.toy.hashedId)} className="btn btn-outline text-sm py-1 px-2">
                        Detail hračky
                      </Link>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="bg-white rounded-lg shadow-sm p-8 text-center">
            <div className="text-5xl mb-4">🧸</div>
            <h3 className="text-xl font-semibold mb-2">Zatiaľ nemáte žiadne dopyty na rezervácie</h3>
            <p className="text-neutral-500 mb-4">
              Keď si niekto rezervuje vašu hračku, zobrazí sa to tu.
            </p>
            <Link href="/moje-hracky" className="btn btn-primary">
              Moje hračky
            </Link>
          </div>
        )}
      </div>
    </div>
  );
}
