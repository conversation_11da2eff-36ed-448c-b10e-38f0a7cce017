'use client';

import { useState, useEffect } from 'react';
import { useParams } from 'next/navigation';
import Link from 'next/link';
import { useAuth } from '../../../contexts/AuthContext';
import LoadingSpinner from '../../../components/LoadingSpinner';
import { generateToyUrl } from '../../../lib/seoUtils';

// Typy pre dáta
interface Toy {
  id: string; // Hashované ID
  name: string;
  description: string;
  image: string;
  type: string;
  typeLabel?: string; // Popisný label pre typ hračky
  location: string;
  price: number;
  deposit: number;
  priceType?: string; // Typ ceny (PER_DAY alebo PER_RENTAL)
  status: string;
  distance?: number; // Vzdialenosť od používateľa v km (voliteľné)
}

interface User {
  id: number;
  hashedId: string;
  name: string;
  city: string | null;
  email?: string;
}

export default function UserProfilePage() {
  const { userHash } = useParams();
  const { user } = useAuth();
  const [profileUser, setProfileUser] = useState<User | null>(null);
  const [toys, setToys] = useState<Toy[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  // Načítanie údajov používateľa a jeho hračiek
  useEffect(() => {
    async function loadUserProfile() {
      if (!userHash) return;

      setLoading(true);
      try {
        // Načítanie údajov o používateľovi
        const userResponse = await fetch(`/api/users/public/${userHash}`);

        if (!userResponse.ok) {
          if (userResponse.status === 404) {
            throw new Error('Používateľ nebol nájdený');
          }
          throw new Error('Nepodarilo sa načítať profil používateľa');
        }

        const userData = await userResponse.json();
        setProfileUser(userData);

        // Načítanie hračiek používateľa - pridáme autentifikačné údaje, ak je používateľ prihlásený
        // aby sa filtrovali skryté hračky
        let url = `/api/toys?userId=${userHash}`;
        let headers = {};

        // Ak je používateľ prihlásený, pridáme autentifikačné údaje
        if (user && user.getIdToken) {
          const token = await user.getIdToken();
          headers = {
            'Authorization': `Bearer ${token}`,
          };
        }

        const toysResponse = await fetch(url, { headers });

        if (!toysResponse.ok) {
          throw new Error('Nepodarilo sa načítať hračky používateľa');
        }

        const toysData = await toysResponse.json();

        // Filtrujeme hračky, aby sa nezobrazovali tie v stave DRAFT
        const filteredToys = toysData.filter((toy: Toy) => toy.status !== 'DRAFT');
        setToys(filteredToys);
        setError('');
      } catch (err: any) {
        console.error('Chyba pri načítaní profilu:', err);
        setError(err.message || 'Nepodarilo sa načítať profil používateľa');
        setProfileUser(null);
        setToys([]);
      } finally {
        setLoading(false);
      }
    }

    loadUserProfile();
  }, [userHash, user]);

  // Získanie iniciálok používateľa pre avatar
  const getInitials = (name: string) => {
    if (!name) return '?';

    const names = name.split(' ');
    if (names.length >= 2) {
      return `${names[0][0]}${names[1][0]}`.toUpperCase();
    }
    return name[0].toUpperCase();
  };

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="flex justify-center items-center h-64">
          <LoadingSpinner />
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg">
          <p>{error}</p>
        </div>
        <div className="mt-4 text-center">
          <Link href="/hracky" className="btn btn-primary">
            Späť na zoznam hračiek
          </Link>
        </div>
      </div>
    );
  }

  if (!profileUser) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="bg-yellow-50 border border-yellow-200 text-yellow-700 px-4 py-3 rounded-lg">
          <p>Používateľ nebol nájdený</p>
        </div>
        <div className="mt-4 text-center">
          <Link href="/hracky" className="btn btn-primary">
            Späť na zoznam hračiek
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8 mb-16">
      {/* Compact user profile header */}
      <div className="max-w-md mx-auto bg-white rounded-lg shadow-sm overflow-hidden mb-8">
        <div className="p-4">
          <div className="flex items-center">
            <div className="w-16 h-16 rounded-full bg-primary flex items-center justify-center text-white text-xl font-bold mr-3">
              {getInitials(profileUser.name)}
            </div>
            <div>
              <h1 className="text-xl font-bold">{profileUser.name}</h1>
              {profileUser.city && (
                <p className="text-sm text-neutral-500">
                  <span className="inline-flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                    </svg>
                    {profileUser.city}
                  </span>
                </p>
              )}
            </div>
          </div>
        </div>
      </div>

      {toys.length > 0 ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-24">
          {toys.map((toy) => (
            <div key={toy.id} className="bg-white rounded-lg shadow-sm overflow-hidden">
              <div className="relative h-48 bg-neutral-200">
                {toy.image && !toy.image.includes('placeholder.jpg') ? (
                  <div className="w-full h-full">
                    <img
                      src={toy.image}
                      alt={toy.name}
                      className="w-full h-full object-cover"
                    />
                  </div>
                ) : (
                  <div className="w-full h-full flex items-center justify-center text-neutral-400">
                    {/* Placeholder pre obrázok */}
                    <span className="text-5xl">🧸</span>
                  </div>
                )}
              </div>
              <div className="p-4">
                <div className="flex justify-between items-start">
                  <h2 className="text-xl font-semibold">{toy.name}</h2>
                  <span className="text-primary font-bold">{toy.priceType === 'PER_RENTAL' ? `${toy.price}€ / výpožičku` : `${toy.price}€ / deň`}</span>
                </div>
                <p className="text-neutral-600 text-sm mt-2">{toy.description}</p>
                <div className="flex items-center mt-3 text-sm text-neutral-500">
                  <span className="mr-4">📍 {toy.location}</span>
                  <span className="mr-4">🏷️ {toy.typeLabel || toy.type}</span>
                  {toy.distance !== undefined && (
                    <span className="text-primary font-medium">📏 {toy.distance.toFixed(1)} km</span>
                  )}
                </div>
                <div className="mt-4 pt-4 border-t border-neutral-100 flex justify-between items-center">
                  {toy.deposit === 0 ? (
                    <span className="text-sm text-green-600 bg-green-100 px-2 py-1 rounded-full">bez zálohy</span>
                  ) : (
                    <span className="text-sm text-neutral-500">Záloha: {toy.deposit}€</span>
                  )}
                  <Link href={generateToyUrl(toy.name, toy.id)} className="btn btn-primary text-sm py-1">
                    Zobraziť detail
                  </Link>
                </div>
              </div>
            </div>
          ))}
        </div>
      ) : (
        <div className="text-center py-12 bg-neutral-50 rounded-lg">
          <p className="text-neutral-500 text-lg">
            Tento používateľ zatiaľ nemá žiadne hračky na požičanie.
          </p>
        </div>
      )}
    </div>
  );
}
