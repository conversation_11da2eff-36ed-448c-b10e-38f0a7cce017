import type { <PERSON><PERSON><PERSON>, Viewport } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_<PERSON><PERSON> } from "next/font/google";
import "./globals.css";
import Header from "../components/Header";
import Footer from "../components/Footer";
import { AuthProvider } from "../contexts/AuthContext";
import { CookieConsentProvider } from "../contexts/CookieConsentContext";
import { ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import Script from 'next/script';
import ClientWebViewWrapper from "../components/ClientWebViewWrapper";
import ClientCookieBannerWrapper from "../components/ClientCookieBannerWrapper";
import AuthLoadingProvider from "../components/AuthLoadingProvider";
import CacheManager from "../components/CacheManager";
import ReservationNotification from "../components/ReservationNotification";
import { ReservationNotificationProvider } from "../contexts/ReservationNotificationContext";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "Swapka - Požičiavanie detských hračiek",
  description: "Aplikácia na zdieľanie a požičiavanie detských hračiek medzi rodičmi",
};

export const viewport: Viewport = {
  width: "device-width",
  initialScale: 1.0,
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="sk" data-google-analytics-opt-out="">
      <head>
      </head>
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased flex flex-col min-h-screen`}
      >
        <AuthProvider>
          <CookieConsentProvider>
            <AuthLoadingProvider>
              <ReservationNotificationProvider>
                <Header />
                <main className="flex-grow">
                  {children}
                </main>
                <Footer />
                <ToastContainer
                  position="top-right"
                  autoClose={3000}
                  hideProgressBar={false}
                  newestOnTop
                  closeOnClick
                  rtl={false}
                  pauseOnFocusLoss
                  draggable
                  pauseOnHover
                  theme="light"
                />
                {/* WebView detector component */}
                <ClientWebViewWrapper />
                {/* Cookie banner component */}
                <ClientCookieBannerWrapper />
                {/* Reservation notification component */}
                <ReservationNotification />
              </ReservationNotificationProvider>
            </AuthLoadingProvider>
          </CookieConsentProvider>
        </AuthProvider>
      </body>
    </html>
  );
}
