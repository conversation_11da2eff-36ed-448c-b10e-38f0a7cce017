/*
 * Mobile-specific form styles
 * Ensures proper stacking and spacing of form elements on mobile devices
 *
 * Note: Some selectors use :has() which may not be supported in all browsers.
 * We provide alternative selectors for broader compatibility.
 */

@media (max-width: 768px) {
  /* Form groups - ensure proper vertical spacing */
  .form-group,
  form > div > div,
  form .grid > div {
    margin-bottom: 0.75rem;
  }

  /* Force form elements to stack vertically on mobile */
  .form-row,
  .form-inline,
  .form-group-inline,
  form .flex:not(.flex-col):not(.items-center) {
    flex-direction: column !important;
    align-items: stretch !important;
  }

  /* Exception: Keep TagSelect tag containers horizontal on mobile */
  /* Ensure individual tags with X buttons stay inline */
  div[role="combobox"] .flex.items-center,
  .relative div[role="combobox"] .flex.items-center {
    flex-direction: row !important;
    align-items: center !important;
    flex-wrap: nowrap !important;
  }

  /* Ensure TagSelect main container maintains proper flex-wrap behavior */
  div[role="combobox"].flex.flex-wrap,
  .relative div[role="combobox"].flex.flex-wrap {
    display: flex !important;
    flex-direction: row !important;
    flex-wrap: wrap !important;
    align-items: center !important;
    gap: 0.5rem !important;
  }

  /* Ensure TagSelect individual tag buttons stay inline with their X buttons */
  div[role="combobox"] > div.flex.items-center,
  .relative div[role="combobox"] > div.flex.items-center {
    display: flex !important;
    flex-direction: row !important;
    align-items: center !important;
    justify-content: space-between !important;
    flex-wrap: nowrap !important;
    white-space: nowrap !important;
    min-width: fit-content !important;
  }

  /* Ensure tag text in TagSelect takes available space but doesn't grow unnecessarily */
  div[role="combobox"] > div.flex.items-center > *:first-child,
  .relative div[role="combobox"] > div.flex.items-center > *:first-child {
    flex: 0 1 auto !important;
    margin-right: 0.5rem !important;
  }

  /* Ensure X buttons in TagSelect are properly right-aligned */
  div[role="combobox"] button,
  .relative div[role="combobox"] button {
    display: inline-flex !important;
    align-items: center !important;
    justify-content: center !important;
    margin-left: auto !important;
    flex-shrink: 0 !important;
    min-width: 1rem !important;
    min-height: 1rem !important;
  }

  /* Additional TagSelect tag styling for consistent appearance on mobile */
  div[role="combobox"] > div.bg-primary,
  .relative div[role="combobox"] > div.bg-primary {
    min-width: fit-content !important;
    max-width: 100% !important;
    padding-left: 0.75rem !important;
    padding-right: 0.5rem !important;
  }

  /* Ensure proper spacing between TagSelect tag text and X button */
  div[role="combobox"] > div.flex.items-center button.ml-2,
  .relative div[role="combobox"] > div.flex.items-center button.ml-2 {
    margin-left: 0.5rem !important;
    margin-right: 0 !important;
  }

  /* Ensure inputs take full width */
  input,
  select,
  textarea,
  .custom-select,
  [role="combobox"] {
    width: 100% !important;
    max-width: 100% !important;
    margin-right: 0 !important;
    margin-left: 0 !important;
  }

  /* Add spacing between stacked elements
  .form-row > *,
  .form-inline > *,
  .form-group-inline > *,
  form .flex:not(.flex-col) > * {
    margin-bottom: 0.5rem;
  } */

  /* Fix for input groups that have labels and inputs side by side */
  .input-group,
  .form-group,
  form .grid > div {
    display: flex;
    flex-direction: column;
  }

  /* Ensure labels are above inputs */
  label {
    display: block;
    margin-bottom: 0.25rem;
    width: 100% !important;
  }

  /* Fix for side-by-side inputs like price and currency */
  .input-group-append,
  .input-group-prepend {
    width: 100%;
    margin-top: 0.5rem;
  }

  /* Ensure proper spacing for form sections */
  form section,
  form fieldset,
  form .card,
  form .bg-white {
    margin-bottom: 1rem;
    /* padding: 0.75rem; */
  }

  /* Fix for grid layouts in forms */
  form .grid,
  form .grid-cols-2,
  form .grid-cols-3,
  form .grid-cols-4 {
    display: flex !important;
    flex-direction: column !important;
    gap: 0.5rem !important;
  }

  /* Fix for the specific layout in the screenshot */
  form .grid > div {
    grid-column: span 1 !important;
  }

  /* Specific fixes for form elements that appear side by side */
  .form-row,
  .form-group,
  .input-group,
  .form-inline,
  div[class*="flex items-center"]:not(.hidden),
  div[class*="flex justify-between"]:not(.hidden),
  div[class*="flex space-x"]:not(.hidden) {
    display: flex !important;
    flex-direction: column !important;
    align-items: flex-start !important;
    width: 100% !important;
  }

  /* Ensure view mode toggle stays hidden on mobile */
  div.hidden.md\:flex {
    display: none !important;
  }

  /* Ensure proper spacing between label and input */
  label + input,
  label + select,
  label + textarea,
  label + div[role="combobox"] {
    margin-top: 0.5rem !important;
  }

  /* Fix for price and deposit fields that might be side by side */
  input[type="number"],
  input[name="price"],
  input[name="deposit"],
  select[name="maxReservationDays"],
  select[name="status"] {
    width: 100% !important;
    margin-bottom: 0.5rem !important;
  }

  /* Ensure proper spacing for the status dropdown */
  div:has(> select[name="status"]),
  div:has(> [role="combobox"]) {
    margin-bottom: 0.5rem !important;
    width: 100% !important;
  }

  /* Fix for any flex containers that might be used for layout */
  div[class*="md:flex"],
  div[class*="md:grid"],
  div[class*="md:items-center"] {
    display: block !important;
  }

  /* Ensure all form controls take full width on mobile */
  .form-control,
  .form-select,
  .form-input,
  .form-textarea,
  div[role="combobox"],
  div[class*="custom-select"] {
    width: 100% !important;
    max-width: 100% !important;
  }

  /* Ensure CustomSelect components work properly on mobile */
  .relative div[role="combobox"] {
    width: 100% !important;
    margin-bottom: 0.5rem !important;
  }

  /* Specific fixes for the toy form layout shown in the screenshot */
  /* Target the specific layout with price, deposit, status, and max reservation days */
  div:has(> input[name="price"]),
  div:has(> input[name="deposit"]),
  div:has(> select[name="status"]),
  div:has(> select[name="maxReservationDays"]) {
    width: 100% !important;
    margin-bottom: 1.5rem !important;
  }

  /* Ensure proper spacing between label and input */
  label[for="price"],
  label[for="deposit"],
  label[for="status"],
  label[for="maxReservationDays"] {
    display: block !important;
    width: 100% !important;
    margin-bottom: 0.5rem !important;
  }

  /* Fix for any side-by-side layout in the form */
  div:has(> label[for="price"]) + div:has(> label[for="deposit"]),
  div:has(> label[for="status"]) + div:has(> label[for="maxReservationDays"]) {
    margin-top: 1rem !important;
  }

  /* Additional fixes for the specific form layout in the screenshot */
  form .grid {
    display: flex !important;
    flex-direction: column !important;
  }

  form .grid > div {
    width: 100% !important;
    margin-bottom: 1.5rem !important; /* Match the gap-6 from the original grid */
  }

  /* Ensure proper spacing for form groups */
  .form-group,
  .form-row,
  .form-section {
    margin-bottom: 1.5rem !important;
  }

  /* Fix for any custom selects or dropdowns - remove extra margin */
  div[role="combobox"],
  .custom-select,
  .dropdown {
    width: 100% !important;
    margin-bottom: 0 !important; /* Remove extra margin to match /hracky page */
  }

  /* Specific fixes for CustomSelect components in toy forms - remove extra margin */
  div:has(> .relative > div[role="combobox"]) {
    width: 100% !important;
    margin-bottom: 0 !important; /* Remove extra margin to match /hracky page */
  }

  /* Fix for toy type and max reservation days CustomSelect */
  #type + .relative,
  #maxReservationDays + .relative,
  .relative:has(div[role="combobox"]) {
    width: 100% !important;
  }

  /* Specific fixes for the form elements in the screenshot - remove extra margin */
  textarea#description {
    width: 100% !important;
    margin-bottom: 0 !important; /* Remove extra margin to match /hracky page */
  }

  /* Fix for the status dropdown - remove extra margin */
  select[name="status"],
  div:has(> select[name="status"]) {
    width: 100% !important;
    margin-bottom: 0 !important; /* Remove extra margin to match /hracky page */
  }

  /* Fix for the max reservation days dropdown - remove extra margin */
  select[name="maxReservationDays"],
  div:has(> select[name="maxReservationDays"]) {
    width: 100% !important;
    margin-bottom: 0 !important; /* Remove extra margin to match /hracky page */
  }

  /* Fix for the price input - remove extra margin */
  input[name="price"],
  div:has(> input[name="price"]) {
    width: 100% !important;
    margin-bottom: 0 !important; /* Remove extra margin to match /hracky page */
  }

  /* Fix for the deposit input - remove extra margin */
  input[name="deposit"],
  div:has(> input[name="deposit"]) {
    width: 100% !important;
    margin-bottom: 0 !important; /* Remove extra margin to match /hracky page */
  }

  /* Alternative selectors for browsers that don't support :has() */
  /* Target common parent containers - use proper spacing */
  .grid > div,
  .form-row > div,
  .form-group > div,
  .form-section > div,
  .form-container > div,
  form > div > div {
    width: 100% !important;
    margin-bottom: 1.5rem !important; /* Match the gap-6 from the original grid */
  }

  /* Target all form controls directly - remove extra margin to match /hracky page */
  input,
  select,
  textarea,
  .custom-select,
  [role="combobox"] {
    width: 100% !important;
    margin-bottom: 0 !important; /* Remove extra margin to match /hracky page */
  }

  /* Ensure all labels are properly displayed */
  label {
    display: block !important;
    width: 100% !important;
    margin-bottom: 0.25rem !important;
  }

  /* Additional spacing fixes for the form in the screenshot */
  form > div > div + div {
    margin-top: 0.5rem !important;
  }

  /* Fix for buttons in toy listings to display horizontally on mobile */
  .bg-white.rounded-lg.shadow-sm.overflow-hidden .flex.space-x-2,
  .bg-white.rounded-lg.shadow-sm.overflow-hidden .flex.items-center.mt-3 .flex,
  .bg-white.rounded-lg.shadow-sm.overflow-hidden .mt-4.pt-4.border-t.border-neutral-100.flex.justify-between.items-center .flex.space-x-2 {
    display: flex !important;
    flex-direction: row !important;
    flex-wrap: wrap !important;
    justify-content: flex-start !important;
    gap: 0.5rem !important;
  }

  /* Ensure buttons in toy listings have proper width */
  .bg-white.rounded-lg.shadow-sm.overflow-hidden .btn,
  .bg-white.rounded-lg.shadow-sm.overflow-hidden .btn-delete-subtle,
  .bg-white.rounded-lg.shadow-sm.overflow-hidden a[href^="/moje-hracky/nova"],
  .bg-white.rounded-lg.shadow-sm.overflow-hidden a[href^="/hracky/"],
  .bg-white.rounded-lg.shadow-sm.overflow-hidden button[title="Odstrániť"],
  .bg-white.rounded-lg.shadow-sm.overflow-hidden button[title="Odstrániť hračku"] {
    width: auto !important;
    margin-bottom: 0 !important;
    display: inline-flex !important;
    align-items: center !important;
    justify-content: center !important;
  }

  /* Specific styling for delete button in toy listings */
  .bg-white.rounded-lg.shadow-sm.overflow-hidden button[title*="Odstrániť"],
  .bg-white.rounded-lg.shadow-sm.overflow-hidden .btn-delete-subtle {
    display: inline-flex !important;
    visibility: visible !important;
    opacity: 1 !important;
    min-width: 32px !important;
    min-height: 32px !important;
  }

  /* Mobile-specific fixes for toy card buttons in /moje-hracky */
  @media (max-width: 768px) {
    /* Ensure the button container in toy cards is properly displayed */
    .bg-white.rounded-lg.shadow-sm.overflow-hidden .mt-4.pt-4.border-t.border-neutral-100.flex.justify-between.items-center {
      display: flex !important;
      flex-direction: column !important;
      align-items: stretch !important;
      gap: 0.75rem !important;
    }

    /* Make the button container take full width and display buttons side by side */
    .bg-white.rounded-lg.shadow-sm.overflow-hidden .mt-4.pt-4.border-t.border-neutral-100.flex.justify-between.items-center .flex.space-x-2 {
      display: flex !important;
      flex-direction: row !important;
      justify-content: flex-end !important;
      align-items: center !important;
      width: 100% !important;
      gap: 0.5rem !important;
      flex-wrap: nowrap !important;
    }

    /* Edit button takes more space (primary action) */
    .bg-white.rounded-lg.shadow-sm.overflow-hidden .mt-4.pt-4.border-t.border-neutral-100.flex.justify-between.items-center .flex.space-x-2 > a.btn {
      flex: 1 !important;
      min-width: 0 !important;
      display: flex !important;
      align-items: center !important;
      justify-content: center !important;
      padding: 0.5rem 0.75rem !important;
      font-size: 0.875rem !important;
      white-space: nowrap !important;
    }

    /* Delete button is smaller (secondary action) */
    .bg-white.rounded-lg.shadow-sm.overflow-hidden .mt-4.pt-4.border-t.border-neutral-100.flex.justify-between.items-center .flex.space-x-2 > .btn-delete-subtle {
      flex: 0 0 auto !important;
      min-width: 44px !important;
      display: flex !important;
      align-items: center !important;
      justify-content: center !important;
      white-space: nowrap !important;
    }

    /* Specific styling for the delete button on mobile */
    .bg-white.rounded-lg.shadow-sm.overflow-hidden .mt-4.pt-4.border-t.border-neutral-100.flex.justify-between.items-center button[title*="Odstrániť"],
    .bg-white.rounded-lg.shadow-sm.overflow-hidden .mt-4.pt-4.border-t.border-neutral-100.flex.justify-between.items-center .btn-delete-subtle {
      display: flex !important;
      align-items: center !important;
      justify-content: center !important;
      min-width: 44px !important;
      min-height: 44px !important;
      flex-shrink: 0 !important;
      padding: 0.75rem !important;
    }

    /* Ensure the deposit/price info doesn't take too much space */
    .bg-white.rounded-lg.shadow-sm.overflow-hidden .mt-4.pt-4.border-t.border-neutral-100.flex.justify-between.items-center > span {
      font-size: 0.75rem !important;
      flex-shrink: 1 !important;
      margin-bottom: 0.25rem !important;
    }

    /* Additional mobile touch target improvements */
    .bg-white.rounded-lg.shadow-sm.overflow-hidden .btn {
      min-height: 44px !important;
      touch-action: manipulation !important;
      -webkit-tap-highlight-color: transparent !important;
    }

    /* Ensure trash icon is visible in delete button */
    .bg-white.rounded-lg.shadow-sm.overflow-hidden button[title*="Odstrániť"] img,
    .bg-white.rounded-lg.shadow-sm.overflow-hidden button[title*="Odstrániť"] svg,
    .bg-white.rounded-lg.shadow-sm.overflow-hidden .btn-delete-subtle img,
    .bg-white.rounded-lg.shadow-sm.overflow-hidden .btn-delete-subtle svg {
      width: 14px !important;
      height: 14px !important;
      display: inline-block !important;
      opacity: 0.8 !important;
    }

    /* Force visibility of all elements in button container */
    .bg-white.rounded-lg.shadow-sm.overflow-hidden .mt-4.pt-4.border-t.border-neutral-100.flex.justify-between.items-center .flex.space-x-2 * {
      visibility: visible !important;
      opacity: 1 !important;
    }
  }

  /* Override any flex-direction column on the button container */
  .mt-4.pt-4.border-t.border-neutral-100.flex.justify-between.items-center .flex.space-x-2 {
    flex-direction: row !important;
    flex-wrap: nowrap !important;
    justify-content: flex-end !important;
    gap: 0.5rem !important;
  }

  /* Fix for the space-x utility not working in some mobile browsers */
  .flex.space-x-2 > * {
    margin-left: 0 !important;
    margin-right: 0.5rem !important;
  }

  .flex.space-x-2 > *:last-child {
    margin-right: 0 !important;
  }

  /* Exception: Keep location and category horizontal in toy listings on mobile */
  .bg-white.rounded-lg.shadow-sm.overflow-hidden .flex.items-center.justify-between.mt-3.text-sm.text-neutral-500,
  .bg-white.rounded-lg.shadow-sm .flex.items-center.justify-between.mt-3.text-sm.text-neutral-500 {
    display: flex !important;
    flex-direction: row !important;
    justify-content: space-between !important;
    align-items: center !important;
    flex-wrap: nowrap !important;
    width: 100% !important;
  }

  /* Ensure location and category text doesn't overflow */
  .bg-white.rounded-lg.shadow-sm.overflow-hidden .flex.items-center.justify-between.mt-3.text-sm.text-neutral-500 > span,
  .bg-white.rounded-lg.shadow-sm .flex.items-center.justify-between.mt-3.text-sm.text-neutral-500 > span,
  .bg-white.rounded-lg.shadow-sm.overflow-hidden .flex.items-center.justify-between.mt-3.text-sm.text-neutral-500 > div,
  .bg-white.rounded-lg.shadow-sm .flex.items-center.justify-between.mt-3.text-sm.text-neutral-500 > div {
    flex: 0 1 auto !important;
    white-space: nowrap !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
    max-width: 48% !important;
  }

  /* Additional fix for location text with distance to prevent wrapping */
  .toy-location {
    white-space: nowrap !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
    flex-shrink: 1 !important;
    min-width: 0 !important;
  }

  /* Ensure category text stays on one line */
  .toy-category {
    white-space: nowrap !important;
    flex-shrink: 0 !important;
    min-width: fit-content !important;
  }

  /* Ensure the right side container (category + distance) stays horizontal */
  .bg-white.rounded-lg.shadow-sm.overflow-hidden .flex.items-center.justify-between.mt-3.text-sm.text-neutral-500 > div.flex.items-center.space-x-2,
  .bg-white.rounded-lg.shadow-sm .flex.items-center.justify-between.mt-3.text-sm.text-neutral-500 > div.flex.items-center.space-x-2 {
    display: flex !important;
    flex-direction: row !important;
    align-items: center !important;
    gap: 0.5rem !important;
    flex-wrap: nowrap !important;
    text-align: right !important;
  }

  /* Right-align category text on mobile using specific class names */
  /* Category container (for hracky page with distance) */
  .toy-category-container {
    justify-content: flex-end !important;
    text-align: right !important;
  }

  /* Category text elements */
  .toy-category {
    text-align: right !important;
  }

  /* Ensure location stays left-aligned */
  .toy-location {
    text-align: left !important;
  }

  /* Fix for CustomSelect dropdown options on mobile */
  /* Hide checkmark container completely - target the exact DOM structure */
  .relative div[role="listbox"] div[role="option"] div.flex.items-center > div.mr-2.w-5.flex-shrink-0 {
    display: none !important;
    visibility: hidden !important;
    width: 0 !important;
    height: 0 !important;
    margin: 0 !important;
    padding: 0 !important;
  }

  /* Hide SVG checkmark icons with maximum specificity */
  .relative div[role="listbox"] div[role="option"] div.flex.items-center > div.mr-2.w-5.flex-shrink-0 > svg {
    display: none !important;
    visibility: hidden !important;
  }

  /* Alternative: Hide all SVG elements in dropdown options */
  .relative div[role="listbox"] div[role="option"] svg.h-5.w-5.text-primary {
    display: none !important;
    visibility: hidden !important;
  }

  /* Hide any path elements within checkmark SVGs */
  .relative div[role="listbox"] div[role="option"] svg path[d="M5 13l4 4L19 7"] {
    display: none !important;
    visibility: hidden !important;
  }

  /* Ensure the flex container stays horizontal but without checkmark space */
  .relative div[role="listbox"] div[role="option"] div.flex.items-center {
    display: flex !important;
    flex-direction: row !important;
    align-items: center !important;
    justify-content: flex-start !important;
    min-height: auto !important;
    height: auto !important;
  }

  /* Remove margin from the text span to compensate for hidden checkmark */
  .relative div[role="listbox"] div[role="option"] div.flex.items-center > span {
    margin-left: 0 !important;
    line-height: 1.5 !important;
    vertical-align: middle !important;
  }

  /* Maintain proper padding and vertical alignment for dropdown options */
  .relative div[role="listbox"] div[role="option"] {
    padding: 0.5rem 1rem !important; /* py-2 px-4 equivalent */
    display: flex !important;
    align-items: center !important;
    min-height: 2.5rem !important; /* Ensure consistent height */
    line-height: 1.5 !important;
  }

  /* Ensure text is properly aligned within the option */
  .relative div[role="listbox"] div[role="option"] span {
    display: inline-block !important;
    vertical-align: middle !important;
    line-height: 1.5 !important;
    margin: 0 !important;
    padding: 0 !important;
  }

  /* Override any problematic flex rules that might affect dropdown options */
  .relative div[role="listbox"] div[role="option"]:not(.flex-col) {
    flex-direction: row !important;
    align-items: center !important;
  }

  /* Additional coverage for toy editing forms */
  /* Ensure CustomSelect components in all toy forms work correctly */
  form .relative div[role="listbox"] div[role="option"] div.mr-2.w-5.flex-shrink-0 {
    display: none !important;
    visibility: hidden !important;
  }

  form .relative div[role="listbox"] div[role="option"] svg {
    display: none !important;
    visibility: hidden !important;
  }

  /* Specific coverage for toy type, price type, status, and max reservation days selectors */
  #type + .relative div[role="listbox"] div[role="option"] div.mr-2.w-5.flex-shrink-0,
  #priceType + .relative div[role="listbox"] div[role="option"] div.mr-2.w-5.flex-shrink-0,
  #status + .relative div[role="listbox"] div[role="option"] div.mr-2.w-5.flex-shrink-0,
  #maxReservationDays + .relative div[role="listbox"] div[role="option"] div.mr-2.w-5.flex-shrink-0 {
    display: none !important;
    visibility: hidden !important;
    width: 0 !important;
    height: 0 !important;
  }

  /* Ensure ToyStatusSelect component (which wraps CustomSelect) also works correctly */
  .relative div[role="listbox"] div[role="option"] div.flex.items-center > div.mr-2:first-child {
    display: none !important;
    visibility: hidden !important;
  }

  /* Additional protection for any remaining checkmark icons in toy forms */
  form div[role="listbox"] div[role="option"] svg.h-5.w-5,
  form div[role="listbox"] div[role="option"] svg[stroke="currentColor"] {
    display: none !important;
    visibility: hidden !important;
  }

  /* Ensure proper spacing for all toy form CustomSelect components */
  form .relative div[role="listbox"] div[role="option"] {
    padding: 0.5rem 1rem !important;
    display: flex !important;
    align-items: center !important;
    min-height: 2.5rem !important;
  }

  /* Text alignment for all toy form dropdown options */
  form .relative div[role="listbox"] div[role="option"] span {
    line-height: 1.5 !important;
    vertical-align: middle !important;
    margin: 0 !important;
  }

  /* Additional fixes for TagSelect dropdown options on mobile */
  /* Ensure TagSelect dropdown checkmarks are hidden and text is properly aligned */
  .relative div[role="listbox"] div[role="option"] div.flex.items-center > div.mr-2.w-5.flex-shrink-0 {
    display: none !important;
    visibility: hidden !important;
  }

  /* Ensure TagSelect dropdown options maintain horizontal layout */
  .relative div[role="listbox"] div[role="option"] div.flex.items-center {
    display: flex !important;
    flex-direction: row !important;
    align-items: center !important;
  }

  /* Fix for "bez zálohy" and deposit text wrapping in toy listings */
  /* Ensure the bottom container with deposit info and buttons stays horizontal */
  .bg-white.rounded-lg.shadow-sm.overflow-hidden .mt-4.pt-4.border-t.border-neutral-100.flex.justify-between.items-center {
    display: flex !important;
    flex-direction: row !important;
    justify-content: space-between !important;
    align-items: center !important;
    flex-wrap: nowrap !important;
    gap: 0.5rem !important;
  }

  /* Prevent "bez zálohy" text from wrapping */
  .bg-white.rounded-lg.shadow-sm.overflow-hidden .mt-4.pt-4.border-t.border-neutral-100 span {
    white-space: nowrap !important;
    flex-shrink: 0 !important;
    min-width: fit-content !important;
  }

  /* Ensure deposit text doesn't wrap */
  .bg-white.rounded-lg.shadow-sm.overflow-hidden .text-sm.text-green-600.bg-green-100,
  .bg-white.rounded-lg.shadow-sm.overflow-hidden .text-sm.text-neutral-500 {
    white-space: nowrap !important;
    flex-shrink: 0 !important;
    min-width: fit-content !important;
    display: inline-block !important;
  }

  /* Ensure the button container on the right doesn't shrink too much */
  .bg-white.rounded-lg.shadow-sm.overflow-hidden .mt-4.pt-4.border-t.border-neutral-100 .flex.space-x-2,
  .bg-white.rounded-lg.shadow-sm.overflow-hidden .mt-4.pt-4.border-t.border-neutral-100 a.btn {
    flex-shrink: 0 !important;
    min-width: fit-content !important;
  }

  /* Additional fix for the specific "bez zálohy" badge styling */
  .text-sm.text-green-600.bg-green-100.px-2.py-1.rounded-full {
    white-space: nowrap !important;
    display: inline-block !important;
    flex-shrink: 0 !important;
    min-width: fit-content !important;
  }

  /* Fix for deposit amount text */
  span.text-sm.text-neutral-500 {
    white-space: nowrap !important;
    flex-shrink: 0 !important;
    min-width: fit-content !important;
  }

  /* Ensure toy title and price container doesn't cause wrapping */
  .bg-white.rounded-lg.shadow-sm.overflow-hidden .p-4 .flex.justify-between.items-start {
    display: flex !important;
    flex-direction: row !important;
    justify-content: space-between !important;
    align-items: flex-start !important;
    flex-wrap: nowrap !important;
    gap: 0.5rem !important;
  }

  /* Ensure toy title can shrink but price stays on one line */
  .bg-white.rounded-lg.shadow-sm.overflow-hidden .p-4 .flex.justify-between.items-start h2 {
    flex: 1 1 auto !important;
    min-width: 0 !important;
    margin-right: 0.5rem !important;
  }

  /* Ensure price text never wraps */
  .bg-white.rounded-lg.shadow-sm.overflow-hidden .p-4 .flex.justify-between.items-start .text-primary.font-bold {
    white-space: nowrap !important;
    flex-shrink: 0 !important;
    min-width: fit-content !important;
  }
}
