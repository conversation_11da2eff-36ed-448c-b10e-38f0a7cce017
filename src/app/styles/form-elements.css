/* 
 * <PERSON>t<PERSON>ly pre formulárové prvky
 * Zabezpečujú konzistentný vzhľad naprieč prehliadačmi vrátane Safari na macOS
 */

/* Základné štýly pre všetky formulárové prvky */
input,
select,
textarea,
button {
  font-family: var(--font-geist-sans, Arial, Helvetica, sans-serif);
  font-size: 1rem;
  line-height: 1.5;
  color: var(--foreground);
  transition: all 0.2s ease-in-out;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
}

/* Štýly pre input polia */
input[type="text"],
input[type="email"],
input[type="password"],
input[type="number"],
input[type="search"],
input[type="tel"],
input[type="url"],
input[type="date"],
textarea {
  width: 100%;
  padding: 0.5rem 0.75rem;
  border: 1px solid var(--neutral-300);
  border-radius: 0.375rem;
  background-color: white;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

/* <PERSON>t<PERSON>ly pre select */
select {
  width: 100%;
  padding: 0.5rem 2.5rem 0.5rem 0.75rem;
  border: 1px solid var(--neutral-300);
  border-radius: 0.375rem;
  background-color: white;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20' stroke='%236c757d'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3E%3C/svg%3E");
  background-position: right 0.5rem center;
  background-repeat: no-repeat;
  background-size: 1.5em 1.5em;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  cursor: pointer;
}

/* Odstránenie natívnych štýlov pre select v Safari */
select::-ms-expand {
  display: none;
}

/* Štýly pre focus stav */
input:focus,
select:focus,
textarea:focus {
  outline: none;
  border-color: var(--primary);
  box-shadow: 0 0 0 3px rgba(78, 205, 196, 0.2);
}

/* Štýly pre disabled stav */
input:disabled,
select:disabled,
textarea:disabled,
button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* Štýly pre placeholder */
::placeholder {
  color: var(--neutral-500);
  opacity: 1;
}

/* Štýly pre tlačidlá */
button,
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.5rem 1rem;
  border: 1px solid transparent;
  border-radius: 0.375rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease-in-out;
}

/* Štýly pre checkbox a radio */
input[type="checkbox"],
input[type="radio"] {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  padding: 0;
  width: 1.25rem;
  height: 1.25rem;
  border: 1px solid var(--neutral-300);
  background-color: white;
  cursor: pointer;
  vertical-align: middle;
  position: relative;
}

input[type="checkbox"] {
  border-radius: 0.25rem;
}

input[type="radio"] {
  border-radius: 50%;
}

input[type="checkbox"]:checked,
input[type="radio"]:checked {
  background-color: var(--primary);
  border-color: var(--primary);
}

input[type="checkbox"]:checked::after {
  content: "";
  position: absolute;
  left: 0.4rem;
  top: 0.2rem;
  width: 0.25rem;
  height: 0.5rem;
  border: solid white;
  border-width: 0 2px 2px 0;
  transform: rotate(45deg);
}

input[type="radio"]:checked::after {
  content: "";
  position: absolute;
  left: 0.3rem;
  top: 0.3rem;
  width: 0.6rem;
  height: 0.6rem;
  border-radius: 50%;
  background-color: white;
}

/* Štýly pre focus stav checkbox a radio */
input[type="checkbox"]:focus,
input[type="radio"]:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(78, 205, 196, 0.2);
}
