/*
 * Mobile-specific header styles
 * Ensures proper layout of header elements on mobile devices
 * Updated for new mobile header without hamburger menu
 */

/* Mobile-only styles */
@media (max-width: 768px) {
  /* Force hide desktop header on mobile - target by position */
  header .container > div:first-child {
    display: none !important;
  }

  /* Ensure mobile header is visible - target by position */
  header .container > div:nth-child(2) {
    display: block !important;
  }

  /* Mobile header container */
  header .container {
    padding-left: 1rem !important;
    padding-right: 1rem !important;
  }

  /* Mobile header row with logo and user actions */
  header .container > div:nth-child(2) > div:first-child {
    display: flex !important;
    flex-direction: row !important;
    justify-content: space-between !important;
    align-items: center !important;
    margin-bottom: 0.75rem !important;
    width: 100% !important;
    min-height: 40px !important;
  }

  /* Mobile navigation row */
  header .container > div:nth-child(2) nav {
    display: flex !important;
    flex-wrap: wrap !important;
    justify-content: center !important;
    gap: 1.5rem 1.5rem !important;
    padding-top: 0.10rem !important;
    border-top: 1px solid #f3f4f6 !important;
  }

  /* Mobile navigation links */
  header .container > div:nth-child(2) nav a {
    font-size: 0.875rem !important;
    padding: 0.25rem 0 !important;
    white-space: nowrap !important;
  }

  /* Mobile user profile dropdown positioning - fix for mobile */
  header .container > div:nth-child(2) .relative .absolute {
    position: fixed !important;
    top: 2rem !important;
    right: 1rem !important;
    left: auto !important;
    transform: none !important;
    z-index: 9999 !important;
    max-width: calc(100vw - 2rem) !important;
    width: 12rem !important;
  }

  /* Mobile login button styling */
  header .container > div:nth-child(2) button {
    font-size: 0.875rem !important;
  }

  /* Mobile logo sizing */
  header .container > div:nth-child(2) .text-xl {
    font-size: 1.25rem !important;
  }

  /* Ensure proper spacing for mobile user profile */
  header .container > div:nth-child(2) .flex.items-center {
    display: flex !important;
    align-items: center !important;
    margin-bottom: 0px !important;
  }

  /* Force horizontal layout for mobile header elements */
  header .container > div:nth-child(2) > div:first-child > div:first-child {
    flex-shrink: 0 !important;
  }

  header .container > div:nth-child(2) > div:first-child > div:last-child {
    flex-shrink: 0 !important;
    margin-left: 1rem !important;
  }

  /* New specific classes for mobile header layout */
  .mobile-header-container {
    display: block !important;
  }

  .mobile-header-row {
    display: flex !important;
    flex-direction: row !important;
    justify-content: space-between !important;
    align-items: center !important;
    width: 100% !important;
    min-height: 40px !important;
    margin-bottom: 0.75rem !important;
  }

  .mobile-header-row > div:first-child {
    flex-shrink: 0 !important;
  }

  .mobile-header-row > div:last-child {
    flex-shrink: 0 !important;
    margin-left: 1rem !important;
  }

  /* Mobile navigation active state */
  header .container > div:nth-child(2) nav a.text-primary {
    border-bottom: 2px solid currentColor !important;
    padding-bottom: 0.25rem !important;
  }

  /* Mobile responsive adjustments for very small screens */
  @media (max-width: 480px) {
    header .container > div:nth-child(2) nav {
      gap: 1rem 1rem !important;
    }

    header .container > div:nth-child(2) nav a {
      font-size: 0.8125rem !important;
    }

    header .container > div:nth-child(2) .text-xl {
      font-size: 1.125rem !important;
    }

    /* Adjust dropdown position for very small screens */
    header .container > div:nth-child(2) .relative .absolute {
      top: 3.5rem !important;
      right: 0.5rem !important;
      width: 11rem !important;
      max-width: calc(100vw - 1rem) !important;
    }
  }
}

/* Ensure mobile header is completely hidden on desktop */
@media (min-width: 769px) {
  /* Force hide mobile header on desktop */
  header .container > div:nth-child(2) {
    display: none !important;
  }

  /* Ensure desktop header is visible on desktop */
  header .container > div:first-child {
    display: flex !important;
  }
}

/* Fix for page title layout (non-header) */
.container .flex.justify-between.items-center:not(header *) {
  display: flex !important;
  flex-direction: row !important;
  flex-wrap: nowrap !important;
  justify-content: space-between !important;
  align-items: center !important;
}

/* Ensure "Pridať novú hračku" button is properly aligned (non-header) */
.container .flex.justify-between.items-center:not(header *) .btn {
  margin-left: auto !important;
}
