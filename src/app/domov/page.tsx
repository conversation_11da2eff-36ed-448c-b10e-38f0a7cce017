'use client';
import Link from 'next/link';
import { useState } from 'react';
import LoginModal from '../../components/LoginModal';

export default function Home() {
  const [isLoginModalOpen, setIsLoginModalOpen] = useState(false);
  const [initialView, setInitialView] = useState<'login' | 'register'>('login');

  const handleLoginClick = (view: 'login' | 'register' = 'login') => {
    setInitialView(view);
    setIsLoginModalOpen(true);
  };
  return (
    <>
      {/* Hero sekcia */}
      <section className="bg-gradient-to-b from-neutral-100 to-white pt-4 pb-8 md:pt-6 md:pb-12">
        <div className="container mx-auto px-4">
          <div className="flex flex-col md:flex-row items-center">
            <div className="md:w-1/2 mb-8 md:mb-0">
              <h1 className="text-4xl md:text-5xl font-bold text-neutral-800 mb-4">
                Zdieľ<PERSON><PERSON> hra<PERSON>, <span className="text-primary">šetrite prírodu</span>
              </h1>
              <p className="text-lg text-neutral-600 mb-4">
                Swapka je platforma, kde si rodičia môžu požičiavať a vymieňať detské hračky.
                Šetrite peniaze, priestor a prírodu tým, že dáte hračkám druhý život.
              </p>
              <div className="flex flex-col sm:flex-row gap-4">
                <Link href="/hracky" className="btn btn-primary">
                  Prehliadať hračky
                </Link>
                <button onClick={() => handleLoginClick('register')} className="btn btn-outline">
                  Prihlásiť sa
                </button>
              </div>
            </div>
            <div className="md:w-1/2 flex justify-center">
              <div className="relative w-full max-w-md">
                <div className="absolute -top-4 -left-4 w-64 h-64 bg-primary-light rounded-full opacity-20 z-0"></div>
                <div className="absolute -bottom-8 -right-8 w-48 h-48 bg-secondary-light rounded-full opacity-20 z-0"></div>
                <div className="relative z-10 bg-white p-4 rounded-lg shadow-lg">
                  <div className="w-full h-64 bg-primary-light rounded-lg flex items-center justify-center">
                    <span className="text-7xl">🧸</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Ako to funguje sekcia - moderný dizajn 2024 */}
      <section className="py-20 md:py-4 relative overflow-hidden bg-gradient-to-br from-slate-50 via-white to-primary/5">
        {/* Moderný background pattern */}
        <div className="absolute inset-0 opacity-30">
          <div className="absolute top-0 left-0 w-72 h-72 bg-primary/10 rounded-full blur-3xl"></div>
          <div className="absolute bottom-0 right-0 w-96 h-96 bg-secondary/10 rounded-full blur-3xl"></div>
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-64 h-64 bg-accent/10 rounded-full blur-3xl"></div>
        </div>

        <div className="container mx-auto px-4 relative z-10">
          {/* Moderný nadpis s progress indikátorom */}
          <div className="text-center mb-16 md:mb-4">
            <h2 className="text-4xl md:text-6xl font-bold mb-6 bg-gradient-to-r from-neutral-900 via-neutral-700 to-neutral-900 bg-clip-text text-transparent leading-tight">
              Ako to funguje
            </h2>
            <p className="text-lg md:text-xl text-neutral-600 max-w-3xl mx-auto leading-relaxed">
              Zdieľanie hračiek nikdy nebolo jednoduchšie. Stačí pár kliknutí a vaše dieťa si môže užiť novú hračku.
            </p>
          </div>

        
          {/* Moderné kroky s contemporary design */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 md:gap-12">
            {/* Krok 1 - Pridajte hračku */}
            <div className="group relative">
              <div className="bg-white/80 backdrop-blur-sm p-8 md:p-10 rounded-3xl border border-white/20 shadow-xl hover:shadow-2xl transition-all duration-500 hover:-translate-y-2 relative overflow-hidden">
                {/* Gradient overlay */}
                <div className="absolute inset-0 bg-gradient-to-br from-primary/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500 rounded-3xl"></div>

                {/* Step indicator */}
                <div className="relative z-10">
                  <div className="flex items-center justify-between mb-6">
                    <div className="text-primary/20 group-hover:text-primary/40 transition-colors duration-300">
                      <svg className="w-8 h-8" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clipRule="evenodd" />
                      </svg>
                    </div>
                  </div>

                  <h3 className="text-2xl font-bold mb-4 text-neutral-900">Pridajte hračku</h3>
                  <p className="text-neutral-600 leading-relaxed mb-6">
                    Vytvorte atraktívny profil hračky s fotografiami a popisom. Nastavte podmienky požičania a dostupnosť.
                  </p>
                 
                </div>
              </div>
            </div>

            {/* Krok 2 - Dohodnite sa */}
            <div className="group relative">
              <div className="bg-white/80 backdrop-blur-sm p-8 md:p-10 rounded-3xl border border-white/20 shadow-xl hover:shadow-2xl transition-all duration-500 hover:-translate-y-2 relative overflow-hidden">
                {/* Gradient overlay */}
                <div className="absolute inset-0 bg-gradient-to-br from-secondary/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500 rounded-3xl"></div>

                {/* Step indicator */}
                <div className="relative z-10">
                  <div className="flex items-center justify-between mb-6">
                    <div className="text-secondary/20 group-hover:text-secondary/40 transition-colors duration-300">
                      <svg className="w-8 h-8" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M18 10c0 3.866-3.582 7-8 7a8.841 8.841 0 01-4.083-.98L2 17l1.338-3.123C2.493 12.767 2 11.434 2 10c0-3.866 3.582-7 8-7s8 3.134 8 7zM7 9H5v2h2V9zm8 0h-2v2h2V9zM9 9h2v2H9V9z" clipRule="evenodd" />
                      </svg>
                    </div>
                  </div>

                  <h3 className="text-2xl font-bold mb-4 text-neutral-900">Dohodnite sa</h3>
                  <p className="text-neutral-600 leading-relaxed mb-6">
                    Komunikujte bezpečne cez našu platformu. Dohodnite si termín, miesto a podmienky požičania.
                  </p>

                </div>
              </div>
            </div>

            {/* Krok 3 - Užite si hračku */}
            <div className="group relative">
              <div className="bg-white/80 backdrop-blur-sm p-8 md:p-10 rounded-3xl border border-white/20 shadow-xl hover:shadow-2xl transition-all duration-500 hover:-translate-y-2 relative overflow-hidden">
                {/* Gradient overlay */}
                <div className="absolute inset-0 bg-gradient-to-br from-accent/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500 rounded-3xl"></div>

                {/* Step indicator */}
                <div className="relative z-10">
                  <div className="flex items-center justify-between mb-6">
                    <div className="text-accent/20 group-hover:text-accent/40 transition-colors duration-300">
                      <svg className="w-8 h-8" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z" clipRule="evenodd" />
                      </svg>
                    </div>
                  </div>

                  <h3 className="text-2xl font-bold mb-4 text-neutral-900">Užite si hračku</h3>
                  <p className="text-neutral-600 leading-relaxed mb-6">
                    Vyzdvihnite si hračku a nechajte svoje dieťa objaviť nové zážitky. Vráťte ju v dohodnutom termíne.
                  </p>
                 
                </div>
              </div>
            </div>
          </div>
        
        </div>
      </section>

      {/* Výhody sekcia */}
      <section className="py-16 bg-neutral-100">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold text-center mb-12">Prečo používať Swapku?</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            <div className="bg-white p-6 rounded-lg shadow-sm">
              <div className="text-primary text-3xl mb-4">💰</div>
              <h3 className="text-xl font-semibold mb-2">Šetrite peniaze</h3>
              <p className="text-neutral-600">
                Prečo kupovať nové hračky, keď si ich môžete požičať za zlomok ceny?
              </p>
            </div>
            <div className="bg-white p-6 rounded-lg shadow-sm">
              <div className="text-primary text-3xl mb-4">🌱</div>
              <h3 className="text-xl font-semibold mb-2">Ekologické riešenie</h3>
              <p className="text-neutral-600">
                Znížte svoju uhlíkovú stopu a prispejte k udržateľnejšej budúcnosti.
              </p>
            </div>
            <div className="bg-white p-6 rounded-lg shadow-sm">
              <div className="text-primary text-3xl mb-4">🏠</div>
              <h3 className="text-xl font-semibold mb-2">Menej neporiadku</h3>
              <p className="text-neutral-600">
                Zbavte sa nepoužívaných hračiek a získajte priestor vo svojom domove.
              </p>
            </div>
            <div className="bg-white p-6 rounded-lg shadow-sm">
              <div className="text-primary text-3xl mb-4">👨‍👩‍👧‍👦</div>
              <h3 className="text-xl font-semibold mb-2">Budujte komunitu</h3>
              <p className="text-neutral-600">
                Spoznajte ďalších rodičov vo vašom okolí a vytvorte nové priateľstvá.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* CTA sekcia */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl font-bold mb-4">Pripravení začať?</h2>
          <p className="text-lg text-neutral-600 mb-8 max-w-2xl mx-auto">
            Pridajte sa k rastúcej komunite rodičov, ktorí sa rozhodli pre udržateľnejší prístup k detským hračkám.
          </p>
          <button onClick={() => handleLoginClick('register')} className="btn btn-primary px-8 py-3 text-lg">
            Prihlásiť sa
          </button>
        </div>
      </section>

      {/* Login Modal */}
      <LoginModal
        isOpen={isLoginModalOpen}
        onClose={() => setIsLoginModalOpen(false)}
        initialView={initialView}
      />
    </>
  );
}
