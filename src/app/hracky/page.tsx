'use client';

import { useState, useEffect } from 'react';
import TagSelect from '../../components/TagSelect';
import CustomSelect from '../../components/CustomSelect';
import ToyCard from '../../components/ToyCard';
import { useAuth } from '../../contexts/AuthContext';

// Typy pre dáta
interface ToyOwner {
  hashedId: string;
  name: string;
  city: string | null;
}

interface Toy {
  id: string; // Hashované ID
  name: string;
  description: string;
  image: string;
  type: string;
  typeLabel?: string; // Popisný label pre typ hračky
  location: string;
  price: number;
  deposit: number;
  priceType?: string; // Typ ceny (PER_DAY alebo PER_RENTAL)
  status: string;
  statusLabel?: string; // Popisný label pre status hračky
  distance?: number; // Vzdialenosť od používateľa v km (voliteľné)
  owner: ToyOwner; // Informácie o vlastníkovi hračky
}

interface ToyType {
  value: string;
  label: string;
}

interface Filters {
  toyTypes: ToyType[];
  locations: string[];
}

export default function ToysPage() {
  const { user } = useAuth();
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedTypes, setSelectedTypes] = useState<string[]>(['Všetky typy']);
  const [selectedLocation, setSelectedLocation] = useState('Všetky lokality');
  const [radius, setRadius] = useState(0); // Default radius is 0 km (exact city match)
  const [toys, setToys] = useState<Toy[]>([]);
  const [toyTypes, setToyTypes] = useState<ToyType[]>([{ value: 'Všetky typy', label: 'Všetky typy' }]);
  const [locations, setLocations] = useState<string[]>(['Všetky lokality']);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [userCoordinates, setUserCoordinates] = useState<{lat: number, lon: number} | null>(null);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid'); // New state for view mode
  const [isGettingLocation, setIsGettingLocation] = useState(false);
  const [geolocationStatus, setGeolocationStatus] = useState<'idle' | 'loading' | 'granted' | 'denied'>('idle');

  // localStorage key for view mode persistence
  const VIEW_MODE_STORAGE_KEY = 'swapka-toy-view-mode';

  // Function to check if localStorage is available
  const isLocalStorageAvailable = (): boolean => {
    try {
      const test = '__localStorage_test__';
      localStorage.setItem(test, test);
      localStorage.removeItem(test);
      return true;
    } catch {
      return false;
    }
  };

  // Function to save view mode to localStorage
  const saveViewMode = (mode: 'grid' | 'list') => {
    if (!isLocalStorageAvailable()) {
      console.warn('localStorage is not available, view mode will not be persisted');
      return;
    }

    try {
      localStorage.setItem(VIEW_MODE_STORAGE_KEY, mode);
    } catch (error) {
      console.warn('Failed to save view mode to localStorage:', error);
    }
  };

  // Function to load view mode from localStorage
  const loadViewMode = (): 'grid' | 'list' => {
    if (!isLocalStorageAvailable()) {
      console.warn('localStorage is not available, using default grid view');
      return 'grid';
    }

    try {
      const savedMode = localStorage.getItem(VIEW_MODE_STORAGE_KEY);
      if (savedMode === 'grid' || savedMode === 'list') {
        return savedMode;
      }
    } catch (error) {
      console.warn('Failed to load view mode from localStorage:', error);
    }
    return 'grid'; // Default fallback
  };

  // Custom setViewMode function that also saves to localStorage
  const handleViewModeChange = (mode: 'grid' | 'list') => {
    setViewMode(mode);
    saveViewMode(mode);
  };

  // Load saved view mode on component mount
  useEffect(() => {
    const savedViewMode = loadViewMode();
    setViewMode(savedViewMode);
  }, []);

  // Function to deactivate geolocation
  const deactivateGeolocation = () => {
    setUserCoordinates(null);
    setGeolocationStatus('idle');
    console.log('Geolocation deaktivovaná používateľom');
  };

  // Function to get user's current location or toggle it off
  const toggleGeolocation = () => {
    // Ak je geolocation už aktívna, deaktivujeme ju
    if (geolocationStatus === 'granted') {
      deactivateGeolocation();
      return;
    }

    // Ak geolocation nie je podporovaná
    if (!navigator.geolocation) {
      console.error('Geolocation is not supported by your browser');
      setGeolocationStatus('denied');
      return;
    }

    // Aktivujeme geolocation
    setIsGettingLocation(true);
    setGeolocationStatus('loading');

    navigator.geolocation.getCurrentPosition(
      (position) => {
        const { latitude, longitude } = position.coords;
        setUserCoordinates({ lat: latitude, lon: longitude });
        setGeolocationStatus('granted');
        setIsGettingLocation(false);
        console.log('Poloha úspešne získaná:', { latitude, longitude });
      },
      (error) => {
        // Podrobnejšie spracovanie chyby
        setIsGettingLocation(false);
        switch(error.code) {
          case error.PERMISSION_DENIED:
            // Používateľ zamietol povolenie
            console.error('Prístup k polohe bol zamietnutý používateľom');
            setGeolocationStatus('denied');
            return;
          case error.POSITION_UNAVAILABLE:
            console.error('Informácie o polohe nie sú dostupné');
            setGeolocationStatus('denied');
            break;
          case error.TIMEOUT:
            console.error('Vypršal časový limit pre získanie polohy');
            setGeolocationStatus('denied');
            break;
          default:
            console.error('Nepodarilo sa získať polohu používateľa:', error);
            setGeolocationStatus('denied');
            break;
        }
      },
      {
        enableHighAccuracy: true,
        timeout: 10000,
        maximumAge: 0
      }
    );
  };

  // Funkcia na generovanie CSS tried pre geolocation tlačidlo
  const getGeolocationButtonClasses = () => {
    const baseClasses = "btn btn-sm transition-all duration-200";

    switch (geolocationStatus) {
      case 'loading':
        return `${baseClasses} btn-outline opacity-75 cursor-not-allowed`;
      case 'granted':
        return `${baseClasses} btn-primary text-white shadow-md`;
      case 'denied':
        return `${baseClasses} btn-outline`;
      default: // 'idle'
        return `${baseClasses} btn-outline`;
    }
  };

  // Funkcia na generovanie textu tlačidla
  const getGeolocationButtonText = () => {
    switch (geolocationStatus) {
      case 'loading':
        return 'Získavam polohu...';
      case 'granted':
        return 'Deaktivovať polohu';
      case 'denied':
        return 'Použiť moju polohu';
      default: // 'idle'
        return 'Použiť moju polohu';
    }
  };

  // Funkcia na resetovanie všetkých filtrov vrátane geolocation
  const resetAllFilters = () => {
    setSearchTerm('');
    setSelectedTypes(['Všetky typy']);
    setSelectedLocation('Všetky lokality');
    setRadius(0);
    setGeolocationStatus('idle');
    setUserCoordinates(null);
  };

  // AuthLoadingProvider už zabezpečuje, že táto komponenta sa vykreslí
  // až keď je stav autentifikácie známy

  // Reset geolocation stavu pri zmene lokality
  useEffect(() => {
    if (selectedLocation === 'Všetky lokality') {
      // Ak sa vrátime na "Všetky lokality", resetujeme geolocation stav
      setGeolocationStatus('idle');
      setUserCoordinates(null);
    }
  }, [selectedLocation]);

  // Načítanie filtrov
  useEffect(() => {
    async function loadFilters() {
      try {
        const response = await fetch('/api/filters');
        if (!response.ok) {
          throw new Error('Nepodarilo sa načítať filtre');
        }
        const data: Filters = await response.json();
        setToyTypes(data.toyTypes);
        setLocations(data.locations);

        // Geolocation sa už nevolá automaticky - len na explicitné kliknutie tlačidla
        // getUserLocation();
      } catch (err) {
        console.error('Chyba pri načítaní filtrov:', err);
        setError('Nepodarilo sa načítať filtre');
      }
    }

    loadFilters();
  }, []);

  // Načítanie hračiek
  useEffect(() => {
    async function loadToys() {
      // AuthLoadingProvider už zabezpečuje, že táto komponenta sa vykreslí
      // až keď je stav autentifikácie známy, takže nemusíme kontrolovať authStateKnown
      setLoading(true);
      try {
        let url = '/api/toys';
        const params = new URLSearchParams();

        if (searchTerm) {
          params.append('search', searchTerm);
        }

        // Handle multiple selected toy types
        if (selectedTypes.length > 0 && !selectedTypes.includes('Všetky typy')) {
          // Add each selected type as a separate parameter
          selectedTypes.forEach(type => {
            params.append('types', type);
          });
        }

        // Pridanie parametrov pre lokáciu a vzdialenosť
        if (selectedLocation !== 'Všetky lokality') {
          // Konkrétna lokalita je vybraná
          params.append('location', selectedLocation);

          // Add radius parameter if a location is selected
          if (radius > 0) {
            params.append('radius', radius.toString());

            // Add coordinates if available
            if (userCoordinates) {
              params.append('lat', userCoordinates.lat.toString());
              params.append('lon', userCoordinates.lon.toString());
            }
          }
        } else if (userCoordinates) {
          // Všetky lokality, ale máme súradnice používateľa - zoradíme podľa vzdialenosti
          params.append('sortByDistance', 'true');
          params.append('lat', userCoordinates.lat.toString());
          params.append('lon', userCoordinates.lon.toString());
        }

        if (params.toString()) {
          url += `?${params.toString()}`;
        }

        // Pripravíme hlavičky pre požiadavku
        const headers: Record<string, string> = {};

        // Pridáme autentifikačné hlavičky, ak je používateľ prihlásený
        if (user && user.hashedUserId) {
          // Pridávame autentifikačné hlavičky pre prihláseného používateľa
          // Pridáme autorizačný token, ak je k dispozícii
          if (typeof user.getIdToken === 'function') {
            try {
              const token = await user.getIdToken();
              headers['Authorization'] = `Bearer ${token}`;
            } catch (error) {
              console.error('Chyba pri získavaní autentifikačného tokenu:', error);
            }
          }
        } else {
          // Používateľ nie je prihlásený, požiadavka bude bez autentifikačných hlavičiek
        }

        // Načítavanie hračiek
        const response = await fetch(url, { headers });
        if (!response.ok) {
          throw new Error('Nepodarilo sa načítať hračky');
        }

        const data: Toy[] = await response.json();
        setToys(data);
        setError('');
      } catch (err) {
        console.error('Chyba pri načítaní hračiek:', err);
        setError('Nepodarilo sa načítať hračky');
        setToys([]);
      } finally {
        setLoading(false);
      }
    }

    loadToys();
  }, [searchTerm, selectedTypes, selectedLocation, radius, userCoordinates, user]);

  return (
    <div className="container mx-auto px-4 py-8 mb-16">
      {/* <h1 className="text-3xl font-bold mb-8">Prehliadať hračky</h1> */}

      {/* Filtrovanie */}
      <div className="bg-white p-6 rounded-lg shadow-sm mb-8">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {/* Vyhľadávanie */}
          <div>
            <label htmlFor="search" className="block text-sm font-medium text-neutral-700 mb-1">
              Vyhľadávanie
            </label>
            <input
              type="text"
              id="search"
              className="w-full"
              placeholder="Hľadať hračky..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>

          {/* Filter podľa typu - tag select */}
          <div>
            <label htmlFor="type" className="block text-sm font-medium text-neutral-700 mb-1">
              Typy hračiek
            </label>
            <TagSelect
              id="type"
              options={toyTypes}
              value={selectedTypes}
              onChange={setSelectedTypes}
              placeholder="Všetky typy hračiek"
              allOptionValue="Všetky typy"
            />
            <p className="text-xs text-neutral-500 mt-1">
              Kliknutím vyberte viacero typov hračiek
            </p>
          </div>

          {/* Filter podľa lokality */}
          <div>
            <label htmlFor="location" className={`block text-sm font-medium mb-1 ${
              geolocationStatus === 'granted' ? 'text-neutral-400' : 'text-neutral-700'
            }`}>
              Lokalita
            </label>
            <CustomSelect
              id="location"
              options={locations}
              value={selectedLocation}
              onChange={setSelectedLocation}
              placeholder="Vyberte lokalitu..."
              disabled={geolocationStatus === 'granted'}
              disabledTooltip="Selektor lokality je vypnutý, pretože používate geolocation. Deaktivujte geolocation pre výber konkrétnej lokality."
            />
            {geolocationStatus === 'granted' && (
              <p className="text-xs text-neutral-400 mt-1">
                📍 Používa sa vaša aktuálna poloha namiesto výberu lokality
              </p>
            )}
          </div>
        </div>

        {/* Radius filter - only shown when a location is selected */}
        {selectedLocation !== 'Všetky lokality' && (
          <div className="mt-4 pt-4 border-t border-neutral-100">
            <div className="flex flex-col md:flex-row md:items-center gap-4">
              <div className="flex-grow">
                <label htmlFor="radius" className="block text-sm font-medium text-neutral-700 mb-1">
                  Vzdialenosť (km)
                </label>
                <input
                  type="range"
                  id="radius"
                  min="0"
                  max="50"
                  step="5"
                  className="w-full"
                  value={radius}
                  onChange={(e) => setRadius(parseInt(e.target.value))}
                />
              </div>
              <div className="md:w-24 flex items-center">
                <span className="text-lg font-medium">{radius} km</span>
              </div>
              <div>
                <button
                  className={getGeolocationButtonClasses()}
                  onClick={toggleGeolocation}
                  disabled={isGettingLocation}
                  title={
                    geolocationStatus === 'granted'
                      ? 'Kliknite pre deaktiváciu geolocation'
                      : geolocationStatus === 'loading'
                      ? 'Získavam vašu polohu...'
                      : 'Použiť moju aktuálnu polohu'
                  }
                >
                  {isGettingLocation ? (
                    <div className="flex items-center gap-2">
                      <div className="animate-spin h-4 w-4 border-2 border-current border-t-transparent rounded-full"></div>
                      <span>Získavam...</span>
                    </div>
                  ) : (
                    <div className="flex items-center gap-2">
                      {geolocationStatus === 'granted' ? (
                        <>
                          <span className="text-green-200">✅</span>
                          <span>{getGeolocationButtonText()}</span>
                        </>
                      ) : (
                        <>
                          <span>📍</span>
                          <span>{getGeolocationButtonText()}</span>
                        </>
                      )}
                    </div>
                  )}
                </button>
              </div>
            </div>
            <p className="text-xs text-neutral-500 mt-1">
              {radius === 0
                ? 'Presná zhoda s mestom'
                : `Hľadať hračky do ${radius} km od zvolenej lokality`}
            </p>
          </div>
        )}
      </div>

      {/* Informácia o zoradení podľa vzdialenosti */}
      {selectedLocation === 'Všetky lokality' && userCoordinates && (
        <div className="bg-blue-50 p-3 rounded-lg mb-4 text-sm text-blue-700">
          <p>
            <span className="font-medium">📏 Hračky sú zoradené podľa vzdialenosti</span> od vašej aktuálnej polohy.
          </p>
        </div>
      )}

      {/* View Mode Toggle - Hidden on mobile, single toggle button on desktop */}
      <div className="hidden md:flex justify-start mb-4">
        <div className="bg-white rounded-lg shadow-sm p-2 flex items-center gap-2">
          <span className="text-sm text-neutral-600 mr-2">Zobrazenie:</span>
          <button
            onClick={() => handleViewModeChange(viewMode === 'grid' ? 'list' : 'grid')}
            className="p-2 rounded-md transition-colors text-neutral-600 hover:bg-neutral-100"
            title={viewMode === 'grid' ? 'Prepnúť na zobrazenie v zozname' : 'Prepnúť na zobrazenie v mriežke'}
          >
            {viewMode === 'grid' ? (
              // Show list icon when in grid mode (clicking will switch to list)
              <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clipRule="evenodd" />
              </svg>
            ) : (
              // Show grid icon when in list mode (clicking will switch to grid)
              <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                <path d="M5 3a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2V5a2 2 0 00-2-2H5zM5 11a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2v-2a2 2 0 00-2-2H5zM11 5a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V5zM11 13a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z" />
              </svg>
            )}
          </button>
        </div>
      </div>

      {/* Výsledky */}
      {loading ? (
        <div className="text-center py-12">
          <p className="text-neutral-500 text-lg">Načítavam hračky...</p>
        </div>
      ) : error ? (
        <div className="text-center py-12">
          <p className="text-neutral-500 text-lg">{error}</p>
          <button
            className="mt-4 btn btn-outline"
            onClick={resetAllFilters}
          >
            Skúsiť znova
          </button>
        </div>
      ) : (
        <div className={viewMode === 'grid'
          ? "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-24"
          : "flex flex-col gap-4 mb-24"
        }>
          {toys.length > 0 ? (
            toys.map((toy) => (
              <ToyCard key={toy.id} toy={toy} viewMode={viewMode} />
            ))
          ) : (
            <div className="col-span-full text-center py-12">
              <p className="text-neutral-500 text-lg">
                Nenašli sa žiadne hračky zodpovedajúce vašim filtrom.
              </p>
              <button
                className="mt-4 btn btn-outline"
                onClick={resetAllFilters}
              >
                Resetovať filtre
              </button>
            </div>
          )}
        </div>
      )}
    </div>
  );
}
