import { Metadata } from 'next';
import { notFound } from 'next/navigation';
import {
  getToyForSeo,
  validateToyUrlParam,
  generateToyMetaTitle,
  generateToyMetaDescription,
  generateToyOpenGraphTags,
  generateToyTwitterCardTags,
  generateToyStructuredData,
  getBaseUrl
} from '../../../lib/seoUtils';
import ToyDetailClient from './ToyDetailClient';

/**
 * Generate metadata for toy detail page
 */
export async function generateMetadata({ params }: { params: Promise<{ id: string }> }): Promise<Metadata> {
  const resolvedParams = await params;
  const { hashedId } = validateToyUrlParam(resolvedParams.id);

  if (!hashedId) {
    return {
      title: 'Hračka nenájdená | Swapka',
      description: 'Požadovaná hračka nebola nájdená.',
    };
  }

  const toy = await getToyForSeo(hashedId);

  if (!toy) {
    return {
      title: 'Hračka nenájdená | Swapka',
      description: 'Požadovaná hračka nebola nájdená.',
    };
  }

  const baseUrl = getBaseUrl();
  const location = toy.location?.city || toy.owner?.city;

  // Generate meta tags
  const title = generateToyMetaTitle(toy.name, location);
  const description = generateToyMetaDescription(
    toy.name,
    toy.description,
    location,
    toy.typeLabel || toy.type
  );

  const openGraphTags = generateToyOpenGraphTags(toy, baseUrl);
  const twitterCardTags = generateToyTwitterCardTags(toy, baseUrl);

  return {
    title,
    description,
    openGraph: {
      type: 'website',
      title: openGraphTags['og:title'],
      description: openGraphTags['og:description'],
      url: openGraphTags['og:url'],
      images: [
        {
          url: openGraphTags['og:image'],
          alt: openGraphTags['og:image:alt'],
        },
      ],
      siteName: openGraphTags['og:site_name'],
    },
    twitter: {
      card: 'summary_large_image',
      title: twitterCardTags['twitter:title'],
      description: twitterCardTags['twitter:description'],
      images: [twitterCardTags['twitter:image']],
    },
    other: {
      'product:price:amount': openGraphTags['product:price:amount'],
      'product:price:currency': openGraphTags['product:price:currency'],
    },
  };
}

/**
 * Server component for toy detail page
 */
export default async function ToyDetailPage({ params }: { params: Promise<{ id: string }> }) {
  const resolvedParams = await params;
  const { hashedId, isLegacy } = validateToyUrlParam(resolvedParams.id);

  if (!hashedId) {
    notFound();
  }

  const toy = await getToyForSeo(hashedId);

  if (!toy) {
    notFound();
  }

  // Generate structured data for JSON-LD
  const baseUrl = getBaseUrl();
  const structuredData = generateToyStructuredData(toy, baseUrl);

  return (
    <>
      {/* JSON-LD Structured Data */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(structuredData),
        }}
      />

      {/* Client Component */}
      <ToyDetailClient toyId={hashedId} isLegacyUrl={isLegacy} />
    </>
  );
}
