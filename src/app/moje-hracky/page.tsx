'use client';

import { useEffect, useState } from 'react';
import { useAuth } from '../../contexts/AuthContext';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import Image from 'next/image';
import DeleteToyModal from '../../components/DeleteToyModal';
import LoadingSpinner from '../../components/LoadingSpinner';
import { User } from 'firebase/auth';
import { addCsrfHeader, fetchWithCsrf } from '../../lib/csrfClient';
import { translateToyStatus, getToyStatusClasses } from '../../lib/constants';
import { formatToyPricePerDay } from '../../lib/priceUtils';

interface Toy {
  id: number | string; // Can be either numeric or hashed ID
  hashedId: string; // Hashované ID je povinné
  name: string;
  description: string;
  image: string;
  type: string;
  typeLabel?: string; // Popisný label pre typ hračky
  location: string;
  price: number;
  deposit: number;
  priceType?: string; // Typ ceny (PER_DAY alebo PER_RENTAL)
  status: string;
  statusLabel?: string; // Popisný label pre status hračky
}

export default function MyToysPage() {
  const { user, getLongLivedToken } = useAuth();
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(true);
  const [toys, setToys] = useState<Toy[]>([]);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [isDeleting, setIsDeleting] = useState(false);
  const [isCreatingToy, setIsCreatingToy] = useState(false);
  const [hasLocationSet, setHasLocationSet] = useState<boolean>(false);
  const [isCheckingLocation, setIsCheckingLocation] = useState<boolean>(true);

  // Stav pre modálne okno odstránenia
  const [deleteModalOpen, setDeleteModalOpen] = useState(false);
  const [toyToDelete, setToyToDelete] = useState<Toy | null>(null);

  // Presmerovanie na domovskú stránku, ak používateľ nie je prihlásený
  useEffect(() => {
    if (!user && !isLoading) {
      router.push('/');
    }
  }, [user, router, isLoading]);

  // Kontrola, či má používateľ nastavenú polohu
  useEffect(() => {
    async function checkUserLocation() {
      if (!user || !user.hashedUserId) {
        setIsCheckingLocation(false);
        return;
      }

      try {
        // Získanie Firebase tokenu pre autentifikáciu s dlhšou platnosťou
        let token;
        try {
          // Skúsime získať token pomocou getLongLivedToken
          token = await getLongLivedToken(user as User);
        } catch (error) {
          console.error('Chyba pri získavaní tokenu pomocou getLongLivedToken:', error);

          // Fallback: Skúsime získať token priamo
          if (user.getIdToken) {
            token = await user.getIdToken(true);
          } else {
            throw new Error('Chyba pri získavaní autentifikačného tokenu');
          }
        }

        // Kontrola, či má používateľ hashedUserId
        if (!user.hashedUserId) {
          throw new Error('Chýba hashedUserId používateľa');
        }

        // Získanie detailov používateľa z API
        const response = await fetch(`/api/users/${user.hashedUserId}`, {
          headers: {
            'Authorization': `Bearer ${token}`,
          },
        });

        if (response.ok) {
          const userData = await response.json();
          // Kontrola, či má používateľ nastavenú polohu (latitude a longitude)
          setHasLocationSet(userData.latitude !== null && userData.longitude !== null);
        } else {
          console.error('Nepodarilo sa načítať údaje používateľa');
        }
      } catch (err) {
        console.error('Chyba pri kontrole polohy používateľa:', err);
      } finally {
        setIsCheckingLocation(false);
      }
    }

    if (user) {
      checkUserLocation();
    } else {
      setIsCheckingLocation(false);
    }
  }, [user]);

  // Načítanie hračiek používateľa
  useEffect(() => {
    async function loadUserToys() {
      if (!user || !user.hashedUserId) {
        setIsLoading(false);
        return;
      }

      try {
        // Pripravíme hlavičky pre autentifikáciu
        const headers: Record<string, string> = {};

        // Pridáme autentifikačné hlavičky
        try {
          // Skúsime získať token pomocou getLongLivedToken
          let token;
          try {
            token = await getLongLivedToken(user as User);
          } catch (error) {
            console.error('Chyba pri získavaní tokenu pomocou getLongLivedToken:', error);

            // Fallback: Skúsime získať token priamo
            if (user.getIdToken) {
              token = await user.getIdToken(true);
            } else {
              throw new Error('Chyba pri získavaní autentifikačného tokenu');
            }
          }

          headers['Authorization'] = `Bearer ${token}`;
        } catch (error) {
          console.error('Chyba pri získavaní autentifikačného tokenu:', error);
        }

        // Kontrola, či má používateľ hashedUserId
        if (!user.hashedUserId) {
          throw new Error('Chýba hashedUserId používateľa');
        }

        // Načítanie hračiek používateľa podľa jeho hashovaného ID
        const response = await fetch(`/api/toys?userId=${user.hashedUserId}`, {
          headers: headers
        });

        if (!response.ok) {
          throw new Error('Nepodarilo sa načítať hračky');
        }
        const data = await response.json();

        // Kontrola, či každá hračka má hashedId
        const processedToys = data.map((toy: any) => {
          // Ak hračka nemá hashedId, použijeme id ako fallback
          if (!toy.hashedId && toy.id) {
            console.warn(`Hračka s ID ${toy.id} nemá hashedId, používam ID ako fallback`);
            toy.hashedId = toy.id;
          }
          return toy;
        });

        console.log('Načítané hračky:', processedToys);
        setToys(processedToys);
      } catch (err) {
        setError('Nastala chyba pri načítaní hračiek');
        console.error('Chyba pri načítaní hračiek:', err);
      } finally {
        setIsLoading(false);
      }
    }

    if (user) {
      loadUserToys();
    } else {
      setIsLoading(false);
    }
  }, [user]);

  // Funkcia pre odstránenie hračky
  const handleDeleteToy = async () => {
    if (!toyToDelete || !user) return;

    setIsDeleting(true);
    setError(null);
    setSuccess(null);

    try {
      // Získanie Firebase tokenu pre autentifikáciu s dlhšou platnosťou
      let token;
      try {
        // Skúsime získať token pomocou getLongLivedToken
        token = await getLongLivedToken(user as User);
      } catch (error) {
        console.error('Chyba pri získavaní tokenu pomocou getLongLivedToken:', error);

        // Fallback: Skúsime získať token priamo
        if (user.getIdToken) {
          token = await user.getIdToken(true);
        } else {
          throw new Error('Chyba pri získavaní autentifikačného tokenu');
        }
      }

      // Kontrola, či má používateľ hashedUserId
      if (!user.hashedUserId) {
        throw new Error('Chýba hashedUserId používateľa');
      }

      // Pripravíme hlavičky s autentifikáciou
      const baseHeaders = {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`,
      };

      console.log(`Odstraňujem hračku s ID: ${toyToDelete.id}`);

      // Použitie fetchWithCsrf namiesto manuálneho pridávania CSRF tokenu
      // Táto funkcia automaticky získa nový CSRF token a pridá ho do hlavičiek
      const response = await fetchWithCsrf('/api/toys/delete', {
        method: 'POST',
        headers: baseHeaders,
        body: JSON.stringify({ id: toyToDelete.id }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Nepodarilo sa odstrániť hračku');
      }

      // Aktualizácia zoznamu hračiek - použijeme ID pre filtrovanie
      setToys(toys.filter(toy => toy.id !== toyToDelete.id));
      setSuccess('Hračka bola úspešne odstránená');

      // Skrytie hlásenia o úspechu po 3 sekundách
      setTimeout(() => {
        setSuccess(null);
      }, 3000);
    } catch (err) {
      console.error('Chyba pri odstraňovaní hračky:', err);
      setError(err instanceof Error ? err.message : 'Nastala chyba pri odstraňovaní hračky');
    } finally {
      setIsDeleting(false);
      setDeleteModalOpen(false);
    }
  };

  // Funkcia pre otvorenie modálneho okna odstránenia
  const openDeleteModal = (toy: Toy) => {
    setToyToDelete(toy);
    setDeleteModalOpen(true);
  };

  // Funkcia pre vytvorenie novej hračky
  const handleCreateNewToy = async () => {
    if (!user || !user.hashedUserId) {
      setError('Nie ste prihlásený');
      return;
    }

    setIsCreatingToy(true);
    setError(null);
    setSuccess(null);

    try {
      // Získanie Firebase tokenu pre autentifikáciu s dlhšou platnosťou
      if (!user.getIdToken) {
        throw new Error('Chyba pri získavaní autentifikačného tokenu');
      }
      // Použitie funkcie getLongLivedToken namiesto priameho volania getIdToken
      const token = await getLongLivedToken(user as User);

      // Pripravíme hlavičky s autentifikáciou
      const baseHeaders = {
        'Authorization': `Bearer ${token}`,
      };

      console.log('Vytváram novú hračku');

      // Použitie fetchWithCsrf namiesto manuálneho pridávania CSRF tokenu
      // Táto funkcia automaticky získa nový CSRF token a pridá ho do hlavičiek
      const response = await fetchWithCsrf('/api/toys/init', {
        method: 'POST',
        headers: baseHeaders,
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Nepodarilo sa vytvoriť novú hračku');
      }

      const data = await response.json();

      // Kontrola, či API vrátilo hashedId
      if (!data.hashedId) {
        console.error('API nevrátilo hashedId pre novú hračku:', data);
        // Použijeme ID ako fallback, ak hashedId chýba
        data.hashedId = data.id;
      }

      console.log('Vytvorená nová hračka:', data);

      // Presmerovanie na stránku úpravy hračky s ID novej hračky
      router.push(`/moje-hracky/nova?id=${data.hashedId || data.id}`);
    } catch (err) {
      console.error('Chyba pri vytváraní novej hračky:', err);
      setError(err instanceof Error ? err.message : 'Nastala chyba pri vytváraní novej hračky');
      setIsCreatingToy(false);
    }
  };

  if (isLoading || isCheckingLocation) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">
          <p className="text-neutral-500 text-lg">Načítavam hračky...</p>
        </div>
      </div>
    );
  }

  if (!user) {
    return null; // Toto sa nezobrazí, pretože useEffect presmeruje používateľa
  }



  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex justify-between items-center mb-8">
        <h1 className="text-3xl font-bold">Moje hračky</h1>
        <button
          onClick={handleCreateNewToy}
          className={`btn ${hasLocationSet ? 'btn-primary' : 'btn-disabled bg-gray-300'}`}
          disabled={isCreatingToy || !hasLocationSet}
          title={!hasLocationSet ? 'Pre pridanie hračky si najprv nastavte polohu v profile' : ''}
        >
          {isCreatingToy ? (
            <span className="flex items-center">
              <LoadingSpinner size="small" />
              <span className="ml-2">Vytváram...</span>
            </span>
          ) : (
            'Pridať novú hračku'
          )}
        </button>
      </div>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          {error}
        </div>
      )}

      {success && (
        <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
          {success}
        </div>
      )}

      {toys.length > 0 ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {toys.map((toy) => (
            <div key={toy.id} className="bg-white rounded-lg shadow-sm overflow-hidden">
              <div className="relative h-48 bg-neutral-200">
                {toy.image && !toy.image.includes('placeholder.jpg') ? (
                  <div className="w-full h-full">
                    <img
                      src={toy.image}
                      alt={toy.name}
                      className="w-full h-full object-cover"
                    />
                  </div>
                ) : (
                  <div className="w-full h-full flex items-center justify-center text-neutral-400">
                    {/* Placeholder pre obrázok */}
                    <span className="text-5xl">🧸</span>
                  </div>
                )}
                <div className="absolute top-2 right-2">
                  <span className={`inline-block px-2 py-1 text-xs rounded-full font-medium ${getToyStatusClasses(toy.status)}`}>
                    {toy.statusLabel || translateToyStatus(toy.status)}
                  </span>
                </div>
              </div>
              <div className="p-4">
                <div className="flex justify-between items-start">
                  <h2 className="text-xl font-semibold">{toy.name}</h2>
                  <span className="text-primary font-bold">{formatToyPricePerDay(toy.price, toy.priceType)}</span>
                </div>
                <p className="text-neutral-600 text-sm mt-2 line-clamp-2">{toy.description}</p>
                <div className="flex items-center justify-between mt-3 text-sm text-neutral-500">
                  <span className="toy-location">📍 {toy.location}</span>
                  <span className="toy-category">🏷️ {toy.typeLabel || toy.type}</span>
                </div>
                <div className="flex items-center justify-between mt-3 text-sm text-neutral-500">
                  {toy.deposit === 0 ? (
                    <span className="text-sm text-green-600 bg-green-100 px-2 py-1 rounded-full">bez zálohy</span>
                  ) : (
                    <span className="text-sm text-neutral-500">Záloha: {toy.deposit} €</span>
                  )}
                </div>

                <div className="mt-4 pt-4 border-t border-neutral-100 flex justify-between items-center">
                 
                  <div className="flex space-x-2">
                    <Link
                      href={`/moje-hracky/nova?id=${toy.hashedId || toy.id}`}
                      className="btn btn-outline text-sm py-1 px-2"
                    >
                      Upraviť
                    </Link>
                    <button
                      onClick={() => openDeleteModal(toy)}
                      className="btn-delete-subtle"
                      disabled={isDeleting}
                      title="Odstrániť hračku"
                    >
                      <Image
                        src="/trash.svg"
                        alt="Odstrániť"
                        width={12}
                        height={12}
                        className="inline-block opacity-70"
                      />
                    </button>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      ) : (
        <div className="bg-white rounded-lg shadow-sm p-8 text-center">
          <div className="text-5xl mb-4">🧸</div>
          <h2 className="text-xl font-semibold mb-2">Zatiaľ nemáte žiadne hračky</h2>
          <p className="text-neutral-500 mb-4">
            Začnite zdieľať svoje hračky s ostatnými rodičmi a prispejte k udržateľnosti.
          </p>

          {!hasLocationSet ? (
            <div>
              <div className="bg-blue-50 border border-blue-200 text-blue-700 px-4 py-3 rounded mb-4">
                <p className="font-medium">Pre pridanie prvej hračky si prosím nastavte polohu v nastaveniach profilu.</p>
              </div>
              <Link href="/profil" className="btn btn-primary">
                Nastaviť polohu v profile
              </Link>
            </div>
          ) : (
            <button
              onClick={handleCreateNewToy}
              className="btn btn-primary"
              disabled={isCreatingToy}
            >
              {isCreatingToy ? (
                <span className="flex items-center">
                  <LoadingSpinner size="small" />
                  <span className="ml-2">Vytváram...</span>
                </span>
              ) : (
                'Pridať prvú hračku'
              )}
            </button>
          )}
        </div>
      )}

      {/* Modálne okno pre odstránenie hračky */}
      {toyToDelete && (
        <DeleteToyModal
          isOpen={deleteModalOpen}
          onClose={() => setDeleteModalOpen(false)}
          onConfirm={handleDeleteToy}
          toyName={toyToDelete.name}
          toyDescription={toyToDelete.description}
          isDeleting={isDeleting}
        />
      )}
    </div>
  );
}
