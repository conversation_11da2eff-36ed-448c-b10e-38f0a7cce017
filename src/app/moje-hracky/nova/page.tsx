'use client';

import { useState, useEffect, Suspense, useCallback } from 'react';
import { useAuth } from '../../../contexts/AuthContext';
import { useRouter, useSearchParams } from 'next/navigation';
import Link from 'next/link';
import { toNumericId } from '../../../lib/hashUtils';
import CloudinaryUpload from '../../../components/CloudinaryUpload';
import { addCsrfHeader } from '../../../lib/csrfClient';
import ToyStatusSelect from '../../../components/ToyStatusSelect';
import CustomSelect from '../../../components/CustomSelect';
import LoadingOverlay from '../../../components/LoadingOverlay';
import LoadingButton from '../../../components/LoadingButton';

// Typy pre formulár
interface ToyFormData {
  name: string;
  description: string;
  type: string;
  price: string;
  deposit: string;
  priceType: string;
  status: string;
  maxReservationDays: number;
  images: string[];
}

// Typy pre filtre
interface Filters {
  toyTypes: {
    value: string;
    label: string;
  }[];
}

// Component that uses searchParams
function ToyEditForm() {
  const { user } = useAuth();
  const router = useRouter();
  const searchParams = useSearchParams();
  const hashedToyId = searchParams.get('id');
  // Konvertujeme hashované ID na číselné ID
  const toyId = hashedToyId ? toNumericId(hashedToyId) : null;

  // Rest of the component code...
  const [isLoading, setIsLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [filters, setFilters] = useState<Filters>({
    toyTypes: []
  });

  // Formulárové dáta
  const [formData, setFormData] = useState<ToyFormData>({
    name: '',
    description: '',
    type: '',
    price: '',
    deposit: '',
    priceType: 'PER_DAY', // Predvolená hodnota je cena za deň
    // locationId už nie je potrebné, používame lokalitu používateľa
    status: 'DRAFT', // Predvolený status je DRAFT
    maxReservationDays: 7, // Predvolená hodnota je 7 dní
    images: []
  });

  // Presmerovanie na domovskú stránku, ak používateľ nie je prihlásený
  useEffect(() => {
    if (!user && !isLoading) {
      router.push('/');
    }
  }, [user, router, isLoading]);

  // Načítanie filtrov a existujúcej hračky (ak existuje)
  useEffect(() => {
    async function loadData() {
      if (!user || !user.hashedUserId) {
        setIsLoading(false);
        return;
      }

      try {
        // Načítanie typov hračiek
        const typesResponse = await fetch('/api/filters');
        if (!typesResponse.ok) {
          throw new Error('Nepodarilo sa načítať typy hračiek');
        }
        const typesData = await typesResponse.json();

        setFilters({
          toyTypes: typesData.toyTypes.filter((type: any) => type.value !== 'Všetky typy')
        });

        // Získanie Firebase tokenu pre autentifikáciu
        if (!user.getIdToken) {
          throw new Error('Chyba pri získavaní autentifikačného tokenu');
        }
        const token = await user.getIdToken();

        // Kontrola, či máme ID hračky v URL parametri
        if (!toyId) {
          // Ak nemáme ID hračky, presmerujeme používateľa späť na zoznam hračiek
          setError('Chýba ID hračky. Budete presmerovaný späť na zoznam hračiek.');
          setTimeout(() => {
            router.push('/moje-hracky');
          }, 2000);
          return;
        }

        // Kontrola, či má používateľ hashedUserId
        if (!user.hashedUserId) {
          throw new Error('Chýba hashedUserId používateľa');
        }

        // Načítame existujúcu hračku - použijeme priamo hashedToyId
        const toyResponse = await fetch(`/api/toys/${hashedToyId}`, {
          headers: {
            'Authorization': `Bearer ${token}`,
          },
        });

        if (!toyResponse.ok) {
          throw new Error('Nepodarilo sa načítať hračku');
        }

        const toyData = await toyResponse.json();

        // Debug logging pre API response
        console.log('=== TOY DATA DEBUG ===');
        console.log('Full toyData:', toyData);
        console.log('toyData.owner:', toyData.owner);
        console.log('Available owner fields:', toyData.owner ? Object.keys(toyData.owner) : 'owner is null/undefined');
        console.log('=== USER DATA DEBUG ===');
        console.log('user.hashedUserId:', user.hashedUserId);
        console.log('=====================');

        // Kontrola, či existuje owner objekt
        if (!toyData.owner) {
          throw new Error('Informácie o vlastníkovi hračky nie sú dostupné');
        }

        // Kontrola, či používateľ je vlastníkom hračky
        // Skúsime rôzne možné polia pre owner ID
        let ownerHashedId = toyData.owner.hashedId || toyData.owner.id || toyData.owner.hashedUserId;

        if (!ownerHashedId) {
          console.error('Chýba hashované ID vlastníka hračky v API response');
          console.error('Dostupné polia v owner objekte:', Object.keys(toyData.owner));
          throw new Error('Chyba pri overovaní vlastníctva hračky - chýba ID vlastníka');
        }

        if (ownerHashedId !== user.hashedUserId) {
          console.log('Kontrola vlastníctva hračky zlyhala:');
          console.log('ownerHashedId (z API):', ownerHashedId);
          console.log('user.hashedUserId:', user.hashedUserId);
          throw new Error('Nemáte oprávnenie upraviť túto hračku');
        }

        // Filtrujeme placeholder obrázky
        const filteredImages = (toyData.images || []).filter((img: string) => !img.includes('/toys/placeholder.jpg') && !img.includes('placeholder.jpg'));

        // Kontrola, či ide o novú hračku s predvolenými hodnotami
        const isNewToy = toyData.name === 'Nová hračka' && toyData.description === 'Popis hračky';

        // Nastavenie formulárových dát
        setFormData({
          // Ak ide o novú hračku, nastavíme prázdne hodnoty pre názov a popis
          name: isNewToy ? '' : toyData.name,
          description: isNewToy ? '' : toyData.description,
          type: toyData.type,
          price: toyData.price.toString(),
          deposit: toyData.deposit.toString(),
          priceType: toyData.priceType || 'PER_DAY',
          status: toyData.status || 'DRAFT',
          maxReservationDays: toyData.maxReservationDays || 7,
          images: filteredImages
        });
      } catch (err) {
        console.error('Chyba pri načítaní dát:', err);
        setError(err instanceof Error ? err.message : 'Nepodarilo sa načítať potrebné údaje');
      } finally {
        setIsLoading(false);
      }
    }

    if (user) {
      loadData();
    } else {
      setIsLoading(false);
    }
  }, [user, toyId, router, hashedToyId]);

  // Spracovanie zmien vo formulári
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  // Memoizovaný handler pre zmenu obrázkov
  const handleImagesChange = useCallback((images: string[]) => {
    setFormData(prev => ({ ...prev, images }));
  }, []);

  // Odoslanie formulára
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!user || !user.hashedUserId) {
      setError('Nie ste prihlásený');
      return;
    }

    // Validácia formulára
    if (!formData.name || !formData.description || !formData.type || !formData.price || !formData.deposit || !formData.priceType) {
      setError('Vyplňte všetky povinné polia');
      return;
    }

    // Kontrola, či máme ID hračky
    if (!toyId) {
      setError('Chýba ID hračky. Budete presmerovaný späť na zoznam hračiek.');
      setTimeout(() => {
        router.push('/moje-hracky');
      }, 2000);
      return;
    }

    setIsSubmitting(true);
    setError(null);

    try {
      // Kontrola, či je používateľ prihlásený
      if (!user) {
        setError('Nie ste prihlásený');
        return;
      }

      // Získanie Firebase tokenu pre autentifikáciu
      if (!user.getIdToken) {
        throw new Error('Chyba pri získavaní autentifikačného tokenu');
      }
      const token = await user.getIdToken();

      let response;

      // Kontrola, či má používateľ hashedUserId
      if (!user.hashedUserId) {
        throw new Error('Chýba hashedUserId používateľa');
      }

      // Pripravíme hlavičky s autentifikáciou
      const baseHeaders = {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`,
      };

      // Pridáme CSRF token do hlavičiek
      const headers = await addCsrfHeader(baseHeaders);

      // Aktualizácia existujúcej hračky (vždy máme toyId, pretože hračka bola vytvorená pri načítaní stránky)
      response = await fetch('/api/toys/update', {
        method: 'POST',
        headers,
        body: JSON.stringify({
          id: hashedToyId, // Použijeme len hashované ID
          ...formData,
          // Pridanie placeholder obrázka, ak nie sú zadané žiadne obrázky
          // Filtrujeme placeholder obrázky, ak sú v zozname
          images: formData.images.filter(img => !img.includes('/toys/placeholder.jpg')).length > 0
            ? formData.images.filter(img => !img.includes('/toys/placeholder.jpg'))
            : ['/toys/placeholder.jpg']
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Nepodarilo sa aktualizovať hračku');
      }

      await response.json(); // Spracujeme odpoveď, ale nepotrebujeme ju uložiť
      setSuccess('Hračka bola úspešne aktualizovaná');

      // Presmerovanie na stránku s hračkami po 2 sekundách
      setTimeout(() => {
        router.push('/moje-hracky');
      }, 2000);
    } catch (err) {
      console.error('Chyba pri aktualizácii hračky:', err);
      setError(err instanceof Error ? err.message : 'Nastala chyba pri aktualizácii hračky');
    } finally {
      setIsSubmitting(false);
    }
  };

  if (isLoading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">
          <div className="flex justify-center mb-4">
            <svg className="animate-spin h-10 w-10 text-primary" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
          </div>
          <p className="text-neutral-500 text-lg">Načítavam údaje o hračke...</p>
        </div>
      </div>
    );
  }

  if (!user) {
    return null; // Toto sa nezobrazí, pretože useEffect presmeruje používateľa
  }

  return (
    <div className="container mx-auto px-4 py-8 mb-16">
      <div className="mb-8">
        <Link href="/moje-hracky" className="text-primary hover:underline flex items-center">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
          </svg>
          Späť na moje hračky
        </Link>
      </div>

      <h1 className="text-3xl font-bold mb-6">Upraviť hračku</h1>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          {error}
        </div>
      )}

      {success && (
        <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
          {success}
        </div>
      )}

      {/* Loading Overlay */}
      <LoadingOverlay
        isVisible={isSubmitting}
        message="Ukladám zmeny hračky..."
      />

      <form onSubmit={handleSubmit} className="bg-white rounded-lg shadow-sm p-6 mb-24">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Názov hračky */}
          <div className="col-span-2">
            <label htmlFor="name" className="block text-sm font-medium text-neutral-700 mb-1">
              Názov hračky *
            </label>
            <input
              type="text"
              id="name"
              name="name"
              value={formData.name}
              onChange={handleChange}
              className="w-full"
              required
              disabled={isSubmitting}
            />
          </div>

          {/* Popis hračky */}
          <div className="col-span-2">
            <label htmlFor="description" className="block text-sm font-medium text-neutral-700 mb-1">
              Popis hračky *
            </label>
            <textarea
              id="description"
              name="description"
              value={formData.description}
              onChange={handleChange}
              rows={4}
              className="w-full"
              required
              disabled={isSubmitting}
            ></textarea>
          </div>

          {/* Typ hračky */}
          <div>
            <label htmlFor="type" className="block text-sm font-medium text-neutral-700 mb-1">
              Typ hračky *
            </label>
            <CustomSelect
              id="type"
              options={filters.toyTypes}
              value={formData.type}
              onChange={(value) => setFormData({...formData, type: value})}
              placeholder="Vyberte typ hračky"
              disabled={isSubmitting}
            />
          </div>

          {/* Cena */}
          <div>
            <label htmlFor="price" className="block text-sm font-medium text-neutral-700 mb-1">
              Cena (€) *
            </label>
            <input
              type="number"
              id="price"
              name="price"
              value={formData.price}
              onChange={handleChange}
              min="0"
              step="0.01"
              className="w-full"
              required
              placeholder="Zadajte cenu alebo 0 pre dohodou"
              disabled={isSubmitting}
            />
            <p className="text-xs text-neutral-500 mt-1">
              💡 Tip: Ak zadáte 0 €, používateľom sa zobrazí "Dohodou" namiesto ceny
            </p>
          </div>

          {/* Typ ceny */}
          <div>
            <label htmlFor="priceType" className="block text-sm font-medium text-neutral-700 mb-1">
              Typ ceny *
            </label>
            <CustomSelect
              id="priceType"
              options={[
                { value: "PER_DAY", label: "Cena za deň" },
                { value: "PER_RENTAL", label: "Cena za výpožičku" }
              ]}
              value={formData.priceType}
              onChange={(value) => setFormData({...formData, priceType: value})}
              placeholder="Vyberte typ ceny"
              disabled={isSubmitting}
            />
            <p className="text-xs text-neutral-500 mt-1">
              Vyberte, či je cena za jeden deň alebo za celú výpožičku.
            </p>
          </div>

          {/* Záloha */}
          <div>
            <label htmlFor="deposit" className="block text-sm font-medium text-neutral-700 mb-1">
              Záloha (€) *
            </label>
            <input
              type="number"
              id="deposit"
              name="deposit"
              value={formData.deposit}
              onChange={handleChange}
              min="0"
              step="0.01"
              className="w-full"
              required
              disabled={isSubmitting}
            />
          </div>

          {/* Status hračky */}
          <div>
            <label htmlFor="status" className="block text-sm font-medium text-neutral-700 mb-1">
              Status hračky *
            </label>
            <ToyStatusSelect
              id="status"
              name="status"
              value={formData.status}
              onChange={handleChange}
              className="w-full"
              required
              disabled={isSubmitting}
            />
            <p className="text-xs text-neutral-500 mt-1">
              Nepublikovaná hračka nebude viditeľná pre ostatných používateľov.
            </p>
          </div>

          {/* Maximálny počet dní rezervácie */}
          <div>
            <label htmlFor="maxReservationDays" className="block text-sm font-medium text-neutral-700 mb-1">
              Maximálny počet dní rezervácie *
            </label>
            <CustomSelect
              id="maxReservationDays"
              options={[
                { value: "3", label: "3 dni" },
                { value: "7", label: "7 dní (týždeň)" },
                { value: "14", label: "14 dní (dva týždne)" },
                { value: "30", label: "30 dní (mesiac)" }
              ]}
              value={formData.maxReservationDays.toString()}
              onChange={(value) => setFormData({...formData, maxReservationDays: parseInt(value)})}
              placeholder="Vyberte počet dní"
              disabled={isSubmitting}
            />
            <p className="text-xs text-neutral-500 mt-1">
              Maximálna doba, na ktorú si môže používateľ rezervovať túto hračku.
            </p>
          </div>

          {/* Fotografie hračky */}
          <div className="col-span-2 mt-6">
            <CloudinaryUpload
              images={formData.images}
              onChange={handleImagesChange}
              toyId={hashedToyId || undefined}
            />
          </div>
        </div>

        <div className="mt-8 flex justify-end">
          <Link
            href="/moje-hracky"
            className={`btn btn-outline mr-2 ${isSubmitting ? 'pointer-events-none opacity-50' : ''}`}
          >
            Zrušiť
          </Link>
          <LoadingButton
            type="submit"
            isLoading={isSubmitting}
            loadingText="Ukladám..."
            className="btn btn-primary"
          >
            Uložiť zmeny
          </LoadingButton>
        </div>
      </form>
    </div>
  );
}

// Main page component with Suspense boundary
export default function NewToyPage() {
  return (
    <Suspense fallback={
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">
          <div className="flex justify-center mb-4">
            <svg className="animate-spin h-10 w-10 text-primary" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
          </div>
          <p className="text-neutral-500 text-lg">Načítavam...</p>
        </div>
      </div>
    }>
      <ToyEditForm />
    </Suspense>
  );

}
