
/* !!! This is code generated by Prisma. Do not edit directly. !!!
/* eslint-disable */

Object.defineProperty(exports, "__esModule", { value: true });

const {
  Decimal,
  objectEnumValues,
  makeStrictEnum,
  Public,
  getRuntime,
  skip
} = require('./runtime/index-browser.js')


const Prisma = {}

exports.Prisma = Prisma
exports.$Enums = {}

/**
 * Prisma Client JS version: 6.7.0
 * Query Engine version: 3cff47a7f5d65c3ea74883f1d736e41d68ce91ed
 */
Prisma.prismaVersion = {
  client: "6.7.0",
  engine: "3cff47a7f5d65c3ea74883f1d736e41d68ce91ed"
}

Prisma.PrismaClientKnownRequestError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientKnownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)};
Prisma.PrismaClientUnknownRequestError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientUnknownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientRustPanicError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientRustPanicError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientInitializationError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientInitializationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientValidationError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientValidationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.Decimal = Decimal

/**
 * Re-export of sql-template-tag
 */
Prisma.sql = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`sqltag is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.empty = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`empty is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.join = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`join is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.raw = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`raw is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.validator = Public.validator

/**
* Extensions
*/
Prisma.getExtensionContext = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`Extensions.getExtensionContext is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.defineExtension = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`Extensions.defineExtension is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}

/**
 * Shorthand utilities for JSON filtering
 */
Prisma.DbNull = objectEnumValues.instances.DbNull
Prisma.JsonNull = objectEnumValues.instances.JsonNull
Prisma.AnyNull = objectEnumValues.instances.AnyNull

Prisma.NullTypes = {
  DbNull: objectEnumValues.classes.DbNull,
  JsonNull: objectEnumValues.classes.JsonNull,
  AnyNull: objectEnumValues.classes.AnyNull
}



/**
 * Enums
 */

exports.Prisma.TransactionIsolationLevel = makeStrictEnum({
  ReadUncommitted: 'ReadUncommitted',
  ReadCommitted: 'ReadCommitted',
  RepeatableRead: 'RepeatableRead',
  Serializable: 'Serializable'
});

exports.Prisma.UserScalarFieldEnum = {
  id: 'id',
  email: 'email',
  name: 'name',
  password: 'password',
  phone: 'phone',
  address: 'address',
  city: 'city',
  postalCode: 'postalCode',
  role: 'role',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  status: 'status',
  firebaseUid: 'firebaseUid',
  latitude: 'latitude',
  longitude: 'longitude',
  termsAccepted: 'termsAccepted',
  isAnonymized: 'isAnonymized',
  deletedAt: 'deletedAt',
  emailNotificationsEnabled: 'emailNotificationsEnabled',
  emailReservationCreated: 'emailReservationCreated',
  emailReservationApproved: 'emailReservationApproved',
  emailUnsubscribeToken: 'emailUnsubscribeToken'
};

exports.Prisma.ToyScalarFieldEnum = {
  id: 'id',
  name: 'name',
  description: 'description',
  type: 'type',
  price: 'price',
  deposit: 'deposit',
  priceType: 'priceType',
  status: 'status',
  maxReservationDays: 'maxReservationDays',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  userId: 'userId',
  deletedAt: 'deletedAt'
};

exports.Prisma.ToyImageScalarFieldEnum = {
  id: 'id',
  url: 'url',
  toyId: 'toyId',
  createdAt: 'createdAt',
  originalFilename: 'originalFilename',
  hashedFilename: 'hashedFilename',
  fileFormat: 'fileFormat'
};

exports.Prisma.ReservationScalarFieldEnum = {
  id: 'id',
  startDate: 'startDate',
  endDate: 'endDate',
  status: 'status',
  position: 'position',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  userId: 'userId',
  toyId: 'toyId',
  ownerId: 'ownerId',
  deletedAt: 'deletedAt'
};

exports.Prisma.ReservationStatusScalarFieldEnum = {
  name: 'name'
};

exports.Prisma.ToyLocationBackupScalarFieldEnum = {
  toyId: 'toyId',
  locationId: 'locationId'
};

exports.Prisma.ToyStatusScalarFieldEnum = {
  name: 'name',
  label: 'label'
};

exports.Prisma.ToyTypeScalarFieldEnum = {
  name: 'name',
  label: 'label'
};

exports.Prisma.UserRoleScalarFieldEnum = {
  name: 'name'
};

exports.Prisma.HiddenToyScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  toyId: 'toyId',
  createdAt: 'createdAt'
};

exports.Prisma.SystemLogScalarFieldEnum = {
  id: 'id',
  action: 'action',
  targetId: 'targetId',
  targetType: 'targetType',
  adminId: 'adminId',
  details: 'details',
  createdAt: 'createdAt'
};

exports.Prisma.EmailUnsubscribeScalarFieldEnum = {
  id: 'id',
  email: 'email',
  token: 'token',
  reason: 'reason',
  unsubscribedAt: 'unsubscribedAt',
  ipAddress: 'ipAddress',
  userAgent: 'userAgent'
};

exports.Prisma.EmailLogScalarFieldEnum = {
  id: 'id',
  emailId: 'emailId',
  emailType: 'emailType',
  recipientEmail: 'recipientEmail',
  recipientUserId: 'recipientUserId',
  subject: 'subject',
  status: 'status',
  attempt: 'attempt',
  maxAttempts: 'maxAttempts',
  errorMessage: 'errorMessage',
  messageId: 'messageId',
  queuedAt: 'queuedAt',
  sentAt: 'sentAt',
  failedAt: 'failedAt',
  processingTime: 'processingTime',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.SortOrder = {
  asc: 'asc',
  desc: 'desc'
};

exports.Prisma.NullsOrder = {
  first: 'first',
  last: 'last'
};

exports.Prisma.UserOrderByRelevanceFieldEnum = {
  email: 'email',
  name: 'name',
  password: 'password',
  phone: 'phone',
  address: 'address',
  city: 'city',
  postalCode: 'postalCode',
  role: 'role',
  status: 'status',
  firebaseUid: 'firebaseUid',
  emailUnsubscribeToken: 'emailUnsubscribeToken'
};

exports.Prisma.ToyOrderByRelevanceFieldEnum = {
  name: 'name',
  description: 'description',
  type: 'type',
  priceType: 'priceType',
  status: 'status'
};

exports.Prisma.ToyImageOrderByRelevanceFieldEnum = {
  url: 'url',
  originalFilename: 'originalFilename',
  hashedFilename: 'hashedFilename',
  fileFormat: 'fileFormat'
};

exports.Prisma.ReservationOrderByRelevanceFieldEnum = {
  status: 'status'
};

exports.Prisma.ReservationStatusOrderByRelevanceFieldEnum = {
  name: 'name'
};

exports.Prisma.ToyStatusOrderByRelevanceFieldEnum = {
  name: 'name',
  label: 'label'
};

exports.Prisma.ToyTypeOrderByRelevanceFieldEnum = {
  name: 'name',
  label: 'label'
};

exports.Prisma.UserRoleOrderByRelevanceFieldEnum = {
  name: 'name'
};

exports.Prisma.SystemLogOrderByRelevanceFieldEnum = {
  action: 'action',
  targetType: 'targetType',
  details: 'details'
};

exports.Prisma.EmailUnsubscribeOrderByRelevanceFieldEnum = {
  email: 'email',
  token: 'token',
  reason: 'reason',
  ipAddress: 'ipAddress',
  userAgent: 'userAgent'
};

exports.Prisma.EmailLogOrderByRelevanceFieldEnum = {
  emailId: 'emailId',
  emailType: 'emailType',
  recipientEmail: 'recipientEmail',
  subject: 'subject',
  status: 'status',
  errorMessage: 'errorMessage',
  messageId: 'messageId'
};


exports.Prisma.ModelName = {
  User: 'User',
  Toy: 'Toy',
  ToyImage: 'ToyImage',
  Reservation: 'Reservation',
  ReservationStatus: 'ReservationStatus',
  ToyLocationBackup: 'ToyLocationBackup',
  ToyStatus: 'ToyStatus',
  ToyType: 'ToyType',
  UserRole: 'UserRole',
  HiddenToy: 'HiddenToy',
  SystemLog: 'SystemLog',
  EmailUnsubscribe: 'EmailUnsubscribe',
  EmailLog: 'EmailLog'
};

/**
 * This is a stub Prisma Client that will error at runtime if called.
 */
class PrismaClient {
  constructor() {
    return new Proxy(this, {
      get(target, prop) {
        let message
        const runtime = getRuntime()
        if (runtime.isEdge) {
          message = `PrismaClient is not configured to run in ${runtime.prettyName}. In order to run Prisma Client on edge runtime, either:
- Use Prisma Accelerate: https://pris.ly/d/accelerate
- Use Driver Adapters: https://pris.ly/d/driver-adapters
`;
        } else {
          message = 'PrismaClient is unable to run in this browser environment, or has been bundled for the browser (running in `' + runtime.prettyName + '`).'
        }

        message += `
If this is unexpected, please open an issue: https://pris.ly/prisma-prisma-bug-report`

        throw new Error(message)
      }
    })
  }
}

exports.PrismaClient = PrismaClient

Object.assign(exports, Prisma)
