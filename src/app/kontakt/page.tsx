'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';

export default function ContactPage() {
  const router = useRouter();

  // Redirect to homepage when component mounts
  useEffect(() => {
    router.replace('/');
  }, [router]);

  // The rest of the component won't be rendered because of the redirect
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    subject: '',
    message: ''
  });

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitSuccess, setSubmitSuccess] = useState(false);
  const [submitError, setSubmitError] = useState('');

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    setSubmitError('');

    // Simulácia odoslania formulára
    setTimeout(() => {
      setIsSubmitting(false);
      setSubmitSuccess(true);
      setFormData({
        name: '',
        email: '',
        subject: '',
        message: ''
      });
    }, 1500);
  };

  return (
    <div className="container mx-auto px-4 py-12">
      <h1 className="text-4xl font-bold mb-8 text-center">Kontaktujte nás</h1>

      <div className="max-w-3xl mx-auto">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-12">

          {/* Kontaktný formulár */}
          <div>
            <h2 className="text-2xl font-semibold mb-6">Napíšte nám</h2>

            {submitSuccess ? (
              <div className="bg-success bg-opacity-20 p-6 rounded-lg">
                <h3 className="text-xl font-medium mb-2 text-primary-dark">Správa bola odoslaná</h3>
                <p className="text-neutral-700">
                  Ďakujeme za vašu správu. Budeme vás kontaktovať čo najskôr.
                </p>
                <button
                  className="mt-4 btn btn-primary"
                  onClick={() => setSubmitSuccess(false)}
                >
                  Poslať ďalšiu správu
                </button>
              </div>
            ) : (
              <form onSubmit={handleSubmit} className="space-y-4">
                <div>
                  <label htmlFor="name" className="block text-sm font-medium text-neutral-700 mb-1">
                    Meno a priezvisko
                  </label>
                  <input
                    type="text"
                    id="name"
                    name="name"
                    required
                    className="w-full p-2 border border-neutral-300 rounded-md"
                    value={formData.name}
                    onChange={handleChange}
                  />
                </div>

                <div>
                  <label htmlFor="email" className="block text-sm font-medium text-neutral-700 mb-1">
                    Email
                  </label>
                  <input
                    type="email"
                    id="email"
                    name="email"
                    required
                    className="w-full p-2 border border-neutral-300 rounded-md"
                    value={formData.email}
                    onChange={handleChange}
                  />
                </div>

                <div>
                  <label htmlFor="subject" className="block text-sm font-medium text-neutral-700 mb-1">
                    Predmet
                  </label>
                  <select
                    id="subject"
                    name="subject"
                    required
                    className="w-full p-2 border border-neutral-300 rounded-md"
                    value={formData.subject}
                    onChange={handleChange}
                  >
                    <option value="">Vyberte predmet</option>
                    <option value="Otázka o službe">Otázka o službe</option>
                    <option value="Technický problém">Technický problém</option>
                    <option value="Spolupráca">Spolupráca</option>
                    <option value="Iné">Iné</option>
                  </select>
                </div>

                <div>
                  <label htmlFor="message" className="block text-sm font-medium text-neutral-700 mb-1">
                    Správa
                  </label>
                  <textarea
                    id="message"
                    name="message"
                    rows={5}
                    required
                    className="w-full p-2 border border-neutral-300 rounded-md"
                    value={formData.message}
                    onChange={handleChange}
                  ></textarea>
                </div>

                {submitError && (
                  <div className="text-error">
                    {submitError}
                  </div>
                )}

                <button
                  type="submit"
                  className="btn btn-primary w-full"
                  disabled={isSubmitting}
                >
                  {isSubmitting ? 'Odosielanie...' : 'Odoslať správu'}
                </button>
              </form>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
