import React from 'react';
import Link from 'next/link';

export default function HowItWorksPage() {
  return (
    <div className="container mx-auto px-4 py-12">
      <h1 className="text-4xl font-bold mb-8 text-center">Ako funguje <PERSON>wapka?</h1>
      
      {/* Úvod */}
      <div className="max-w-3xl mx-auto mb-16">
        <p className="text-lg text-neutral-700 text-center">
          <PERSON><PERSON>p<PERSON> je platforma, ktor<PERSON> spá<PERSON> rod<PERSON>, ktor<PERSON> chcú zdieľať detské hračky.
          Namiesto kupovania nových hračiek, ktor<PERSON> deti rýchlo prerastú, môž<PERSON> využiť
          hračky, ktor<PERSON> už iné deti nepotrebujú.
        </p>
      </div>
      
      {/* Kroky pre požičiavajúcich */}
      <div className="mb-16">
        <h2 className="text-2xl font-bold mb-8 text-center">Pre tých, k<PERSON><PERSON> si chcú požičať hračky</h2>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          <div className="bg-white p-6 rounded-lg shadow-sm">
            <div className="w-12 h-12 bg-primary-light rounded-full flex items-center justify-center mb-4 text-primary font-bold">
              1
            </div>
            <h3 className="text-xl font-semibold mb-2">Registrujte sa</h3>
            <p className="text-neutral-600">
              Vytvorte si účet na Swapke. Je to rýchle, jednoduché a bezplatné.
              Stačí vám e-mail a heslo, alebo sa môžete prihlásiť cez Google či Facebook.
            </p>
          </div>
          
          <div className="bg-white p-6 rounded-lg shadow-sm">
            <div className="w-12 h-12 bg-primary-light rounded-full flex items-center justify-center mb-4 text-primary font-bold">
              2
            </div>
            <h3 className="text-xl font-semibold mb-2">Nájdite hračky</h3>
            <p className="text-neutral-600">
              Prehliadajte dostupné hračky vo vašom okolí. Môžete filtrovať podľa typu,
              veku, lokality a ďalších parametrov.
            </p>
          </div>
          
          <div className="bg-white p-6 rounded-lg shadow-sm">
            <div className="w-12 h-12 bg-primary-light rounded-full flex items-center justify-center mb-4 text-primary font-bold">
              3
            </div>
            <h3 className="text-xl font-semibold mb-2">Požičajte si</h3>
            <p className="text-neutral-600">
              Kontaktujte majiteľa hračky, dohodnite sa na podmienkach a dĺžke výpožičky.
              Zaplaťte zálohu a poplatok za požičanie a užite si novú hračku!
            </p>
          </div>
        </div>
      </div>
      
      {/* Kroky pre požičiavajúcich */}
      <div className="mb-16">
        <h2 className="text-2xl font-bold mb-8 text-center">Pre tých, ktorí chcú ponúknuť hračky</h2>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          <div className="bg-white p-6 rounded-lg shadow-sm">
            <div className="w-12 h-12 bg-secondary-light rounded-full flex items-center justify-center mb-4 text-secondary-dark font-bold">
              1
            </div>
            <h3 className="text-xl font-semibold mb-2">Pridajte hračky</h3>
            <p className="text-neutral-600">
              Po registrácii pridajte hračky, ktoré chcete ponúknuť. Pridajte fotografie,
              popis, cenu za požičanie a výšku zálohy.
            </p>
          </div>
          
          <div className="bg-white p-6 rounded-lg shadow-sm">
            <div className="w-12 h-12 bg-secondary-light rounded-full flex items-center justify-center mb-4 text-secondary-dark font-bold">
              2
            </div>
            <h3 className="text-xl font-semibold mb-2">Komunikujte</h3>
            <p className="text-neutral-600">
              Keď má niekto záujem o vašu hračku, dostanete notifikáciu. Dohodnite sa
              na podmienkach a spôsobe odovzdania.
            </p>
          </div>
          
          <div className="bg-white p-6 rounded-lg shadow-sm">
            <div className="w-12 h-12 bg-secondary-light rounded-full flex items-center justify-center mb-4 text-secondary-dark font-bold">
              3
            </div>
            <h3 className="text-xl font-semibold mb-2">Zarábajte</h3>
            <p className="text-neutral-600">
              Získajte peniaze za hračky, ktoré by inak len ležali nevyužité. Po vrátení
              skontrolujte stav a vráťte zálohu.
            </p>
          </div>
        </div>
      </div>
      
      {/* Pravidlá a podmienky */}
      <div className="bg-neutral-100 p-8 rounded-lg mb-16">
        <h2 className="text-2xl font-bold mb-4">Pravidlá a podmienky</h2>
        <ul className="list-disc pl-5 space-y-2 text-neutral-700">
          <li>Všetky hračky musia byť čisté a v dobrom stave.</li>
          <li>Záloha slúži ako poistka pre prípad poškodenia hračky.</li>
          <li>Maximálna doba výpožičky je 30 dní, ak sa nedohodnete inak.</li>
          <li>V prípade poškodenia hračky sa záloha nevracia alebo sa vracia len čiastočne.</li>
          <li>Swapka si účtuje 10% z ceny požičania ako poplatok za sprostredkovanie.</li>
        </ul>
      </div>
      
      {/* CTA */}
      <div className="text-center">
        <h2 className="text-2xl font-bold mb-4">Pripravení začať?</h2>
        <p className="text-lg text-neutral-600 mb-6">
          Pridajte sa k našej komunite a začnite zdieľať hračky už dnes!
        </p>
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <Link href="/registracia" className="btn btn-primary">
            Zaregistrovať sa
          </Link>
          <Link href="/hracky" className="btn btn-outline">
            Prehliadať hračky
          </Link>
        </div>
      </div>
    </div>
  );
}
