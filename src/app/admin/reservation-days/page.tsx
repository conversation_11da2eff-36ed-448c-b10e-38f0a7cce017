'use client';

import { useEffect, useState } from 'react';
import { useAuth } from '../../../contexts/AuthContext';
import { useRouter } from 'next/navigation';
import AdminTabs from '../../../components/admin/AdminTabs';

interface ReservationDaySetting {
  id: number;
  days: number;
  isDefault: boolean;
  description: string;
}

export default function ReservationDaysPage() {
  const { user } = useAuth();
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(true);
  const [reservationDays, setReservationDays] = useState<ReservationDaySetting[]>([]);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Formulárové dáta pre novú možnosť
  const [newSetting, setNewSetting] = useState({
    days: 7,
    description: '',
    isDefault: false
  });

  // Presmerovanie, ak používateľ nie je prihlásený alebo nemá rolu admin
  useEffect(() => {
    if (!isLoading && (!user || user.role !== 'admin')) {
      router.push('/');
    }
  }, [user, router, isLoading]);

  // Načítanie existujúcich nastavení
  useEffect(() => {
    async function loadReservationDays() {
      try {
        if (!user || !user.getIdToken) return;
        const token = await user.getIdToken();

        // Kontrola, či má používateľ hashedUserId
        if (!user.hashedUserId) {
          throw new Error('Chýba ID používateľa');
        }

        const response = await fetch('/api/admin/reservation-days', {
          headers: {
            'Authorization': `Bearer ${token}`,
          },
        });

        if (!response.ok) {
          throw new Error('Nepodarilo sa načítať nastavenia rezervačných dní');
        }

        const data = await response.json();
        setReservationDays(data);
      } catch (err) {
        console.error('Chyba pri načítaní nastavení rezervačných dní:', err);
        setError('Nastala chyba pri načítaní nastavení rezervačných dní');
      } finally {
        setIsLoading(false);
      }
    }

    if (user && user.role === 'admin') {
      loadReservationDays();
    } else {
      setIsLoading(false);
    }
  }, [user]);

  // Spracovanie zmien vo formulári
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value, type } = e.target as HTMLInputElement;

    if (type === 'checkbox') {
      const checked = (e.target as HTMLInputElement).checked;
      setNewSetting(prev => ({ ...prev, [name]: checked }));
    } else if (type === 'number') {
      setNewSetting(prev => ({ ...prev, [name]: parseInt(value) || 0 }));
    } else {
      setNewSetting(prev => ({ ...prev, [name]: value }));
    }
  };

  // Pridanie novej možnosti
  const handleAddSetting = async (e: React.FormEvent) => {
    e.preventDefault();

    if (newSetting.days <= 0 || !newSetting.description.trim()) {
      setError('Všetky polia sú povinné a počet dní musí byť kladné číslo');
      return;
    }

    setIsSubmitting(true);
    setError(null);

    try {
      // Získanie Firebase tokenu pre autentifikáciu
      if (!user || !user.getIdToken) return;
      const token = await user.getIdToken();

      // Kontrola, či má používateľ hashedUserId
      if (!user.hashedUserId) {
        throw new Error('Chýba ID používateľa');
      }

      const response = await fetch('/api/admin/reservation-days', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify(newSetting),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Nepodarilo sa pridať nastavenie');
      }

      const data = await response.json();

      // Ak je nové nastavenie predvolené, aktualizujeme všetky ostatné
      if (newSetting.isDefault) {
        setReservationDays(prevDays =>
          prevDays.map(day => ({
            ...day,
            isDefault: false
          })).concat([data])
        );
      } else {
        setReservationDays([...reservationDays, data]);
      }

      setNewSetting({ days: 7, description: '', isDefault: false });
      setSuccess('Nastavenie bolo úspešne pridané');

      // Skrytie hlásenia o úspechu po 3 sekundách
      setTimeout(() => {
        setSuccess(null);
      }, 3000);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Nastala chyba pri pridávaní nastavenia');
    } finally {
      setIsSubmitting(false);
    }
  };

  // Nastavenie predvolenej hodnoty
  const handleSetDefault = async (id: number) => {
    try {
      // Získanie Firebase tokenu pre autentifikáciu
      if (!user || !user.getIdToken) return;
      const token = await user.getIdToken();

      // Kontrola, či má používateľ hashedUserId
      if (!user.hashedUserId) {
        throw new Error('Chýba ID používateľa');
      }

      const response = await fetch(`/api/admin/reservation-days/${id}/default`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Nepodarilo sa nastaviť predvolenú hodnotu');
      }

      // Aktualizácia stavu
      setReservationDays(prevDays =>
        prevDays.map(day => ({
          ...day,
          isDefault: day.id === id
        }))
      );

      setSuccess('Predvolená hodnota bola úspešne nastavená');

      // Skrytie hlásenia o úspechu po 3 sekundách
      setTimeout(() => {
        setSuccess(null);
      }, 3000);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Nastala chyba pri nastavovaní predvolenej hodnoty');
    }
  };

  // Odstránenie nastavenia
  const handleDeleteSetting = async (id: number) => {
    try {
      // Získanie Firebase tokenu pre autentifikáciu
      if (!user || !user.getIdToken) return;
      const token = await user.getIdToken();

      // Kontrola, či má používateľ hashedUserId
      if (!user.hashedUserId) {
        throw new Error('Chýba ID používateľa');
      }

      const response = await fetch(`/api/admin/reservation-days/${id}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Nepodarilo sa odstrániť nastavenie');
      }

      setReservationDays(reservationDays.filter(day => day.id !== id));
      setSuccess('Nastavenie bolo úspešne odstránené');

      // Skrytie hlásenia o úspechu po 3 sekundách
      setTimeout(() => {
        setSuccess(null);
      }, 3000);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Nastala chyba pri odstraňovaní nastavenia');
    }
  };

  if (isLoading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">
          <p className="text-neutral-500 text-lg">Načítavam administrátorský panel...</p>
        </div>
      </div>
    );
  }

  if (!user || user.role !== 'admin') {
    return null; // Toto sa nezobrazí, pretože useEffect presmeruje používateľa
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-3xl font-bold mb-4">Administrátorský panel</h1>

      <AdminTabs />

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          {error}
        </div>
      )}

      {success && (
        <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
          {success}
        </div>
      )}

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Formulár pre pridanie novej možnosti */}
        <div className="bg-white rounded-lg shadow-sm overflow-hidden">
          <div className="p-6">
            <h2 className="text-xl font-semibold mb-4">Pridať novú možnosť rezervačných dní</h2>
            <form onSubmit={handleAddSetting}>
              <div className="mb-4">
                <label htmlFor="days" className="block text-sm font-medium text-neutral-700 mb-1">
                  Počet dní *
                </label>
                <input
                  type="number"
                  id="days"
                  name="days"
                  min="1"
                  value={newSetting.days}
                  onChange={handleChange}
                  className="w-full px-3 py-2 border border-neutral-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
                  required
                />
              </div>

              <div className="mb-4">
                <label htmlFor="description" className="block text-sm font-medium text-neutral-700 mb-1">
                  Popis *
                </label>
                <input
                  type="text"
                  id="description"
                  name="description"
                  value={newSetting.description}
                  onChange={handleChange}
                  className="w-full px-3 py-2 border border-neutral-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
                  placeholder="Napr. Týždeň, Mesiac, atď."
                  required
                />
              </div>

              <div className="mb-4">
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    name="isDefault"
                    checked={newSetting.isDefault}
                    onChange={handleChange}
                    className="mr-2"
                  />
                  <span className="text-sm font-medium text-neutral-700">Nastaviť ako predvolené</span>
                </label>
              </div>

              <button
                type="submit"
                className="btn btn-primary"
                disabled={isSubmitting}
              >
                {isSubmitting ? 'Pridávam...' : 'Pridať možnosť'}
              </button>
            </form>
          </div>
        </div>

        {/* Zoznam existujúcich možností */}
        <div className="bg-white rounded-lg shadow-sm overflow-hidden">
          <div className="p-6">
            <h2 className="text-xl font-semibold mb-4">Existujúce možnosti rezervačných dní</h2>

            {reservationDays.length === 0 ? (
              <p className="text-neutral-500">Zatiaľ nie sú pridané žiadne možnosti.</p>
            ) : (
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-neutral-200">
                  <thead>
                    <tr>
                      <th className="px-4 py-3 text-left text-xs font-medium text-neutral-500 uppercase tracking-wider">Počet dní</th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-neutral-500 uppercase tracking-wider">Popis</th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-neutral-500 uppercase tracking-wider">Predvolené</th>
                      <th className="px-4 py-3 text-right text-xs font-medium text-neutral-500 uppercase tracking-wider">Akcie</th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-neutral-200">
                    {reservationDays.map((setting) => (
                      <tr key={setting.id}>
                        <td className="px-4 py-3 whitespace-nowrap">{setting.days}</td>
                        <td className="px-4 py-3">{setting.description}</td>
                        <td className="px-4 py-3">
                          {setting.isDefault ? (
                            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                              Áno
                            </span>
                          ) : (
                            <button
                              onClick={() => handleSetDefault(setting.id)}
                              className="text-primary hover:text-primary-dark text-sm"
                            >
                              Nastaviť ako predvolené
                            </button>
                          )}
                        </td>
                        <td className="px-4 py-3 text-right">
                          <button
                            onClick={() => handleDeleteSetting(setting.id)}
                            className="text-red-600 hover:text-red-900 ml-2"
                            disabled={setting.isDefault}
                            title={setting.isDefault ? "Predvolenú hodnotu nie je možné odstrániť" : "Odstrániť"}
                          >
                            Odstrániť
                          </button>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
