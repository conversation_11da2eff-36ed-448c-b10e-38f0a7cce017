'use client';

import { useEffect, useState } from 'react';
import { useAuth } from '../../../contexts/AuthContext';
import { useRouter } from 'next/navigation';
import AdminTabs from '../../../components/admin/AdminTabs';

interface ToyType {
  name: string;
  label: string;
  count?: number;
}

export default function ToyTypesPage() {
  const { user } = useAuth();
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(true);
  const [toyTypes, setToyTypes] = useState<ToyType[]>([]);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [newToyType, setNewToyType] = useState('');
  const [newToyTypeLabel, setNewToyTypeLabel] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [editingType, setEditingType] = useState<ToyType | null>(null);

  // Presmerovanie, ak používateľ nie je prihlásený alebo nemá rolu admin
  useEffect(() => {
    if (!isLoading && (!user || user.role !== 'admin')) {
      router.push('/');
    }
  }, [user, router, isLoading]);

  // Načítanie typov hračiek
  useEffect(() => {
    async function loadToyTypes() {
      try {
        // Kontrola, či je používateľ prihlásený
        if (!user) {
          setIsLoading(false);
          return;
        }

        // Získanie Firebase tokenu pre autentifikáciu
        if (!user.getIdToken) return;
        const token = await user.getIdToken();

        // Kontrola, či má používateľ hashedUserId
        if (!user.hashedUserId) {
          throw new Error('Chýba ID používateľa');
        }

        // Volanie API s autentifikačnými údajmi
        const response = await fetch('/api/toy-types', {
          headers: {
            'Authorization': `Bearer ${token}`,
          }
        });

        if (!response.ok) {
          throw new Error('Nepodarilo sa načítať typy hračiek');
        }
        const data = await response.json();
        setToyTypes(data);
      } catch (err) {
        setError('Nastala chyba pri načítaní typov hračiek');
        console.error('Chyba pri načítaní typov hračiek:', err);
      } finally {
        setIsLoading(false);
      }
    }

    if (user && user.role === 'admin') {
      loadToyTypes();
    } else {
      setIsLoading(false);
    }
  }, [user]);

  // Pridanie nového typu hračky
  const handleAddToyType = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!newToyType.trim()) {
      setError('Názov typu hračky nemôže byť prázdny');
      return;
    }

    // Kontrola, či je používateľ prihlásený
    if (!user) {
      setError('Nie ste prihlásený');
      return;
    }

    setIsSubmitting(true);
    setError(null);

    try {
      // Získanie Firebase tokenu pre autentifikáciu
      if (!user.getIdToken) return;
      const token = await user.getIdToken();

      // Kontrola, či má používateľ hashedUserId
      if (!user.hashedUserId) {
        throw new Error('Chýba ID používateľa');
      }

      const response = await fetch('/api/toy-types', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify({
          name: newToyType.toUpperCase(),
          label: newToyTypeLabel || newToyType // Use label if provided, otherwise use the name
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Nepodarilo sa pridať typ hračky');
      }

      const data = await response.json();
      setToyTypes([...toyTypes, data]);
      setNewToyType('');
      setNewToyTypeLabel('');
      setSuccess('Typ hračky bol úspešne pridaný');

      // Skrytie hlásenia o úspechu po 3 sekundách
      setTimeout(() => {
        setSuccess(null);
      }, 3000);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Nastala chyba pri pridávaní typu hračky');
    } finally {
      setIsSubmitting(false);
    }
  };

  // Aktualizácia labelu typu hračky
  const handleUpdateToyTypeLabel = async () => {
    if (!editingType) return;

    try {
      // Kontrola, či je používateľ prihlásený
      if (!user) {
        setError('Nie ste prihlásený');
        return;
      }

      // Získanie Firebase tokenu pre autentifikáciu
      if (!user.getIdToken) return;
      const token = await user.getIdToken();

      // Kontrola, či má používateľ hashedUserId
      if (!user.hashedUserId) {
        throw new Error('Chýba ID používateľa');
      }

      const response = await fetch(`/api/toy-types/${encodeURIComponent(editingType.name)}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify({ label: editingType.label }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Nepodarilo sa aktualizovať label typu hračky');
      }

      // Aktualizácia zoznamu typov hračiek
      setToyTypes(toyTypes.map(type =>
        type.name === editingType.name ? { ...type, label: editingType.label } : type
      ));

      setEditingType(null);
      setSuccess('Label typu hračky bol úspešne aktualizovaný');

      // Skrytie hlásenia o úspechu po 3 sekundách
      setTimeout(() => {
        setSuccess(null);
      }, 3000);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Nastala chyba pri aktualizácii labelu typu hračky');
    }
  };

  // Odstránenie typu hračky
  const handleDeleteToyType = async (name: string) => {
    try {
      // Kontrola, či je používateľ prihlásený
      if (!user) {
        setError('Nie ste prihlásený');
        return;
      }

      // Získanie Firebase tokenu pre autentifikáciu
      if (!user.getIdToken) return;
      const token = await user.getIdToken();

      // Kontrola, či má používateľ hashedUserId
      if (!user.hashedUserId) {
        throw new Error('Chýba ID používateľa');
      }

      const response = await fetch(`/api/toy-types/${encodeURIComponent(name)}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Nepodarilo sa odstrániť typ hračky');
      }

      setToyTypes(toyTypes.filter(type => type.name !== name));
      setSuccess('Typ hračky bol úspešne odstránený');

      // Skrytie hlásenia o úspechu po 3 sekundách
      setTimeout(() => {
        setSuccess(null);
      }, 3000);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Nastala chyba pri odstraňovaní typu hračky');
    }
  };

  if (isLoading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">
          <p className="text-neutral-500 text-lg">Načítavam administrátorský panel...</p>
        </div>
      </div>
    );
  }

  if (!user || user.role !== 'admin') {
    return null; // Toto sa nezobrazí, pretože useEffect presmeruje používateľa
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-3xl font-bold mb-4">Administrátorský panel</h1>

      <AdminTabs />

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          {error}
        </div>
      )}

      {success && (
        <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
          {success}
        </div>
      )}

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Formulár pre pridanie nového typu hračky */}
        <div className="bg-white rounded-lg shadow-sm overflow-hidden">
          <div className="p-6">
            <h2 className="text-xl font-semibold mb-4">Pridať nový typ hračky</h2>

            <form onSubmit={handleAddToyType}>
              <div className="mb-4">
                <label htmlFor="newToyType" className="block text-sm font-medium text-neutral-700 mb-1">
                  Kód typu hračky
                </label>
                <input
                  type="text"
                  id="newToyType"
                  value={newToyType}
                  onChange={(e) => setNewToyType(e.target.value)}
                  className="w-full px-3 py-2 border border-neutral-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
                  placeholder="Napr. EDUCATIONAL, PLUSH, BUILDING..."
                  required
                />
                <p className="text-xs text-neutral-500 mt-1">
                  Kód bude automaticky prevedený na veľké písmená
                </p>
              </div>

              <div className="mb-4">
                <label htmlFor="newToyTypeLabel" className="block text-sm font-medium text-neutral-700 mb-1">
                  Popisný názov typu hračky
                </label>
                <input
                  type="text"
                  id="newToyTypeLabel"
                  value={newToyTypeLabel}
                  onChange={(e) => setNewToyTypeLabel(e.target.value)}
                  className="w-full px-3 py-2 border border-neutral-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
                  placeholder="Napr. Vzdelávacie, Plyšové, Stavebnice..."
                />
                <p className="text-xs text-neutral-500 mt-1">
                  Tento názov sa bude zobrazovať používateľom. Ak ho nezadáte, použije sa kód.
                </p>
              </div>

              <button
                type="submit"
                className="btn btn-primary"
                disabled={isSubmitting}
              >
                {isSubmitting ? 'Pridávam...' : 'Pridať typ hračky'}
              </button>
            </form>
          </div>
        </div>

        {/* Zoznam existujúcich typov hračiek */}
        <div className="bg-white rounded-lg shadow-sm overflow-hidden">
          <div className="p-6">
            <h2 className="text-xl font-semibold mb-4">Existujúce typy hračiek</h2>

            {toyTypes.length > 0 ? (
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-neutral-200">
                  <thead className="bg-neutral-50">
                    <tr>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-neutral-500 uppercase tracking-wider">
                        Kód
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-neutral-500 uppercase tracking-wider">
                        Popisný názov
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-neutral-500 uppercase tracking-wider">
                        Počet hračiek
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-neutral-500 uppercase tracking-wider">
                        Akcie
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-neutral-200">
                    {toyTypes.map((type) => (
                      <tr key={type.name}>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm font-medium text-neutral-900">{type.name}</div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          {editingType && editingType.name === type.name ? (
                            <input
                              type="text"
                              value={editingType.label}
                              onChange={(e) => setEditingType({...editingType, label: e.target.value})}
                              className="w-full px-2 py-1 border border-neutral-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary text-sm"
                            />
                          ) : (
                            <div className="text-sm text-neutral-700">{type.label || type.name}</div>
                          )}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-neutral-500">
                          {type.count || 0}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-neutral-500">
                          <div className="flex space-x-2">
                            {editingType && editingType.name === type.name ? (
                              <>
                                <button
                                  onClick={handleUpdateToyTypeLabel}
                                  className="bg-green-50 text-green-600 hover:text-green-800 hover:bg-green-100 p-1 rounded"
                                  title="Uložiť zmeny"
                                >
                                  <span className="text-lg font-medium">
                                    ✓
                                  </span>
                                </button>
                                <button
                                  onClick={() => setEditingType(null)}
                                  className="bg-gray-50 text-gray-600 hover:text-gray-800 hover:bg-gray-100 p-1 rounded"
                                  title="Zrušiť úpravy"
                                >
                                  <span className="text-lg font-medium">
                                    ✕
                                  </span>
                                </button>
                              </>
                            ) : (
                              <button
                                onClick={() => setEditingType(type)}
                                className="bg-blue-50 text-blue-600 hover:text-blue-800 hover:bg-blue-100 p-1 rounded"
                                title="Upraviť popisný názov"
                              >
                                <span className="text-lg font-medium">
                                  ✎
                                </span>
                              </button>
                            )}

                            {!editingType && !(!!type.count && type.count > 0) && (
                              <button
                                onClick={() => handleDeleteToyType(type.name)}
                                className="bg-red-50 text-red-600 hover:text-red-800 hover:bg-red-100 p-1 rounded"
                                title="Odstrániť typ hračky"
                              >
                                <span className="text-lg font-medium">
                                  🗑️
                                </span>
                              </button>
                            )}
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            ) : (
              <p className="text-neutral-500">Žiadne typy hračiek neboli nájdené.</p>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
