'use client';

import { useEffect, useState } from 'react';
import { useAuth } from '../../contexts/AuthContext';
import { useRouter } from 'next/navigation';
import AdminTabs from '../../components/admin/AdminTabs';

interface User {
  id: string; // Contains hashed ID for security
  hashedId: string; // Backward compatibility
  email: string;
  name: string;
  role: string;
  status?: string;
  isAnonymized?: boolean;
  createdAt: string;
  updatedAt: string;
}

export default function AdminPage() {
  const { user } = useAuth();
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(true);
  const [users, setUsers] = useState<User[]>([]);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [showConfirmDialog, setShowConfirmDialog] = useState(false);
  const [userToAnonymize, setUserToAnonymize] = useState<string | null>(null);

  // Presmerovanie, ak používateľ nie je prihlásený alebo nemá rolu admin
  useEffect(() => {
    if (!isLoading && (!user || user.role !== 'admin')) {
      router.push('/');
    }
  }, [user, router, isLoading]);

  // Načítanie zoznamu používateľov
  useEffect(() => {
    async function loadUsers() {
      try {
        // Získanie Firebase tokenu pre autentifikáciu
        if (!user || !user.getIdToken) return;
        const token = await user.getIdToken();

        // Volanie API s autentifikačnými údajmi
        const response = await fetch('/api/users', {
          headers: {
            'Authorization': `Bearer ${token}`,
          }
        });

        if (!response.ok) {
          throw new Error('Nepodarilo sa načítať používateľov');
        }
        const data = await response.json();
        setUsers(data);
      } catch (err) {
        setError('Nastala chyba pri načítaní používateľov');
        console.error('Chyba pri načítaní používateľov:', err);
      } finally {
        setIsLoading(false);
      }
    }

    if (user && user.role === 'admin') {
      loadUsers();
    } else {
      setIsLoading(false);
    }
  }, [user]);

  if (isLoading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">
          <p className="text-neutral-500 text-lg">Načítavam administrátorský panel...</p>
        </div>
      </div>
    );
  }

  if (!user || user.role !== 'admin') {
    return null; // Toto sa nezobrazí, pretože useEffect presmeruje používateľa
  }

  // Formátovanie dátumu
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('sk-SK', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // Funkcia pre zablokovanie/odblokovanie používateľa
  const toggleUserStatus = async (userToUpdate: User, currentStatus: string) => {
    try {
      // Získanie Firebase tokenu pre autentifikáciu
      if (!user || !user.getIdToken) return;
      const token = await user.getIdToken();

      const newStatus = currentStatus === 'BLOCKED' ? 'ACTIVE' : 'BLOCKED';

      // Use hashed ID for API call instead of raw numeric ID for security
      const response = await fetch(`/api/users/${userToUpdate.hashedId}/status`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify({ status: newStatus }),
      });

      if (!response.ok) {
        throw new Error('Nepodarilo sa aktualizovať stav používateľa');
      }

      // Aktualizácia zoznamu používateľov using hashedId for comparison
      setUsers(users.map(u =>
        u.hashedId === userToUpdate.hashedId ? { ...u, status: newStatus } : u
      ));
    } catch (err) {
      setError('Nastala chyba pri aktualizácii stavu používateľa');
      console.error('Chyba pri aktualizácii stavu používateľa:', err);
    }
  };

  // Funkcia pre zobrazenie potvrdenia anonymizácie
  const showAnonymizeConfirmation = (userToAnonymize: User) => {
    setUserToAnonymize(userToAnonymize.hashedId);
    setShowConfirmDialog(true);
  };

  // Funkcia pre anonymizáciu používateľa
  const anonymizeUser = async () => {
    try {
      if (!userToAnonymize) return;

      // Získanie Firebase tokenu pre autentifikáciu
      if (!user || !user.getIdToken) return;
      const token = await user.getIdToken();

      // Use hashed ID for API call - userToAnonymize is now already a hashed ID
      const response = await fetch(`/api/users/${userToAnonymize}/anonymize`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Nepodarilo sa anonymizovať používateľa');
      }

      const data = await response.json();

      // Aktualizácia zoznamu používateľov using hashedId for comparison
      setUsers(users.map(u =>
        u.hashedId === userToAnonymize ? { ...u, isAnonymized: true, name: data.user.name, email: data.user.email } : u
      ));

      // Zobrazenie hlásenia o úspechu
      setSuccess('Používateľ bol úspešne anonymizovaný');

      // Skrytie hlásenia o úspechu po 3 sekundách
      setTimeout(() => {
        setSuccess(null);
      }, 3000);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Nastala chyba pri anonymizácii používateľa');
      console.error('Chyba pri anonymizácii používateľa:', err);
    } finally {
      // Zatvorenie dialógu
      setShowConfirmDialog(false);
      setUserToAnonymize(null);
    }
  };

  return (
    <div className="container mx-auto px-4 py-8 mb-16">
      <h1 className="text-3xl font-bold mb-4">Administrátorský panel</h1>

      <AdminTabs />

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          {error}
        </div>
      )}

      {success && (
        <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
          {success}
        </div>
      )}

      {/* Confirmation Dialog for Anonymization */}
      {showConfirmDialog && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white p-6 rounded-lg shadow-lg max-w-md w-full">
            <h3 className="text-lg font-semibold mb-4">Potvrdenie anonymizácie</h3>
            <p className="mb-6 text-neutral-600">
              Ste si istí, že chcete anonymizovať osobné údaje tohto používateľa? Táto akcia je nevratná a všetky osobné údaje budú nahradené generickými hodnotami.
            </p>
            <div className="flex justify-end space-x-3">
              <button
                onClick={() => setShowConfirmDialog(false)}
                className="px-4 py-2 border border-neutral-300 rounded text-neutral-700 hover:bg-neutral-50"
              >
                Zrušiť
              </button>
              <button
                onClick={anonymizeUser}
                className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700"
              >
                Anonymizovať
              </button>
            </div>
          </div>
        </div>
      )}

      <div className="bg-white rounded-lg shadow-sm overflow-hidden mb-24">
        <div className="p-6">
          <h2 className="text-xl font-semibold mb-4">Správa používateľov</h2>

          {users.length > 0 ? (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-neutral-200">
                <thead className="bg-neutral-50">
                  <tr>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-neutral-500 uppercase tracking-wider">
                      ID
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-neutral-500 uppercase tracking-wider">
                      Meno
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-neutral-500 uppercase tracking-wider">
                      Email
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-neutral-500 uppercase tracking-wider">
                      Rola
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-neutral-500 uppercase tracking-wider">
                      Vytvorené
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-neutral-500 uppercase tracking-wider">
                      Stav
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-neutral-500 uppercase tracking-wider">
                      GDPR
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-neutral-500 uppercase tracking-wider">
                      Akcie
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-neutral-200">
                  {users.map((user) => (
                    <tr key={user.hashedId}>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-neutral-500">
                        {user.id}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-neutral-900">{user.name}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-neutral-500">{user.email}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                          user.role === 'ADMIN' ? 'bg-red-100 text-red-800' :
                          user.role === 'USER' ? 'bg-blue-100 text-blue-800' :
                          'bg-gray-100 text-gray-800'
                        }`}>
                          {user.role}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-neutral-500">
                        {formatDate(user.createdAt)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                          user.status === 'BLOCKED' ? 'bg-red-100 text-red-800' :
                          'bg-green-100 text-green-800'
                        }`}>
                          {user.status === 'BLOCKED' ? 'Blokovaný' : 'Aktívny'}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                          user.isAnonymized ? 'bg-blue-100 text-blue-800' : 'bg-gray-100 text-gray-800'
                        }`}>
                          {user.isAnonymized ? 'Anonymizovaný' : 'Štandardný'}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-neutral-500">
                        {user.role !== 'ADMIN' && (
                          <>
                            <button
                              onClick={() => toggleUserStatus(user, user.status || 'ACTIVE')}
                              className={`p-1 rounded mr-2 inline-block ${
                                user.status === 'BLOCKED'
                                  ? 'bg-green-50 text-green-600 hover:text-green-800 hover:bg-green-100'
                                  : 'bg-red-50 text-red-600 hover:text-red-800 hover:bg-red-100'
                              }`}
                              title={user.status === 'BLOCKED' ? 'Odblokovať' : 'Zablokovať'}
                            >
                              <span className="text-lg font-medium">
                                {user.status === 'BLOCKED' ? '✓' : '✕'}
                              </span>
                            </button>

                            {!user.isAnonymized && (
                              <button
                                onClick={() => showAnonymizeConfirmation(user)}
                                className="p-1 rounded inline-block bg-blue-50 text-blue-600 hover:text-blue-800 hover:bg-blue-100"
                                title="Anonymizovať (GDPR)"
                              >
                                <span className="text-lg font-medium">
                                  🔒
                                </span>
                              </button>
                            )}

                            {user.isAnonymized && (
                              <span className="p-1 rounded inline-block bg-gray-100 text-gray-500 cursor-not-allowed" title="Používateľ je anonymizovaný">
                                <span className="text-lg font-medium">
                                  ✓🔒
                                </span>
                              </span>
                            )}
                          </>
                        )}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          ) : (
            <p className="text-neutral-500">Žiadni používatelia neboli nájdení.</p>
          )}
        </div>
      </div>
    </div>
  );
}
