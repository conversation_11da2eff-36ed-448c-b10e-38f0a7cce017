'use client';

import { useEffect, useState } from 'react';
import { useAuth } from '../../../contexts/AuthContext';
import { useRouter } from 'next/navigation';
import AdminTabs from '../../../components/admin/AdminTabs';
import EmailStatisticsCard from '../../../components/admin/EmailStatisticsCard';
import EmailTypeBreakdown from '../../../components/admin/EmailTypeBreakdown';
import EmailQueueStatus from '../../../components/admin/EmailQueueStatus';
import EmailLogsTable from '../../../components/admin/EmailLogsTable';
import { toast } from 'react-toastify';

interface EmailQueueStatus {
  queueLength: number;
  isProcessing: boolean;
  pendingEmails: Array<{
    id: string;
    type: string;
    retryCount: number;
    scheduledAt: Date;
  }>;
}

interface EmailStatistics {
  totalEmails: number;
  sentEmails: number;
  failedEmails: number;
  pendingEmails: number;
  successRate: number;
  dailyStats: Array<{
    date: string;
    sent: number;
    failed: number;
  }>;
  weeklyStats: Array<{
    week: string;
    sent: number;
    failed: number;
  }>;
  monthlyStats: Array<{
    month: string;
    sent: number;
    failed: number;
  }>;
  emailTypeBreakdown: Array<{
    type: string;
    sent: number;
    failed: number;
  }>;
}

interface EmailLog {
  id: number;
  emailId: string;
  emailType: string;
  recipientEmail: string;
  status: string;
  attempt: number;
  maxAttempts: number;
  errorMessage?: string;
  messageId?: string;
  queuedAt?: Date;
  sentAt?: Date;
  failedAt?: Date;
  createdAt: Date;
}

interface EmailMonitoringData {
  queue: EmailQueueStatus;
  statistics: EmailStatistics;
  recentLogs: EmailLog[];
}

export default function EmailMonitoringPage() {
  const { user } = useAuth();
  const router = useRouter();
  const [monitoringData, setMonitoringData] = useState<EmailMonitoringData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [isClearingQueue, setIsClearingQueue] = useState(false);
  const [autoRefresh, setAutoRefresh] = useState(true);

  // Redirect if not admin
  useEffect(() => {
    if (user && user.role !== 'admin') {
      router.push('/');
    }
  }, [user, router]);

  // Load monitoring data
  const loadMonitoringData = async (showLoading = true) => {
    try {
      if (showLoading) {
        setIsLoading(true);
      } else {
        setIsRefreshing(true);
      }

      if (!user || !user.getIdToken) {
        throw new Error('No user authenticated or getIdToken method not available');
      }
      const token = await user.getIdToken();
      if (!token) {
        throw new Error('No authentication token');
      }

      const response = await fetch('/api/admin/email-monitoring?type=all&limit=50', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error('Failed to fetch monitoring data');
      }

      const result = await response.json();
      if (result.success) {
        setMonitoringData(result.data);
      } else {
        throw new Error(result.error || 'Unknown error');
      }
    } catch (error) {
      console.error('Error loading monitoring data:', error);
      toast.error('Chyba pri načítavaní údajov o emailovom monitoringu');
    } finally {
      setIsLoading(false);
      setIsRefreshing(false);
    }
  };

  // Clear email queue
  const clearEmailQueue = async () => {
    if (!confirm('Naozaj chcete vymazať email frontu? Táto akcia je nevratná.')) {
      return;
    }

    try {
      setIsClearingQueue(true);

      if (!user || !user.getIdToken) {
        throw new Error('No user authenticated or getIdToken method not available');
      }
      const token = await user.getIdToken();
      if (!token) {
        throw new Error('No authentication token');
      }

      const response = await fetch('/api/admin/email-monitoring', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ action: 'clear-queue' }),
      });

      if (!response.ok) {
        throw new Error('Failed to clear queue');
      }

      const result = await response.json();
      if (result.success) {
        toast.success('Email fronta bola vymazaná');
        await loadMonitoringData(false);
      } else {
        throw new Error(result.error || 'Unknown error');
      }
    } catch (error) {
      console.error('Error clearing queue:', error);
      toast.error('Chyba pri mazaní email fronty');
    } finally {
      setIsClearingQueue(false);
    }
  };

  // Auto-refresh functionality
  useEffect(() => {
    if (!user || user.role !== 'admin') return;

    loadMonitoringData();

    let interval: NodeJS.Timeout;
    if (autoRefresh) {
      interval = setInterval(() => {
        loadMonitoringData(false);
      }, 30000); // Refresh every 30 seconds
    }

    return () => {
      if (interval) {
        clearInterval(interval);
      }
    };
  }, [user, autoRefresh]);



  if (isLoading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">
          <p className="text-neutral-500 text-lg">Načítavam email monitoring...</p>
        </div>
      </div>
    );
  }

  if (!user || user.role !== 'admin') {
    return null;
  }

  return (
    <div className="container mx-auto px-4 py-8 mb-16">
      <h1 className="text-3xl font-bold mb-4">Email Monitoring</h1>

      <AdminTabs />

      {/* Auto-refresh controls */}
      <div className="mb-6 flex justify-between items-center">
        <div className="flex items-center space-x-4">
          <label className="flex items-center">
            <input
              type="checkbox"
              checked={autoRefresh}
              onChange={(e) => setAutoRefresh(e.target.checked)}
              className="mr-2"
            />
            Automatické obnovenie (30s)
          </label>
          <button
            onClick={() => loadMonitoringData(false)}
            disabled={isRefreshing}
            className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:opacity-50"
          >
            {isRefreshing ? 'Obnovuje sa...' : 'Obnoviť'}
          </button>
        </div>
        <button
          onClick={clearEmailQueue}
          disabled={isClearingQueue}
          className="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600 disabled:opacity-50"
        >
          {isClearingQueue ? 'Maže sa...' : 'Vymazať frontu'}
        </button>
      </div>

      {monitoringData && (
        <>
          {/* Queue Status */}
          <EmailQueueStatus
            queueLength={monitoringData.queue.queueLength}
            isProcessing={monitoringData.queue.isProcessing}
            pendingEmails={monitoringData.statistics.pendingEmails}
            pendingEmailsDetails={monitoringData.queue.pendingEmails}
          />

          {/* Email Statistics */}
          <div className="mb-8">
            <h2 className="text-2xl font-semibold mb-4">Štatistiky emailov</h2>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
              <EmailStatisticsCard
                title="Celkom emailov"
                value={monitoringData.statistics.totalEmails}
                color="gray"
              />
              <EmailStatisticsCard
                title="Úspešne odoslané"
                value={monitoringData.statistics.sentEmails}
                color="green"
              />
              <EmailStatisticsCard
                title="Neúspešné"
                value={monitoringData.statistics.failedEmails}
                color="red"
              />
              <EmailStatisticsCard
                title="Úspešnosť"
                value={`${monitoringData.statistics.successRate.toFixed(1)}%`}
                color="blue"
              />
            </div>

            {/* Email Type Breakdown */}
            <EmailTypeBreakdown emailTypeBreakdown={monitoringData.statistics.emailTypeBreakdown} />
          </div>

          {/* Recent Email Logs */}
          <EmailLogsTable logs={monitoringData.recentLogs} />
        </>
      )}
    </div>
  );
}
