'use client';

import { useEffect, useState } from 'react';
import { useAuth } from '../../../contexts/AuthContext';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import AdminTabs from '../../../components/admin/AdminTabs';
import { translateToyStatus, getToyStatusClasses } from '../../../lib/constants';
import { formatToyPriceAdmin } from '../../../lib/priceUtils';
import { generateToyUrl } from '../../../lib/seoUtils';

interface Toy {
  id: string;
  name: string;
  description: string;
  type: string;
  location: string;
  price: number;
  deposit: number;
  priceType?: string; // Typ ceny (PER_DAY alebo PER_RENTAL)
  status: string;
  statusLabel?: string; // Popisný label pre status hračky
  image: string;
  owner?: {
    hashedId: string;
    name: string;
    email: string;
    status?: string;
  };
}

export default function AdminToys() {
  const { user } = useAuth();
  const router = useRouter();
  const [toys, setToys] = useState<Toy[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [isDeleting, setIsDeleting] = useState(false);

  // Presmerovanie, ak používateľ nie je admin
  useEffect(() => {
    if (user === null) {
      // Používateľ nie je prihlásený
      router.push('/prihlasenie');
    } else if (user && user.role !== 'admin') {
      // Používateľ je prihlásený, ale nie je admin
      router.push('/');
    }
  }, [user, router]);

  // Načítanie všetkých hračiek
  useEffect(() => {
    async function loadToys() {
      try {
        if (!user) return;

        // Získanie Firebase tokenu pre autentifikáciu
        if (!user.getIdToken) return;
        const token = await user.getIdToken();

        // Volanie API s autentifikačnými údajmi
        // Rola používateľa sa overí na backende podľa autentifikačných údajov
        // Použijeme špeciálny endpoint pre administrátora, ktorý vráti všetky hračky vrátane DRAFT
        const response = await fetch('/api/admin/toys', {
          headers: {
            'Authorization': `Bearer ${token}`,
          }
        });

        if (!response.ok) {
          throw new Error('Nepodarilo sa načítať hračky');
        }
        const data = await response.json();
        setToys(data);
      } catch (err) {
        setError('Nastala chyba pri načítaní hračiek');
        console.error('Chyba pri načítaní hračiek:', err);
      } finally {
        setIsLoading(false);
      }
    }

    if (user && user.role === 'admin') {
      loadToys();
    } else {
      setIsLoading(false);
    }
  }, [user]);



  // Funkcia pre odstránenie hračky
  const handleDeleteToy = async (toyId: string) => {
    if (!confirm('Naozaj chcete odstrániť túto hračku?')) {
      return;
    }

    setIsDeleting(true);
    setError(null);
    setSuccess(null);

    try {
      // Kontrola, či je používateľ prihlásený
      if (!user) {
        setError('Nie ste prihlásený');
        return;
      }

      // Získanie Firebase tokenu pre autentifikáciu
      if (!user.getIdToken) {
        setError('Chyba pri získavaní autentifikačného tokenu');
        return;
      }
      const token = await user.getIdToken();

      // Volanie API pre odstránenie hračky
      const response = await fetch('/api/admin/toys/delete', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify({ id: toyId }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Nepodarilo sa odstrániť hračku');
      }

      // Aktualizácia zoznamu hračiek - použijeme ID pre filtrovanie
      setToys(toys.filter(toy => toy.id !== toyId));
      setSuccess('Hračka bola úspešne odstránená');

      // Skrytie hlásenia o úspechu po 3 sekundách
      setTimeout(() => {
        setSuccess(null);
      }, 3000);
    } catch (err) {
      console.error('Chyba pri odstraňovaní hračky:', err);
      setError(err instanceof Error ? err.message : 'Nastala chyba pri odstraňovaní hračky');
    } finally {
      setIsDeleting(false);
    }
  };

  if (isLoading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">
          <p className="text-neutral-500 text-lg">Načítavam administrátorský panel...</p>
        </div>
      </div>
    );
  }

  if (!user || user.role !== 'admin') {
    return null; // Toto sa nezobrazí, pretože useEffect presmeruje používateľa
  }

  return (
    <div className="container mx-auto px-4 py-8 mb-16">
      <h1 className="text-3xl font-bold mb-4">Administrátorský panel</h1>

      <AdminTabs />

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          {error}
        </div>
      )}

      {success && (
        <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
          {success}
        </div>
      )}

      <div className="bg-white rounded-lg shadow-sm overflow-hidden mb-24">
        <div className="p-6">
          <h2 className="text-xl font-semibold mb-4">Správa hračiek</h2>

          {toys.length > 0 ? (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-neutral-200">
                <thead className="bg-neutral-50">
                  <tr>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-neutral-500 uppercase tracking-wider">
                      ID
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-neutral-500 uppercase tracking-wider">
                      Názov
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-neutral-500 uppercase tracking-wider">
                      Typ
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-neutral-500 uppercase tracking-wider">
                      Lokalita
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-neutral-500 uppercase tracking-wider">
                      Cena
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-neutral-500 uppercase tracking-wider">
                      Stav
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-neutral-500 uppercase tracking-wider">
                      Vlastník
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-neutral-500 uppercase tracking-wider">
                      Akcie
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-neutral-200">
                  {toys.map((toy) => (
                    <tr key={toy.id}>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-neutral-500">
                        {toy.id}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-neutral-900">{toy.name}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-neutral-500">
                        {toy.type}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-neutral-500">
                        {toy.location}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-neutral-500">
                        {formatToyPriceAdmin(toy.price, toy.priceType)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${getToyStatusClasses(toy.status)}`}>
                          {toy.statusLabel || translateToyStatus(toy.status)}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-neutral-500">
                        {toy.owner ? (
                          <div>
                            {toy.owner.name}
                            {toy.owner.status === 'BLOCKED' && (
                              <span className="ml-2 px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">
                                Blokovaný
                              </span>
                            )}
                          </div>
                        ) : 'Neznámy'}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-neutral-500">
                        <Link
                          href={`/admin/toys/edit/${toy.id}`}
                          className="bg-primary-50 text-primary hover:text-primary-dark p-1 rounded mr-2 inline-block"
                          title="Upraviť"
                        >
                          <span className="text-lg font-medium">
                            ✏️
                          </span>
                        </Link>
                        <Link
                          href={generateToyUrl(toy.name, toy.id)}
                          className="bg-neutral-50 text-neutral-500 hover:text-neutral-700 p-1 rounded mr-2 inline-block"
                          target="_blank"
                          title="Zobraziť"
                        >
                          <span className="text-lg font-medium">
                            👁️
                          </span>
                        </Link>
                        <button
                          onClick={() => handleDeleteToy(toy.id)}
                          className="text-red-600 hover:text-red-800 bg-red-50 p-1 rounded"
                          disabled={isDeleting}
                          title="Odstrániť"
                        >
                          <span className="text-lg font-medium">
                            🗑️
                          </span>
                        </button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          ) : (
            <p className="text-neutral-500">Žiadne hračky neboli nájdené.</p>
          )}
        </div>
      </div>
    </div>
  );
}
