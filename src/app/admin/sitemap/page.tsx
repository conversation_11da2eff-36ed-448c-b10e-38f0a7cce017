'use client';

import { useEffect, useState } from 'react';
import { useAuth } from '../../../contexts/AuthContext';
import { useRouter } from 'next/navigation';
import AdminTabs from '../../../components/admin/AdminTabs';
import { toast } from 'react-toastify';

interface SitemapStats {
  totalUrls: number;
  toyUrls: number;
  categoryUrls: number;
  locationUrls: number;
  userProfileUrls: number;
  staticUrls: number;
  lastGenerated?: string;
}

interface CacheStats {
  size: number;
  entries: string[];
}

interface SitemapData {
  sitemap: SitemapStats;
  cache: CacheStats;
  timestamp: string;
}

export default function SitemapManagementPage() {
  const { user } = useAuth();
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(true);
  const [sitemapData, setSitemapData] = useState<SitemapData | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [isClearing, setIsClearing] = useState(false);
  const [isWarmingUp, setIsWarmingUp] = useState(false);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [showClearConfirm, setShowClearConfirm] = useState(false);

  // Presmerovanie, ak používateľ nie je prihlásený alebo nemá rolu admin
  useEffect(() => {
    if (!isLoading && (!user || user.role !== 'admin')) {
      router.push('/');
    }
  }, [user, router, isLoading]);

  // Načítanie štatistík sitemap
  const loadSitemapStats = async () => {
    try {
      if (!user || !user.getIdToken) return;
      const token = await user.getIdToken();

      const response = await fetch('/api/admin/sitemap', {
        headers: {
          'Authorization': `Bearer ${token}`,
        }
      });

      if (!response.ok) {
        throw new Error('Nepodarilo sa načítať štatistiky sitemap');
      }

      const data = await response.json();
      setSitemapData(data);
      setError(null);
    } catch (err) {
      setError('Nastala chyba pri načítaní štatistík sitemap');
      console.error('Chyba pri načítaní štatistík sitemap:', err);
    }
  };

  useEffect(() => {
    async function loadData() {
      if (user && user.role === 'admin') {
        await loadSitemapStats();
      }
      setIsLoading(false);
    }

    loadData();
  }, [user]);

  // Vyčistenie cache
  const handleClearCache = async () => {
    setIsClearing(true);
    setError(null);

    try {
      if (!user || !user.getIdToken) return;
      const token = await user.getIdToken();

      const response = await fetch('/api/admin/sitemap', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify({ action: 'clear' }),
      });

      if (!response.ok) {
        throw new Error('Nepodarilo sa vyčistiť cache');
      }

      toast.success('Cache bol úspešne vyčistený');
      await loadSitemapStats(); // Obnovenie štatistík
    } catch (err) {
      const errorMessage = 'Nastala chyba pri čistení cache';
      setError(errorMessage);
      toast.error(errorMessage);
      console.error('Chyba pri čistení cache:', err);
    } finally {
      setIsClearing(false);
      setShowClearConfirm(false);
    }
  };

  // Zahriatie cache
  const handleWarmUpCache = async () => {
    setIsWarmingUp(true);
    setError(null);

    try {
      if (!user || !user.getIdToken) return;
      const token = await user.getIdToken();

      const response = await fetch('/api/admin/sitemap', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify({ action: 'warmup' }),
      });

      if (!response.ok) {
        throw new Error('Nepodarilo sa zahriať cache');
      }

      toast.success('Cache bol úspešne zahriaty');
      await loadSitemapStats(); // Obnovenie štatistík
    } catch (err) {
      const errorMessage = 'Nastala chyba pri zahrievaní cache';
      setError(errorMessage);
      toast.error(errorMessage);
      console.error('Chyba pri zahrievaní cache:', err);
    } finally {
      setIsWarmingUp(false);
    }
  };

  // Obnovenie štatistík
  const handleRefreshStats = async () => {
    setIsRefreshing(true);
    await loadSitemapStats();
    setIsRefreshing(false);
    toast.success('Štatistiky boli obnovené');
  };

  // Formátovanie dátumu
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('sk-SK', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });
  };

  if (isLoading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">
          <p className="text-neutral-500 text-lg">Načítavam administrátorský panel...</p>
        </div>
      </div>
    );
  }

  if (!user || user.role !== 'admin') {
    return null;
  }

  return (
    <div className="container mx-auto px-4 py-8 mb-16">
      <h1 className="text-3xl font-bold mb-4">Administrátorský panel</h1>

      <AdminTabs />

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          {error}
        </div>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Štatistiky sitemap */}
        <div className="bg-white rounded-lg shadow-sm overflow-hidden">
          <div className="p-6">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-xl font-semibold">Štatistiky Sitemap</h2>
              <button
                onClick={handleRefreshStats}
                disabled={isRefreshing}
                className="btn btn-secondary btn-sm"
              >
                {isRefreshing ? (
                  <span className="flex items-center">
                    <svg className="animate-spin h-4 w-4 mr-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Obnovujem...
                  </span>
                ) : (
                  'Obnoviť'
                )}
              </button>
            </div>

            {sitemapData ? (
              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="bg-blue-50 p-4 rounded-lg">
                    <div className="text-2xl font-bold text-blue-600">{sitemapData.sitemap.totalUrls}</div>
                    <div className="text-sm text-blue-600">Celkový počet URL</div>
                  </div>
                  <div className="bg-green-50 p-4 rounded-lg">
                    <div className="text-2xl font-bold text-green-600">{sitemapData.sitemap.toyUrls}</div>
                    <div className="text-sm text-green-600">Hračky</div>
                  </div>
                  <div className="bg-purple-50 p-4 rounded-lg">
                    <div className="text-2xl font-bold text-purple-600">{sitemapData.sitemap.categoryUrls}</div>
                    <div className="text-sm text-purple-600">Kategórie</div>
                  </div>
                  <div className="bg-orange-50 p-4 rounded-lg">
                    <div className="text-2xl font-bold text-orange-600">{sitemapData.sitemap.locationUrls}</div>
                    <div className="text-sm text-orange-600">Lokality</div>
                  </div>
                  <div className="bg-pink-50 p-4 rounded-lg">
                    <div className="text-2xl font-bold text-pink-600">{sitemapData.sitemap.userProfileUrls}</div>
                    <div className="text-sm text-pink-600">Profily používateľov</div>
                  </div>
                  <div className="bg-gray-50 p-4 rounded-lg">
                    <div className="text-2xl font-bold text-gray-600">{sitemapData.sitemap.staticUrls}</div>
                    <div className="text-sm text-gray-600">Statické stránky</div>
                  </div>
                </div>

                <div className="border-t pt-4">
                  <div className="text-sm text-neutral-600">
                    <div><strong>Posledná aktualizácia:</strong> {formatDate(sitemapData.timestamp)}</div>
                  </div>
                </div>
              </div>
            ) : (
              <div className="text-center py-8">
                <p className="text-neutral-500">Načítavam štatistiky...</p>
              </div>
            )}
          </div>
        </div>

        {/* Správa cache */}
        <div className="bg-white rounded-lg shadow-sm overflow-hidden">
          <div className="p-6">
            <h2 className="text-xl font-semibold mb-4">Správa Cache</h2>

            {sitemapData && (
              <div className="space-y-4">
                <div className="bg-gray-50 p-4 rounded-lg">
                  <div className="text-lg font-semibold text-gray-700">{sitemapData.cache.size}</div>
                  <div className="text-sm text-gray-600">Počet položiek v cache</div>
                </div>

                <div className="space-y-2">
                  <h3 className="font-medium text-gray-700">Akcie cache:</h3>
                  
                  <button
                    onClick={handleWarmUpCache}
                    disabled={isWarmingUp}
                    className="w-full btn btn-primary"
                  >
                    {isWarmingUp ? (
                      <span className="flex items-center justify-center">
                        <svg className="animate-spin h-4 w-4 mr-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        Zahrievam cache...
                      </span>
                    ) : (
                      'Zahriať Cache'
                    )}
                  </button>

                  <button
                    onClick={() => setShowClearConfirm(true)}
                    disabled={isClearing}
                    className="w-full btn btn-danger"
                  >
                    {isClearing ? (
                      <span className="flex items-center justify-center">
                        <svg className="animate-spin h-4 w-4 mr-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        Čistím cache...
                      </span>
                    ) : (
                      'Vyčistiť Cache'
                    )}
                  </button>
                </div>

                <div className="text-xs text-gray-500 mt-4">
                  <p><strong>Zahriať Cache:</strong> Vygeneruje sitemap a uloží ho do cache pre rýchlejší prístup.</p>
                  <p><strong>Vyčistiť Cache:</strong> Odstráni všetky uložené dáta z cache. Ďalšia požiadavka vygeneruje nový sitemap.</p>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Potvrdzovacie okno pre vyčistenie cache */}
      {showClearConfirm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
            <h3 className="text-lg font-semibold mb-4">Potvrdiť vyčistenie cache</h3>
            <p className="text-gray-600 mb-6">
              Naozaj chcete vyčistiť cache sitemap? Táto akcia odstráni všetky uložené dáta a ďalšia požiadavka bude musieť vygenerovať nový sitemap.
            </p>
            <div className="flex space-x-4">
              <button
                onClick={() => setShowClearConfirm(false)}
                className="flex-1 btn btn-secondary"
              >
                Zrušiť
              </button>
              <button
                onClick={handleClearCache}
                disabled={isClearing}
                className="flex-1 btn btn-danger"
              >
                {isClearing ? 'Čistím...' : 'Vyčistiť'}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
