/**
 * Root-level sitemap endpoint
 * Generates and serves XML sitemap for search engines at /sitemap.xml
 */

import { NextRequest, NextResponse } from 'next/server';
import { generateSitemap } from '../../lib/sitemapGenerator';

/**
 * GET handler for sitemap.xml
 * Returns XML sitemap with all publicly accessible URLs
 */
export async function GET(request: NextRequest): Promise<NextResponse> {
  const startTime = Date.now();

  try {
    console.log('Generating sitemap...');

    // Generate sitemap XML
    const sitemapXml = await generateSitemap();

    const generationTime = Date.now() - startTime;
    console.log(`Sitemap generated successfully in ${generationTime}ms`);

    // Return XML response with appropriate headers
    return new NextResponse(sitemapXml, {
      status: 200,
      headers: {
        'Content-Type': 'application/xml; charset=utf-8',
        'Cache-Control': 'public, max-age=900, s-maxage=900', // Cache for 15 minutes
        'X-Robots-Tag': 'noindex', // Don't index the sitemap itself
        'X-Generation-Time': `${generationTime}ms`,
        'Last-Modified': new Date().toUTCString(),
      },
    });
  } catch (error) {
    const errorTime = Date.now() - startTime;
    console.error(`Error generating sitemap after ${errorTime}ms:`, error);

    // Return error response with proper XML format
    const errorXml = `<?xml version="1.0" encoding="UTF-8"?>
<error>
  <message>Sitemap temporarily unavailable</message>
  <timestamp>${new Date().toISOString()}</timestamp>
</error>`;

    return new NextResponse(errorXml, {
      status: 500,
      headers: {
        'Content-Type': 'application/xml; charset=utf-8',
        'Cache-Control': 'no-cache, no-store, must-revalidate',
      },
    });
  }
}

// Disable static generation for this route to ensure fresh data
export const dynamic = 'force-dynamic';
