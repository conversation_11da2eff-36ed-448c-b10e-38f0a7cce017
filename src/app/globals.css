@import "tailwindcss";
@import "./styles/form-elements.css";
@import "./styles/mobile-form.css";
@import "./styles/mobile-header.css";

:root {
  /* Základné farby */
  --background: #ffffff;
  --foreground: #333333;

  /* P<PERSON>árna farebná paleta - jemné pastelové farby */
  --primary: #4ECDC4;      /* Tyrkysová - hlavná farba */
  --primary-light: #A7E8BD; /* Svet<PERSON> */
  --primary-dark: #1A535C;  /* Tmavo tyrkysová */

  /* Sekundárna farebná paleta */
  --secondary: #FF6B6B;    /* <PERSON><PERSON>ová - akcentová farba */
  --secondary-light: #FFE66D; /* <PERSON><PERSON><PERSON> */
  --secondary-dark: #F25F5C;  /* Tmavo <PERSON> */

  /* Neutrálne farby */
  --neutral-100: #f8f9fa;
  --neutral-200: #e9ecef;
  --neutral-300: #dee2e6;
  --neutral-400: #ced4da;
  --neutral-500: #adb5bd;
  --neutral-600: #6c757d;
  --neutral-700: #495057;
  --neutral-800: #343a40;
  --neutral-900: #212529;

  /* Stavové farby */
  --success: #A7E8BD;
  --warning: #FFE66D;
  --error: #F25F5C;
  --info: #4ECDC4;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: var(--font-sans, Arial, Helvetica, sans-serif);
}

/* Základné komponenty */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: 0.5rem;
  padding: 0.5rem 1rem;
  font-weight: 500;
  transition: all 0.2s ease-in-out;
  cursor: pointer;
}

.btn-primary {
  background-color: var(--primary);
  color: white;
}

.btn-primary:hover {
  background-color: var(--primary-dark);
}

.btn-secondary {
  background-color: var(--secondary);
  color: white;
}

.btn-secondary:hover {
  background-color: var(--secondary-dark);
}

.btn-outline {
  background-color: transparent;
  border: 1px solid var(--primary);
  color: var(--primary);
}

.btn-outline:hover {
  background-color: var(--primary);
  color: white;
}

.btn-danger {
  background-color: #dc2626;
  color: white;
}

.btn-danger:hover {
  background-color: #b91c1c;
}

.btn-sm {
  padding: 0.25rem 0.75rem;
  font-size: 0.875rem;
}

/* Subtle delete button for toy cards */
.btn-delete-subtle {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: 0.375rem;
  padding: 0.375rem;
  background-color: transparent;
  border: 1px solid #fecaca;
  color: #dc2626;
  transition: all 0.2s ease-in-out;
  cursor: pointer;
  min-width: 32px;
  min-height: 32px;
  opacity: 0.8;
}

.btn-delete-subtle:hover {
  background-color: #fef2f2;
  border-color: #fca5a5;
  color: #b91c1c;
  opacity: 1;
  transform: scale(1.05);
}

.btn-delete-subtle:active {
  transform: scale(0.95);
}

.btn-delete-subtle:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

.btn-delete-subtle:disabled:hover {
  background-color: transparent;
  border-color: #fecaca;
  color: #dc2626;
  transform: none;
}

.container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

/* Animácie */
@keyframes fade-in {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in {
  animation: fade-in 0.5s ease-in-out;
}

/* Modern Footer Styles */
.footer-gradient-border {
  background: linear-gradient(90deg, transparent 0%, var(--neutral-200) 50%, transparent 100%);
  height: 1px;
}

/* Enhanced hover effects for footer links */
footer a:hover {
  transform: translateX(2px);
}

/* Smooth animations for footer elements */
footer .group:hover .bg-gradient-to-r {
  background-size: 200% 200%;
  animation: gradient-shift 2s ease infinite;
}

@keyframes gradient-shift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

/* Version badge pulse animation */
footer .inline-flex.items-center.px-3.py-1 {
  animation: subtle-pulse 3s ease-in-out infinite;
}

@keyframes subtle-pulse {
  0%, 100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.02);
    opacity: 0.9;
  }
}

/* Responzívne nastavenia */
@media (max-width: 768px) {
  .container {
    padding: 0 0.5rem;
  }

  /* Mobile footer adjustments */
  footer .absolute.w-96.h-96 {
    width: 200px;
    height: 200px;
  }

  footer .grid {
    gap: 2rem;
  }


}
