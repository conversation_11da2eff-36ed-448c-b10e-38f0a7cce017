/**
 * Dynamic robots.txt endpoint
 * Generates robots.txt with environment-aware sitemap URL
 */

import { NextRequest, NextResponse } from 'next/server';

/**
 * GET handler for robots.txt
 * Returns robots.txt with correct sitemap URL for current environment
 */
export async function GET(request: NextRequest): Promise<NextResponse> {
  // Determine base URL based on environment
  const baseUrl = process.env.NODE_ENV === 'production' 
    ? (process.env.NEXT_PUBLIC_BASE_URL || 'https://swapka.sk')
    : `http://localhost:3000`;

  const robotsTxt = `User-agent: *
Allow: /

# Sitemap location
Sitemap: ${baseUrl}/sitemap.xml

# Disallow admin and API routes
Disallow: /admin/
Disallow: /api/

# Disallow private user areas
Disallow: /moje-hracky/
Disallow: /rezervacie/

# Allow public toy and profile pages
Allow: /hracky/
Allow: /profil/

# Crawl delay (be respectful to server resources)
Crawl-delay: 1`;

  return new NextResponse(robotsTxt, {
    status: 200,
    headers: {
      'Content-Type': 'text/plain',
      'Cache-Control': 'public, max-age=86400', // Cache for 24 hours
    },
  });
}

// Disable static generation for this route to ensure environment-aware URLs
export const dynamic = 'force-dynamic';
