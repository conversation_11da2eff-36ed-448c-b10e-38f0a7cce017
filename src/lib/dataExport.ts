/**
 * Utility functions for GDPR-compliant user data export
 * These functions handle exporting user data in XML and JSON formats,
 * downloading user images, and packaging everything into a ZIP file.
 */

import { prisma } from './db';
import { Readable } from 'stream';
import xmlbuilder from 'xmlbuilder';
import archiver from 'archiver';
import fetch from 'node-fetch';
import { PassThrough } from 'stream';
import { deleteCloudinaryImage } from './cloudinary';

/**
 * Fetches all data for a user, including related records
 * @param userId ID of the user to fetch data for
 * @returns All user data with related records
 */
export async function fetchUserData(userId: number) {
  // Get user with all related data
  const user = await prisma.user.findUnique({
    where: { id: userId },
    include: {
      // Include all toys owned by the user
      toys: {
        include: {
          images: true,
          reservations: {
            include: {
              user: {
                select: {
                  id: true,
                  name: true,
                  email: true,
                  phone: true,
                  city: true,
                  postalCode: true,
                }
              }
            }
          }
        }
      },
      // Include all reservations made by the user
      reservations: {
        include: {
          toy: {
            include: {
              images: true,
              user: {
                select: {
                  id: true,
                  name: true,
                  email: true,
                  phone: true,
                  city: true,
                  postalCode: true,
                }
              }
            }
          }
        }
      },
      // Include all reservations for toys owned by the user
      toyReservations: {
        include: {
          toy: {
            include: {
              images: true
            }
          },
          user: {
            select: {
              id: true,
              name: true,
              email: true,
              phone: true,
              city: true,
              postalCode: true,
            }
          }
        }
      },
      // Include all toys hidden by the user
      hiddenToys: {
        include: {
          toy: {
            include: {
              images: true,
              user: {
                select: {
                  id: true,
                  name: true,
                  email: true,
                }
              }
            }
          }
        }
      }
    }
  });

  return user;
}

/**
 * Converts user data to JSON format
 * @param userData User data to convert
 * @returns JSON string of user data
 */
export function convertToJson(userData: any): string {
  return JSON.stringify(userData, null, 2);
}

/**
 * Converts user data to XML format
 * @param userData User data to convert
 * @returns XML string of user data
 */
export function convertToXml(userData: any): string {
  // Create root element
  const root = xmlbuilder.create('userData', { encoding: 'utf-8' });

  // Add user data to XML
  addObjectToXml(root, 'user', userData);

  // Return XML as string
  return root.end({ pretty: true });
}

/**
 * Helper function to recursively add objects to XML
 * @param parent Parent XML element
 * @param name Name of the element
 * @param data Data to add
 */
function addObjectToXml(parent: any, name: string, data: any) {
  if (data === null || data === undefined) {
    parent.ele(name);
    return;
  }

  if (typeof data !== 'object') {
    parent.ele(name, data.toString());
    return;
  }

  if (Array.isArray(data)) {
    const arrayElement = parent.ele(name);
    data.forEach((item, index) => {
      // For arrays, use singular form of the name for items
      const itemName = name.endsWith('s') ? name.slice(0, -1) : 'item';
      addObjectToXml(arrayElement, itemName, item);
    });
    return;
  }

  // Handle regular objects
  const element = parent.ele(name);
  for (const [key, value] of Object.entries(data)) {
    addObjectToXml(element, key, value);
  }
}

/**
 * Downloads an image from a URL
 * @param url URL of the image to download
 * @returns Buffer containing the image data
 */
export async function downloadImage(url: string): Promise<Buffer> {
  const response = await fetch(url);
  if (!response.ok) {
    throw new Error(`Failed to download image: ${response.statusText}`);
  }

  const buffer = await response.buffer();
  return buffer;
}

/**
 * Creates a ZIP file containing user data and images
 * @param userData User data object
 * @param format Format of the data file ('json' or 'xml')
 * @returns Readable stream of the ZIP file
 */
export async function createUserDataZip(userData: any, format: 'json' | 'xml'): Promise<Readable> {
  // Create a PassThrough stream to pipe the archive to
  const passThrough = new PassThrough();

  // Create a new archive
  const archive = archiver('zip', {
    zlib: { level: 9 } // Maximum compression
  });

  // Pipe the archive to the PassThrough stream
  archive.pipe(passThrough);

  // Add user data file to the archive
  let dataContent: string;
  if (format === 'json') {
    dataContent = convertToJson(userData);
    archive.append(dataContent, { name: 'user_data.json' });
  } else {
    dataContent = convertToXml(userData);
    archive.append(dataContent, { name: 'user_data.xml' });
  }

  // Add README file to the archive
  const readmeContent = `GDPR Data Export
==============

This archive contains all your personal data from our service, as required by GDPR regulations.
The data is provided in ${format.toUpperCase()} format.

Contents:
- user_data.${format}: Your personal data and related records
- images/: All images associated with your toys

If you have any questions about this data, please contact our support team.
`;
  archive.append(readmeContent, { name: 'README.txt' });

  // Create a folder for images
  archive.append(Buffer.from(''), { name: 'images/' });

  // Add all toy images to the archive
  if (userData.toys && userData.toys.length > 0) {
    for (const toy of userData.toys) {
      if (toy.images && toy.images.length > 0) {
        for (const [index, image] of toy.images.entries()) {
          try {
            const imageBuffer = await downloadImage(image.url);
            // Extract filename from URL or generate one
            const filename = image.url.split('/').pop() || `toy_${toy.id}_image_${index}.jpg`;
            archive.append(imageBuffer, { name: `images/${filename}` });
          } catch (error) {
            console.error(`Error downloading image ${image.url}:`, error);
            // Continue with other images even if one fails
          }
        }
      }
    }
  }

  // Finalize the archive
  await archive.finalize();

  return passThrough;
}
