// Cloudinary configuration
// Constants for Cloudinary upload
export const CLOUDINARY_FOLDER = 'swapka/toys';
export const MAX_IMAGES = 5;

// Only import and configure cloudinary on the server side
// This prevents 'fs' module errors in the browser
let cloudinary: any = null;

// This code will only run on the server
if (typeof window === 'undefined') {
  // Dynamic import to avoid client-side inclusion
  const { v2 } = require('cloudinary');

  // Initialize Cloudinary with environment variables
  v2.config({
    cloud_name: process.env.CLOUDINARY_CLOUD_NAME,
    api_key: process.env.CLOUDINARY_API_KEY,
    api_secret: process.env.CLOUDINARY_API_SECRET,
  });

  cloudinary = v2;
}

/**
 * Fyzicky odstráni obrázok z Cloudinary pomocou hashed filename alebo URL
 * @param imageIdentifier URL obrázku alebo hashed filename
 * @param hashedFilename Voliteľný hashed filename pre priame použitie
 * @returns Promise<boolean> true ak sa odstránenie podarilo, false ak nie
 */
export async function deleteCloudinaryImage(imageIdentifier: string, hashedFilename?: string): Promise<boolean> {
  // Kontrola, či je cloudinary inicializovaný
  if (!cloudinary) {
    console.error('Cloudinary nie je inicializovaný');
    return false;
  }

  try {
    console.log(`Začínam odstraňovanie obrázku z Cloudinary: ${imageIdentifier}`);

    let publicId: string | null = null;

    // Ak máme priamo hashed filename, použijeme ho
    if (hashedFilename) {
      // Odstránime príponu z hashed filename pre public_id
      const hashWithoutExtension = hashedFilename.replace(/\.[^/.]+$/, '');
      // Cloudinary public_id je len hash bez cesty, ale upload sa robí do folder-a
      // Takže skutočný public_id je folder/hash
      publicId = `${CLOUDINARY_FOLDER}/${hashWithoutExtension}`;
      console.log(`Používam hashed filename: ${hashedFilename} -> public_id: ${publicId}`);
    } else {
      // Fallback na extrakciu z URL pre backward compatibility
      console.log(`Extrahujem public_id z URL: ${imageIdentifier}`);

      // Najprv skontrolujeme, či URL obsahuje 'swapka/toys'
      if (!imageIdentifier.includes('swapka/toys')) {
        console.error(`URL ${imageIdentifier} neobsahuje cestu 'swapka/toys'`);
        return false;
      }

      // Metóda 1: Extrakcia pomocou regulárneho výrazu
      // Hľadáme vzor: upload/[transformácie/verzia/]/swapka/toys/[id].[prípona]
      const regex = /\/swapka\/toys\/([^/?]+)/;
      const match = imageIdentifier.match(regex);

      if (match && match[1]) {
        // Získame ID obrázku (bez prípony)
        const imageId = match[1].split('.')[0];
        publicId = `swapka/toys/${imageId}`;
        console.log(`Extrahované public_id pomocou regex: ${publicId}`);
      } else {
        // Metóda 2: Extrakcia pomocou indexu (záložná metóda)
        const uploadIndex = imageIdentifier.indexOf('upload/');
        if (uploadIndex === -1) {
          console.error(`URL ${imageIdentifier} neobsahuje 'upload/'`);
          return false;
        }

        // Získame časť URL po 'upload/'
        let pathAfterUpload = imageIdentifier.substring(uploadIndex + 7);

        // Odstránime parametre z URL (ak existujú)
        pathAfterUpload = pathAfterUpload.split('?')[0];

        // Odstránime príponu súboru (ak existuje)
        const parts = pathAfterUpload.split('.');
        if (parts.length > 1) {
          // Ak URL obsahuje príponu, odstránime ju
          publicId = parts.slice(0, -1).join('.');
        } else {
          // Ak URL neobsahuje príponu
          publicId = pathAfterUpload;
        }

        // Ak URL obsahuje transformácie (napr. v1234567890/ alebo c_fill,g_auto/)
        // Potrebujeme nájsť začiatok "swapka/toys/"
        const swapkaIndex = publicId.indexOf('swapka/toys/');
        if (swapkaIndex !== -1) {
          publicId = publicId.substring(swapkaIndex);
        }

        console.log(`Extrahované public_id pomocou indexu: ${publicId}`);
      }
    }

    if (!publicId) {
      console.error(`Nepodarilo sa extrahovať public_id z URL: ${imageIdentifier}`);
      return false;
    }

    // Výpis pre debugging
    console.log(`Odstraňujem obrázok s public_id: ${publicId}`);

    // Volanie Cloudinary API pre odstránenie obrázku
    const result = await cloudinary.uploader.destroy(publicId);

    console.log(`Výsledok odstránenia obrázku z Cloudinary:`, result);

    if (result.result === 'ok') {
      console.log(`Obrázok ${publicId} bol úspešne odstránený z Cloudinary`);
      return true;
    } else {
      console.error(`Nepodarilo sa odstrániť obrázok ${publicId} z Cloudinary:`, result);
      return false;
    }
  } catch (error) {
    console.error('Chyba pri odstraňovaní obrázku z Cloudinary:', error);
    return false;
  }
}

/**
 * Fyzicky odstráni všetky obrázky hračky z Cloudinary
 * @param toyId ID hračky
 * @returns Promise<boolean> true ak sa odstránenie podarilo, false ak nie
 */
export async function deleteToyImages(toyId: number): Promise<boolean> {
  try {
    console.log(`Začínam odstraňovanie všetkých obrázkov hračky ${toyId} z Cloudinary`);

    // Importujeme prisma klienta
    const { prisma } = require('./db');

    // Získanie všetkých obrázkov hračky s rozšírenými metadátami
    console.log(`Získavam zoznam obrázkov hračky ${toyId} z databázy`);
    const toyImages = await prisma.toyImage.findMany({
      where: { toyId },
      select: {
        id: true,
        url: true,
        hashedFilename: true,
        originalFilename: true
      }
    });

    console.log(`Nájdených ${toyImages?.length || 0} obrázkov hračky ${toyId} v databáze`);

    // Ak hračka nemá žiadne obrázky, vrátime true
    if (!toyImages || toyImages.length === 0) {
      console.log(`Hračka ${toyId} nemá žiadne obrázky na odstránenie`);
      return true;
    }

    console.log(`Odstraňujem ${toyImages.length} obrázkov hračky ${toyId} z Cloudinary`);
    console.log(`Zoznam obrázkov na odstránenie:`, toyImages.map((img: any) => ({
      url: img.url,
      hashedFilename: img.hashedFilename,
      originalFilename: img.originalFilename
    })));

    // Odstránenie každého obrázku z Cloudinary
    const results = [];
    for (const image of toyImages) {
      try {
        console.log(`Odstraňujem obrázok hračky ${toyId}:`, {
          url: image.url,
          hashedFilename: image.hashedFilename,
          originalFilename: image.originalFilename
        });

        // Použijeme hashed filename ak je k dispozícii, inak fallback na URL
        const result = await deleteCloudinaryImage(image.url, image.hashedFilename || undefined);
        results.push(result);
        console.log(`Výsledok odstránenia obrázku: ${result ? 'úspešné' : 'neúspešné'}`);
      } catch (imageError) {
        console.error(`Chyba pri odstraňovaní obrázku ${image.url}:`, imageError);
        results.push(false);
      }
    }

    // Kontrola výsledkov
    const successCount = results.filter(result => result === true).length;
    console.log(`Úspešne odstránených ${successCount} z ${toyImages.length} obrázkov hračky ${toyId}`);

    // Ak sa aspoň jeden obrázok podarilo odstrániť, považujeme to za úspech
    // Zmena: Pôvodne sme vyžadovali, aby boli všetky obrázky úspešne odstránené
    const success = successCount > 0;
    console.log(`Celkový výsledok odstraňovania obrázkov hračky ${toyId}: ${success ? 'úspešné' : 'neúspešné'}`);

    return success;
  } catch (error) {
    console.error(`Chyba pri odstraňovaní obrázkov hračky ${toyId} z Cloudinary:`, error);
    return false;
  }
}

/**
 * Fyzicky odstráni všetky obrázky používateľa z Cloudinary
 * @param userId ID používateľa
 * @returns Promise<boolean> true ak sa odstránenie podarilo, false ak nie
 */
export async function deleteUserImages(userId: number): Promise<boolean> {
  try {
    // Importujeme prisma klienta
    const { prisma } = require('./db');

    // Získanie všetkých hračiek používateľa
    const userToys = await prisma.toy.findMany({
      where: { userId },
      select: { id: true },
    });

    // Ak používateľ nemá žiadne hračky, vrátime true
    if (!userToys || userToys.length === 0) {
      console.log(`Používateľ ${userId} nemá žiadne hračky na odstránenie obrázkov`);
      return true;
    }

    console.log(`Odstraňujem obrázky pre ${userToys.length} hračiek používateľa ${userId}`);

    // Odstránenie obrázkov pre každú hračku
    const results = [];
    for (const toy of userToys) {
      console.log(`Odstraňujem obrázky hračky ${toy.id} používateľa ${userId}`);
      const result = await deleteToyImages(toy.id);
      results.push(result);
    }

    // Kontrola výsledkov
    const successCount = results.filter(result => result === true).length;
    console.log(`Úspešne odstránené obrázky pre ${successCount} z ${userToys.length} hračiek používateľa ${userId}`);

    // Ak sa všetky obrázky podarilo odstrániť, vrátime true
    return results.every(result => result === true);
  } catch (error) {
    console.error(`Chyba pri odstraňovaní obrázkov používateľa ${userId} z Cloudinary:`, error);
    return false;
  }
}

export default cloudinary;
