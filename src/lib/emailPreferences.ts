import { prisma } from './db';
import crypto from 'crypto';

// Email preference types
export interface EmailPreferences {
  emailNotificationsEnabled: boolean;
  emailReservationCreated: boolean;
  emailReservationApproved: boolean;
}

// Unsubscribe data
export interface UnsubscribeData {
  email: string;
  token: string;
  reason?: string;
  ipAddress?: string;
  userAgent?: string;
}

/**
 * Generates a unique unsubscribe token for a user
 */
export function generateUnsubscribeToken(userId: number, email: string): string {
  const timestamp = Date.now().toString();
  const randomBytes = crypto.randomBytes(16).toString('hex');
  const data = `${userId}_${email}_${timestamp}_${randomBytes}`;
  return crypto.createHash('sha256').update(data).digest('hex');
}

/**
 * Gets email preferences for a user
 */
export async function getUserEmailPreferences(userId: number): Promise<EmailPreferences | null> {
  try {
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: {
        emailNotificationsEnabled: true,
        emailReservationCreated: true,
        emailReservationApproved: true,
      },
    });

    return user;
  } catch (error) {
    console.error('Error getting user email preferences:', error);
    return null;
  }
}

/**
 * Updates email preferences for a user
 */
export async function updateUserEmailPreferences(
  userId: number,
  preferences: Partial<EmailPreferences>
): Promise<boolean> {
  try {
    await prisma.user.update({
      where: { id: userId },
      data: preferences,
    });

    console.log(`Email preferences updated for user ${userId}:`, preferences);
    return true;
  } catch (error) {
    console.error('Error updating user email preferences:', error);
    return false;
  }
}

/**
 * Checks if a user should receive a specific type of email notification
 * Note: Reservation-related emails are always sent as they are essential for service functionality
 */
export async function shouldSendEmailNotification(
  userId: number,
  notificationType: 'reservation_created' | 'reservation_approved' | 'reservation_request'
): Promise<boolean> {
  try {
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: {
        status: true,
        isAnonymized: true,
        email: true,
      },
    });

    if (!user) {
      console.log(`User ${userId} not found, skipping email notification`);
      return false;
    }

    // Don't send emails to inactive or anonymized users
    if (user.status !== 'ACTIVE' || user.isAnonymized) {
      console.log(`User ${userId} is inactive or anonymized, skipping email notification`);
      return false;
    }

    // Don't send emails if user has no email address
    if (!user.email) {
      console.log(`User ${userId} has no email address, skipping email notification`);
      return false;
    }

    // Reservation-related emails are always sent as they are essential for service functionality
    switch (notificationType) {
      case 'reservation_created':
      case 'reservation_approved':
      case 'reservation_request':
        console.log(`Sending ${notificationType} email to user ${userId} - essential service notification`);
        return true;
      default:
        return false;
    }
  } catch (error) {
    console.error('Error checking email notification preferences:', error);
    return false;
  }
}

/**
 * Generates and stores an unsubscribe token for a user
 */
export async function generateUserUnsubscribeToken(userId: number): Promise<string | null> {
  try {
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: { email: true, emailUnsubscribeToken: true },
    });

    if (!user) {
      console.error(`User ${userId} not found`);
      return null;
    }

    // Return existing token if available
    if (user.emailUnsubscribeToken) {
      return user.emailUnsubscribeToken;
    }

    // Generate new token
    const token = generateUnsubscribeToken(userId, user.email);

    // Store token in database
    await prisma.user.update({
      where: { id: userId },
      data: { emailUnsubscribeToken: token },
    });

    console.log(`Unsubscribe token generated for user ${userId}`);
    return token;
  } catch (error) {
    console.error('Error generating unsubscribe token:', error);
    return null;
  }
}

/**
 * Processes an unsubscribe request
 */
export async function processUnsubscribeRequest(
  token: string,
  unsubscribeData: Partial<UnsubscribeData>
): Promise<{ success: boolean; message: string }> {
  try {
    // Find user by unsubscribe token
    const user = await prisma.user.findFirst({
      where: { emailUnsubscribeToken: token },
      select: { id: true, email: true, name: true },
    });

    if (!user) {
      return {
        success: false,
        message: 'Neplatný alebo expirovaný odkaz na odhlásenie.',
      };
    }

    // Record unsubscribe event for GDPR compliance
    await prisma.emailUnsubscribe.create({
      data: {
        email: user.email,
        token: token,
        reason: unsubscribeData.reason || 'User requested unsubscribe',
        ipAddress: unsubscribeData.ipAddress,
        userAgent: unsubscribeData.userAgent,
      },
    });

    // Note: We only record the unsubscribe request but don't disable reservation emails
    // as they are essential for service functionality. Only marketing emails would be disabled.
    await prisma.user.update({
      where: { id: user.id },
      data: {
        emailNotificationsEnabled: false, // This would affect marketing emails only
        // emailReservationCreated and emailReservationApproved remain TRUE
      },
    });

    console.log(`User ${user.id} (${user.email}) unsubscribed from marketing emails (reservation emails remain active)`);

    return {
      success: true,
      message: `Úspešne ste sa odhlásili z marketingových emailov pre ${user.email}. Emailové notifikácie o rezerváciách zostávajú aktívne, pretože sú nevyhnutné pre fungovanie služby.`,
    };
  } catch (error) {
    console.error('Error processing unsubscribe request:', error);
    return {
      success: false,
      message: 'Nastala chyba pri spracovaní požiadavky na odhlásenie.',
    };
  }
}

/**
 * Gets unsubscribe URL for a user
 */
export function getUnsubscribeUrl(token: string): string {
  const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'https://swapka.sk';
  return `${baseUrl}/api/email/unsubscribe?token=${token}`;
}

/**
 * Validates if an email address is unsubscribed
 */
export async function isEmailUnsubscribed(email: string): Promise<boolean> {
  try {
    const unsubscribeRecord = await prisma.emailUnsubscribe.findFirst({
      where: { email: email },
      orderBy: { unsubscribedAt: 'desc' },
    });

    return !!unsubscribeRecord;
  } catch (error) {
    console.error('Error checking if email is unsubscribed:', error);
    return false;
  }
}

/**
 * Re-subscribes a user to email notifications (if they want to opt back in)
 */
export async function resubscribeUser(userId: number): Promise<boolean> {
  try {
    await prisma.user.update({
      where: { id: userId },
      data: {
        emailNotificationsEnabled: true,
        emailReservationCreated: true,
        emailReservationApproved: true,
      },
    });

    console.log(`User ${userId} re-subscribed to email notifications`);
    return true;
  } catch (error) {
    console.error('Error re-subscribing user:', error);
    return false;
  }
}
