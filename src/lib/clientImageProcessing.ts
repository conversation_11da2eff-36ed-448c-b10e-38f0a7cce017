/**
 * Client-side image processing utilities for compressing images before upload
 * This module handles image compression in the browser to reduce file sizes
 * before sending to the server, preventing 413 Request Entity Too Large errors
 */

// Configuration for client-side image processing
export const CLIENT_IMAGE_CONFIG = {
  MAX_DIMENSION: 1200, // Maximum width or height in pixels
  QUALITY: 0.8, // JPEG quality (0-1)
  MAX_FILE_SIZE: 2 * 1024 * 1024, // 2MB target size after compression
  SUPPORTED_FORMATS: ['image/jpeg', 'image/jpg', 'image/png', 'image/webp', 'image/heic', 'image/heif'],
  OUTPUT_FORMAT: 'image/jpeg', // Always output as JPEG for compatibility
  OUTPUT_QUALITY: 0.85, // Output quality
};

export interface CompressionResult {
  file: File;
  originalSize: number;
  compressedSize: number;
  compressionRatio: number;
  dimensions: {
    width: number;
    height: number;
  };
  format: string;
}

/**
 * Compresses an image file using Canvas API
 */
export async function compressImage(file: File): Promise<CompressionResult> {
  return new Promise((resolve, reject) => {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    const img = new Image();

    if (!ctx) {
      reject(new Error('Canvas context not available'));
      return;
    }

    img.onload = () => {
      try {
        // Calculate new dimensions
        const { width: newWidth, height: newHeight } = calculateDimensions(
          img.width,
          img.height,
          CLIENT_IMAGE_CONFIG.MAX_DIMENSION
        );

        // Set canvas dimensions
        canvas.width = newWidth;
        canvas.height = newHeight;

        // Draw and compress image
        ctx.drawImage(img, 0, 0, newWidth, newHeight);

        // Convert to blob with compression
        canvas.toBlob(
          (blob) => {
            if (!blob) {
              reject(new Error('Failed to compress image'));
              return;
            }

            // Create new file from compressed blob
            const compressedFile = new File(
              [blob],
              file.name.replace(/\.[^/.]+$/, '.jpg'), // Change extension to .jpg
              {
                type: CLIENT_IMAGE_CONFIG.OUTPUT_FORMAT,
                lastModified: Date.now(),
              }
            );

            const compressionRatio = Math.round(
              ((file.size - compressedFile.size) / file.size) * 100
            );

            resolve({
              file: compressedFile,
              originalSize: file.size,
              compressedSize: compressedFile.size,
              compressionRatio,
              dimensions: {
                width: newWidth,
                height: newHeight,
              },
              format: CLIENT_IMAGE_CONFIG.OUTPUT_FORMAT,
            });
          },
          CLIENT_IMAGE_CONFIG.OUTPUT_FORMAT,
          CLIENT_IMAGE_CONFIG.OUTPUT_QUALITY
        );
      } catch (error) {
        reject(error);
      }
    };

    img.onerror = () => {
      reject(new Error('Failed to load image'));
    };

    // Load image from file
    const reader = new FileReader();
    reader.onload = (e) => {
      if (e.target?.result) {
        img.src = e.target.result as string;
      } else {
        reject(new Error('Failed to read file'));
      }
    };
    reader.onerror = () => {
      reject(new Error('Failed to read file'));
    };
    reader.readAsDataURL(file);
  });
}

/**
 * Calculate new dimensions maintaining aspect ratio
 */
function calculateDimensions(
  originalWidth: number,
  originalHeight: number,
  maxDimension: number
): { width: number; height: number } {
  if (originalWidth <= maxDimension && originalHeight <= maxDimension) {
    return { width: originalWidth, height: originalHeight };
  }

  const aspectRatio = originalWidth / originalHeight;

  if (originalWidth > originalHeight) {
    return {
      width: maxDimension,
      height: Math.round(maxDimension / aspectRatio),
    };
  } else {
    return {
      width: Math.round(maxDimension * aspectRatio),
      height: maxDimension,
    };
  }
}

/**
 * Check if file needs compression
 */
export function needsCompression(file: File): boolean {
  return (
    file.size > CLIENT_IMAGE_CONFIG.MAX_FILE_SIZE ||
    !CLIENT_IMAGE_CONFIG.SUPPORTED_FORMATS.includes(file.type)
  );
}

/**
 * Validate if file is a supported image
 */
export function isValidImageFile(file: File): boolean {
  // Check MIME type
  if (CLIENT_IMAGE_CONFIG.SUPPORTED_FORMATS.includes(file.type)) {
    return true;
  }

  // Check file extension for HEIC files (often have wrong MIME type)
  const extension = file.name.split('.').pop()?.toLowerCase();
  if (extension && ['heic', 'heif'].includes(extension)) {
    return true;
  }

  return false;
}

/**
 * Format file size for display
 */
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
}

/**
 * Progressive compression - try multiple quality levels if file is still too large
 */
export async function progressiveCompress(file: File): Promise<CompressionResult> {
  const qualityLevels = [0.85, 0.7, 0.6, 0.5, 0.4];
  const maxDimensions = [1200, 1024, 800, 600];

  for (const maxDim of maxDimensions) {
    for (const quality of qualityLevels) {
      try {
        // Temporarily override config
        const originalMaxDim = CLIENT_IMAGE_CONFIG.MAX_DIMENSION;
        const originalQuality = CLIENT_IMAGE_CONFIG.OUTPUT_QUALITY;
        
        CLIENT_IMAGE_CONFIG.MAX_DIMENSION = maxDim;
        CLIENT_IMAGE_CONFIG.OUTPUT_QUALITY = quality;

        const result = await compressImage(file);

        // Restore original config
        CLIENT_IMAGE_CONFIG.MAX_DIMENSION = originalMaxDim;
        CLIENT_IMAGE_CONFIG.OUTPUT_QUALITY = originalQuality;

        // If compressed file is under target size, return it
        if (result.compressedSize <= CLIENT_IMAGE_CONFIG.MAX_FILE_SIZE) {
          return result;
        }
      } catch (error) {
        console.warn(`Compression failed at ${maxDim}px, quality ${quality}:`, error);
        continue;
      }
    }
  }

  // If all attempts failed, try one more time with lowest settings
  CLIENT_IMAGE_CONFIG.MAX_DIMENSION = 600;
  CLIENT_IMAGE_CONFIG.OUTPUT_QUALITY = 0.3;
  
  try {
    const result = await compressImage(file);
    return result;
  } finally {
    // Restore original config
    CLIENT_IMAGE_CONFIG.MAX_DIMENSION = 1200;
    CLIENT_IMAGE_CONFIG.OUTPUT_QUALITY = 0.85;
  }
}
