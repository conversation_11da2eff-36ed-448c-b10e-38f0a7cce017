/**
 * Centrálna knižnica pre sanitizáciu a validáciu používateľských vstupov
 * Implementuje ochranu proti XSS, SQL injection a ďalším web vulnerabilities
 * 
 * <AUTHOR> Security Team
 * @version 1.0.0
 */

import { createHash } from 'crypto';

// Typy pre konfiguráciu sanitizácie
export interface TextSanitizeOptions {
  maxLength?: number;
  minLength?: number;
  allowSpecialChars?: boolean;
  allowNumbers?: boolean;
  allowUnicode?: boolean;
  allowSlovakDiacritics?: boolean;
  trim?: boolean;
  toLowerCase?: boolean;
  removeHtml?: boolean;
}

export interface NumericSanitizeOptions {
  min?: number;
  max?: number;
  decimals?: number;
  allowNegative?: boolean;
}

export interface EmailSanitizeOptions {
  maxLength?: number;
  allowInternational?: boolean;
}

export interface SearchSanitizeOptions {
  maxLength?: number;
  removeSpecialChars?: boolean;
  preventSqlInjection?: boolean;
  allowWildcards?: boolean;
  allowSlovakDiacritics?: boolean;
}

export interface HtmlSanitizeOptions {
  maxLength?: number;
  allowedTags?: string[];
  stripScripts?: boolean;
  allowLinks?: boolean;
}

export interface FileValidationOptions {
  allowedTypes?: string[];
  maxSize?: number;
  minSize?: number;
  maxDimensions?: { width: number; height: number };
  allowedExtensions?: string[];
  relaxedFilenameValidation?: boolean;
}

export interface CoordinateOptions {
  validateRange?: boolean;
  precision?: number;
}

// Výsledky validácie
export interface ValidationResult {
  isValid: boolean;
  sanitizedValue?: any;
  errors: string[];
  warnings?: string[];
}

/**
 * Sanitizácia textových vstupov
 */
export function sanitizeText(
  input: any,
  options: TextSanitizeOptions = {}
): ValidationResult {
  const {
    maxLength = 1000,
    minLength = 0,
    allowSpecialChars = true,
    allowNumbers = true,
    allowUnicode = true,
    allowSlovakDiacritics = false,
    trim = true,
    toLowerCase = false,
    removeHtml = true
  } = options;

  const errors: string[] = [];
  
  // Kontrola typu
  if (typeof input !== 'string') {
    if (input === null || input === undefined) {
      return { isValid: false, errors: ['Text je povinný'] };
    }
    input = String(input);
  }

  let sanitized = input;

  // Trimovanie
  if (trim) {
    sanitized = sanitized.trim();
  }

  // Kontrola dĺžky
  if (sanitized.length < minLength) {
    errors.push(`Text musí mať aspoň ${minLength} znakov`);
  }
  
  if (sanitized.length > maxLength) {
    sanitized = sanitized.substring(0, maxLength);
    errors.push(`Text bol skrátený na ${maxLength} znakov`);
  }

  // Odstránenie HTML tagov
  if (removeHtml) {
    sanitized = sanitized.replace(/<[^>]*>/g, '');
  }

  // Odstránenie nebezpečných znakov pre XSS
  sanitized = sanitized
    .replace(/[<>]/g, '')
    .replace(/javascript:/gi, '')
    .replace(/on\w+\s*=/gi, '');

  // Kontrola špeciálnych znakov
  if (!allowSpecialChars) {
    if (allowSlovakDiacritics) {
      // Povolenie slovenských diakritických znakov: á, č, ď, é, í, ľ, ň, ó, ô, ŕ, š, ť, ú, ý, ž a ich veľkých verzií
      sanitized = sanitized.replace(/[^a-zA-ZáčďéíľňóôŕšťúýžÁČĎÉÍĽŇÓÔŔŠŤÚÝŽ0-9\s\-_.]/g, '');
    } else {
      sanitized = sanitized.replace(/[^a-zA-Z0-9\s\-_.]/g, '');
    }
  }

  // Kontrola čísel
  if (!allowNumbers) {
    sanitized = sanitized.replace(/[0-9]/g, '');
  }

  // Kontrola unicode znakov
  if (!allowUnicode) {
    sanitized = sanitized.replace(/[^\x00-\x7F]/g, '');
  }

  // Konverzia na malé písmená
  if (toLowerCase) {
    sanitized = sanitized.toLowerCase();
  }

  return {
    isValid: errors.length === 0,
    sanitizedValue: sanitized,
    errors
  };
}

/**
 * Sanitizácia emailových adries
 */
export function sanitizeEmail(
  input: any,
  options: EmailSanitizeOptions = {}
): ValidationResult {
  const { maxLength = 254, allowInternational = true } = options;
  const errors: string[] = [];

  if (typeof input !== 'string') {
    return { isValid: false, errors: ['Email musí byť text'] };
  }

  let sanitized = input.trim().toLowerCase();

  // Kontrola dĺžky
  if (sanitized.length > maxLength) {
    errors.push(`Email je príliš dlhý (max ${maxLength} znakov)`);
  }

  // Základná validácia emailu
  const emailRegex = allowInternational 
    ? /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    : /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;

  if (!emailRegex.test(sanitized)) {
    errors.push('Neplatný formát emailu');
  }

  // Odstránenie nebezpečných znakov
  sanitized = sanitized.replace(/[<>'"]/g, '');

  return {
    isValid: errors.length === 0,
    sanitizedValue: sanitized,
    errors
  };
}

/**
 * Sanitizácia číselných vstupov
 */
export function sanitizeNumericInput(
  input: any,
  options: NumericSanitizeOptions = {}
): ValidationResult {
  const {
    min = Number.MIN_SAFE_INTEGER,
    max = Number.MAX_SAFE_INTEGER,
    decimals = -1,
    allowNegative = true
  } = options;

  const errors: string[] = [];

  // Konverzia na číslo
  let num: number;
  if (typeof input === 'number') {
    num = input;
  } else if (typeof input === 'string') {
    num = parseFloat(input.trim());
  } else {
    return { isValid: false, errors: ['Vstup musí byť číslo'] };
  }

  // Kontrola, či je to platné číslo
  if (isNaN(num) || !isFinite(num)) {
    return { isValid: false, errors: ['Neplatné číslo'] };
  }

  // Kontrola záporných čísel
  if (!allowNegative && num < 0) {
    errors.push('Záporné čísla nie sú povolené');
  }

  // Kontrola rozsahu
  if (num < min) {
    errors.push(`Číslo musí byť aspoň ${min}`);
  }
  
  if (num > max) {
    errors.push(`Číslo nesmie byť väčšie ako ${max}`);
  }

  // Kontrola desatinných miest
  if (decimals >= 0) {
    num = parseFloat(num.toFixed(decimals));
  }

  return {
    isValid: errors.length === 0,
    sanitizedValue: num,
    errors
  };
}

/**
 * Sanitizácia vyhľadávacích dotazov
 */
export function sanitizeSearchQuery(
  input: any,
  options: SearchSanitizeOptions = {}
): ValidationResult {
  const {
    maxLength = 100,
    removeSpecialChars = true,
    preventSqlInjection = true,
    allowWildcards = false,
    allowSlovakDiacritics = false
  } = options;

  const errors: string[] = [];

  if (typeof input !== 'string') {
    if (!input) {
      return { isValid: true, sanitizedValue: '', errors: [] };
    }
    input = String(input);
  }

  let sanitized = input.trim();

  // Kontrola dĺžky
  if (sanitized.length > maxLength) {
    sanitized = sanitized.substring(0, maxLength);
    errors.push(`Vyhľadávací dotaz bol skrátený na ${maxLength} znakov`);
  }

  // Ochrana proti SQL injection
  if (preventSqlInjection) {
    const sqlKeywords = [
      'SELECT', 'INSERT', 'UPDATE', 'DELETE', 'DROP', 'CREATE', 'ALTER',
      'UNION', 'OR', 'AND', 'WHERE', 'FROM', 'JOIN', '--', ';', '/*', '*/'
    ];
    
    const upperInput = sanitized.toUpperCase();
    for (const keyword of sqlKeywords) {
      if (upperInput.includes(keyword)) {
        sanitized = sanitized.replace(new RegExp(keyword, 'gi'), '');
      }
    }
  }

  // Odstránenie nebezpečných znakov
  sanitized = sanitized
    .replace(/[<>'"]/g, '')
    .replace(/javascript:/gi, '')
    .replace(/on\w+\s*=/gi, '');

  // Odstránenie špeciálnych znakov
  if (removeSpecialChars) {
    if (allowSlovakDiacritics) {
      if (allowWildcards) {
        // Povolenie slovenských diakritických znakov a wildcards
        sanitized = sanitized.replace(/[^a-zA-ZáčďéíľňóôŕšťúýžÁČĎÉÍĽŇÓÔŔŠŤÚÝŽ0-9\s\-_.*%]/g, '');
      } else {
        // Povolenie slovenských diakritických znakov bez wildcards
        sanitized = sanitized.replace(/[^a-zA-ZáčďéíľňóôŕšťúýžÁČĎÉÍĽŇÓÔŔŠŤÚÝŽ0-9\s\-_.]/g, '');
      }
    } else {
      if (allowWildcards) {
        sanitized = sanitized.replace(/[^a-zA-Z0-9\s\-_.*%]/g, '');
      } else {
        sanitized = sanitized.replace(/[^a-zA-Z0-9\s\-_.]/g, '');
      }
    }
  }

  return {
    isValid: errors.length === 0,
    sanitizedValue: sanitized,
    errors
  };
}

/**
 * Sanitizácia HTML obsahu
 */
export function sanitizeHtml(
  input: any,
  options: HtmlSanitizeOptions = {}
): ValidationResult {
  const {
    maxLength = 2000,
    allowedTags = ['p', 'br', 'strong', 'em', 'u'],
    stripScripts = true,
    allowLinks = false
  } = options;

  const errors: string[] = [];

  if (typeof input !== 'string') {
    if (!input) {
      return { isValid: true, sanitizedValue: '', errors: [] };
    }
    input = String(input);
  }

  let sanitized = input.trim();

  // Kontrola dĺžky
  if (sanitized.length > maxLength) {
    sanitized = sanitized.substring(0, maxLength);
    errors.push(`HTML obsah bol skrátený na ${maxLength} znakov`);
  }

  // Odstránenie skriptov a nebezpečných tagov
  if (stripScripts) {
    sanitized = sanitized
      .replace(/<script[^>]*>.*?<\/script>/gis, '')
      .replace(/<iframe[^>]*>.*?<\/iframe>/gis, '')
      .replace(/<object[^>]*>.*?<\/object>/gis, '')
      .replace(/<embed[^>]*>/gi, '')
      .replace(/javascript:/gi, '')
      .replace(/on\w+\s*=/gi, '');
  }

  // Povolenie len špecifických tagov
  if (allowedTags.length > 0) {
    const allowedTagsRegex = new RegExp(
      `<(?!\/?(?:${allowedTags.join('|')})(?:\s|>))[^>]*>`,
      'gi'
    );
    sanitized = sanitized.replace(allowedTagsRegex, '');
  }

  // Kontrola odkazov
  if (!allowLinks) {
    sanitized = sanitized.replace(/<a[^>]*>.*?<\/a>/gis, '');
  }

  return {
    isValid: errors.length === 0,
    sanitizedValue: sanitized,
    errors
  };
}

/**
 * Validácia nahrávaných súborov
 */
export function validateFileUpload(
  file: File | null,
  options: FileValidationOptions = {}
): ValidationResult {
  const {
    allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/jpg', 'image/webp', 'image/heic', 'image/heif'],
    maxSize = 5 * 1024 * 1024, // 5MB
    minSize = 1024, // 1KB
    maxDimensions = { width: 2048, height: 2048 },
    allowedExtensions = ['jpg', 'jpeg', 'png', 'gif', 'webp', 'heic', 'heif'],
    relaxedFilenameValidation = false
  } = options;

  const errors: string[] = [];

  if (!file) {
    return { isValid: false, errors: ['Súbor je povinný'] };
  }

  // Kontrola prípony súboru najprv (pre HEIC detekciu)
  const fileExtension = file.name.split('.').pop()?.toLowerCase();
  const hasValidExtension = fileExtension && allowedExtensions.includes(fileExtension);

  // Kontrola typu súboru s fallback na príponu pre HEIC súbory
  const hasValidMimeType = allowedTypes.includes(file.type);

  // HEIC súbory môžu mať nesprávny MIME type v niektorých prehliadačoch
  const isHeicFile = fileExtension === 'heic' || fileExtension === 'heif' ||
                     file.type === 'image/heic' || file.type === 'image/heif';

  if (!hasValidMimeType && !hasValidExtension && !isHeicFile) {
    errors.push(`Nepovolený typ súboru. Povolené typy: ${allowedTypes.join(', ')} alebo prípony: ${allowedExtensions.join(', ')}`);
  }

  // Kontrola veľkosti súboru
  if (file.size > maxSize) {
    errors.push(`Súbor je príliš veľký. Maximálna veľkosť: ${Math.round(maxSize / 1024 / 1024)}MB`);
  }

  if (file.size < minSize) {
    errors.push(`Súbor je príliš malý. Minimálna veľkosť: ${Math.round(minSize / 1024)}KB`);
  }

  // Kontrola prípony súboru (už vykonaná vyššie, ale ponecháme pre istotu)
  if (fileExtension && !allowedExtensions.includes(fileExtension) && !isHeicFile) {
    errors.push(`Nepovolená prípona súboru. Povolené prípony: ${allowedExtensions.join(', ')}`);
  }

  // Kontrola názvu súboru (voliteľná pre relaxed validation)
  let sanitizedFileName = file.name;
  if (!relaxedFilenameValidation) {
    sanitizedFileName = file.name.replace(/[^a-zA-Z0-9.-]/g, '_');
    if (sanitizedFileName !== file.name) {
      errors.push('Názov súboru obsahuje nepovolené znaky');
    }
  }

  return {
    isValid: errors.length === 0,
    sanitizedValue: {
      file,
      sanitizedName: sanitizedFileName,
      type: file.type,
      size: file.size
    },
    errors
  };
}

/**
 * Sanitizácia geografických súradníc
 */
export function sanitizeCoordinates(
  latitude: any,
  longitude: any,
  options: CoordinateOptions = {}
): ValidationResult {
  const { validateRange = true, precision = 6 } = options;
  const errors: string[] = [];

  // Validácia latitude
  const latResult = sanitizeNumericInput(latitude, {
    min: validateRange ? -90 : Number.MIN_SAFE_INTEGER,
    max: validateRange ? 90 : Number.MAX_SAFE_INTEGER,
    decimals: precision
  });

  if (!latResult.isValid) {
    errors.push(`Neplatná zemepisná šírka: ${latResult.errors.join(', ')}`);
  }

  // Validácia longitude
  const lngResult = sanitizeNumericInput(longitude, {
    min: validateRange ? -180 : Number.MIN_SAFE_INTEGER,
    max: validateRange ? 180 : Number.MAX_SAFE_INTEGER,
    decimals: precision
  });

  if (!lngResult.isValid) {
    errors.push(`Neplatná zemepisná dĺžka: ${lngResult.errors.join(', ')}`);
  }

  return {
    isValid: errors.length === 0,
    sanitizedValue: {
      latitude: latResult.sanitizedValue,
      longitude: lngResult.sanitizedValue
    },
    errors
  };
}

/**
 * Sanitizácia telefónneho čísla
 */
export function sanitizePhoneNumber(input: any): ValidationResult {
  const errors: string[] = [];

  if (typeof input !== 'string') {
    if (!input) {
      return { isValid: true, sanitizedValue: '', errors: [] };
    }
    input = String(input);
  }

  let sanitized = input.trim();

  // Odstránenie všetkých znakov okrem čísel, +, -, (, ), medzier
  sanitized = sanitized.replace(/[^0-9+\-() ]/g, '');

  // Základná validácia formátu
  const phoneRegex = /^[\+]?[0-9\-() ]{7,20}$/;
  if (sanitized && !phoneRegex.test(sanitized)) {
    errors.push('Neplatný formát telefónneho čísla');
  }

  return {
    isValid: errors.length === 0,
    sanitizedValue: sanitized,
    errors
  };
}

/**
 * Sanitizácia URL adries
 */
export function sanitizeUrl(input: any): ValidationResult {
  const errors: string[] = [];

  if (typeof input !== 'string') {
    if (!input) {
      return { isValid: true, sanitizedValue: '', errors: [] };
    }
    input = String(input);
  }

  let sanitized = input.trim();

  // Kontrola protokolu
  if (sanitized && !sanitized.match(/^https?:\/\//)) {
    if (sanitized.startsWith('//')) {
      sanitized = 'https:' + sanitized;
    } else if (!sanitized.startsWith('http')) {
      sanitized = 'https://' + sanitized;
    }
  }

  // Validácia URL
  try {
    if (sanitized) {
      new URL(sanitized);
    }
  } catch {
    errors.push('Neplatná URL adresa');
  }

  // Odstránenie nebezpečných protokolov
  if (sanitized.match(/^(javascript|data|vbscript):/i)) {
    errors.push('Nebezpečný protokol v URL');
    sanitized = '';
  }

  return {
    isValid: errors.length === 0,
    sanitizedValue: sanitized,
    errors
  };
}

/**
 * Generovanie bezpečného hash-u pre súbory
 */
export function generateSecureFileHash(
  originalName: string,
  userId?: number,
  additionalData?: string
): string {
  const timestamp = Date.now();
  const random = Math.random().toString(36).substring(2);
  const dataToHash = `${timestamp}-${random}-${originalName}-${userId || ''}-${additionalData || ''}`;

  return createHash('sha256')
    .update(dataToHash)
    .digest('hex')
    .substring(0, 16);
}

/**
 * Komplexná sanitizácia objektu s viacerými poľami
 */
export function sanitizeObject<T extends Record<string, any>>(
  input: T,
  schema: Record<keyof T, (value: any) => ValidationResult>
): { isValid: boolean; sanitizedData: Partial<T>; errors: Record<keyof T, string[]> } {
  const sanitizedData: Partial<T> = {};
  const errors: Record<keyof T, string[]> = {} as Record<keyof T, string[]>;
  let isValid = true;

  for (const [key, sanitizer] of Object.entries(schema)) {
    const result = sanitizer(input[key]);

    if (result.isValid) {
      sanitizedData[key as keyof T] = result.sanitizedValue;
    } else {
      isValid = false;
      errors[key as keyof T] = result.errors;
    }
  }

  return { isValid, sanitizedData, errors };
}

/**
 * Sanitizácia pre API parametre z URL
 */
export function sanitizeApiParams(params: Record<string, string | string[]>): Record<string, any> {
  const sanitized: Record<string, any> = {};

  for (const [key, value] of Object.entries(params)) {
    if (Array.isArray(value)) {
      sanitized[key] = value.map(v => sanitizeText(v, { maxLength: 100, removeHtml: true }).sanitizedValue);
    } else {
      sanitized[key] = sanitizeText(value, { maxLength: 100, removeHtml: true }).sanitizedValue;
    }
  }

  return sanitized;
}
