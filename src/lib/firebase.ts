// Firebase konfigurácia
import { initializeApp } from 'firebase/app';
import { getAuth, GoogleAuthProvider, createUserWithEmailAndPassword, signInWithEmailAndPassword, User } from 'firebase/auth';

// Konfiguračn<PERSON> ú<PERSON>je pre Firebase
// POZNÁMKA: Tieto ú<PERSON> by mali byť nahraden<PERSON> skuto<PERSON> ú<PERSON> z Firebase konzoly
const firebaseConfig = {
  apiKey: process.env.NEXT_PUBLIC_FIREBASE_API_KEY || "YOUR_API_KEY",
  authDomain: process.env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN || "YOUR_AUTH_DOMAIN",
  projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID || "YOUR_PROJECT_ID",
  storageBucket: process.env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET || "YOUR_STORAGE_BUCKET",
  messagingSenderId: process.env.NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID || "YOUR_MESSAGING_SENDER_ID",
  appId: process.env.NEXT_PUBLIC_FIREBASE_APP_ID || "YOUR_APP_ID"
};

// Inicializácia Firebase
const app = initializeApp(firebaseConfig);

// Inicializácia autentifikácie
const auth = getAuth(app);

// Poskytovateľ autentifikácie pre Google
const googleProvider = new GoogleAuthProvider();

// Funkcie pre email/password autentifikáciu
const registerWithEmailAndPassword = async (email: string, password: string) => {
  try {
    const userCredential = await createUserWithEmailAndPassword(auth, email, password);
    return userCredential.user;
  } catch (error) {
    throw error;
  }
};

const loginWithEmailAndPassword = async (email: string, password: string) => {
  try {
    const userCredential = await signInWithEmailAndPassword(auth, email, password);
    return userCredential.user;
  } catch (error) {
    throw error;
  }
};

// Funkcia pre získanie tokenu s dlhšou platnosťou (7 dní)
const getLongLivedToken = async (user: User) => {
  try {
    // Nastavenie platnosti tokenu na 7 dní (604800 sekúnd)
    // Poznámka: Firebase má maximálnu platnosť tokenu 3600 sekúnd (1 hodina),
    // ale môžeme nastaviť forceRefresh=true, aby sa vygeneroval nový token
    const token = await user.getIdToken(true);
    return token;
  } catch (error) {
    console.error('Chyba pri získavaní tokenu s dlhšou platnosťou:', error);
    throw error;
  }
};

export {
  auth,
  googleProvider,
  registerWithEmailAndPassword,
  loginWithEmailAndPassword,
  getLongLivedToken
};
