/**
 * Utility functions for geospatial calculations
 */

/**
 * Earth radius in kilometers
 */
const EARTH_RADIUS_KM = 6371;

/**
 * Converts degrees to radians
 * @param degrees Angle in degrees
 * @returns Angle in radians
 */
function degreesToRadians(degrees: number): number {
  return degrees * (Math.PI / 180);
}

/**
 * Calculates the distance between two points on Earth using the Haversine formula
 * @param lat1 Latitude of the first point in degrees
 * @param lon1 Longitude of the first point in degrees
 * @param lat2 Latitude of the second point in degrees
 * @param lon2 Longitude of the second point in degrees
 * @returns Distance in kilometers
 */
export function calculateDistance(
  lat1: number,
  lon1: number,
  lat2: number,
  lon2: number
): number {
  // Convert latitude and longitude from degrees to radians
  const radLat1 = degreesToRadians(lat1);
  const radLon1 = degreesToRadians(lon1);
  const radLat2 = degreesToRadians(lat2);
  const radLon2 = degreesToRadians(lon2);

  // Calculate differences
  const dLat = radLat2 - radLat1;
  const dLon = radLon2 - radLon1;

  // Haversine formula
  const a =
    Math.sin(dLat / 2) * Math.sin(dLat / 2) +
    Math.cos(radLat1) * Math.cos(radLat2) *
    Math.sin(dLon / 2) * Math.sin(dLon / 2);
  
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
  const distance = EARTH_RADIUS_KM * c;

  return distance;
}

/**
 * Checks if a point is within a specified radius of another point
 * @param centerLat Latitude of the center point in degrees
 * @param centerLon Longitude of the center point in degrees
 * @param pointLat Latitude of the point to check in degrees
 * @param pointLon Longitude of the point to check in degrees
 * @param radiusKm Radius in kilometers
 * @returns True if the point is within the radius, false otherwise
 */
export function isPointWithinRadius(
  centerLat: number,
  centerLon: number,
  pointLat: number,
  pointLon: number,
  radiusKm: number
): boolean {
  // If any of the coordinates are null or undefined, return false
  if (
    centerLat == null || 
    centerLon == null || 
    pointLat == null || 
    pointLon == null
  ) {
    return false;
  }
  
  // If radius is 0, check if the city names match (handled elsewhere)
  if (radiusKm === 0) {
    return true;
  }
  
  const distance = calculateDistance(centerLat, centerLon, pointLat, pointLon);
  return distance <= radiusKm;
}
