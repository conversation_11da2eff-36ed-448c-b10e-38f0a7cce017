/**
 * Utility functions for price formatting and display
 */

// Price type constants
export const PRICE_TYPES = {
  PER_DAY: 'PER_DAY',
  PER_RENTAL: 'PER_RENTAL'
} as const;

export type PriceType = typeof PRICE_TYPES[keyof typeof PRICE_TYPES];

/**
 * Formats toy price for display
 * @param price - The price value (number)
 * @param suffix - Optional suffix to add (e.g., "/deň", "€")
 * @returns Formatted price string or "Dohodou" for 0 EUR
 */
export function formatToyPrice(price: number, suffix: string = ''): string {
  if (price === 0) {
    return 'Dohodou';
  }

  return `${price}${suffix}`;
}

/**
 * Formats toy price with Euro symbol for display
 * @param price - The price value (number)
 * @param priceType - The pricing type (PER_DAY or PER_RENTAL)
 * @returns Formatted price string with € symbol or "Dohodou" for 0 EUR
 */
export function formatToyPriceWithEuro(price: number, priceType: string = PRICE_TYPES.PER_DAY): string {
  if (price === 0) {
    return 'Dohodou';
  }

  const suffix = getPriceTypeSuffix(priceType);
  return `${price} € ${suffix}`;
}

/**
 * Formats toy price for display with appropriate suffix based on price type
 * @param price - The price value (number)
 * @param priceType - The pricing type (PER_DAY or PER_RENTAL)
 * @returns Formatted price string with appropriate suffix or "Dohodou" for 0 EUR
 */
export function formatToyPricePerDay(price: number, priceType: string = PRICE_TYPES.PER_DAY): string {
  if (price === 0) {
    return 'Dohodou';
  }

  if (priceType === PRICE_TYPES.PER_RENTAL) {
    return `${price}€ / výpožičku`;
  }

  return `${price}€ / deň`;
}

/**
 * Formats toy price for admin display
 * @param price - The price value (number)
 * @param priceType - The pricing type (PER_DAY or PER_RENTAL)
 * @returns Formatted price string with appropriate suffix or "Dohodou" for 0 EUR
 */
export function formatToyPriceAdmin(price: number, priceType: string = PRICE_TYPES.PER_DAY): string {
  if (price === 0) {
    return 'Dohodou';
  }

  if (priceType === PRICE_TYPES.PER_RENTAL) {
    return `${price} € / výpožičku`;
  }

  return `${price} € / deň`;
}

/**
 * Gets the price type label for display
 * @param priceType - The pricing type (PER_DAY or PER_RENTAL)
 * @returns Human-readable label for the price type
 */
export function getPriceTypeLabel(priceType: string): string {
  switch (priceType) {
    case PRICE_TYPES.PER_RENTAL:
      return 'Cena za výpožičku';
    case PRICE_TYPES.PER_DAY:
    default:
      return 'Cena za deň';
  }
}

/**
 * Gets the price type suffix for display
 * @param priceType - The pricing type (PER_DAY or PER_RENTAL)
 * @returns Suffix string for the price type
 */
export function getPriceTypeSuffix(priceType: string): string {
  switch (priceType) {
    case PRICE_TYPES.PER_RENTAL:
      return '/ výpožičku';
    case PRICE_TYPES.PER_DAY:
    default:
      return '/ deň';
  }
}
