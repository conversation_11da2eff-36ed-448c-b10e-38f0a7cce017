/**
 * Image processing utilities for handling HEIC/HEIF conversion, resizing, and WebP conversion
 * This module provides comprehensive image processing capabilities for the photo upload system
 */

import sharp from 'sharp';
// Dynamic import for heic-convert to avoid webpack dependency extraction issues
// import heicConvert from 'heic-convert';

// Configuration constants
export const IMAGE_CONFIG = {
  MAX_DIMENSION: 1024, // Maximum width or height in pixels
  WEBP_QUALITY: 85, // WebP quality (0-100)
  JPEG_QUALITY: 90, // Fallback JPEG quality for unsupported formats
  SUPPORTED_INPUT_FORMATS: [
    'image/jpeg',
    'image/jpg',
    'image/png',
    'image/gif',
    'image/webp',
    'image/heic',
    'image/heif',
    'application/octet-stream' // HEIC files often have this MIME type
  ],
  SUPPORTED_EXTENSIONS: [
    'jpg',
    'jpeg',
    'png',
    'gif',
    'webp',
    'heic',
    'heif'
  ]
};

export interface ProcessedImage {
  buffer: Buffer;
  format: string;
  width: number;
  height: number;
  size: number;
  originalFormat: string;
}

export interface ImageProcessingOptions {
  maxDimension?: number;
  quality?: number;
  format?: 'webp' | 'jpeg' | 'png';
  maintainAspectRatio?: boolean;
}

/**
 * Detects if a file is in HEIC/HEIF format
 */
export function isHeicFormat(file: File | Buffer, mimeType?: string, fileName?: string): boolean {
  // Check File object
  if (file instanceof File) {
    const isHeicMimeType = file.type === 'image/heic' ||
                          file.type === 'image/heif' ||
                          file.type === 'application/octet-stream'; // HEIC files often have this MIME type

    const isHeicExtension = file.name.toLowerCase().endsWith('.heic') ||
                           file.name.toLowerCase().endsWith('.heif');

    // FIXED: Return true if extension is HEIC (regardless of MIME type) OR if both MIME and extension suggest HEIC
    // This fixes the issue where HEIC files with wrong MIME types were rejected
    return isHeicExtension || (isHeicMimeType && file.name.toLowerCase().includes('heic'));
  }

  // Check MIME type parameter
  if (mimeType) {
    const isHeicMimeType = mimeType === 'image/heic' ||
                          mimeType === 'image/heif' ||
                          mimeType === 'application/octet-stream';

    // If we have a filename, check extension too
    if (fileName) {
      const isHeicExtension = fileName.toLowerCase().endsWith('.heic') ||
                             fileName.toLowerCase().endsWith('.heif');
      return isHeicExtension || (isHeicMimeType && isHeicExtension);
    }

    return mimeType === 'image/heic' || mimeType === 'image/heif';
  }

  // Check buffer magic bytes for HEIC/HEIF (more comprehensive)
  if (file instanceof Buffer && file.length >= 12) {
    try {
      const header = file.toString('ascii', 4, 12);
      const isHeicHeader = header === 'ftypheic' ||
                          header === 'ftypmif1' ||
                          header === 'ftypmsf1' ||
                          header === 'ftypheix' ||
                          header === 'ftyphevc' ||
                          header === 'ftyphevx';

      if (isHeicHeader) {
        console.log(`Detected HEIC format by magic bytes: ${header}`);
        return true;
      }

      // Additional check for different HEIC variants
      if (file.length >= 20) {
        const extendedHeader = file.toString('ascii', 0, 20);
        if (extendedHeader.includes('ftyp') && (extendedHeader.includes('heic') || extendedHeader.includes('mif1'))) {
          console.log(`Detected HEIC format by extended header analysis`);
          return true;
        }
      }
    } catch (error) {
      console.warn('Error reading buffer header for HEIC detection:', error);
    }
  }

  return false;
}

/**
 * Converts HEIC/HEIF buffer to JPEG buffer
 */
export async function convertHeicToJpeg(buffer: Buffer, fileName?: string): Promise<Buffer> {
  try {
    console.log(`Starting HEIC conversion for file: ${fileName || 'unknown'}, buffer size: ${buffer.length} bytes`);

    // Validate buffer
    if (!buffer || buffer.length === 0) {
      throw new Error('Empty or invalid buffer provided for HEIC conversion');
    }

    // Check if buffer is actually HEIC format
    if (!isHeicFormat(buffer)) {
      console.warn('Buffer does not appear to be HEIC format, attempting conversion anyway');
    }

    // Dynamic import to avoid webpack dependency extraction issues
    const heicConvert = (await import('heic-convert')).default;

    const outputBuffer = await heicConvert({
      buffer,
      format: 'JPEG',
      quality: IMAGE_CONFIG.JPEG_QUALITY / 100
    });

    const convertedBuffer = Buffer.from(outputBuffer);
    console.log(`HEIC conversion successful: ${buffer.length} bytes -> ${convertedBuffer.length} bytes`);

    // Validate the converted buffer
    if (!convertedBuffer || convertedBuffer.length === 0) {
      throw new Error('HEIC conversion produced empty buffer');
    }

    return convertedBuffer;
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    console.error('Error converting HEIC to JPEG:', {
      error: errorMessage,
      fileName,
      bufferSize: buffer?.length,
      bufferStart: buffer?.length > 0 ? buffer.toString('hex', 0, Math.min(20, buffer.length)) : 'empty'
    });

    // Provide more specific error messages
    if (errorMessage.includes('Invalid input')) {
      throw new Error('Súbor nie je platný HEIC obrázok alebo je poškodený');
    } else if (errorMessage.includes('Unsupported')) {
      throw new Error('Tento typ HEIC súboru nie je podporovaný');
    } else {
      throw new Error(`Nepodarilo sa konvertovať HEIC obrázok: ${errorMessage}`);
    }
  }
}

/**
 * Processes an image: converts format, resizes, and optimizes
 */
export async function processImage(
  inputBuffer: Buffer,
  originalMimeType: string,
  options: ImageProcessingOptions = {},
  fileName?: string
): Promise<ProcessedImage> {
  const {
    maxDimension = IMAGE_CONFIG.MAX_DIMENSION,
    quality = IMAGE_CONFIG.WEBP_QUALITY,
    format = 'webp',
    maintainAspectRatio = true
  } = options;

  console.log(`Starting image processing for file: ${fileName || 'unknown'}, MIME: ${originalMimeType}, size: ${inputBuffer.length} bytes`);

  try {
    let processBuffer = inputBuffer;
    let currentFormat = originalMimeType;
    let wasHeicConverted = false;

    // Step 1: Convert HEIC/HEIF to JPEG if needed
    const isHeic = isHeicFormat(inputBuffer, originalMimeType, fileName);
    console.log(`HEIC detection result: ${isHeic} for file: ${fileName}, MIME: ${originalMimeType}, buffer size: ${inputBuffer.length}`);

    if (isHeic) {
      console.log('Converting HEIC/HEIF to JPEG...');
      try {
        processBuffer = await convertHeicToJpeg(inputBuffer, fileName);
        currentFormat = 'image/jpeg';
        wasHeicConverted = true;
        console.log(`HEIC conversion completed successfully, new buffer size: ${processBuffer.length} bytes`);
      } catch (heicError) {
        console.error('HEIC conversion failed:', {
          error: heicError,
          fileName,
          originalMimeType,
          bufferSize: inputBuffer.length,
          bufferStart: inputBuffer.length > 0 ? inputBuffer.toString('hex', 0, Math.min(20, inputBuffer.length)) : 'empty'
        });
        const heicErrorMessage = heicError instanceof Error ? heicError.message : 'Unknown HEIC conversion error';

        // Try to provide more helpful error messages
        if (heicErrorMessage.includes('Invalid input')) {
          throw new Error(`HEIC súbor "${fileName}" nie je platný alebo je poškodený. Skúste otvoriť súbor v aplikácii Fotografie a uložiť ho znovu.`);
        } else if (heicErrorMessage.includes('Unsupported')) {
          throw new Error(`Tento typ HEIC súboru "${fileName}" nie je podporovaný. Skúste konvertovať súbor do JPEG formátu.`);
        } else {
          throw new Error(`Nepodarilo sa konvertovať HEIC súbor "${fileName}": ${heicErrorMessage}`);
        }
      }
    }

    // Step 2: Process with Sharp
    console.log(`Creating Sharp instance with buffer size: ${processBuffer.length} bytes`);
    let sharpInstance;

    try {
      sharpInstance = sharp(processBuffer);
    } catch (sharpError) {
      console.error('Failed to create Sharp instance:', sharpError);
      const sharpErrorMessage = sharpError instanceof Error ? sharpError.message : 'Unknown Sharp error';
      throw new Error(`Nepodarilo sa inicializovať spracovanie obrázku: ${sharpErrorMessage}`);
    }

    // Get original metadata
    let metadata;
    try {
      metadata = await sharpInstance.metadata();
    } catch (metadataError) {
      console.error('Failed to read image metadata:', metadataError);
      const metadataErrorMessage = metadataError instanceof Error ? metadataError.message : 'Unknown metadata error';
      throw new Error(`Nepodarilo sa prečítať metadáta obrázku: ${metadataErrorMessage}`);
    }

    const originalWidth = metadata.width || 0;
    const originalHeight = metadata.height || 0;

    console.log(`Image metadata: ${originalWidth}x${originalHeight}, format: ${currentFormat}, HEIC converted: ${wasHeicConverted}`);

    // Step 3: Resize if needed
    if (originalWidth > maxDimension || originalHeight > maxDimension) {
      if (maintainAspectRatio) {
        // Resize maintaining aspect ratio
        sharpInstance = sharpInstance.resize(maxDimension, maxDimension, {
          fit: 'inside',
          withoutEnlargement: true
        });
      } else {
        // Resize to exact dimensions
        sharpInstance = sharpInstance.resize(maxDimension, maxDimension);
      }
      console.log(`Resizing to max dimension: ${maxDimension}px`);
    }

    // Step 4: Convert to target format and optimize
    let outputBuffer: Buffer;
    let outputFormat: string;

    console.log(`Converting to ${format} format with quality ${quality}`);

    try {
      switch (format) {
        case 'webp':
          outputBuffer = await sharpInstance
            .webp({
              quality,
              effort: 6, // Higher effort for better compression
              smartSubsample: true
            })
            .toBuffer();
          outputFormat = 'image/webp';
          break;

        case 'jpeg':
          outputBuffer = await sharpInstance
            .jpeg({
              quality,
              progressive: true,
              mozjpeg: true
            })
            .toBuffer();
          outputFormat = 'image/jpeg';
          break;

        case 'png':
          outputBuffer = await sharpInstance
            .png({
              quality,
              compressionLevel: 9,
              progressive: true
            })
            .toBuffer();
          outputFormat = 'image/png';
          break;

        default:
          throw new Error(`Nepodporovaný výstupný formát: ${format}`);
      }
    } catch (conversionError) {
      console.error('Sharp format conversion failed:', conversionError);
      const conversionErrorMessage = conversionError instanceof Error ? conversionError.message : 'Unknown conversion error';
      throw new Error(`Nepodarilo sa konvertovať obrázok do formátu ${format}: ${conversionErrorMessage}`);
    }

    // Validate output buffer
    if (!outputBuffer || outputBuffer.length === 0) {
      throw new Error('Spracovanie obrázku vytvorilo prázdny výstup');
    }

    // Get final metadata
    let finalMetadata;
    try {
      finalMetadata = await sharp(outputBuffer).metadata();
    } catch (metadataError) {
      console.warn('Could not read final metadata, using fallback values:', metadataError);
      finalMetadata = { width: originalWidth, height: originalHeight };
    }

    const result: ProcessedImage = {
      buffer: outputBuffer,
      format: outputFormat,
      width: finalMetadata.width || originalWidth,
      height: finalMetadata.height || originalHeight,
      size: outputBuffer.length,
      originalFormat: originalMimeType
    };

    console.log(`Image processing completed successfully: ${result.width}x${result.height}, format: ${result.format}, size: ${Math.round(result.size / 1024)}KB`);

    return result;

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    const errorStack = error instanceof Error ? error.stack : undefined;

    console.error('Error processing image:', {
      error: errorMessage,
      fileName,
      originalMimeType,
      bufferSize: inputBuffer?.length,
      stack: errorStack
    });

    // Provide more specific error messages based on error type
    if (errorMessage.includes('HEIC konverzia zlyhala')) {
      throw error; // Re-throw HEIC conversion errors as-is
    } else if (errorMessage.includes('bad seek')) {
      throw new Error('Obrázok je poškodený alebo má nepodporovaný formát. Skúste iný súbor.');
    } else if (errorMessage.includes('Input buffer contains unsupported image format')) {
      throw new Error('Nepodporovaný formát obrázku. Podporované sú: JPEG, PNG, WebP, GIF a HEIC.');
    } else if (errorMessage.includes('Input file is missing')) {
      throw new Error('Súbor sa nepodarilo načítať. Skúste to znovu.');
    } else {
      throw new Error(`Nepodarilo sa spracovať obrázok: ${errorMessage}`);
    }
  }
}

/**
 * Validates if the file format is supported for processing
 */
export function isSupportedImageFormat(file: File): boolean {
  // Use the improved HEIC detection function
  if (isHeicFormat(file, file.type, file.name)) {
    return true;
  }

  // Check MIME type
  if (IMAGE_CONFIG.SUPPORTED_INPUT_FORMATS.includes(file.type)) {
    return true;
  }

  // Check file extension as fallback
  const extension = file.name.split('.').pop()?.toLowerCase();
  if (extension && IMAGE_CONFIG.SUPPORTED_EXTENSIONS.includes(extension)) {
    return true;
  }

  return false;
}

/**
 * Gets the appropriate file extension for the processed format
 */
export function getFileExtension(format: string): string {
  switch (format) {
    case 'image/webp':
      return 'webp';
    case 'image/jpeg':
    case 'image/jpg':
      return 'jpg';
    case 'image/png':
      return 'png';
    case 'image/gif':
      return 'gif';
    default:
      return 'webp'; // Default to WebP
  }
}

/**
 * Calculates compression ratio
 */
export function getCompressionRatio(originalSize: number, processedSize: number): number {
  if (originalSize === 0) return 0;
  return Math.round(((originalSize - processedSize) / originalSize) * 100);
}
