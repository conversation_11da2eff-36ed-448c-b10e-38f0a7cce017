import { PrismaClient } from '../app/generated/prisma';

// PrismaClient je pripojený k databáze pri inicializácii
const globalForPrisma = global as unknown as { prisma: PrismaClient };

// Vytvorenie a export inštancie PrismaClient
export const prisma =
  globalForPrisma.prisma ||
  new PrismaClient({
    log: process.env.NODE_ENV === 'development' ? ['query', 'error', 'warn'] : ['error'],
  });

// Zabránenie vytváraniu viacerých inštancií v development móde
if (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma;

// Funkcie pre prácu s hračkami
export async function getAllToys(userId?: number) {
  const whereCondition: any = {
    user: {
      status: {
        not: 'BLOCKED'
      }
    },
    status: {
      not: 'DRAFT'
    }
  };

  // Ak je zadané userId, vyl<PERSON><PERSON><PERSON> h<PERSON>, k<PERSON><PERSON> skryl
  if (userId) {
    whereCondition.NOT = {
      hiddenBy: {
        some: {
          userId: userId
        }
      }
    };
  }

  return prisma.toy.findMany({
    where: whereCondition,
    include: {
      images: true,
      user: true,
    },
  });
}

// Funkcia pre získanie všetkých hračiek pre administrátora (bez filtrovania)
export async function getAllToysForAdmin() {
  return prisma.toy.findMany({
    include: {
      images: true,
      user: true,
    },
  });
}

export async function getToyById(id: number) {
  return prisma.toy.findUnique({
    where: { id },
    include: {
      images: true,
      user: {
        select: {
          id: true,
          name: true,
          email: true,
          phone: true,
          address: true,
          city: true,
          postalCode: true,
          latitude: true,
          longitude: true,
          status: true,
        },
      },
      reservations: {
        where: {
          status: { in: ['PENDING', 'CONFIRMED', 'ACTIVE'] }
        },
        orderBy: {
          createdAt: 'desc'
        },
        take: 1,
      },
    },
  });
}

export async function getToysByType(type: string, userId?: number) {
  const whereCondition: any = {
    type: type,
    user: {
      status: {
        not: 'BLOCKED'
      }
    },
    status: {
      not: 'DRAFT'
    }
  };

  // Ak je zadané userId, vylúčime hračky, ktoré používateľ skryl
  if (userId) {
    whereCondition.NOT = {
      hiddenBy: {
        some: {
          userId: userId
        }
      }
    };
  }

  return prisma.toy.findMany({
    where: whereCondition,
    include: {
      images: true,
      user: true,
    },
  });
}

export async function getToysByLocation(city: string, userId?: number) {
  const whereCondition: any = {
    user: {
      city: city,
      status: {
        not: 'BLOCKED'
      }
    },
    status: {
      not: 'DRAFT'
    }
  };

  // Ak je zadané userId, vylúčime hračky, ktoré používateľ skryl
  if (userId) {
    whereCondition.NOT = {
      hiddenBy: {
        some: {
          userId: userId
        }
      }
    };
  }

  return prisma.toy.findMany({
    where: whereCondition,
    include: {
      images: true,
      user: true,
    },
  });
}

/**
 * Get toys by city and radius
 * @param city The city to search in
 * @param radius The radius in kilometers (0 means exact city match)
 * @param latitude The latitude of the center point (required if radius > 0)
 * @param longitude The longitude of the center point (required if radius > 0)
 * @returns List of toys within the specified radius
 */
export async function getToysByCityAndRadius(
  city: string,
  radius: number = 0,
  latitude?: number,
  longitude?: number,
  userId?: number
) {
  // If radius is 0 or coordinates are not provided, just filter by city
  if (radius === 0 || !latitude || !longitude) {
    return getToysByLocation(city, userId);
  }

  // If radius > 0 and coordinates are provided, get all toys and filter by distance in the application
  // This is not the most efficient approach, but it's simpler than implementing a geospatial query
  // For a production app with many toys, we would use a database with geospatial capabilities
  const whereCondition: any = {
    user: {
      status: {
        not: 'BLOCKED'
      }
    },
    status: {
      not: 'DRAFT'
    }
  };

  // Ak je zadané userId, vylúčime hračky, ktoré používateľ skryl
  if (userId) {
    whereCondition.NOT = {
      hiddenBy: {
        some: {
          userId: userId
        }
      }
    };
  }

  return prisma.toy.findMany({
    where: whereCondition,
    include: {
      images: true,
      user: true,
    },
  });
}

export async function searchToys(searchTerm: string, userId?: number) {
  const whereCondition: any = {
    OR: [
      { name: { contains: searchTerm } },
      { description: { contains: searchTerm } },
    ],
    user: {
      status: {
        not: 'BLOCKED'
      }
    },
    status: {
      not: 'DRAFT'
    }
  };

  // Ak je zadané userId, vylúčime hračky, ktoré používateľ skryl
  if (userId) {
    whereCondition.NOT = {
      hiddenBy: {
        some: {
          userId: userId
        }
      }
    };
  }

  return prisma.toy.findMany({
    where: whereCondition,
    include: {
      images: true,
      user: true,
    },
  });
}

// Funkcie pre prácu s lokalitami - odstránené, keďže lokality sú teraz súčasťou modelu User

// Funkcie pre prácu s typmi hračiek
export async function getAllToyTypes() {
  // Používame type-safe Prisma metódu namiesto raw SQL
  return prisma.toyType.findMany({
    select: {
      name: true,
      label: true
    }
  });
}

// Funkcie pre prácu so statusmi hračiek
export async function getAllToyStatuses() {
  // Používame type-safe Prisma metódu namiesto raw SQL
  return prisma.toyStatus.findMany({
    select: {
      name: true,
      label: true
    }
  });
}

// Funkcia pre získanie hračiek používateľa
export async function getUserToys(userId: number, loggedInUserId?: number) {
  const whereCondition: any = {
    userId: userId,
  };

  // Ak je zadané loggedInUserId a nie je to vlastník hračiek, vylúčime hračky v stave DRAFT
  if (loggedInUserId !== userId) {
    whereCondition.status = {
      not: 'DRAFT'
    };

    // Ak je zadané loggedInUserId, vylúčime hračky, ktoré používateľ skryl
    if (loggedInUserId) {
      whereCondition.NOT = {
        hiddenBy: {
          some: {
            userId: loggedInUserId
          }
        }
      };
    }
  }

  return prisma.toy.findMany({
    where: whereCondition,
    include: {
      images: true,
      user: {
        select: {
          id: true,
          name: true,
          email: true,
          status: true,
          city: true,
          postalCode: true,
          address: true,
          latitude: true,
          longitude: true,
        },
      },
    },
  });
}

// Funkcia pre vytvorenie novej hračky
export async function createToy(data: {
  name: string;
  description: string;
  type: string;
  price: number;
  deposit: number;
  priceType?: string;
  status?: string;
  maxReservationDays?: number;
  userId: number;
  locationId?: number; // Voliteľné, pre spätnú kompatibilitu
}) {
  // Pripravíme dáta pre vytvorenie
  const createData: any = {
    name: data.name,
    description: data.description,
    type: data.type,
    price: data.price,
    deposit: data.deposit,
    priceType: data.priceType || 'PER_DAY',
    status: data.status || 'AVAILABLE',
    userId: data.userId,
  };

  // Pridáme locationId, ak je poskytnuté (pre spätnú kompatibilitu)
  if (data.locationId) {
    createData.locationId = data.locationId;
  }

  // Pridáme maxReservationDays, ak je podporovaný v schéme
  try {
    if (data.maxReservationDays !== undefined) {
      createData.maxReservationDays = data.maxReservationDays;
    }
  } catch (e) {
    console.log('maxReservationDays nie je podporovaný v aktuálnej schéme databázy');
  }

  return prisma.toy.create({
    data: createData,
    include: {
      user: true,
    },
  });
}

// Funkcia pre vytvorenie obrázku hračky
export async function createToyImage(toyId: number, url: string) {
  return prisma.toyImage.create({
    data: {
      url,
      toyId,
    },
  });
}