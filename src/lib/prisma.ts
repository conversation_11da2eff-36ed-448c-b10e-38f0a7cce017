import { PrismaClient } from '@prisma/client';

// PrismaClient je pripojený k databáze pri inicializácii
const globalForPrisma = global as unknown as { prisma: PrismaClient };

// Vytvorenie a export inštancie PrismaClient
export const prisma =
  globalForPrisma.prisma ||
  new PrismaClient({
    log: process.env.NODE_ENV === 'development' ? ['query', 'error', 'warn'] : ['error'],
  });

// Zabránenie vytváraniu viacerých inštancií v development móde
if (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma;
