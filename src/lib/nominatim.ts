/**
 * Klient pre Nominatim API (OpenStreetMap)
 * <PERSON>lúži na vyhľadávanie adries a získavanie geografických súradníc
 */

// Základná URL pre Nominatim API
const NOMINATIM_API_URL = 'https://nominatim.openstreetmap.org';

// Typ pre výsledok vyhľadávania adresy
export interface NominatimSearchResult {
  place_id: number;
  licence: string;
  osm_type: string;
  osm_id: number;
  boundingbox: string[];
  lat: string;
  lon: string;
  display_name: string;
  class: string;
  type: string;
  importance: number;
  address: {
    house_number?: string;
    road?: string;
    neighbourhood?: string;
    suburb?: string;
    city?: string;
    town?: string;
    village?: string;
    county?: string;
    state?: string;
    postcode?: string;
    country?: string;
    country_code?: string;
  };
}

// Typ pre výsledok reverzného geocodingu
export interface NominatimReverseResult {
  place_id: number;
  licence: string;
  osm_type: string;
  osm_id: number;
  lat: string;
  lon: string;
  display_name: string;
  address: {
    house_number?: string;
    road?: string;
    neighbourhood?: string;
    suburb?: string;
    city?: string;
    town?: string;
    village?: string;
    county?: string;
    state?: string;
    postcode?: string;
    country?: string;
    country_code?: string;
  };
  boundingbox: string[];
}

/**
 * Vyhľadávanie adresy pomocou Nominatim API
 * @param query Hľadaný výraz (adresa)
 * @param limit Maximálny počet výsledkov
 * @param countryCode Kód krajiny pre obmedzenie výsledkov (napr. 'sk' pre Slovensko)
 * @returns Pole výsledkov vyhľadávania
 */
export async function searchAddress(
  query: string,
  limit: number = 5,
  countryCode?: string
): Promise<NominatimSearchResult[]> {
  try {
    // Zostavenie URL s parametrami
    const params = new URLSearchParams({
      q: query,
      format: 'jsonv2',
      addressdetails: '1',
      limit: limit.toString(),
    });

    // Pridanie kódu krajiny, ak je zadaný
    if (countryCode) {
      params.append('countrycodes', countryCode);
    }

    // Vykonanie požiadavky
    const response = await fetch(`${NOMINATIM_API_URL}/search?${params.toString()}`, {
      headers: {
        'Accept-Language': 'sk,cs,en', // Preferencia slovenčiny, potom češtiny, potom angličtiny
        'User-Agent': 'Swapka-App', // Identifikácia aplikácie
      },
    });

    if (!response.ok) {
      throw new Error(`Chyba pri vyhľadávaní adresy: ${response.statusText}`);
    }

    return await response.json();
  } catch (error) {
    console.error('Chyba pri vyhľadávaní adresy:', error);
    return [];
  }
}

/**
 * Získanie adresy zo súradníc (reverzné geocoding)
 * @param lat Zemepisná šírka
 * @param lon Zemepisná dĺžka
 * @returns Informácie o adrese na daných súradniciach
 */
export async function reverseGeocode(
  lat: number,
  lon: number
): Promise<NominatimReverseResult | null> {
  try {
    // Zostavenie URL s parametrami
    const params = new URLSearchParams({
      lat: lat.toString(),
      lon: lon.toString(),
      format: 'jsonv2',
      addressdetails: '1',
      zoom: '18', // Najvyššia úroveň detailu (budova)
    });

    // Vykonanie požiadavky
    const response = await fetch(`${NOMINATIM_API_URL}/reverse?${params.toString()}`, {
      headers: {
        'Accept-Language': 'sk,cs,en', // Preferencia slovenčiny, potom češtiny, potom angličtiny
        'User-Agent': 'Swapka-App', // Identifikácia aplikácie
      },
    });

    if (!response.ok) {
      throw new Error(`Chyba pri reverznom geocodingu: ${response.statusText}`);
    }

    return await response.json();
  } catch (error) {
    console.error('Chyba pri reverznom geocodingu:', error);
    return null;
  }
}

/**
 * Formátovanie adresy z výsledku Nominatim API
 * @param result Výsledok z Nominatim API
 * @returns Formátovaná adresa
 */
export function formatAddress(result: NominatimSearchResult | NominatimReverseResult): string {
  const address = result.address;
  const parts = [];

  // Pridanie čísla domu a ulice
  if (address.house_number && address.road) {
    parts.push(`${address.road} ${address.house_number}`);
  } else if (address.road) {
    parts.push(address.road);
  }

  // Pridanie obce/mesta
  const city = address.city || address.town || address.village || address.suburb;

  // Pridanie PSČ
  const postcode = address.postcode;

  // Pridanie mesta a PSČ
  if (city && postcode) {
    parts.push(`${city}, ${postcode}`);
  } else if (city) {
    parts.push(city);
  } else if (postcode) {
    parts.push(postcode);
  }

  // Pridanie krajiny
  if (address.country) {
    parts.push(address.country);
  }

  return parts.join(', ');
}
