/**
 * Simple in-memory cache for sitemap generation
 * Provides caching mechanism to avoid regenerating sitemap on every request
 */

interface CacheEntry {
  data: string;
  timestamp: number;
  expiresAt: number;
}

class SitemapCache {
  private cache: Map<string, CacheEntry> = new Map();
  private readonly defaultTTL: number;

  constructor() {
    // Cache TTL: 15 minutes in production, 5 minutes in development
    this.defaultTTL = process.env.NODE_ENV === 'production' ? 15 * 60 * 1000 : 5 * 60 * 1000;
  }

  /**
   * Get cached sitemap data
   * @param key Cache key
   * @returns Cached data or null if not found/expired
   */
  get(key: string): string | null {
    const entry = this.cache.get(key);
    
    if (!entry) {
      return null;
    }

    // Check if cache entry has expired
    if (Date.now() > entry.expiresAt) {
      this.cache.delete(key);
      return null;
    }

    return entry.data;
  }

  /**
   * Set cache data
   * @param key Cache key
   * @param data Data to cache
   * @param ttl Time to live in milliseconds (optional)
   */
  set(key: string, data: string, ttl?: number): void {
    const now = Date.now();
    const timeToLive = ttl || this.defaultTTL;
    
    const entry: CacheEntry = {
      data,
      timestamp: now,
      expiresAt: now + timeToLive
    };

    this.cache.set(key, entry);
  }

  /**
   * Clear specific cache entry
   * @param key Cache key to clear
   */
  clear(key: string): void {
    this.cache.delete(key);
  }

  /**
   * Clear all cache entries
   */
  clearAll(): void {
    this.cache.clear();
  }

  /**
   * Get cache statistics
   * @returns Object with cache statistics
   */
  getStats(): { size: number; entries: string[] } {
    return {
      size: this.cache.size,
      entries: Array.from(this.cache.keys())
    };
  }

  /**
   * Clean expired entries from cache
   */
  cleanExpired(): void {
    const now = Date.now();
    for (const [key, entry] of this.cache.entries()) {
      if (now > entry.expiresAt) {
        this.cache.delete(key);
      }
    }
  }
}

// Export singleton instance
export const sitemapCache = new SitemapCache();

// Clean expired entries every 30 minutes
if (typeof setInterval !== 'undefined') {
  setInterval(() => {
    sitemapCache.cleanExpired();
  }, 30 * 60 * 1000);
}
