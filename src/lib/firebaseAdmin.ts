// Firebase Admin SDK konfigurácia
import { initializeApp, cert, getApps, getApp } from 'firebase-admin/app';
import { getAuth } from 'firebase-admin/auth';

// Inicializácia Firebase Admin SDK
export function getFirebaseAdmin() {
  // <PERSON><PERSON><PERSON><PERSON>, či sme na serveri
  if (typeof window !== 'undefined') {
    throw new Error('Firebase Admin SDK môže byť inicializovaný len na serveri');
  }

  try {
    // Ak už existuje inicializovaná aplikácia, vrátime ju
    if (getApps().length > 0) {
      return getApp();
    }

    // Výpis pre debugovanie
    console.log('Používam service account credentials z .env');
    console.log('Project ID:', process.env.FIREBASE_PROJECT_ID);
    console.log('Client Email:', process.env.FIREBASE_CLIENT_EMAIL);
    console.log('Private Key je nastavený:', !!process.env.FIREBASE_PRIVATE_KEY);

    // Inicializácia Firebase Admin SDK
    const serviceAccount = {
      projectId: process.env.FIREBASE_PROJECT_ID,
      clientEmail: process.env.FIREBASE_CLIENT_EMAIL,
      // Uistíme sa, že privátny kľúč je správne formátovaný
      privateKey: process.env.FIREBASE_PRIVATE_KEY?.replace(/\\n/g, '\n'),
    };

    const app = initializeApp({
      credential: cert(serviceAccount),
      projectId: process.env.FIREBASE_PROJECT_ID,
    });

    console.log('Firebase Admin SDK bol úspešne inicializovaný');
    return app;
  } catch (error) {
    console.error('Chyba pri inicializácii Firebase Admin SDK:', error);
    throw error;
  }
}

// Typ pre dekódovaný token
export interface DecodedIdToken {
  aud: string;
  auth_time: number;
  email?: string;
  email_verified?: boolean;
  exp: number;
  firebase: {
    identities: {
      [key: string]: any;
    };
    sign_in_provider: string;
    sign_in_second_factor?: string;
    second_factor_identifier?: string;
    tenant?: string;
    [key: string]: any;
  };
  iat: number;
  iss: string;
  phone_number?: string;
  picture?: string;
  sub: string;
  uid: string;
  [key: string]: any;
}

// Funkcia pre overenie Firebase tokenu
export async function verifyFirebaseToken(token: string): Promise<DecodedIdToken> {
  try {
    const app = getFirebaseAdmin();
    // Nastavenie dlhšej platnosti tokenov (7 dní = 604800 sekúnd)
    // Firebase Admin SDK automaticky akceptuje tokeny s dlhšou platnosťou
    const auth = getAuth(app);
    const decodedToken = await auth.verifyIdToken(token);
    return decodedToken;
  } catch (error) {
    console.error('Chyba pri overovaní Firebase tokenu:', error);
    throw error;
  }
}
