// Removed express-rate-limit to avoid Edge Runtime compatibility issues
// Using custom in-memory rate limiting implementation instead
import { NextRequest, NextResponse } from 'next/server';

/**
 * Rate limiting configuration for different endpoint types
 * Implementuje komplexné obmedzenie počtu požiadaviek s in-memory riešením
 */

// Konfigurácia z environment premenných s rozumnými defaultmi
const config = {
  enabled: process.env.RATE_LIMIT_ENABLED === 'true',
  
  // Autentifikačné endpointy - prísne limity (5 req/min)
  auth: {
    max: parseInt(process.env.RATE_LIMIT_AUTH_MAX || '5'),
    windowMs: parseInt(process.env.RATE_LIMIT_AUTH_WINDOW_MS || '60000'), // 1 minúta
  },
  
  // Všeobecné API endpointy - stredné limity (100 req/min)
  general: {
    max: parseInt(process.env.RATE_LIMIT_GENERAL_MAX || '100'),
    windowMs: parseInt(process.env.RATE_LIMIT_GENERAL_WINDOW_MS || '60000'), // 1 minúta
  },
  
  // Admin endpointy - prísnejšie limity (20 req/min)
  admin: {
    max: parseInt(process.env.RATE_LIMIT_ADMIN_MAX || '20'),
    windowMs: parseInt(process.env.RATE_LIMIT_ADMIN_WINDOW_MS || '60000'), // 1 minúta
  },
  
  // Upload endpointy - obmedzené limity (10 req/min)
  upload: {
    max: parseInt(process.env.RATE_LIMIT_UPLOAD_MAX || '10'),
    windowMs: parseInt(process.env.RATE_LIMIT_UPLOAD_WINDOW_MS || '60000'), // 1 minúta
  },

  // Ochrana proti útokom - prísne limity (10 req/min)
  attack: {
    max: parseInt(process.env.RATE_LIMIT_ATTACK_MAX || '10'),
    windowMs: parseInt(process.env.RATE_LIMIT_ATTACK_WINDOW_MS || '60000'), // 1 minúta
  },
  
  // Globálne nastavenia
  skipSuccessfulRequests: process.env.RATE_LIMIT_SKIP_SUCCESSFUL_REQUESTS === 'true',
  skipFailedRequests: process.env.RATE_LIMIT_SKIP_FAILED_REQUESTS === 'true',
  whitelistIPs: process.env.RATE_LIMIT_WHITELIST_IPS?.split(',').filter(ip => ip.trim()) || [],
};

/**
 * Získanie IP adresy z NextRequest objektu
 */
function getClientIP(request: NextRequest): string {
  // Skúsime rôzne hlavičky pre získanie skutočnej IP adresy
  const forwarded = request.headers.get('x-forwarded-for');
  const realIP = request.headers.get('x-real-ip');
  const cfConnectingIP = request.headers.get('cf-connecting-ip');
  
  if (forwarded) {
    // x-forwarded-for môže obsahovať viacero IP adries oddelených čiarkami
    return forwarded.split(',')[0].trim();
  }
  
  if (realIP) {
    return realIP.trim();
  }
  
  if (cfConnectingIP) {
    return cfConnectingIP.trim();
  }

  // Fallback na localhost pre development (NextRequest nemá vlastnosť ip v Next.js 15)
  return '127.0.0.1';
}

/**
 * Kontrola, či je IP adresa na whitelist
 */
function isWhitelisted(ip: string): boolean {
  if (config.whitelistIPs.length === 0) {
    return false;
  }
  
  return config.whitelistIPs.includes(ip);
}

/**
 * Custom in-memory rate limiting store (Edge Runtime compatible)
 */
interface RateLimitEntry {
  count: number;
  resetTime: number;
}

export const rateLimitStore = new Map<string, RateLimitEntry>();

// Clean up expired entries every 5 minutes
setInterval(() => {
  const now = Date.now();
  for (const [key, entry] of rateLimitStore.entries()) {
    if (entry.resetTime < now) {
      rateLimitStore.delete(key);
    }
  }
}, 5 * 60 * 1000);

/**
 * Custom rate limiter implementation (Edge Runtime compatible)
 */
function createRateLimiter(type: 'auth' | 'general' | 'admin' | 'upload' | 'attack') {
  const limiterConfig = config[type];

  return {
    type,
    config: limiterConfig,
    check: (ip: string): { allowed: boolean; remaining: number; resetTime: number } => {
      if (!config.enabled) {
        return { allowed: true, remaining: limiterConfig.max, resetTime: Date.now() + limiterConfig.windowMs };
      }

      if (isWhitelisted(ip)) {
        return { allowed: true, remaining: limiterConfig.max, resetTime: Date.now() + limiterConfig.windowMs };
      }

      const key = `${type}:${ip}`;
      const now = Date.now();
      const entry = rateLimitStore.get(key);

      if (!entry || entry.resetTime < now) {
        // Create new entry or reset expired entry
        const newEntry: RateLimitEntry = {
          count: 1,
          resetTime: now + limiterConfig.windowMs
        };
        rateLimitStore.set(key, newEntry);
        return {
          allowed: true,
          remaining: limiterConfig.max - 1,
          resetTime: newEntry.resetTime
        };
      }

      // Increment existing entry
      entry.count++;
      const remaining = Math.max(0, limiterConfig.max - entry.count);
      const allowed = entry.count <= limiterConfig.max;

      if (!allowed) {
        // Log rate limit exceeded
        console.warn(`Rate limit exceeded for ${type} endpoint`, {
          ip: ip.substring(0, 8) + '***',
          type,
          count: entry.count,
          limit: limiterConfig.max,
          timestamp: new Date().toISOString(),
        });
      }

      return { allowed, remaining, resetTime: entry.resetTime };
    }
  };
}

// Vytvorenie rate limiterov pre rôzne typy endpointov
export const authRateLimiter = createRateLimiter('auth');
export const generalRateLimiter = createRateLimiter('general');
export const adminRateLimiter = createRateLimiter('admin');
export const uploadRateLimiter = createRateLimiter('upload');
export const attackRateLimiter = createRateLimiter('attack');

/**
 * Wrapper funkcia pre aplikovanie rate limiting na Next.js API route
 */
export function withRateLimit(
  limiter: ReturnType<typeof createRateLimiter>,
  handler: (request: NextRequest, ...args: any[]) => Promise<NextResponse>
) {
  return async (request: NextRequest, ...args: any[]) => {
    if (!config.enabled) {
      return handler(request, ...args);
    }

    const ip = getClientIP(request);
    const result = limiter.check(ip);

    if (!result.allowed) {
      // Rate limit exceeded - return 429 response
      const genericMessages = {
        auth: 'Príliš veľa pokusov o prihlásenie. Skúste to znovu neskôr.',
        admin: 'Príliš veľa požiadaviek na admin operácie. Skúste to znovu neskôr.',
        upload: 'Príliš veľa nahrávaní súborov. Skúste to znovu neskôr.',
        general: 'Príliš veľa požiadaviek. Skúste to znovu neskôr.',
        attack: 'Podozrivá aktivita detekovaná. Prístup dočasne obmedzený.',
      };

      return NextResponse.json(
        {
          error: 'Príliš veľa požiadaviek',
          message: genericMessages[limiter.type as keyof typeof genericMessages] || genericMessages.general,
        },
        {
          status: 429,
          headers: {
            'X-RateLimit-Limit': limiter.config.max.toString(),
            'X-RateLimit-Remaining': result.remaining.toString(),
            'X-RateLimit-Reset': new Date(result.resetTime).toISOString(),
            'X-RateLimit-Type': limiter.type,
          },
        }
      );
    }

    // Rate limit not exceeded - proceed with request
    const response = await handler(request, ...args);

    // Add rate limit headers to successful responses
    response.headers.set('X-RateLimit-Limit', limiter.config.max.toString());
    response.headers.set('X-RateLimit-Remaining', result.remaining.toString());
    response.headers.set('X-RateLimit-Reset', new Date(result.resetTime).toISOString());
    response.headers.set('X-RateLimit-Type', limiter.type);

    return response;
  };
}

/**
 * Utility funkcie pre identifikáciu typov endpointov
 */
export function isAuthEndpoint(pathname: string): boolean {
  const authPatterns = [
    '/api/auth/',
    '/api/users/route', // POST pre registráciu
    '/api/csrf/',
  ];

  return authPatterns.some(pattern => pathname.includes(pattern));
}

export function isAdminEndpoint(pathname: string): boolean {
  return pathname.includes('/api/admin/') || 
         pathname.includes('/anonymize') ||
         pathname.includes('/api/toy-types/') ||
         pathname.includes('/api/cache-bust/');
}

export function isUploadEndpoint(pathname: string): boolean {
  return pathname.includes('/api/cloudinary/upload') ||
         pathname.includes('/api/toys/create');
}

/**
 * Detekcia WordPress a iných známych attack patterns
 */
export function isAttackPattern(pathname: string): boolean {
  const attackPatterns = [
    // WordPress admin patterns
    '/wp-admin',
    '/wordpress/wp-admin',
    '/wp-login.php',
    '/wp-config.php',
    '/wp-content',
    '/wp-includes',
    '/xmlrpc.php',

    // WordPress setup patterns
    '/wp-admin/setup-config.php',
    '/wordpress/wp-admin/setup-config.php',

    // Common CMS patterns
    '/admin.php',
    '/administrator',
    '/phpmyadmin',
    '/phpMyAdmin',
    '/mysql',

    // Common exploit patterns
    '/.env',
    '/.git',
    '/config.php',
    '/database.php',
    '/backup',
    '/sql',
    '/dump',

    // API abuse patterns
    '/api/v1',
    '/api/v2',
    '/rest/api',
    '/graphql',
  ];

  return attackPatterns.some(pattern =>
    pathname.toLowerCase().includes(pattern.toLowerCase())
  );
}

/**
 * Hlavná funkcia pre automatické určenie a aplikovanie správneho rate limitera
 */
export function applyRateLimit(pathname: string) {
  // Priorita: attack patterns majú najvyššiu prioritu
  if (isAttackPattern(pathname)) {
    return attackRateLimiter;
  } else if (isAuthEndpoint(pathname)) {
    return authRateLimiter;
  } else if (isAdminEndpoint(pathname)) {
    return adminRateLimiter;
  } else if (isUploadEndpoint(pathname)) {
    return uploadRateLimiter;
  } else {
    return generalRateLimiter;
  }
}

// Export konfigurácie pre debugging a monitoring
export { config as rateLimitConfig };
