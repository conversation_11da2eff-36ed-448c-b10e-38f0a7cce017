import { Resend } from 'resend';
import { generateUserUnsubscribeToken, getUnsubscribeUrl } from './emailPreferences';

// Initialize Resend client
const resend = new Resend(process.env.RESEND_API_KEY);

// Email configuration
const EMAIL_FROM = process.env.EMAIL_FROM || '<EMAIL>';
const EMAIL_FROM_NAME = process.env.EMAIL_FROM_NAME || 'Swapka - Zdieľanie hračiek';

// Types for email data
export interface ReservationCreatedEmailData {
  userId: number;
  userName: string;
  userEmail: string;
  toyName: string;
  reservationId: string;
  position?: number;
  queueLength?: number;
}

export interface ReservationApprovedEmailData {
  userId: number;
  userName: string;
  userEmail: string;
  toyName: string;
  reservationId: string;
  ownerName: string;
  ownerEmail?: string | null;
  ownerPhone?: string | null;
  ownerCity?: string | null;
}

export interface ReservationRequestEmailData {
  ownerId: number;
  ownerName: string;
  ownerEmail: string;
  toyName: string;
  reservationId: string;
  requesterName: string;
  requesterEmail?: string | null;
  requesterPhone?: string | null;
  requesterCity?: string | null;
  position?: number;
  queueLength?: number;
}

// Email sending result
export interface EmailResult {
  success: boolean;
  messageId?: string;
  error?: string;
}

/**
 * Sends an email notification when a new reservation is created
 */
export async function sendReservationCreatedEmail(
  data: ReservationCreatedEmailData
): Promise<EmailResult> {
  try {
    console.log('Sending reservation created email to:', data.userEmail);

    const subject = `Potvrdenie rezervácie - ${data.toyName}`;

    // Generate unsubscribe token
    const unsubscribeToken = await generateUserUnsubscribeToken(data.userId);
    const unsubscribeUrl = unsubscribeToken ? getUnsubscribeUrl(unsubscribeToken) : null;

    // Create position message if user is in queue
    let positionMessage = '';
    if (data.position && data.position > 0) {
      positionMessage = `<p style="color: #f59e0b; font-weight: 600; margin: 16px 0;">
        Ste ${data.position + 1}. v poradí pre túto hračku.
      </p>`;
    }

    const htmlContent = `
      <!DOCTYPE html>
      <html lang="sk">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>${subject}</title>
      </head>
      <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">
        <div style="background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
          <h1 style="color: #2563eb; margin: 0 0 10px 0; font-size: 24px;">Swapka - Zdieľanie hračiek</h1>
          <h2 style="color: #1f2937; margin: 0; font-size: 20px;">Potvrdenie rezervácie</h2>
        </div>
        
        <div style="background-color: white; padding: 20px; border-radius: 8px; border: 1px solid #e5e7eb;">
          <p style="margin: 0 0 16px 0; font-size: 16px;">Dobrý deň <strong>${data.userName}</strong>,</p>
          
          <p style="margin: 0 0 16px 0;">Vaša rezervácia bola úspešne vytvorená!</p>
          
          <div style="background-color: #f3f4f6; padding: 16px; border-radius: 6px; margin: 16px 0;">
            <h3 style="margin: 0 0 8px 0; color: #1f2937; font-size: 18px;">Detaily rezervácie:</h3>
            <p style="margin: 0 0 8px 0;"><strong>Hračka:</strong> ${data.toyName}</p>
            <p style="margin: 0;"><strong>ID rezervácie:</strong> ${data.reservationId}</p>
          </div>
          
          ${positionMessage}
          
          <p style="margin: 16px 0;">Teraz čakáme na schválenie od vlastníka hračky. Keď bude vaša rezervácia schválená, pošleme vám ďalší email s kontaktnými údajmi.</p>
          
          <div style="text-align: center; margin: 24px 0;">
            <a href="${process.env.NEXT_PUBLIC_BASE_URL || 'https://swapka.sk'}/rezervacie" 
               style="background-color: #2563eb; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; font-weight: 600; display: inline-block;">
              Zobraziť moje rezervácie
            </a>
          </div>
          
          <p style="margin: 16px 0 0 0; font-size: 14px; color: #6b7280;">
            Ďakujeme, že používate Swapka!<br>
            Tím Swapka
          </p>
        </div>
        
        <div style="margin-top: 20px; padding: 16px; background-color: #f9fafb; border-radius: 6px; font-size: 12px; color: #6b7280; text-align: center;">
          <p style="margin: 0 0 8px 0;">Tento email bol odoslaný automaticky. Prosím, neodpovedajte na túto správu.</p>
          ${unsubscribeUrl ? `<p style="margin: 0;"><a href="${unsubscribeUrl}" style="color: #6b7280; text-decoration: underline;">Odhlásiť sa z emailových notifikácií</a></p>` : ''}
        </div>
      </body>
      </html>
    `;

    const textContent = `
Dobrý deň ${data.userName},

Vaša rezervácia bola úspešne vytvorená!

Detaily rezervácie:
- Hračka: ${data.toyName}
- ID rezervácie: ${data.reservationId}

${data.position && data.position > 0 ? `Ste ${data.position + 1}. v poradí pre túto hračku.` : ''}

Teraz čakáme na schválenie od vlastníka hračky. Keď bude vaša rezervácia schválená, pošleme vám ďalší email s kontaktnými údajmi.

Zobraziť moje rezervácie: ${process.env.NEXT_PUBLIC_BASE_URL || 'https://swapka.sk'}/rezervacie

Ďakujeme, že používate Swapka!
Tím Swapka

---
Tento email bol odoslaný automaticky. Prosím, neodpovedajte na túto správu.
${unsubscribeUrl ? `\nOdhlásiť sa z emailových notifikácií: ${unsubscribeUrl}` : ''}
    `;

    const result = await resend.emails.send({
      from: `${EMAIL_FROM_NAME} <${EMAIL_FROM}>`,
      to: [data.userEmail],
      subject: subject,
      html: htmlContent,
      text: textContent,
    });

    console.log('Reservation created email sent successfully:', result.data?.id);
    
    return {
      success: true,
      messageId: result.data?.id,
    };
  } catch (error) {
    console.error('Error sending reservation created email:', error);
    
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred',
    };
  }
}

/**
 * Sends an email notification when a reservation is approved
 */
export async function sendReservationApprovedEmail(
  data: ReservationApprovedEmailData
): Promise<EmailResult> {
  try {
    console.log('Sending reservation approved email to:', data.userEmail);

    const subject = `Rezervácia schválená - ${data.toyName}`;

    // Generate unsubscribe token
    const unsubscribeToken = await generateUserUnsubscribeToken(data.userId);
    const unsubscribeUrl = unsubscribeToken ? getUnsubscribeUrl(unsubscribeToken) : null;

    // Create contact information section
    let contactInfo = `<p><strong>Meno:</strong> ${data.ownerName}</p>`;
    if (data.ownerEmail) {
      contactInfo += `<p><strong>Email:</strong> <a href="mailto:${data.ownerEmail}">${data.ownerEmail}</a></p>`;
    }
    if (data.ownerPhone) {
      contactInfo += `<p><strong>Telefón:</strong> <a href="tel:${data.ownerPhone}">${data.ownerPhone}</a></p>`;
    }
    if (data.ownerCity) {
      contactInfo += `<p><strong>Mesto:</strong> ${data.ownerCity}</p>`;
    }

    const htmlContent = `
      <!DOCTYPE html>
      <html lang="sk">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>${subject}</title>
      </head>
      <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">
        <div style="background-color: #f0fdf4; padding: 20px; border-radius: 8px; margin-bottom: 20px; border-left: 4px solid #22c55e;">
          <h1 style="color: #2563eb; margin: 0 0 10px 0; font-size: 24px;">Swapka - Zdieľanie hračiek</h1>
          <h2 style="color: #16a34a; margin: 0; font-size: 20px;">🎉 Rezervácia schválená!</h2>
        </div>
        
        <div style="background-color: white; padding: 20px; border-radius: 8px; border: 1px solid #e5e7eb;">
          <p style="margin: 0 0 16px 0; font-size: 16px;">Dobrý deň <strong>${data.userName}</strong>,</p>
          
          <p style="margin: 0 0 16px 0; color: #16a34a; font-weight: 600;">Skvelé správy! Vaša rezervácia bola schválená!</p>
          
          <div style="background-color: #f3f4f6; padding: 16px; border-radius: 6px; margin: 16px 0;">
            <h3 style="margin: 0 0 8px 0; color: #1f2937; font-size: 18px;">Detaily rezervácie:</h3>
            <p style="margin: 0 0 8px 0;"><strong>Hračka:</strong> ${data.toyName}</p>
            <p style="margin: 0;"><strong>ID rezervácie:</strong> ${data.reservationId}</p>
          </div>
          
          <div style="background-color: #fef3c7; padding: 16px; border-radius: 6px; margin: 16px 0; border-left: 4px solid #f59e0b;">
            <h3 style="margin: 0 0 12px 0; color: #92400e; font-size: 18px;">Kontaktné údaje vlastníka:</h3>
            <div style="color: #92400e;">
              ${contactInfo}
            </div>
          </div>
          
          <div style="background-color: #e0f2fe; padding: 16px; border-radius: 6px; margin: 16px 0;">
            <h3 style="margin: 0 0 8px 0; color: #0369a1; font-size: 16px;">Ďalšie kroky:</h3>
            <ol style="margin: 0; padding-left: 20px; color: #0369a1;">
              <li>Kontaktujte vlastníka hračky pomocou uvedených kontaktných údajov</li>
              <li>Dohodnite si čas a miesto prevzatia hračky</li>
              <li>Užite si zábavu s hračkou!</li>
            </ol>
          </div>
          
          <div style="text-align: center; margin: 24px 0;">
            <a href="${process.env.NEXT_PUBLIC_BASE_URL || 'https://swapka.sk'}/rezervacie" 
               style="background-color: #2563eb; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; font-weight: 600; display: inline-block;">
              Zobraziť moje rezervácie
            </a>
          </div>
          
          <p style="margin: 16px 0 0 0; font-size: 14px; color: #6b7280;">
            Ďakujeme, že používate Swapka!<br>
            Tím Swapka
          </p>
        </div>
        
        <div style="margin-top: 20px; padding: 16px; background-color: #f9fafb; border-radius: 6px; font-size: 12px; color: #6b7280; text-align: center;">
          <p style="margin: 0 0 8px 0;">Tento email bol odoslaný automaticky. Prosím, neodpovedajte na túto správu.</p>
          ${unsubscribeUrl ? `<p style="margin: 0;"><a href="${unsubscribeUrl}" style="color: #6b7280; text-decoration: underline;">Odhlásiť sa z emailových notifikácií</a></p>` : ''}
        </div>
      </body>
      </html>
    `;

    // Create text version of contact info
    let textContactInfo = `Meno: ${data.ownerName}\n`;
    if (data.ownerEmail) {
      textContactInfo += `Email: ${data.ownerEmail}\n`;
    }
    if (data.ownerPhone) {
      textContactInfo += `Telefón: ${data.ownerPhone}\n`;
    }
    if (data.ownerCity) {
      textContactInfo += `Mesto: ${data.ownerCity}\n`;
    }

    const textContent = `
Dobrý deň ${data.userName},

🎉 Skvelé správy! Vaša rezervácia bola schválená!

Detaily rezervácie:
- Hračka: ${data.toyName}
- ID rezervácie: ${data.reservationId}

Kontaktné údaje vlastníka:
${textContactInfo}

Ďalšie kroky:
1. Kontaktujte vlastníka hračky pomocou uvedených kontaktných údajov
2. Dohodnite si čas a miesto prevzatia hračky
3. Užite si zábavu s hračkou!

Zobraziť moje rezervácie: ${process.env.NEXT_PUBLIC_BASE_URL || 'https://swapka.sk'}/rezervacie

Ďakujeme, že používate Swapka!
Tím Swapka

---
Tento email bol odoslaný automaticky. Prosím, neodpovedajte na túto správu.
${unsubscribeUrl ? `\nOdhlásiť sa z emailových notifikácií: ${unsubscribeUrl}` : ''}
    `;

    const result = await resend.emails.send({
      from: `${EMAIL_FROM_NAME} <${EMAIL_FROM}>`,
      to: [data.userEmail],
      subject: subject,
      html: htmlContent,
      text: textContent,
    });

    console.log('Reservation approved email sent successfully:', result.data?.id);
    
    return {
      success: true,
      messageId: result.data?.id,
    };
  } catch (error) {
    console.error('Error sending reservation approved email:', error);
    
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred',
    };
  }
}

/**
 * Sends an email notification to toy owner when someone requests to reserve their toy
 */
export async function sendReservationRequestEmail(
  data: ReservationRequestEmailData
): Promise<EmailResult> {
  try {
    console.log('Sending reservation request email to toy owner:', data.ownerEmail);

    const subject = `Nová rezervácia - ${data.toyName}`;

    // Generate unsubscribe token
    const unsubscribeToken = await generateUserUnsubscribeToken(data.ownerId);
    const unsubscribeUrl = unsubscribeToken ? getUnsubscribeUrl(unsubscribeToken) : null;

    // Create requester contact information section
    let requesterContactInfo = `<p><strong>Meno:</strong> ${data.requesterName}</p>`;
    if (data.requesterEmail) {
      requesterContactInfo += `<p><strong>Email:</strong> <a href="mailto:${data.requesterEmail}">${data.requesterEmail}</a></p>`;
    }
    if (data.requesterPhone) {
      requesterContactInfo += `<p><strong>Telefón:</strong> <a href="tel:${data.requesterPhone}">${data.requesterPhone}</a></p>`;
    }
    if (data.requesterCity) {
      requesterContactInfo += `<p><strong>Mesto:</strong> ${data.requesterCity}</p>`;
    }

    // Create queue position message if applicable
    let queueMessage = '';
    if (data.position && data.position > 0) {
      queueMessage = `<p style="color: #f59e0b; font-weight: 600; margin: 16px 0;">
        Táto rezervácia je ${data.position + 1}. v poradí pre túto hračku.
      </p>`;
    }

    const htmlContent = `
      <!DOCTYPE html>
      <html lang="sk">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>${subject}</title>
      </head>
      <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">
        <div style="background-color: #fef3c7; padding: 20px; border-radius: 8px; margin-bottom: 20px; border-left: 4px solid #f59e0b;">
          <h1 style="color: #2563eb; margin: 0 0 10px 0; font-size: 24px;">Swapka - Zdieľanie hračiek</h1>
          <h2 style="color: #92400e; margin: 0; font-size: 20px;">🔔 Nová rezervácia</h2>
        </div>

        <div style="background-color: white; padding: 20px; border-radius: 8px; border: 1px solid #e5e7eb;">
          <p style="margin: 0 0 16px 0; font-size: 16px;">Dobrý deň <strong>${data.ownerName}</strong>,</p>

          <p style="margin: 0 0 16px 0;">Niekto má záujem o vašu hračku a vytvoril rezerváciu!</p>

          <div style="background-color: #f3f4f6; padding: 16px; border-radius: 6px; margin: 16px 0;">
            <h3 style="margin: 0 0 8px 0; color: #1f2937; font-size: 18px;">Detaily rezervácie:</h3>
            <p style="margin: 0 0 8px 0;"><strong>Hračka:</strong> ${data.toyName}</p>
            <p style="margin: 0;"><strong>ID rezervácie:</strong> ${data.reservationId}</p>
          </div>

          ${queueMessage}

          <div style="background-color: #e0f2fe; padding: 16px; border-radius: 6px; margin: 16px 0;">
            <h3 style="margin: 0 0 12px 0; color: #0369a1; font-size: 18px;">Kontaktné údaje záujemcu:</h3>
            <div style="color: #0369a1;">
              ${requesterContactInfo}
            </div>
          </div>

          <div style="background-color: #fef3c7; padding: 16px; border-radius: 6px; margin: 16px 0;">
            <h3 style="margin: 0 0 8px 0; color: #92400e; font-size: 16px;">Čo môžete urobiť:</h3>
            <ol style="margin: 0; padding-left: 20px; color: #92400e;">
              <li><strong>Schváliť rezerváciu</strong> - záujemca dostane vaše kontaktné údaje</li>
              <li><strong>Odmietnuť rezerváciu</strong> - ak hračka nie je dostupná</li>
              <li><strong>Kontaktovať záujemcu</strong> - môžete ho kontaktovať priamo</li>
            </ol>
          </div>

          <div style="text-align: center; margin: 24px 0;">
            <a href="${process.env.NEXT_PUBLIC_BASE_URL || 'https://swapka.sk'}/rezervacie/vlastnik"
               style="background-color: #16a34a; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; font-weight: 600; display: inline-block; margin-right: 8px;">
              Spravovať rezervácie
            </a>
            <a href="${process.env.NEXT_PUBLIC_BASE_URL || 'https://swapka.sk'}/moje-hracky"
               style="background-color: #2563eb; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; font-weight: 600; display: inline-block;">
              Moje hračky
            </a>
          </div>

          <p style="margin: 16px 0 0 0; font-size: 14px; color: #6b7280;">
            Ďakujeme, že používate Swapka!<br>
            Tím Swapka
          </p>
        </div>

        <div style="margin-top: 20px; padding: 16px; background-color: #f9fafb; border-radius: 6px; font-size: 12px; color: #6b7280; text-align: center;">
          <p style="margin: 0 0 8px 0;">Tento email bol odoslaný automaticky. Prosím, neodpovedajte na túto správu.</p>
          ${unsubscribeUrl ? `<p style="margin: 0;"><a href="${unsubscribeUrl}" style="color: #6b7280; text-decoration: underline;">Odhlásiť sa z emailových notifikácií</a></p>` : ''}
        </div>
      </body>
      </html>
    `;

    // Create text version of requester contact info
    let textRequesterContactInfo = `Meno: ${data.requesterName}\n`;
    if (data.requesterEmail) {
      textRequesterContactInfo += `Email: ${data.requesterEmail}\n`;
    }
    if (data.requesterPhone) {
      textRequesterContactInfo += `Telefón: ${data.requesterPhone}\n`;
    }
    if (data.requesterCity) {
      textRequesterContactInfo += `Mesto: ${data.requesterCity}\n`;
    }

    const textContent = `
Dobrý deň ${data.ownerName},

🔔 Niekto má záujem o vašu hračku a vytvoril rezerváciu!

Detaily rezervácie:
- Hračka: ${data.toyName}
- ID rezervácie: ${data.reservationId}

${data.position && data.position > 0 ? `Táto rezervácia je ${data.position + 1}. v poradí pre túto hračku.` : ''}

Kontaktné údaje záujemcu:
${textRequesterContactInfo}

Čo môžete urobiť:
1. Schváliť rezerváciu - záujemca dostane vaše kontaktné údaje
2. Odmietnuť rezerváciu - ak hračka nie je dostupná
3. Kontaktovať záujemcu - môžete ho kontaktovať priamo

Spravovať rezervácie: ${process.env.NEXT_PUBLIC_BASE_URL || 'https://swapka.sk'}/rezervacie/vlastnik
Moje hračky: ${process.env.NEXT_PUBLIC_BASE_URL || 'https://swapka.sk'}/moje-hracky

Ďakujeme, že používate Swapka!
Tím Swapka

---
Tento email bol odoslaný automaticky. Prosím, neodpovedajte na túto správu.
${unsubscribeUrl ? `\nOdhlásiť sa z emailových notifikácií: ${unsubscribeUrl}` : ''}
    `;

    const result = await resend.emails.send({
      from: `${EMAIL_FROM_NAME} <${EMAIL_FROM}>`,
      to: [data.ownerEmail],
      subject: subject,
      html: htmlContent,
      text: textContent,
    });

    console.log('Reservation request email sent successfully to toy owner:', result.data?.id);

    return {
      success: true,
      messageId: result.data?.id,
    };
  } catch (error) {
    console.error('Error sending reservation request email to toy owner:', error);

    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred',
    };
  }
}

/**
 * Validates email configuration
 */
export function validateEmailConfig(): boolean {
  if (!process.env.RESEND_API_KEY) {
    console.error('RESEND_API_KEY is not configured');
    return false;
  }
  
  if (!EMAIL_FROM) {
    console.error('EMAIL_FROM is not configured');
    return false;
  }
  
  return true;
}
