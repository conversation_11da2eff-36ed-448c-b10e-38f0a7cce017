/**
 * SEO utility functions for the Swapka application
 * Handles SEO-friendly URL generation, meta tags, and structured data
 */

import { hashId, toNumericId, isHashedId } from './hashUtils';

// Slovak character mapping for URL-friendly slugs
const SLOVAK_CHAR_MAP: Record<string, string> = {
  'á': 'a', 'ä': 'a', 'č': 'c', 'ď': 'd', 'é': 'e', 'í': 'i', 'ĺ': 'l', 'ľ': 'l',
  'ň': 'n', 'ó': 'o', 'ô': 'o', 'ŕ': 'r', 'š': 's', 'ť': 't', 'ú': 'u', 'ý': 'y', 'ž': 'z',
  'Á': 'A', 'Ä': 'A', 'Č': 'C', 'Ď': 'D', 'É': 'E', 'Í': 'I', 'Ĺ': 'L', 'Ľ': 'L',
  'Ň': 'N', 'Ó': 'O', 'Ô': 'O', 'Ŕ': 'R', 'Š': 'S', 'Ť': 'T', 'Ú': 'U', 'Ý': 'Y', 'Ž': 'Z'
};

/**
 * Converts Slovak text to SEO-friendly slug
 * @param text Text to convert to slug
 * @param maxLength Maximum length of the slug (default: 50)
 * @returns SEO-friendly slug
 */
export function createSeoSlug(text: string, maxLength: number = 50): string {
  if (!text || typeof text !== 'string') {
    return '';
  }

  let slug = text
    // Convert Slovak characters to ASCII
    .split('')
    .map(char => SLOVAK_CHAR_MAP[char] || char)
    .join('')
    // Convert to lowercase
    .toLowerCase()
    // Remove special characters except spaces and hyphens
    .replace(/[^a-z0-9\s-]/g, '')
    // Replace multiple spaces with single space
    .replace(/\s+/g, ' ')
    // Trim spaces
    .trim()
    // Replace spaces with hyphens
    .replace(/\s/g, '-')
    // Remove multiple consecutive hyphens
    .replace(/-+/g, '-')
    // Remove leading/trailing hyphens
    .replace(/^-+|-+$/g, '');

  // Truncate to max length while preserving word boundaries
  if (slug.length > maxLength) {
    slug = slug.substring(0, maxLength);
    // Find last hyphen to avoid cutting words
    const lastHyphen = slug.lastIndexOf('-');
    if (lastHyphen > maxLength * 0.7) { // Only if we're not cutting too much
      slug = slug.substring(0, lastHyphen);
    }
  }

  return slug || 'hracka'; // Fallback if slug is empty
}

/**
 * Generates SEO-friendly URL for toy detail page
 * @param toyName Name of the toy
 * @param hashedId Hashed ID of the toy
 * @returns SEO-friendly URL path
 */
export function generateToyUrl(toyName: string, hashedId: string): string {
  const slug = createSeoSlug(toyName);
  return `/hracky/${slug}-${hashedId}`;
}

/**
 * Parses SEO-friendly toy URL to extract hashed ID
 * @param urlPath URL path (e.g., "/hracky/lego-technic-bagr-h-c514910b9a5e")
 * @returns Hashed ID or null if not found
 */
export function parseToyUrl(urlPath: string): string | null {
  if (!urlPath || typeof urlPath !== 'string') {
    return null;
  }

  // Remove leading slash if present
  const path = urlPath.startsWith('/') ? urlPath.substring(1) : urlPath;
  
  // Expected format: hracky/slug-h-hashedid
  const parts = path.split('/');
  if (parts.length !== 2 || parts[0] !== 'hracky') {
    return null;
  }

  const urlPart = parts[1];
  
  // Look for hashed ID pattern (h-followed by 12 characters)
  const hashMatch = urlPart.match(/h-[a-f0-9]{12}$/);
  if (hashMatch) {
    return hashMatch[0];
  }

  // Fallback: check if the entire urlPart is a hashed ID (for backward compatibility)
  if (isHashedId(urlPart)) {
    return urlPart;
  }

  return null;
}

/**
 * Generates meta title for toy detail page
 * @param toyName Name of the toy
 * @param toyLocation Location of the toy (optional)
 * @returns Meta title
 */
export function generateToyMetaTitle(toyName: string, toyLocation?: string): string {
  const baseTitle = `${toyName} - Požičiavanie hračiek | Swapka`;
  
  if (toyLocation && toyLocation.trim()) {
    return `${toyName} v ${toyLocation} - Požičiavanie hračiek | Swapka`;
  }
  
  return baseTitle;
}

/**
 * Generates meta description for toy detail page
 * @param toyName Name of the toy
 * @param toyDescription Description of the toy
 * @param toyLocation Location of the toy (optional)
 * @param toyType Type of the toy (optional)
 * @param maxLength Maximum length of description (default: 160)
 * @returns Meta description
 */
export function generateToyMetaDescription(
  toyName: string,
  toyDescription: string,
  toyLocation?: string,
  toyType?: string,
  maxLength: number = 160
): string {
  let description = `Požičajte si ${toyName}`;
  
  if (toyLocation && toyLocation.trim()) {
    description += ` v ${toyLocation}`;
  }
  
  if (toyDescription && toyDescription.trim()) {
    // Add part of toy description
    const cleanDescription = toyDescription
      .replace(/\n/g, ' ')
      .replace(/\s+/g, ' ')
      .trim();
    
    const remainingLength = maxLength - description.length - 20; // Reserve space for ending
    if (remainingLength > 30) {
      let descPart = cleanDescription.substring(0, remainingLength);
      // Find last space to avoid cutting words
      const lastSpace = descPart.lastIndexOf(' ');
      if (lastSpace > remainingLength * 0.7) {
        descPart = descPart.substring(0, lastSpace);
      }
      description += `. ${descPart}`;
    }
  }
  
  if (toyType && toyType.trim()) {
    const typeText = ` - ${toyType}`;
    if (description.length + typeText.length + 20 < maxLength) {
      description += typeText;
    }
  }
  
  description += '. Zdieľanie detských hračiek na Swapka.sk';
  
  // Ensure we don't exceed max length
  if (description.length > maxLength) {
    description = description.substring(0, maxLength - 3) + '...';
  }
  
  return description;
}

/**
 * Interface for toy data used in SEO functions
 */
export interface ToyForSeo {
  id: number;
  name: string;
  description: string;
  type?: string;
  typeLabel?: string;
  price: number;
  deposit: number;
  priceType?: string;
  images?: string[];
  location?: {
    city?: string;
    postalCode?: string;
  };
  owner?: {
    name: string;
    city?: string;
  };
  createdAt: string;
  updatedAt: string;
}

/**
 * Generates structured data (JSON-LD) for toy detail page
 * @param toy Toy data
 * @param baseUrl Base URL of the application
 * @returns JSON-LD structured data
 */
export function generateToyStructuredData(toy: ToyForSeo, baseUrl: string): object {
  const hashedId = hashId(toy.id);
  const toyUrl = `${baseUrl}${generateToyUrl(toy.name, hashedId)}`;
  const mainImage = toy.images && toy.images.length > 0 ? toy.images[0] : null;
  
  const structuredData = {
    "@context": "https://schema.org",
    "@type": "Product",
    "name": toy.name,
    "description": toy.description,
    "url": toyUrl,
    "category": toy.typeLabel || toy.type || "Hračky",
    "brand": {
      "@type": "Brand",
      "name": "Swapka"
    },
    "offers": {
      "@type": "Offer",
      "price": toy.price.toString(),
      "priceCurrency": "EUR",
      "availability": "https://schema.org/InStock",
      "priceSpecification": {
        "@type": "PriceSpecification",
        "price": toy.price.toString(),
        "priceCurrency": "EUR",
        "unitText": toy.priceType === 'PER_RENTAL' ? 'za výpožičku' : 'za deň'
      }
    },
    "additionalProperty": []
  };

  // Add main image if available
  if (mainImage) {
    (structuredData as any).image = mainImage;
  }

  // Add location information
  if (toy.location?.city || toy.owner?.city) {
    const city = toy.location?.city || toy.owner?.city;
    (structuredData as any).availableAtOrFrom = {
      "@type": "Place",
      "address": {
        "@type": "PostalAddress",
        "addressLocality": city,
        "addressCountry": "SK"
      }
    };
  }

  // Add deposit information
  if (toy.deposit > 0) {
    (structuredData.additionalProperty as any[]).push({
      "@type": "PropertyValue",
      "name": "Záloha",
      "value": `${toy.deposit} EUR`
    });
  }

  // Add postal code if available
  if (toy.location?.postalCode) {
    const address = (structuredData as any).availableAtOrFrom?.address;
    if (address) {
      address.postalCode = toy.location.postalCode;
    }
  }

  return structuredData;
}

/**
 * Generates Open Graph meta tags for toy detail page
 * @param toy Toy data
 * @param baseUrl Base URL of the application
 * @returns Object with Open Graph meta tags
 */
export function generateToyOpenGraphTags(toy: ToyForSeo, baseUrl: string): Record<string, string> {
  const hashedId = hashId(toy.id);
  const toyUrl = `${baseUrl}${generateToyUrl(toy.name, hashedId)}`;
  const mainImage = toy.images && toy.images.length > 0 ? toy.images[0] : `${baseUrl}/og-default.jpg`;
  const location = toy.location?.city || toy.owner?.city;

  return {
    'og:type': 'product',
    'og:title': generateToyMetaTitle(toy.name, location),
    'og:description': generateToyMetaDescription(toy.name, toy.description, location, toy.typeLabel || toy.type),
    'og:url': toyUrl,
    'og:image': mainImage,
    'og:image:alt': `Obrázok hračky ${toy.name}`,
    'og:site_name': 'Swapka - Požičiavanie detských hračiek',
    'product:price:amount': toy.price.toString(),
    'product:price:currency': 'EUR'
  };
}

/**
 * Generates Twitter Card meta tags for toy detail page
 * @param toy Toy data
 * @param baseUrl Base URL of the application
 * @returns Object with Twitter Card meta tags
 */
export function generateToyTwitterCardTags(toy: ToyForSeo, baseUrl: string): Record<string, string> {
  const mainImage = toy.images && toy.images.length > 0 ? toy.images[0] : `${baseUrl}/twitter-default.jpg`;
  const location = toy.location?.city || toy.owner?.city;

  return {
    'twitter:card': 'summary_large_image',
    'twitter:title': generateToyMetaTitle(toy.name, location),
    'twitter:description': generateToyMetaDescription(toy.name, toy.description, location, toy.typeLabel || toy.type),
    'twitter:image': mainImage,
    'twitter:image:alt': `Obrázok hračky ${toy.name}`
  };
}

/**
 * Validates and normalizes toy URL parameter
 * Supports both old format (h-hashedid) and new format (slug-h-hashedid)
 * @param urlParam URL parameter from route
 * @returns Object with hashed ID and whether it's legacy format
 */
export function validateToyUrlParam(urlParam: string): { hashedId: string | null; isLegacy: boolean } {
  if (!urlParam || typeof urlParam !== 'string') {
    return { hashedId: null, isLegacy: false };
  }

  // Check if it's a direct hashed ID (legacy format)
  if (isHashedId(urlParam)) {
    return { hashedId: urlParam, isLegacy: true };
  }

  // Check if it's new SEF format (slug-h-hashedid)
  const hashMatch = urlParam.match(/h-[a-f0-9]{12}$/);
  if (hashMatch) {
    return { hashedId: hashMatch[0], isLegacy: false };
  }

  return { hashedId: null, isLegacy: false };
}

/**
 * Gets base URL for the application
 * @returns Base URL
 */
export function getBaseUrl(): string {
  if (process.env.NODE_ENV === 'production') {
    return process.env.NEXT_PUBLIC_BASE_URL || 'https://swapka.sk';
  }
  return 'http://localhost:3000';
}

/**
 * Fetches toy data for SEO purposes (server-side only)
 * @param hashedId Hashed ID of the toy
 * @returns Toy data or null if not found
 */
export async function getToyForSeo(hashedId: string): Promise<ToyForSeo | null> {
  // This function should only run on the server
  if (typeof window !== 'undefined') {
    throw new Error('getToyForSeo can only be called on the server side');
  }

  try {
    const { prisma } = await import('./db');
    const { toNumericId, isHashedId } = await import('./hashUtils');

    // Validate hashed ID
    if (!isHashedId(hashedId)) {
      return null;
    }

    // Convert to numeric ID
    const numericId = toNumericId(hashedId);
    if (numericId === null) {
      return null;
    }

    // Fetch toy data
    const toy = await prisma.toy.findUnique({
      where: { id: numericId },
      include: {
        images: true,
        user: {
          select: {
            id: true,
            name: true,
            city: true,
            postalCode: true,
            status: true,
          },
        },
      },
    });

    if (!toy) {
      return null;
    }

    // Check if toy is accessible (not blocked user, not draft unless owner)
    if (toy.user.status === 'BLOCKED' || toy.status === 'DRAFT') {
      return null;
    }

    // Get type and status labels
    const [toyType, toyStatus] = await Promise.all([
      prisma.toyType.findUnique({
        where: { name: toy.type },
        select: { label: true }
      }),
      prisma.toyStatus.findUnique({
        where: { name: toy.status },
        select: { label: true }
      })
    ]);

    // Transform to SEO format
    const seoToy: ToyForSeo = {
      id: toy.id,
      name: toy.name,
      description: toy.description,
      type: toy.type,
      typeLabel: toyType?.label || toy.type,
      price: toy.price,
      deposit: toy.deposit,
      priceType: (toy as any).priceType || 'PER_DAY',
      images: toy.images.map(img => img.url),
      location: {
        city: toy.user.city || undefined,
        postalCode: toy.user.postalCode || undefined,
      },
      owner: {
        name: toy.user.name,
        city: toy.user.city || undefined,
      },
      createdAt: toy.createdAt.toISOString(),
      updatedAt: toy.updatedAt.toISOString(),
    };

    return seoToy;
  } catch (error) {
    console.error('Error fetching toy for SEO:', error);
    return null;
  }
}
