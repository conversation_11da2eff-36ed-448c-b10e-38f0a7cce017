import { NextRequest, NextResponse } from 'next/server';
import { prisma } from './db';
import { auth } from './firebase';

import { verifyFirebaseToken } from './firebaseAdmin';
import { withCsrfProtection } from './csrf';

/**
 * Overenie autentifikácie používateľa na strane servera
 * @param request NextRequest objekt
 * @returns Objekt s informáciami o používateľovi alebo null, ak použ<PERSON>vateľ nie je autentifikovaný
 */
export async function verifyAuth(request: NextRequest) {
  try {
    // Získanie autorizačného tokenu z hlavičky
    const authHeader = request.headers.get('authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return null;
    }

    const token = authHeader.split('Bearer ')[1];
    if (!token) {
      return null;
    }

    // Overenie tokenu pomocou Firebase Admin SDK
    let decodedToken;
    try {
      decodedToken = await verifyFirebaseToken(token);
    } catch (tokenError) {
      console.error('Chyba pri overovaní Firebase tokenu:', tokenError);
      return null;
    }

    if (!decodedToken || !decodedToken.uid) {
      return null;
    }

    // Pokúsime sa nájsť používateľa podľa Firebase UID
    let user = await prisma.user.findUnique({
      where: { firebaseUid: decodedToken.uid },
      select: {
        id: true,
        email: true,
        name: true,
        role: true,
      },
    });

    // Ak sme nenašli používateľa podľa Firebase UID, skúsime nájsť používateľa podľa emailu
    if (!user && decodedToken.email) {
      user = await prisma.user.findUnique({
        where: { email: decodedToken.email },
        select: {
          id: true,
          email: true,
          name: true,
          role: true,
        },
      });

      // Ak sme našli používateľa podľa emailu, aktualizujeme jeho Firebase UID
      if (user) {
        await prisma.user.update({
          where: { id: user.id },
          data: { firebaseUid: decodedToken.uid },
        });
      }
    }

    if (!user) {
      console.error('Používateľ s Firebase UID nebol nájdený v databáze:', decodedToken.uid);
      return null;
    }

    // Overenie, či email z tokenu zodpovedá emailu používateľa v databáze
    // Toto je dodatočná kontrola, že token patrí správnemu používateľovi
    if (decodedToken.email && user.email && decodedToken.email !== user.email) {
      console.error('Email v tokene nezodpovedá emailu používateľa v databáze');
      return null;
    }

    return user;
  } catch (error) {
    console.error('Chyba pri overovaní autentifikácie:', error);
    return null;
  }
}



/**
 * Middleware pre overenie autentifikácie používateľa
 * @param handler Handler funkcia, ktorá sa má vykonať, ak je používateľ autentifikovaný
 * @returns NextResponse objekt
 */
export function withAuth(
  handler: (request: NextRequest, user: any) => Promise<NextResponse>
) {
  return async (request: NextRequest) => {
    const user = await verifyAuth(request);

    if (!user) {
      return NextResponse.json(
        { error: 'Neautorizovaný prístup' },
        { status: 401 }
      );
    }

    return handler(request, user);
  };
}

/**
 * Middleware pre overenie autentifikácie používateľa s CSRF ochranou
 * @param handler Handler funkcia, ktorá sa má vykonať, ak je používateľ autentifikovaný a CSRF token je platný
 * @returns NextResponse objekt
 */
export function withAuthAndCsrf(
  handler: (request: NextRequest, user: any) => Promise<NextResponse>
) {
  return withCsrfProtection(async (request: NextRequest) => {
    const user = await verifyAuth(request);

    if (!user) {
      return NextResponse.json(
        { error: 'Neautorizovaný prístup' },
        { status: 401 }
      );
    }

    return handler(request, user);
  });
}

/**
 * Middleware pre overenie, či má používateľ rolu admin
 * @param handler Handler funkcia, ktorá sa má vykonať, ak je používateľ admin
 * @returns NextResponse objekt
 */
export function withAdminAuth(
  handler: (request: NextRequest, user: any) => Promise<NextResponse>
) {
  return async (request: NextRequest) => {
    const user = await verifyAuth(request);

    if (!user) {
      return NextResponse.json(
        { error: 'Neautorizovaný prístup' },
        { status: 401 }
      );
    }

    if (user.role !== 'ADMIN') {
      return NextResponse.json(
        { error: 'Nedostatočné oprávnenia' },
        { status: 403 }
      );
    }

    return handler(request, user);
  };
}

/**
 * Middleware pre overenie, či má používateľ rolu admin s CSRF ochranou
 * @param handler Handler funkcia, ktorá sa má vykonať, ak je používateľ admin a CSRF token je platný
 * @returns NextResponse objekt
 */
export function withAdminAuthAndCsrf(
  handler: (request: NextRequest, user: any) => Promise<NextResponse>
) {
  return withCsrfProtection(async (request: NextRequest) => {
    const user = await verifyAuth(request);

    if (!user) {
      return NextResponse.json(
        { error: 'Neautorizovaný prístup' },
        { status: 401 }
      );
    }

    if (user.role !== 'ADMIN') {
      return NextResponse.json(
        { error: 'Nedostatočné oprávnenia' },
        { status: 403 }
      );
    }

    return handler(request, user);
  });
}
