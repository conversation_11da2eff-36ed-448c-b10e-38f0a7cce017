import { NextRequest, NextResponse } from 'next/server';
import { randomBytes, createHmac } from 'crypto';
import { cookies } from 'next/headers';

// Konštanty pre CSRF ochranu
const CSRF_SECRET = process.env.CSRF_SECRET || randomBytes(32).toString('hex');
const CSRF_COOKIE_NAME = 'swapka_csrf';
const CSRF_HEADER_NAME = 'x-csrf-token';
const CSRF_TOKEN_EXPIRY = 24 * 60 * 60 * 1000; // 24 hodín v milisekundách

/**
 * Generuje CSRF token
 * @returns CSRF token
 */
export function generateCsrfToken(): string {
  // Generovanie náhodného tokenu
  const randomToken = randomBytes(32).toString('hex');
  // Aktuálny čas pre expiráciu
  const timestamp = Date.now();
  // Vytvorenie tokenu vo formáte timestamp|randomToken
  const tokenValue = `${timestamp}|${randomToken}`;
  // Vytvorenie podpisu pomocou HMAC
  const signature = createHmac('sha256', CSRF_SECRET)
    .update(tokenValue)
    .digest('hex');
  // Finálny token vo formáte tokenValue|signature
  return `${tokenValue}|${signature}`;
}

/**
 * Overuje platnosť CSRF tokenu
 * @param token CSRF token na overenie
 * @returns true ak je token platný, inak false
 */
export function verifyCsrfToken(token: string): boolean {
  try {
    // V development móde akceptujeme aj špeciálny testovací token
    if (process.env.NODE_ENV !== 'production' && token === 'test-csrf-token') {
      console.log('Akceptujem testovací CSRF token v development móde');
      return true;
    }

    // V development móde môžeme byť menej striktní
    if (process.env.NODE_ENV !== 'production') {
      console.log('Development mód - menej striktná kontrola CSRF tokenu');
      // Stále kontrolujeme formát tokenu, ale sme menej striktní
    }

    // Rozdelenie tokenu na časti
    const parts = token.split('|');
    if (parts.length !== 3) {
      console.warn(`Neplatný formát CSRF tokenu: ${token}`);
      return false;
    }

    const timestamp = parseInt(parts[0]);
    const randomToken = parts[1];
    const providedSignature = parts[2];

    // Kontrola expirácie tokenu
    const tokenAge = Date.now() - timestamp;
    if (tokenAge > CSRF_TOKEN_EXPIRY) {
      console.warn(`CSRF token expiroval. Vek tokenu: ${tokenAge}ms, limit: ${CSRF_TOKEN_EXPIRY}ms`);
      return false;
    }

    // Vytvorenie očakávaného podpisu
    const tokenValue = `${timestamp}|${randomToken}`;
    const expectedSignature = createHmac('sha256', CSRF_SECRET)
      .update(tokenValue)
      .digest('hex');

    // Porovnanie podpisov
    const isValid = providedSignature === expectedSignature;
    if (!isValid) {
      console.warn('CSRF token má neplatný podpis');
    }

    return isValid;
  } catch (error) {
    console.error('Chyba pri overovaní CSRF tokenu:', error);
    return false;
  }
}

/**
 * Generuje CSRF token pre použitie v cookies
 * @returns Vygenerovaný CSRF token
 */
export function generateCsrfTokenForCookie(): string {
  return generateCsrfToken();
}

/**
 * Nastavuje CSRF token do cookies v response
 * @param response NextResponse objekt, do ktorého sa nastaví cookie
 * @param token CSRF token, ktorý sa má nastaviť do cookies
 * @returns NextResponse objekt s nastaveným cookie
 */
export function setCsrfCookieInResponse(response: NextResponse, token: string): NextResponse {
  // Nastavenie cookie s tokenom
  // Secure: true - cookie sa posiela len cez HTTPS
  // HttpOnly: true - cookie nie je dostupné cez JavaScript
  // SameSite: 'strict' - cookie sa posiela len pri požiadavkách z rovnakej domény
  // Path: '/' - cookie je dostupné na celej doméne
  // MaxAge: 24 hodín v sekundách
  response.cookies.set({
    name: CSRF_COOKIE_NAME,
    value: token,
    httpOnly: true,
    secure: process.env.NODE_ENV === 'production',
    sameSite: 'strict',
    path: '/',
    maxAge: CSRF_TOKEN_EXPIRY / 1000, // Konverzia z milisekúnd na sekundy
  });

  return response;
}

/**
 * Získava CSRF token z cookies
 * @returns CSRF token alebo null, ak cookie neexistuje
 */
export async function getCsrfCookie(): Promise<string | null> {
  try {
    // V Next.js 15 je cookies() asynchrónna funkcia a musí byť použitá s await
    // Ale v development móde môžeme vrátiť null, aby sme neblokovali vývoj
    if (process.env.NODE_ENV !== 'production') {
      console.log('Development mód - ignorujem CSRF cookie');
      return null;
    }

    // V produkčnom prostredí musíme získať cookie
    try {
      const cookieStore = await cookies();
      const csrfCookie = cookieStore.get(CSRF_COOKIE_NAME);

      // Logovanie pre debugovanie
      if (!csrfCookie) {
        console.log('CSRF cookie nebola nájdená');
      } else {
        console.log(`CSRF cookie nájdená: ${csrfCookie.name}=${csrfCookie.value.substring(0, 10)}...`);
      }

      return csrfCookie?.value || null;
    } catch (cookieError) {
      console.error('Chyba pri získavaní CSRF cookie:', cookieError);
      // V produkčnom prostredí vrátime null, aby sme neblokovali požiadavku
      return null;
    }
  } catch (error) {
    console.error('Chyba pri získavaní CSRF cookie:', error);
    return null;
  }
}

/**
 * Middleware pre ochranu proti CSRF
 * @param handler Handler funkcia, ktorá sa má vykonať, ak je CSRF token platný
 * @returns NextResponse objekt
 */
export function withCsrfProtection(
  handler: (request: NextRequest) => Promise<NextResponse>
) {
  return async (request: NextRequest) => {
    // Kontrola, či ide o GET požiadavku (tie nepotrebujú CSRF ochranu)
    if (request.method === 'GET') {
      return handler(request);
    }

    // V development móde môžeme ignorovať CSRF ochranu
    if (process.env.NODE_ENV !== 'production') {
      console.log('Development mód - ignorujem CSRF ochranu');
      return handler(request);
    }

    // Získanie CSRF tokenu z hlavičky
    const csrfToken = request.headers.get(CSRF_HEADER_NAME);
    if (!csrfToken) {
      return NextResponse.json(
        { error: 'Chýba CSRF token' },
        { status: 403 }
      );
    }

    // V produkčnom prostredí overujeme, či token existuje aj v cookies
    if (process.env.NODE_ENV === 'production') {
      try {
        // Získanie CSRF tokenu z cookies
        const csrfCookie = await getCsrfCookie();

        // Kontrola, či cookie existuje
        if (!csrfCookie) {
          console.warn('CSRF cookie chýba v požiadavke');
          return NextResponse.json(
            { error: 'Neplatná CSRF session' },
            { status: 403 }
          );
        }

        // Kontrola, či sa tokeny zhodujú
        if (csrfToken !== csrfCookie) {
          console.warn('CSRF token v hlavičke sa nezhoduje s tokenom v cookie');
          console.warn(`Token v hlavičke: ${csrfToken}`);
          console.warn(`Token v cookie: ${csrfCookie}`);
          return NextResponse.json(
            { error: 'Neplatný CSRF token' },
            { status: 403 }
          );
        }
      } catch (error) {
        console.error('Chyba pri overovaní CSRF cookie:', error);
        return NextResponse.json(
          { error: 'Chyba pri overovaní CSRF ochrany' },
          { status: 500 }
        );
      }
    }

    // Overenie platnosti tokenu (len v produkčnom prostredí)
    if (process.env.NODE_ENV === 'production' && !verifyCsrfToken(csrfToken)) {
      console.warn('CSRF token je neplatný alebo expiroval');
      return NextResponse.json(
        { error: 'Expirovaný alebo neplatný CSRF token' },
        { status: 403 }
      );
    }

    // Ak je token platný, pokračujeme s požiadavkou
    return handler(request);
  };
}
