/**
 * Utility funkcie pre hashovanie a dehashovanie ID
 * Používa sa na skrytie sekvenčných ID v URL adresách a generovanie unikátnych názvov súborov
 *
 * Poznámka: Používa deterministický hash algoritmus kompatibilný s Edge Runtime
 * namiesto Node.js crypto modulu pre lepšiu kompatibilitu.
 */

// Tajný kľúč pre hashovanie - v produkcii by mal byť v .env súbore
const SECRET_KEY = process.env.HASH_SECRET_KEY || 'swapka-secret-key-for-toy-id-hashing';

/**
 * Implementácia SHA-256 like hash algoritmu kompatibilného s Edge Runtime
 * Používa deterministický algoritmus pre konzistentné výsledky
 * @param data Dáta na hashovanie
 * @returns Hex reprezentácia hashu
 */
function createDeterministicHash(data: string): string {
  // Jednoduchá implementácia hash algoritmu podobného SHA-256
  // Nie je kryptograficky bezpečná, ale je deterministická a konzistentná
  let hash = 0x811c9dc5; // FNV offset basis

  for (let i = 0; i < data.length; i++) {
    hash ^= data.charCodeAt(i);
    hash = (hash * 0x01000193) >>> 0; // FNV prime, unsigned 32-bit
  }

  // Rozšírime hash na dlhší string pre lepšiu entropiu
  let extendedHash = hash.toString(16);

  // Pridáme dodatočné iterácie pre dlhší hash
  for (let i = 0; i < 3; i++) {
    let tempHash = 0x811c9dc5;
    const input = extendedHash + data + i;
    for (let j = 0; j < input.length; j++) {
      tempHash ^= input.charCodeAt(j);
      tempHash = (tempHash * 0x01000193) >>> 0;
    }
    extendedHash += tempHash.toString(16);
  }

  return extendedHash.padStart(32, '0');
}

/**
 * Hashuje číselné ID na string, ktorý sa použije v URL
 * @param id Číselné ID na hashovanie
 * @returns Hashovaný string vhodný pre URL
 */
export function hashId(id: number): string {
  if (!id) return '';

  // Konvertujeme ID na string a pridáme tajný kľúč
  const dataToHash = `${id}-${SECRET_KEY}`;

  // Vytvoríme hash pomocou deterministického algoritmu
  const hash = createDeterministicHash(dataToHash);

  // Vezmeme len prvých 12 znakov hashu, aby URL neboli príliš dlhé
  // a pridáme prefix, aby bolo jasné, že ide o hashované ID
  return `h-${hash.substring(0, 12)}`;
}

/**
 * Konvertuje hashované ID späť na číselné ID
 * Keďže hash je jednosmerný, musíme použiť brute-force prístup
 * a skúšať hashovať postupne ID, kým nenájdeme zhodu
 *
 * @param hashedId Hashované ID z URL
 * @param maxIdToCheck Maximálne ID, ktoré sa má skontrolovať (pre optimalizáciu)
 * @returns Pôvodné číselné ID alebo null, ak sa nenašla zhodu
 */
export function dehashId(hashedId: string, maxIdToCheck: number = 100000): number | null {
  if (!hashedId || !hashedId.startsWith('h-')) return null;

  // Odstránime prefix
  const hashWithoutPrefix = hashedId.substring(2);

  // Skúšame postupne hashovať ID od 1 do maxIdToCheck
  for (let i = 1; i <= maxIdToCheck; i++) {
    const currentHash = hashId(i);
    if (currentHash === hashedId) {
      return i;
    }
  }

  return null;
}

/**
 * Kontroluje, či je string hashované ID
 * @param id String na kontrolu
 * @returns true, ak je string hashované ID, inak false
 */
export function isHashedId(id: string): boolean {
  return Boolean(id) && id.startsWith('h-') && id.length === 14; // "h-" + 12 znakov hashu
}

/**
 * Async verzia dehashId funkcie, ktorá môže dynamicky zistiť maximálne ID z databázy
 * @param hashedId Hashované ID z URL
 * @param prismaClient Voliteľný Prisma klient pre zistenie max ID z databázy
 * @returns Pôvodné číselné ID alebo null, ak sa nenašla zhoda
 */
export async function dehashIdAsync(hashedId: string, prismaClient?: any): Promise<number | null> {
  if (!hashedId || !hashedId.startsWith('h-')) return null;

  let maxIdToCheck = 100000; // Default fallback

  // Ak máme prístup k databáze, zistíme skutočné maximálne ID
  if (prismaClient) {
    try {
      const maxUser = await prismaClient.user.findFirst({
        orderBy: { id: 'desc' },
        select: { id: true }
      });
      if (maxUser) {
        maxIdToCheck = Math.max(maxUser.id + 1000, 100000); // Pridáme buffer pre nové záznamy
      }
    } catch (error) {
      console.warn('Nepodarilo sa zistiť maximálne ID z databázy, používam default limit:', error);
    }
  }

  // Skúšame postupne hashovať ID od 1 do maxIdToCheck
  for (let i = 1; i <= maxIdToCheck; i++) {
    const currentHash = hashId(i);
    if (currentHash === hashedId) {
      return i;
    }
  }

  return null;
}

/**
 * Konvertuje ID (číselné alebo hashované) na číselné ID
 * @param id ID na konverziu (číslo alebo hashovaný string)
 * @returns Číselné ID alebo null, ak sa konverzia nepodarila
 */
export function toNumericId(id: string | number): number | null {
  if (typeof id === 'number') return id;

  // Ak je to hashované ID, dekódujeme ho
  if (isHashedId(id)) {
    return dehashId(id);
  }

  // Ak je to číselné ID ako string, konvertujeme ho
  const numericId = parseInt(id);
  return isNaN(numericId) ? null : numericId;
}

/**
 * Async verzia toNumericId funkcie s podporou databázového vyhľadávania
 * @param id ID na konverziu (číslo alebo hashovaný string)
 * @param prismaClient Voliteľný Prisma klient pre optimalizáciu
 * @returns Číselné ID alebo null, ak sa konverzia nepodarila
 */
export async function toNumericIdAsync(id: string | number, prismaClient?: any): Promise<number | null> {
  if (typeof id === 'number') return id;

  // Ak je to hashované ID, dekódujeme ho s databázovou optimalizáciou
  if (isHashedId(id)) {
    return await dehashIdAsync(id, prismaClient);
  }

  // Ak je to číselné ID ako string, konvertujeme ho
  const numericId = parseInt(id);
  return isNaN(numericId) ? null : numericId;
}

/**
 * Generuje unikátny hash pre názov súboru
 * Používa sa na zabezpečenie, aby sa nezachoval pôvodný názov súboru pri nahrávaní
 *
 * @param originalFilename Pôvodný názov súboru (voliteľné)
 * @param userId ID používateľa (voliteľné)
 * @returns Unikátny hash vhodný pre názov súboru
 */
export function generateUniqueFileHash(originalFilename?: string, userId?: string | number): string {
  // Vytvoríme reťazec, ktorý bude obsahovať aktuálny čas, náhodné číslo,
  // a voliteľne pôvodný názov súboru a ID používateľa
  const timestamp = new Date().getTime();
  const randomValue = Math.random().toString();

  // Vytvoríme reťazec na hashovanie
  let dataToHash = `${timestamp}-${randomValue}`;

  // Pridáme pôvodný názov súboru, ak je k dispozícii
  if (originalFilename) {
    dataToHash += `-${originalFilename}`;
  }

  // Pridáme ID používateľa, ak je k dispozícii
  if (userId) {
    dataToHash += `-${userId}`;
  }

  // Pridáme tajný kľúč pre dodatočnú bezpečnosť
  dataToHash += `-${SECRET_KEY}`;

  // Vytvoríme hash pomocou deterministického algoritmu
  const hash = createDeterministicHash(dataToHash);

  // Vrátime prvých 16 znakov hashu, čo by malo byť dostatočne unikátne
  return hash.substring(0, 16);
}

/**
 * Generuje kryptograficky bezpečné náhodné bajty pomocou Web Crypto API alebo fallback
 * @param length Počet bajtov na generovanie
 * @returns Hex reprezentácia náhodných bajtov
 */
function generateSecureRandomBytes(length: number): string {
  try {
    // Pokúsime sa použiť Web Crypto API
    const array = new Uint8Array(length);
    crypto.getRandomValues(array);
    return Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('');
  } catch (error) {
    // Fallback na Math.random() ak Web Crypto API nie je k dispozícii
    let result = '';
    for (let i = 0; i < length; i++) {
      result += Math.floor(Math.random() * 256).toString(16).padStart(2, '0');
    }
    return result;
  }
}

/**
 * Konvertuje string na base64url formát (kompatibilné s Edge Runtime)
 * @param input String na konverziu
 * @returns Base64url string
 */
function stringToBase64Url(input: string): string {
  try {
    return btoa(input)
      .replace(/\+/g, '-')
      .replace(/\//g, '_')
      .replace(/=/g, '');
  } catch (error) {
    // Fallback implementácia ak btoa nie je k dispozícii
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_';
    let result = '';
    for (let i = 0; i < input.length; i += 3) {
      const a = input.charCodeAt(i);
      const b = i + 1 < input.length ? input.charCodeAt(i + 1) : 0;
      const c = i + 2 < input.length ? input.charCodeAt(i + 2) : 0;

      const bitmap = (a << 16) | (b << 8) | c;

      result += chars.charAt((bitmap >> 18) & 63);
      result += chars.charAt((bitmap >> 12) & 63);
      result += i + 1 < input.length ? chars.charAt((bitmap >> 6) & 63) : '';
      result += i + 2 < input.length ? chars.charAt(bitmap & 63) : '';
    }
    return result;
  }
}

/**
 * Generuje bezpečný hash pre názov súboru s rozšírením
 * Táto funkcia je špecificky navrhnutá pre bezpečné ukladanie súborov
 *
 * @param originalFilename Pôvodný názov súboru
 * @param userId ID používateľa
 * @param fileExtension Prípona súboru (napr. 'webp', 'jpg')
 * @returns Objekt s hashovaným názvom súboru a metadátami
 */
export function generateSecureFilename(
  originalFilename: string,
  userId: string | number,
  fileExtension: string
): {
  hashedFilename: string;
  publicId: string;
  fullPath: string;
} {
  // Vytvoríme timestamp s vysokou presnosťou
  const timestamp = Date.now();
  const performanceTime = performance.now().toString();

  // Vytvoríme kryptograficky bezpečný náhodný reťazec
  const randomBytes = generateSecureRandomBytes(16);

  // Sanitizujeme pôvodný názov súboru (odstránime nebezpečné znaky)
  const sanitizedOriginal = originalFilename
    .replace(/[^a-zA-Z0-9.-]/g, '_')
    .substring(0, 50); // Obmedzíme dĺžku

  // Vytvoríme reťazec na hashovanie
  const dataToHash = [
    timestamp,
    performanceTime,
    randomBytes,
    sanitizedOriginal,
    userId,
    SECRET_KEY
  ].join('-');

  // Vytvoríme hash pomocou deterministického algoritmu
  const hash = createDeterministicHash(dataToHash);

  // Vytvoríme URL-safe hash
  const urlSafeHash = stringToBase64Url(hash).substring(0, 24); // 24 znakov pre dostatočnú entropiu

  // Normalizujeme príponu súboru
  const normalizedExtension = fileExtension.toLowerCase().replace(/^\./, '');

  // Vytvoríme finálny hashovaný názov súboru
  const hashedFilename = `${urlSafeHash}.${normalizedExtension}`;

  // Vytvoríme public_id pre Cloudinary (bez prípony)
  const publicId = urlSafeHash;

  // Vytvoríme plnú cestu pre Cloudinary
  const fullPath = `swapka/toys/${publicId}`;

  return {
    hashedFilename,
    publicId,
    fullPath
  };
}
