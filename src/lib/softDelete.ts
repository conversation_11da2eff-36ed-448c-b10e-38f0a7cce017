/**
 * Soft Delete Utility Library
 * 
 * This library provides utility functions and query filters to handle soft delete
 * functionality across the application. It ensures that soft-deleted records
 * are properly excluded from queries unless explicitly requested.
 */

import { Prisma } from '@prisma/client';

/**
 * Base filter to exclude soft-deleted records
 */
export const notDeleted = {
  deletedAt: null
};

/**
 * Filter to include only soft-deleted records
 */
export const onlyDeleted = {
  deletedAt: { not: null }
};

/**
 * User query filters with soft delete support
 */
export const userFilters = {
  // Active users (not deleted, not anonymized, not blocked)
  active: {
    deletedAt: null,
    isAnonymized: false,
    status: 'ACTIVE'
  },
  
  // Not deleted users (includes blocked and anonymized)
  notDeleted: {
    deletedAt: null
  },
  
  // Only deleted users
  deleted: {
    deletedAt: { not: null }
  },
  
  // Public users (for public listings - active and not anonymized)
  public: {
    deletedAt: null,
    isAnonymized: false,
    status: { not: 'BLOCKED' }
  }
};

/**
 * Toy query filters with soft delete support
 */
export const toyFilters = {
  // Available toys (not deleted, available status, owner not deleted/blocked)
  available: {
    deletedAt: null,
    status: 'AVAILABLE',
    user: userFilters.active
  },
  
  // Not deleted toys
  notDeleted: {
    deletedAt: null
  },
  
  // Only deleted toys
  deleted: {
    deletedAt: { not: null }
  },
  
  // Public toys (for public listings)
  public: {
    deletedAt: null,
    status: { in: ['AVAILABLE', 'RESERVED'] },
    user: userFilters.public
  }
};

/**
 * Reservation query filters with soft delete support
 */
export const reservationFilters = {
  // Active reservations (not deleted, not cancelled)
  active: {
    deletedAt: null,
    status: { not: 'CANCELLED' },
    user: userFilters.notDeleted,
    owner: userFilters.notDeleted,
    toy: toyFilters.notDeleted
  },
  
  // Not deleted reservations
  notDeleted: {
    deletedAt: null
  },
  
  // Only deleted reservations
  deleted: {
    deletedAt: { not: null }
  },
  
  // Valid reservations (for user interfaces)
  valid: {
    deletedAt: null,
    user: userFilters.notDeleted,
    owner: userFilters.notDeleted,
    toy: toyFilters.notDeleted
  }
};

/**
 * Utility function to add soft delete filter to any where clause
 */
export function addSoftDeleteFilter<T extends Record<string, any>>(
  where: T,
  includeDeleted: boolean = false
): T & { deletedAt?: any } {
  if (includeDeleted) {
    return where;
  }
  
  return {
    ...where,
    deletedAt: null
  };
}

/**
 * Utility function to create a soft delete update operation
 */
export function createSoftDeleteUpdate(additionalData: Record<string, any> = {}) {
  return {
    deletedAt: new Date(),
    ...additionalData
  };
}

/**
 * Utility function to create a restore operation (undo soft delete)
 */
export function createRestoreUpdate(additionalData: Record<string, any> = {}) {
  return {
    deletedAt: null,
    ...additionalData
  };
}

/**
 * Type-safe soft delete operations for User model
 */
export const userSoftDelete = {
  // Soft delete a user with cascading effects
  async softDelete(userId: number, tx?: Prisma.TransactionClient) {
    const prisma = tx || (await import('./db')).prisma;
    const now = new Date();
    
    return await prisma.$transaction(async (transaction: Prisma.TransactionClient) => {
      // Soft delete user's toys
      await transaction.toy.updateMany({
        where: { userId, deletedAt: null },
        data: createSoftDeleteUpdate({ status: 'UNAVAILABLE' })
      });
      
      // Soft delete user's reservations
      await transaction.reservation.updateMany({
        where: { 
          OR: [{ userId }, { ownerId: userId }],
          deletedAt: null 
        },
        data: createSoftDeleteUpdate({ status: 'CANCELLED' })
      });
      
      // Soft delete the user
      return await transaction.user.update({
        where: { id: userId },
        data: createSoftDeleteUpdate({ status: 'BLOCKED' })
      });
    });
  },
  
  // Restore a soft-deleted user (admin only)
  async restore(userId: number, tx?: Prisma.TransactionClient) {
    const prisma = tx || (await import('./db')).prisma;
    
    return await prisma.user.update({
      where: { id: userId },
      data: createRestoreUpdate({ status: 'ACTIVE' })
    });
  }
};

/**
 * Type-safe soft delete operations for Toy model
 */
export const toySoftDelete = {
  // Soft delete a toy
  async softDelete(toyId: number, tx?: Prisma.TransactionClient) {
    const prisma = tx || (await import('./db')).prisma;
    
    return await prisma.$transaction(async (transaction: Prisma.TransactionClient) => {
      // Soft delete toy's reservations
      await transaction.reservation.updateMany({
        where: { toyId, deletedAt: null },
        data: createSoftDeleteUpdate({ status: 'CANCELLED' })
      });
      
      // Soft delete the toy
      return await transaction.toy.update({
        where: { id: toyId },
        data: createSoftDeleteUpdate({ status: 'UNAVAILABLE' })
      });
    });
  },
  
  // Restore a soft-deleted toy
  async restore(toyId: number, tx?: Prisma.TransactionClient) {
    const prisma = tx || (await import('./db')).prisma;
    
    return await prisma.toy.update({
      where: { id: toyId },
      data: createRestoreUpdate({ status: 'AVAILABLE' })
    });
  }
};

/**
 * Type-safe soft delete operations for Reservation model
 */
export const reservationSoftDelete = {
  // Soft delete a reservation
  async softDelete(reservationId: number, tx?: Prisma.TransactionClient) {
    const prisma = tx || (await import('./db')).prisma;
    
    return await prisma.reservation.update({
      where: { id: reservationId },
      data: createSoftDeleteUpdate({ status: 'CANCELLED' })
    });
  },
  
  // Restore a soft-deleted reservation
  async restore(reservationId: number, tx?: Prisma.TransactionClient) {
    const prisma = tx || (await import('./db')).prisma;
    
    return await prisma.reservation.update({
      where: { id: reservationId },
      data: createRestoreUpdate({ status: 'PENDING' })
    });
  }
};
