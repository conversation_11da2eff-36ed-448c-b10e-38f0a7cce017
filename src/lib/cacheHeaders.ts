/**
 * Cache header utilities for preventing aggressive browser caching
 * Specifically designed to address iPhone/mobile Safari cache issues
 */

export interface CacheConfig {
  maxAge?: number;
  mustRevalidate?: boolean;
  noCache?: boolean;
  noStore?: boolean;
  immutable?: boolean;
  staleWhileRevalidate?: number;
}

/**
 * Generate Cache-Control header value based on configuration
 */
export function generateCacheControl(config: CacheConfig): string {
  const directives: string[] = [];

  if (config.noCache) {
    directives.push('no-cache');
  }

  if (config.noStore) {
    directives.push('no-store');
  }

  if (config.maxAge !== undefined) {
    directives.push(`max-age=${config.maxAge}`);
  }

  if (config.mustRevalidate) {
    directives.push('must-revalidate');
  }

  if (config.immutable) {
    directives.push('immutable');
  }

  if (config.staleWhileRevalidate !== undefined) {
    directives.push(`stale-while-revalidate=${config.staleWhileRevalidate}`);
  }

  // Default to public if not explicitly no-cache/no-store
  if (!config.noCache && !config.noStore) {
    directives.unshift('public');
  }

  return directives.join(', ');
}

/**
 * Predefined cache configurations for different asset types
 */
export const CACHE_CONFIGS = {
  // No caching - for HTML pages and critical application files
  NO_CACHE: {
    noCache: true,
    noStore: true,
    mustRevalidate: true,
    maxAge: 0,
  },

  // Short cache with validation - for JavaScript chunks
  JS_CHUNKS: {
    maxAge: 300, // 5 minutes
    mustRevalidate: true,
    staleWhileRevalidate: 60,
  },

  // Medium cache - for CSS and other assets
  ASSETS: {
    maxAge: 3600, // 1 hour
    mustRevalidate: true,
  },

  // Long cache with immutable - for versioned static files
  STATIC_IMMUTABLE: {
    maxAge: 31536000, // 1 year
    immutable: true,
  },

  // API responses - no caching
  API: {
    noCache: true,
    noStore: true,
    mustRevalidate: true,
    maxAge: 0,
  },
} as const;

/**
 * Generate ETag based on content and timestamp
 */
export function generateETag(content: string, timestamp?: number): string {
  const ts = timestamp || Date.now();
  const hash = simpleHash(content);
  return `"${hash}-${ts}"`;
}

/**
 * Simple hash function for ETag generation
 */
function simpleHash(str: string): string {
  let hash = 0;
  for (let i = 0; i < str.length; i++) {
    const char = str.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash; // Convert to 32-bit integer
  }
  return Math.abs(hash).toString(36);
}

/**
 * Get cache headers for different resource types
 */
export function getCacheHeaders(type: keyof typeof CACHE_CONFIGS, etag?: string) {
  const config = CACHE_CONFIGS[type];
  const headers: Record<string, string> = {
    'Cache-Control': generateCacheControl(config),
  };

  // Add additional headers for no-cache scenarios
  if ('noCache' in config && config.noCache) {
    headers['Pragma'] = 'no-cache';
    headers['Expires'] = '0';
  }

  // Add ETag if provided
  if (etag) {
    headers['ETag'] = etag;
  }

  // Add security headers
  headers['X-Content-Type-Options'] = 'nosniff';

  return headers;
}

/**
 * Check if request has valid cache based on ETag
 */
export function isValidCache(requestETag: string | null, currentETag: string): boolean {
  if (!requestETag || !currentETag) {
    return false;
  }
  return requestETag === currentETag;
}

/**
 * Mobile Safari specific cache prevention headers
 */
export function getMobileSafariHeaders(): Record<string, string> {
  return {
    'X-UA-Compatible': 'IE=edge',
    'X-Mobile-Cache-Control': 'no-cache',
    'X-Webkit-Cache-Control': 'no-cache',
    'Cache-Control': 'no-cache, no-store, must-revalidate, max-age=0',
    'Pragma': 'no-cache',
    'Expires': '0',
  };
}

/**
 * Generate cache-busting query parameter
 */
export function getCacheBustParam(): string {
  return `v=${Date.now()}`;
}

/**
 * Add cache-busting parameter to URL
 */
export function addCacheBust(url: string): string {
  const separator = url.includes('?') ? '&' : '?';
  return `${url}${separator}${getCacheBustParam()}`;
}
