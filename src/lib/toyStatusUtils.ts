import { prisma } from './db';
import { translateToyStatus } from './constants';

export interface ToyStatusOption {
  value: string;
  label: string;
}

/**
 * Validates if a toy status exists in the database
 * @param status The status to validate
 * @returns Promise<boolean> True if status exists, false otherwise
 */
export async function isValidToyStatus(status: string): Promise<boolean> {
  try {
    const result = await prisma.$queryRaw`SELECT name FROM ToyStatus WHERE name = ${status}`;
    return (result as any[]).length > 0;
  } catch (error) {
    console.error('Error validating toy status:', error);
    return false;
  }
}

/**
 * Fetches all toy statuses from the database
 * @returns Promise<ToyStatusOption[]> Array of toy status options
 */
export async function getAllToyStatuses(): Promise<ToyStatusOption[]> {
  try {
    const statuses = await prisma.$queryRaw`SELECT name, label FROM ToyStatus ORDER BY name`;
    
    return (statuses as any[]).map(status => ({
      value: status.name,
      label: status.label || status.name,
    }));
  } catch (error) {
    console.error('Error fetching toy statuses:', error);
    
    // Fallback to hardcoded statuses
    return [
      { value: 'DRAFT', label: translateToyStatus('DRAFT') },
      { value: 'AVAILABLE', label: translateToyStatus('AVAILABLE') },
      { value: 'UNAVAILABLE', label: translateToyStatus('UNAVAILABLE') },
      { value: 'RESERVED', label: translateToyStatus('RESERVED') },
    ];
  }
}

/**
 * Gets the label for a specific toy status
 * @param status The status name
 * @returns Promise<string> The label for the status
 */
export async function getToyStatusLabel(status: string): Promise<string> {
  try {
    const result = await prisma.$queryRaw`SELECT label FROM ToyStatus WHERE name = ${status}`;
    const statusData = result as any[];
    
    if (statusData.length > 0 && statusData[0].label) {
      return statusData[0].label;
    }
    
    // Fallback to hardcoded translation
    return translateToyStatus(status);
  } catch (error) {
    console.error('Error fetching toy status label:', error);
    return translateToyStatus(status);
  }
}
