/**
 * Utility functions for GDPR compliance and user data anonymization
 */

import crypto from 'crypto';
import { prisma } from './db';

/**
 * Generates a random string of specified length
 * @param length Length of the random string
 * @returns Random string
 */
export function generateRandomString(length: number = 10): string {
  return crypto.randomBytes(Math.ceil(length / 2))
    .toString('hex')
    .slice(0, length);
}

// Make sure the SystemLog model is available to Prisma
// If you get errors about SystemLog not being available, you may need to run:
// cd src && npx prisma generate

/**
 * Generates an anonymized email that maintains uniqueness
 * @param userId User ID to include in the anonymized email
 * @returns Anonymized email address
 */
export function generateAnonymizedEmail(userId: number): string {
  const timestamp = Date.now();
  const randomPart = generateRandomString(8);
  return `anonymized-${userId}-${randomPart}@anonymized.com`;
}

/**
 * Generates an anonymized name
 * @returns Anonymized name
 */
export function generateAnonymizedName(): string {
  return `Anonymized User`;
}

/**
 * Generates an anonymized phone number
 * @returns Anonymized phone number
 */
export function generateAnonymizedPhone(): string {
  return '+000000000000';
}

/**
 * Generates an anonymized address
 * @returns Anonymized address
 */
export function generateAnonymizedAddress(): string {
  return 'Anonymized Address';
}

/**
 * Generates an anonymized city
 * @returns Anonymized city
 */
export function generateAnonymizedCity(): string {
  return 'Anonymized City';
}

/**
 * Generates an anonymized postal code
 * @returns Anonymized postal code
 */
export function generateAnonymizedPostalCode(): string {
  return '00000';
}

/**
 * Anonymizes a user's personal data with cascading soft delete
 * @param userId ID of the user to anonymize
 * @param adminId ID of the admin performing the anonymization
 * @returns The anonymized user data
 */
export async function anonymizeUser(userId: number, adminId: number) {
  try {
    // Generate anonymized data
    const anonymizedEmail = generateAnonymizedEmail(userId);
    const anonymizedName = generateAnonymizedName();
    const anonymizedPhone = generateAnonymizedPhone();
    const anonymizedAddress = generateAnonymizedAddress();
    const anonymizedCity = generateAnonymizedCity();
    const anonymizedPostalCode = generateAnonymizedPostalCode();
    const now = new Date();

    // Perform cascading soft delete in a transaction
    const result = await prisma.$transaction(async (tx) => {
      // 1. Soft delete user's toys
      await tx.toy.updateMany({
        where: {
          userId: userId,
          deletedAt: null // Only update toys that aren't already soft deleted
        },
        data: {
          deletedAt: now,
          status: 'UNAVAILABLE' // Also mark as unavailable
        },
      });

      // 2. Soft delete user's reservations (both as requester and owner)
      await tx.reservation.updateMany({
        where: {
          OR: [
            { userId: userId },
            { ownerId: userId }
          ],
          deletedAt: null // Only update reservations that aren't already soft deleted
        },
        data: {
          deletedAt: now,
          status: 'CANCELLED' // Also mark as cancelled
        },
      });

      // 3. Update user with anonymized data and soft delete
      const updatedUser = await tx.user.update({
        where: { id: userId },
        data: {
          email: anonymizedEmail,
          name: anonymizedName,
          phone: anonymizedPhone,
          address: anonymizedAddress,
          city: anonymizedCity,
          postalCode: anonymizedPostalCode,
          isAnonymized: true,
          deletedAt: now,
          status: 'BLOCKED' // Also block the user account
        },
      });

      return updatedUser;
    });

    try {
      // Log the anonymization action with cascading soft delete details
      // We use a separate try/catch to ensure that even if logging fails,
      // the user is still anonymized
      await prisma.systemLog.create({
        data: {
          action: 'USER_ANONYMIZED_WITH_CASCADE',
          targetId: userId,
          targetType: 'USER',
          adminId: adminId,
          details: JSON.stringify({
            timestamp: new Date().toISOString(),
            userId: userId,
            adminId: adminId,
            cascadingActions: [
              'User anonymized and soft deleted',
              'User toys soft deleted and marked unavailable',
              'User reservations soft deleted and cancelled'
            ]
          }),
        },
      });
    } catch (logError) {
      // If SystemLog table doesn't exist yet or there's another issue with logging,
      // we still want the anonymization to succeed
      console.error('Error logging anonymization action:', logError);
      console.warn('User was anonymized but the action was not logged. Make sure to run the database migration.');
    }

    console.log(`Používateľ s ID ${userId} bol úspešne anonymizovaný s kaskádovým soft delete`);
    return result;
  } catch (error) {
    console.error('Error anonymizing user:', error);
    throw error;
  }
}
