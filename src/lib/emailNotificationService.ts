import {
  sendReservationCreatedEmail,
  sendReservationApprovedEmail,
  sendReservationR<PERSON>questEmail,
  ReservationCreatedEmailData,
  ReservationApprovedEmailData,
  ReservationRequestEmailData,
  EmailResult,
  validateEmailConfig
} from './emailService';
import { shouldSendEmailNotification } from './emailPreferences';
import { prisma } from './db';

// Email notification types
export enum EmailNotificationType {
  RESERVATION_CREATED = 'RESERVATION_CREATED',
  RESERVATION_APPROVED = 'RESERVATION_APPROVED',
  RESERVATION_REQUEST = 'RESERVATION_REQUEST'
}

// Email notification queue item
export interface EmailNotificationItem {
  id: string;
  type: EmailNotificationType;
  data: ReservationCreatedEmailData | ReservationApprovedEmailData | ReservationRequestEmailData;
  retryCount: number;
  maxRetries: number;
  createdAt: Date;
  scheduledAt: Date;
}

// Email notification service configuration
const EMAIL_CONFIG = {
  maxRetries: 3,
  retryDelayMs: 5000, // 5 seconds
  maxRetryDelayMs: 60000, // 1 minute
  enabled: process.env.NODE_ENV !== 'test' // Disable in test environment
};

// In-memory queue for email notifications (in production, consider using Redis or a proper queue)
const emailQueue: EmailNotificationItem[] = [];
let isProcessingQueue = false;

/**
 * Generates a unique ID for email notifications
 */
function generateEmailId(): string {
  return `email_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

/**
 * Calculates retry delay with exponential backoff
 */
function calculateRetryDelay(retryCount: number): number {
  const baseDelay = EMAIL_CONFIG.retryDelayMs;
  const exponentialDelay = baseDelay * Math.pow(2, retryCount);
  return Math.min(exponentialDelay, EMAIL_CONFIG.maxRetryDelayMs);
}

/**
 * Anonymizes email address for privacy logging
 */
function anonymizeEmail(email: string): string {
  const [localPart, domain] = email.split('@');
  if (!localPart || !domain) return '<EMAIL>';

  const anonymizedLocal = localPart.length > 2
    ? localPart.substring(0, 2) + '*'.repeat(Math.max(1, localPart.length - 2))
    : localPart.substring(0, 1) + '*';

  return `${anonymizedLocal}@${domain}`;
}

/**
 * Logs email attempt to database
 */
async function logEmailAttempt(
  emailId: string,
  emailType: EmailNotificationType,
  recipientEmail: string,
  recipientUserId: number,
  status: 'QUEUED' | 'SENT' | 'FAILED' | 'RETRY',
  attempt: number = 1,
  maxAttempts: number = 3,
  errorMessage?: string,
  messageId?: string,
  processingTime?: number
): Promise<void> {
  try {
    const now = new Date();
    const anonymizedEmail = anonymizeEmail(recipientEmail);

    // Check if log entry already exists
    const existingLog = await prisma.emailLog.findFirst({
      where: {
        emailId: emailId,
        attempt: attempt
      }
    });

    if (existingLog) {
      // Update existing log entry
      await prisma.emailLog.update({
        where: { id: existingLog.id },
        data: {
          status,
          errorMessage,
          messageId,
          processingTime,
          sentAt: status === 'SENT' ? now : undefined,
          failedAt: status === 'FAILED' ? now : undefined,
          updatedAt: now
        }
      });
    } else {
      // Create new log entry
      await prisma.emailLog.create({
        data: {
          emailId,
          emailType: emailType.toString(),
          recipientEmail: anonymizedEmail,
          recipientUserId,
          status,
          attempt,
          maxAttempts,
          errorMessage,
          messageId,
          processingTime,
          queuedAt: status === 'QUEUED' ? now : undefined,
          sentAt: status === 'SENT' ? now : undefined,
          failedAt: status === 'FAILED' ? now : undefined
        }
      });
    }
  } catch (error) {
    console.error('Error logging email attempt:', error);
    // Don't throw error to avoid breaking email processing
  }
}

/**
 * Adds an email notification to the queue
 */
export function queueEmailNotification(
  type: EmailNotificationType,
  data: ReservationCreatedEmailData | ReservationApprovedEmailData | ReservationRequestEmailData
): string {
  if (!EMAIL_CONFIG.enabled) {
    console.log('Email notifications disabled in test environment');
    return 'test_email_id';
  }

  if (!validateEmailConfig()) {
    console.error('Email configuration is invalid, skipping email notification');
    return '';
  }

  const emailId = generateEmailId();
  const now = new Date();

  const emailItem: EmailNotificationItem = {
    id: emailId,
    type,
    data,
    retryCount: 0,
    maxRetries: EMAIL_CONFIG.maxRetries,
    createdAt: now,
    scheduledAt: now
  };

  emailQueue.push(emailItem);
  console.log(`Email notification queued: ${emailId} (type: ${type})`);

  // Log email queuing to database
  // Handle different email data types for logging
  const recipientEmail = 'userEmail' in data ? data.userEmail : data.ownerEmail;
  const recipientUserId = 'userId' in data ? data.userId : data.ownerId;

  logEmailAttempt(
    emailId,
    type,
    recipientEmail,
    recipientUserId,
    'QUEUED',
    1,
    EMAIL_CONFIG.maxRetries
  ).catch(error => {
    console.error('Failed to log email queuing:', error);
  });

  // Start processing queue if not already running
  if (!isProcessingQueue) {
    processEmailQueue();
  }

  return emailId;
}

/**
 * Processes the email queue
 */
async function processEmailQueue(): Promise<void> {
  if (isProcessingQueue) {
    return;
  }

  isProcessingQueue = true;
  console.log('Starting email queue processing...');

  try {
    while (emailQueue.length > 0) {
      const now = new Date();
      
      // Find emails that are ready to be sent
      const readyEmails = emailQueue.filter(email => email.scheduledAt <= now);
      
      if (readyEmails.length === 0) {
        // No emails ready, wait a bit and check again
        await new Promise(resolve => setTimeout(resolve, 1000));
        continue;
      }

      // Process ready emails
      for (const emailItem of readyEmails) {
        // Declare recipient variables outside try block for catch block access
        let recipientUserId: number = 0;
        let recipientEmail: string = '';

        try {
          console.log(`Processing email: ${emailItem.id} (attempt ${emailItem.retryCount + 1})`);

          // Check user preferences before sending
          const emailData = emailItem.data as ReservationCreatedEmailData | ReservationApprovedEmailData | ReservationRequestEmailData;
          let shouldSend = false;

          switch (emailItem.type) {
            case EmailNotificationType.RESERVATION_CREATED:
              recipientUserId = (emailData as ReservationCreatedEmailData).userId;
              recipientEmail = (emailData as ReservationCreatedEmailData).userEmail;
              shouldSend = await shouldSendEmailNotification(recipientUserId, 'reservation_created');
              break;
            case EmailNotificationType.RESERVATION_APPROVED:
              recipientUserId = (emailData as ReservationApprovedEmailData).userId;
              recipientEmail = (emailData as ReservationApprovedEmailData).userEmail;
              shouldSend = await shouldSendEmailNotification(recipientUserId, 'reservation_approved');
              break;
            case EmailNotificationType.RESERVATION_REQUEST:
              recipientUserId = (emailData as ReservationRequestEmailData).ownerId;
              recipientEmail = (emailData as ReservationRequestEmailData).ownerEmail;
              shouldSend = await shouldSendEmailNotification(recipientUserId, 'reservation_request');
              break;
          }

          if (!shouldSend) {
            console.log(`User ${recipientUserId} has disabled this type of email notification, skipping`);
            // Remove from queue without sending
            const index = emailQueue.findIndex(e => e.id === emailItem.id);
            if (index !== -1) {
              emailQueue.splice(index, 1);
            }
            continue;
          }

          let result: EmailResult;

          switch (emailItem.type) {
            case EmailNotificationType.RESERVATION_CREATED:
              result = await sendReservationCreatedEmail(emailItem.data as ReservationCreatedEmailData);
              break;
            case EmailNotificationType.RESERVATION_APPROVED:
              result = await sendReservationApprovedEmail(emailItem.data as ReservationApprovedEmailData);
              break;
            case EmailNotificationType.RESERVATION_REQUEST:
              result = await sendReservationRequestEmail(emailItem.data as ReservationRequestEmailData);
              break;
            default:
              console.error(`Unknown email type: ${emailItem.type}`);
              result = { success: false, error: 'Unknown email type' };
          }

          if (result.success) {
            console.log(`Email sent successfully: ${emailItem.id} (messageId: ${result.messageId})`);

            // Log successful email sending
            await logEmailAttempt(
              emailItem.id,
              emailItem.type,
              recipientEmail,
              recipientUserId,
              'SENT',
              emailItem.retryCount + 1,
              emailItem.maxRetries,
              undefined,
              result.messageId
            );

            // Remove from queue
            const index = emailQueue.findIndex(e => e.id === emailItem.id);
            if (index !== -1) {
              emailQueue.splice(index, 1);
            }
          } else {
            console.error(`Email sending failed: ${emailItem.id} - ${result.error}`);

            // Handle retry logic
            emailItem.retryCount++;

            if (emailItem.retryCount >= emailItem.maxRetries) {
              console.error(`Email failed permanently after ${emailItem.retryCount} attempts: ${emailItem.id}`);

              // Log final failure
              await logEmailAttempt(
                emailItem.id,
                emailItem.type,
                recipientEmail,
                recipientUserId,
                'FAILED',
                emailItem.retryCount,
                emailItem.maxRetries,
                result.error
              );

              // Remove from queue after max retries
              const index = emailQueue.findIndex(e => e.id === emailItem.id);
              if (index !== -1) {
                emailQueue.splice(index, 1);
              }
            } else {
              // Log retry attempt
              await logEmailAttempt(
                emailItem.id,
                emailItem.type,
                recipientEmail,
                recipientUserId,
                'RETRY',
                emailItem.retryCount,
                emailItem.maxRetries,
                result.error
              );

              // Schedule retry
              const retryDelay = calculateRetryDelay(emailItem.retryCount);
              emailItem.scheduledAt = new Date(Date.now() + retryDelay);
              console.log(`Email retry scheduled: ${emailItem.id} in ${retryDelay}ms (attempt ${emailItem.retryCount + 1}/${emailItem.maxRetries})`);
            }
          }
        } catch (error) {
          console.error(`Unexpected error processing email ${emailItem.id}:`, error);

          // Handle unexpected errors similar to failed sends
          emailItem.retryCount++;
          const errorMessage = error instanceof Error ? error.message : 'Unexpected error occurred';

          if (emailItem.retryCount >= emailItem.maxRetries) {
            console.error(`Email failed permanently due to unexpected error: ${emailItem.id}`);

            // Log final failure due to unexpected error
            await logEmailAttempt(
              emailItem.id,
              emailItem.type,
              recipientEmail,
              recipientUserId,
              'FAILED',
              emailItem.retryCount,
              emailItem.maxRetries,
              errorMessage
            );

            const index = emailQueue.findIndex(e => e.id === emailItem.id);
            if (index !== -1) {
              emailQueue.splice(index, 1);
            }
          } else {
            // Log retry attempt due to unexpected error
            await logEmailAttempt(
              emailItem.id,
              emailItem.type,
              recipientEmail,
              recipientUserId,
              'RETRY',
              emailItem.retryCount,
              emailItem.maxRetries,
              errorMessage
            );

            const retryDelay = calculateRetryDelay(emailItem.retryCount);
            emailItem.scheduledAt = new Date(Date.now() + retryDelay);
            console.log(`Email retry scheduled after error: ${emailItem.id} in ${retryDelay}ms`);
          }
        }
      }
    }
  } catch (error) {
    console.error('Error in email queue processing:', error);
  } finally {
    isProcessingQueue = false;
    console.log('Email queue processing completed');
  }
}

/**
 * Queues a reservation created email notification
 */
export function queueReservationCreatedEmail(data: ReservationCreatedEmailData): string {
  return queueEmailNotification(EmailNotificationType.RESERVATION_CREATED, data);
}

/**
 * Queues a reservation approved email notification
 */
export function queueReservationApprovedEmail(data: ReservationApprovedEmailData): string {
  return queueEmailNotification(EmailNotificationType.RESERVATION_APPROVED, data);
}

/**
 * Queues a reservation request email notification to toy owner
 */
export function queueReservationRequestEmail(data: ReservationRequestEmailData): string {
  return queueEmailNotification(EmailNotificationType.RESERVATION_REQUEST, data);
}

/**
 * Gets the current queue status (for monitoring/debugging)
 */
export function getEmailQueueStatus(): {
  queueLength: number;
  isProcessing: boolean;
  pendingEmails: Array<{
    id: string;
    type: string;
    retryCount: number;
    scheduledAt: Date;
  }>;
} {
  return {
    queueLength: emailQueue.length,
    isProcessing: isProcessingQueue,
    pendingEmails: emailQueue.map(email => ({
      id: email.id,
      type: email.type,
      retryCount: email.retryCount,
      scheduledAt: email.scheduledAt
    }))
  };
}

/**
 * Clears the email queue (for testing purposes)
 */
export function clearEmailQueue(): void {
  emailQueue.length = 0;
  console.log('Email queue cleared');
}

/**
 * Forces processing of the email queue (for testing purposes)
 */
export async function forceProcessEmailQueue(): Promise<void> {
  if (!isProcessingQueue) {
    await processEmailQueue();
  }
}

/**
 * Gets email statistics for admin monitoring
 */
export async function getEmailStatistics(): Promise<{
  totalEmails: number;
  sentEmails: number;
  failedEmails: number;
  pendingEmails: number;
  successRate: number;
  dailyStats: {
    date: string;
    sent: number;
    failed: number;
  }[];
  weeklyStats: {
    week: string;
    sent: number;
    failed: number;
  }[];
  monthlyStats: {
    month: string;
    sent: number;
    failed: number;
  }[];
  emailTypeBreakdown: {
    type: string;
    sent: number;
    failed: number;
  }[];
}> {
  try {
    // Get total counts
    const totalEmails = await prisma.emailLog.count();
    const sentEmails = await prisma.emailLog.count({
      where: { status: 'SENT' }
    });
    const failedEmails = await prisma.emailLog.count({
      where: { status: 'FAILED' }
    });
    const pendingEmails = emailQueue.length;

    const successRate = totalEmails > 0 ? (sentEmails / totalEmails) * 100 : 0;

    // Get daily stats for last 7 days
    const sevenDaysAgo = new Date();
    sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);

    const dailyStats = await prisma.$queryRaw<Array<{
      date: string;
      sent: bigint;
      failed: bigint;
    }>>`
      SELECT
        DATE(createdAt) as date,
        COUNT(CASE WHEN status = 'SENT' THEN 1 END) as sent,
        COUNT(CASE WHEN status = 'FAILED' THEN 1 END) as failed
      FROM EmailLog
      WHERE createdAt >= ${sevenDaysAgo}
      GROUP BY DATE(createdAt)
      ORDER BY date DESC
    `;

    // Get weekly stats for last 4 weeks
    const fourWeeksAgo = new Date();
    fourWeeksAgo.setDate(fourWeeksAgo.getDate() - 28);

    const weeklyStats = await prisma.$queryRaw<Array<{
      week: string;
      sent: bigint;
      failed: bigint;
    }>>`
      SELECT
        CONCAT(YEAR(createdAt), '-W', LPAD(WEEK(createdAt), 2, '0')) as week,
        COUNT(CASE WHEN status = 'SENT' THEN 1 END) as sent,
        COUNT(CASE WHEN status = 'FAILED' THEN 1 END) as failed
      FROM EmailLog
      WHERE createdAt >= ${fourWeeksAgo}
      GROUP BY YEAR(createdAt), WEEK(createdAt)
      ORDER BY week DESC
    `;

    // Get monthly stats for last 6 months
    const sixMonthsAgo = new Date();
    sixMonthsAgo.setMonth(sixMonthsAgo.getMonth() - 6);

    const monthlyStats = await prisma.$queryRaw<Array<{
      month: string;
      sent: bigint;
      failed: bigint;
    }>>`
      SELECT
        DATE_FORMAT(createdAt, '%Y-%m') as month,
        COUNT(CASE WHEN status = 'SENT' THEN 1 END) as sent,
        COUNT(CASE WHEN status = 'FAILED' THEN 1 END) as failed
      FROM EmailLog
      WHERE createdAt >= ${sixMonthsAgo}
      GROUP BY YEAR(createdAt), MONTH(createdAt)
      ORDER BY month DESC
    `;

    // Get email type breakdown
    const emailTypeBreakdown = await prisma.$queryRaw<Array<{
      type: string;
      sent: bigint;
      failed: bigint;
    }>>`
      SELECT
        emailType as type,
        COUNT(CASE WHEN status = 'SENT' THEN 1 END) as sent,
        COUNT(CASE WHEN status = 'FAILED' THEN 1 END) as failed
      FROM EmailLog
      GROUP BY emailType
      ORDER BY type
    `;

    return {
      totalEmails,
      sentEmails,
      failedEmails,
      pendingEmails,
      successRate: Math.round(successRate * 100) / 100,
      dailyStats: dailyStats.map(stat => ({
        date: stat.date,
        sent: Number(stat.sent),
        failed: Number(stat.failed)
      })),
      weeklyStats: weeklyStats.map(stat => ({
        week: stat.week,
        sent: Number(stat.sent),
        failed: Number(stat.failed)
      })),
      monthlyStats: monthlyStats.map(stat => ({
        month: stat.month,
        sent: Number(stat.sent),
        failed: Number(stat.failed)
      })),
      emailTypeBreakdown: emailTypeBreakdown.map(stat => ({
        type: stat.type,
        sent: Number(stat.sent),
        failed: Number(stat.failed)
      }))
    };
  } catch (error) {
    console.error('Error getting email statistics:', error);
    return {
      totalEmails: 0,
      sentEmails: 0,
      failedEmails: 0,
      pendingEmails: emailQueue.length,
      successRate: 0,
      dailyStats: [],
      weeklyStats: [],
      monthlyStats: [],
      emailTypeBreakdown: []
    };
  }
}

/**
 * Gets recent email logs for admin monitoring
 */
export async function getRecentEmailLogs(limit: number = 50): Promise<Array<{
  id: number;
  emailId: string;
  emailType: string;
  recipientEmail: string;
  status: string;
  attempt: number;
  maxAttempts: number;
  errorMessage: string | null;
  messageId: string | null;
  queuedAt: Date;
  sentAt: Date | null;
  failedAt: Date | null;
  createdAt: Date;
}>> {
  try {
    const logs = await prisma.emailLog.findMany({
      orderBy: { createdAt: 'desc' },
      take: limit,
      select: {
        id: true,
        emailId: true,
        emailType: true,
        recipientEmail: true,
        status: true,
        attempt: true,
        maxAttempts: true,
        errorMessage: true,
        messageId: true,
        queuedAt: true,
        sentAt: true,
        failedAt: true,
        createdAt: true
      }
    });

    return logs;
  } catch (error) {
    console.error('Error getting recent email logs:', error);
    return [];
  }
}
