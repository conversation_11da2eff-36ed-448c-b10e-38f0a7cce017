/**
 * Empty module fallback for Turbopack compatibility
 * 
 * This file serves as a fallback for modules that are not compatible
 * with Turbopack in development mode. It exports empty functions
 * and objects to prevent module resolution errors.
 */

// Export empty function as default
module.exports = function() {};

// Export empty object for named exports
module.exports.default = function() {};

// Common export patterns
module.exports.split = function() { return []; };
module.exports.createHook = function() { return function() {}; };
module.exports.register = function() {};
module.exports.unregister = function() {};

// For security agent
module.exports.Agent = function() {};
module.exports.SecurityAgent = function() {};
