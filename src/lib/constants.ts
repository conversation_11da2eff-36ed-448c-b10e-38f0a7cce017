// Enum pre status hračky
export const ToyStatus = {
  AVAILABLE: 'AVAILABLE',
  RESERVED: 'RESERVED',
  UNAVAILABLE: 'UNAVAILABLE',
  DRAFT: 'DRAFT',
} as const;

// Enum pre typy hračiek
export const ToyType = {
  EDUCATIONAL: 'EDUCATIONAL',
  PLUSH: 'PLUSH',
  BUILDING: 'BUILDING',
  ROLEPLAY: 'ROLEPLAY',
  CARS: 'CARS',
  DOLLS: 'DOLLS',
  OUTDOOR: 'OUTDOOR',
  BOARD_GAMES: 'BOARD_GAMES',
  ELECTRONIC: 'ELECTRONIC',
  OTHER: 'OTHER',
} as const;

// Enum pre status rezervácie
export const ReservationStatus = {
  PENDING: 'PENDING',
  CONFIRMED: 'CONFIRMED',
  ACTIVE: 'ACTIVE',
  COMPLETED: 'COMPLETED',
  CANCELLED: 'CANCELLED',
} as const;

// Enum pre role používateľov
export const UserRole = {
  USER: 'USER',
  ADMIN: 'ADMIN',
} as const;

// Funkcia pre preklad statusu hračky - teraz používa dáta z databázy
// Táto funkcia je zachovaná pre spätná kompatibilita, ale odporúča sa používať statusLabel z API
export function translateToyStatus(status: string): string {
  // Fallback translations for cases where database labels are not available
  const fallbackTranslations: { [key: string]: string } = {
    AVAILABLE: 'Dostupná',
    RESERVED: 'Rezervovaná',
    UNAVAILABLE: 'Nedostupná',
    DRAFT: 'Koncept',
  };

  return fallbackTranslations[status] || status;
}

// Funkcia pre získanie CSS tried pre status hračky
export function getToyStatusClasses(status: string): string {
  switch (status) {
    case 'AVAILABLE':
      return 'bg-green-100 text-green-800';
    case 'RESERVED':
      return 'bg-yellow-100 text-yellow-800';
    case 'UNAVAILABLE':
      return 'bg-red-100 text-red-800';
    case 'DRAFT':
      return 'bg-gray-100 text-gray-800';
    default:
      return 'bg-gray-100 text-gray-800';
  }
}
