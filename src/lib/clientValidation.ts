/**
 * Client-side validačné funkcie pre formuláre
 * Tieto funkcie poskytujú okamžitú spätnú väzbu používateľom
 * POZOR: Server-side validácia je stále povinná!
 * 
 * <AUTHOR> Security Team
 * @version 1.0.0
 */

export interface ClientValidationResult {
  isValid: boolean;
  errors: string[];
  warnings?: string[];
}

/**
 * Validácia emailovej adresy na client-side
 */
export function validateEmailClient(email: string): ClientValidationResult {
  const errors: string[] = [];
  
  if (!email) {
    errors.push('Email je povinný');
    return { isValid: false, errors };
  }

  if (email.length > 254) {
    errors.push('Email je príli<PERSON> dlhý');
  }

  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  if (!emailRegex.test(email)) {
    errors.push('Neplatný formát emailu');
  }

  return { isValid: errors.length === 0, errors };
}

/**
 * Valid<PERSON><PERSON> hesla na client-side
 */
export function validatePasswordClient(password: string): ClientValidationResult {
  const errors: string[] = [];
  const warnings: string[] = [];

  if (!password) {
    errors.push('Heslo je povinné');
    return { isValid: false, errors };
  }

  if (password.length < 8) {
    errors.push('Heslo musí mať aspoň 8 znakov');
  }

  if (password.length > 128) {
    errors.push('Heslo je príliš dlhé');
  }

  if (!/[A-Z]/.test(password)) {
    warnings.push('Heslo by malo obsahovať aspoň jedno veľké písmeno');
  }

  if (!/[a-z]/.test(password)) {
    warnings.push('Heslo by malo obsahovať aspoň jedno malé písmeno');
  }

  if (!/[0-9]/.test(password)) {
    warnings.push('Heslo by malo obsahovať aspoň jednu číslicu');
  }

  if (!/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
    warnings.push('Heslo by malo obsahovať aspoň jeden špeciálny znak');
  }

  return { isValid: errors.length === 0, errors, warnings };
}

/**
 * Validácia mena na client-side
 */
export function validateNameClient(name: string): ClientValidationResult {
  const errors: string[] = [];

  if (!name) {
    errors.push('Meno je povinné');
    return { isValid: false, errors };
  }

  if (name.length < 2) {
    errors.push('Meno musí mať aspoň 2 znaky');
  }

  if (name.length > 100) {
    errors.push('Meno je príliš dlhé');
  }

  if (!/^[a-zA-ZáäčďéíĺľňóôŕšťúýžÁÄČĎÉÍĹĽŇÓÔŔŠŤÚÝŽ\s\-']+$/.test(name)) {
    errors.push('Meno obsahuje nepovolené znaky');
  }

  return { isValid: errors.length === 0, errors };
}

/**
 * Validácia telefónneho čísla na client-side
 */
export function validatePhoneClient(phone: string): ClientValidationResult {
  const errors: string[] = [];

  if (!phone) {
    return { isValid: true, errors }; // Telefón nie je povinný
  }

  const cleanPhone = phone.replace(/[\s\-()]/g, '');
  
  if (cleanPhone.length < 7) {
    errors.push('Telefónne číslo je príliš krátke');
  }

  if (cleanPhone.length > 20) {
    errors.push('Telefónne číslo je príliš dlhé');
  }

  if (!/^[\+]?[0-9]+$/.test(cleanPhone)) {
    errors.push('Telefónne číslo obsahuje nepovolené znaky');
  }

  return { isValid: errors.length === 0, errors };
}

/**
 * Validácia adresy na client-side
 */
export function validateAddressClient(address: string): ClientValidationResult {
  const errors: string[] = [];

  if (!address) {
    return { isValid: true, errors }; // Adresa nie je povinná
  }

  if (address.length > 200) {
    errors.push('Adresa je príliš dlhá');
  }

  if (address.length < 5) {
    errors.push('Adresa je príliš krátka');
  }

  return { isValid: errors.length === 0, errors };
}

/**
 * Validácia mesta na client-side
 */
export function validateCityClient(city: string): ClientValidationResult {
  const errors: string[] = [];

  if (!city) {
    return { isValid: true, errors }; // Mesto nie je povinné
  }

  if (city.length > 100) {
    errors.push('Názov mesta je príliš dlhý');
  }

  if (city.length < 2) {
    errors.push('Názov mesta je príliš krátky');
  }

  if (!/^[a-zA-ZáäčďéíĺľňóôŕšťúýžÁÄČĎÉÍĹĽŇÓÔŔŠŤÚÝŽ\s\-']+$/.test(city)) {
    errors.push('Názov mesta obsahuje nepovolené znaky');
  }

  return { isValid: errors.length === 0, errors };
}

/**
 * Validácia PSČ na client-side
 */
export function validatePostalCodeClient(postalCode: string): ClientValidationResult {
  const errors: string[] = [];

  if (!postalCode) {
    return { isValid: true, errors }; // PSČ nie je povinné
  }

  const cleanCode = postalCode.replace(/\s/g, '');
  
  if (!/^[0-9]{5}$/.test(cleanCode)) {
    errors.push('PSČ musí mať formát 12345');
  }

  return { isValid: errors.length === 0, errors };
}

/**
 * Validácia súradníc na client-side
 */
export function validateCoordinatesClient(
  latitude?: number, 
  longitude?: number
): ClientValidationResult {
  const errors: string[] = [];

  if (latitude !== undefined) {
    if (latitude < -90 || latitude > 90) {
      errors.push('Zemepisná šírka musí byť medzi -90 a 90');
    }
  }

  if (longitude !== undefined) {
    if (longitude < -180 || longitude > 180) {
      errors.push('Zemepisná dĺžka musí byť medzi -180 a 180');
    }
  }

  return { isValid: errors.length === 0, errors };
}

/**
 * Validácia názvu hračky na client-side
 */
export function validateToyNameClient(name: string): ClientValidationResult {
  const errors: string[] = [];

  if (!name) {
    errors.push('Názov hračky je povinný');
    return { isValid: false, errors };
  }

  if (name.length < 3) {
    errors.push('Názov hračky musí mať aspoň 3 znaky');
  }

  if (name.length > 100) {
    errors.push('Názov hračky je príliš dlhý');
  }

  return { isValid: errors.length === 0, errors };
}

/**
 * Validácia popisu hračky na client-side
 */
export function validateToyDescriptionClient(description: string): ClientValidationResult {
  const errors: string[] = [];

  if (!description) {
    errors.push('Popis hračky je povinný');
    return { isValid: false, errors };
  }

  if (description.length < 10) {
    errors.push('Popis hračky musí mať aspoň 10 znakov');
  }

  if (description.length > 2000) {
    errors.push('Popis hračky je príliš dlhý');
  }

  return { isValid: errors.length === 0, errors };
}

/**
 * Validácia ceny na client-side
 */
export function validatePriceClient(price: number): ClientValidationResult {
  const errors: string[] = [];

  if (price === undefined || price === null) {
    errors.push('Cena je povinná');
    return { isValid: false, errors };
  }

  if (price < 0) {
    errors.push('Cena nemôže byť záporná');
  }

  if (price > 10000) {
    errors.push('Cena je príliš vysoká');
  }

  return { isValid: errors.length === 0, errors };
}

/**
 * Validácia súboru na client-side
 */
export function validateFileClient(file: File): ClientValidationResult {
  const errors: string[] = [];
  const warnings: string[] = [];

  if (!file) {
    errors.push('Súbor je povinný');
    return { isValid: false, errors };
  }

  // Kontrola typu súboru
  const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/jpg'];
  if (!allowedTypes.includes(file.type)) {
    errors.push('Nepovolený typ súboru. Povolené sú: JPG, PNG, GIF');
  }

  // Kontrola veľkosti súboru
  const maxSize = 5 * 1024 * 1024; // 5MB
  if (file.size > maxSize) {
    errors.push('Súbor je príliš veľký. Maximálna veľkosť je 5MB');
  }

  const minSize = 1024; // 1KB
  if (file.size < minSize) {
    errors.push('Súbor je príliš malý');
  }

  // Varovania pre optimalizáciu
  if (file.size > 2 * 1024 * 1024) { // 2MB
    warnings.push('Súbor je veľký. Zvážte kompresiu pre rýchlejšie nahrávanie');
  }

  return { isValid: errors.length === 0, errors, warnings };
}

/**
 * Validácia vyhľadávacieho dotazu na client-side
 */
export function validateSearchQueryClient(query: string): ClientValidationResult {
  const errors: string[] = [];

  if (!query) {
    return { isValid: true, errors }; // Prázdny dotaz je povolený
  }

  if (query.length > 100) {
    errors.push('Vyhľadávací dotaz je príliš dlhý');
  }

  if (query.length < 2) {
    errors.push('Vyhľadávací dotaz musí mať aspoň 2 znaky');
  }

  return { isValid: errors.length === 0, errors };
}
