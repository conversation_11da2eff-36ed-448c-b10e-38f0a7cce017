/**
 * Utility functions for user name formatting and anonymization
 * Provides consistent name display across the application for privacy protection
 */

/**
 * Anonymizes a full name by showing only first name + first letter of surname + asterisks
 * Example: "<PERSON><PERSON>" → "<PERSON><PERSON>**"
 * Example: "<PERSON>" → "<PERSON>" (single name remains unchanged)
 * Example: "<PERSON>" → "<PERSON> S***" (uses first and last name)
 * 
 * @param fullName - The complete name to anonymize
 * @returns Anonymized name string
 */
export function anonymizeName(fullName: string): string {
  if (!fullName || typeof fullName !== 'string') {
    return 'Neznámy používateľ';
  }

  // Trim and split the name into parts
  const nameParts = fullName.trim().split(/\s+/).filter(part => part.length > 0);
  
  if (nameParts.length === 0) {
    return 'Neznámy používateľ';
  }
  
  if (nameParts.length === 1) {
    // Single name - return as is (could be first name only)
    return nameParts[0];
  }
  
  // Multiple names - use first name + first letter of last name + asterisks
  const firstName = nameParts[0];
  const lastName = nameParts[nameParts.length - 1]; // Use the last part as surname
  const lastNameInitial = lastName.charAt(0).toUpperCase();
  
  return `${firstName} ${lastNameInitial}***`;
}

/**
 * Checks if a name appears to be already anonymized
 * @param name - Name to check
 * @returns True if the name appears to be anonymized
 */
export function isAnonymizedName(name: string): boolean {
  if (!name || typeof name !== 'string') {
    return false;
  }
  
  // Check if name contains asterisks (indicating it's already anonymized)
  return name.includes('***') || name === 'Neznámy používateľ' || name === 'Anonymized User';
}

/**
 * Safely anonymizes a name, avoiding double anonymization
 * @param fullName - The name to anonymize
 * @returns Anonymized name, or original if already anonymized
 */
export function safeAnonymizeName(fullName: string): string {
  if (isAnonymizedName(fullName)) {
    return fullName;
  }
  
  return anonymizeName(fullName);
}
