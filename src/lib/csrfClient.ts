'use client';

// Kon<PERSON><PERSON>y pre CSRF ochranu
const CSRF_HEADER_NAME = 'x-csrf-token';
let csrfToken: string | null = null;

/**
 * Získanie CSRF tokenu z API
 * @returns Promise s CSRF tokenom
 */
export async function fetchCsrfToken(): Promise<string> {
  try {
    // Ak už máme token, vrátime ho
    if (csrfToken) {
      return csrfToken;
    }

    // V testovacom prostredí môžeme použiť špeciálny testovací token
    if (process.env.NODE_ENV === 'test') {
      csrfToken = 'test-csrf-token';
      return csrfToken;
    }

    // Získanie tokenu z API
    console.log('Získavam nový CSRF token z API');
    const response = await fetch('/api/csrf', {
      // Pridanie cache: 'no-store' zabezpečí, že požiadavka nebude cachovaná
      cache: 'no-store',
      // Pridanie credentials: 'include' z<PERSON><PERSON><PERSON><PERSON><PERSON>, že cookies budú poslané a prijaté
      credentials: 'include'
    });

    if (!response.ok) {
      console.error(`Nepodarilo sa získať CSRF token. Status: ${response.status}`);
      throw new Error('Nepodarilo sa získať CSRF token');
    }

    const data = await response.json();
    console.log('CSRF token úspešne získaný');
    csrfToken = data.csrfToken;

    // Zabezpečíme, že vrátime string, nie null
    return csrfToken || 'fallback-csrf-token';
  } catch (error) {
    console.error('Chyba pri získavaní CSRF tokenu:', error);

    // V prípade chyby v development móde vrátime testovací token
    if (process.env.NODE_ENV !== 'production') {
      console.warn('Používam záložný testovací CSRF token');
      return 'test-csrf-token';
    }

    throw error;
  }
}

/**
 * Pridanie CSRF tokenu do hlavičiek
 * @param headers Existujúce hlavičky
 * @returns Promise s aktualizovanými hlavičkami
 */
export async function addCsrfHeader(headers: Record<string, string> = {}): Promise<Record<string, string>> {
  try {
    const token = await fetchCsrfToken();
    return {
      ...headers,
      [CSRF_HEADER_NAME]: token,
    };
  } catch (error) {
    console.error('Chyba pri pridávaní CSRF tokenu do hlavičiek:', error);
    return headers;
  }
}

/**
 * Vykonanie fetch požiadavky s CSRF tokenom
 * @param url URL pre fetch
 * @param options Fetch options
 * @returns Promise s odpoveďou
 */
export async function fetchWithCsrf(
  url: string,
  options: RequestInit = {}
): Promise<Response> {
  try {
    // Vždy získame nový CSRF token pred vykonaním požiadavky
    console.log('Získavam nový CSRF token pre požiadavku na:', url);

    // Získanie nového CSRF tokenu z API
    const csrfResponse = await fetch('/api/csrf', {
      cache: 'no-store',
      credentials: 'include'
    });

    if (!csrfResponse.ok) {
      throw new Error(`Nepodarilo sa získať CSRF token. Status: ${csrfResponse.status}`);
    }

    const csrfData = await csrfResponse.json();
    const token = csrfData.csrfToken;
    console.log(`Získaný nový CSRF token: ${token.substring(0, 10)}...`);

    // Aktualizácia globálneho tokenu
    csrfToken = token;

    // Pridanie CSRF tokenu do hlavičiek
    const headers = {
      ...(options.headers || {}),
      [CSRF_HEADER_NAME]: token,
    };

    console.log(`Posielam požiadavku na ${url} s CSRF tokenom`);

    // Vykonanie fetch požiadavky s CSRF tokenom
    return fetch(url, {
      ...options,
      headers,
      // Pridanie credentials: 'include' zabezpečí, že cookies budú poslané a prijaté
      credentials: 'include',
    });
  } catch (error) {
    console.error('Chyba pri vykonávaní fetch s CSRF tokenom:', error);
    throw error;
  }
}
