'use strict'
/**
 * New Relic agent configuration.
 *
 * See lib/config/default.js in the agent distribution for a more complete
 * description of configuration variables and their potential values.
 */
exports.config = {
  /**
   * Array of application names.
   */
  app_name: [process.env.NEW_RELIC_APP_NAME || 'My Next.js App'],
  /**
   * Your New Relic license key.
   */
  license_key: process.env.NEW_RELIC_LICENSE_KEY,
  logging: {
    /**
     * Level at which to log. 'trace' is most useful to New Relic when diagnosing
     * issues with the agent, 'info' and higher will impose the least overhead on
     * production applications.
     */
    level: 'info'
  },
  /**
   * When true, all request headers are captured for transaction traces, error
   * traces, and slow query traces.
   */
  allow_all_headers: true,
  application_logging: {
    forwarding: {
      /**
       * Toggles forwarding of application logs to New Relic.
       */
      enabled: true
    }
  },
  /**
   * Environment variable names and New Relic configuration file variable names
   * are not always the same. The agent provides robust support for bridging this
   * gap, but cloud providers do not always provide a way to set environment
   * variables with periods in their names. The following object is a map of
   * environment variable names to New Relic configuration file variable names.
   *
   * @see https://docs.newrelic.com/docs/apm/agents/nodejs-agent/installation-configuration/nodejs-agent-configuration/#environment-variables
   */
  newrelic_home: './', // Ensures logs and pid files are written to the project root/logs directory
  attributes: {
    /**
     * Prefix of attributes to exclude from all destinations. Allows '*' as wildcard
     * at end.
     */
    exclude: [
      'request.headers.cookie',
      'request.headers.authorization',
      'request.headers.proxyAuthorization',
      'request.headers.setCookie*',
      'request.headers.x*',
      'response.headers.cookie',
      'response.headers.authorization',
      'response.headers.proxyAuthorization',
      'response.headers.setCookie*',
      'response.headers.x*'
    ]
  }
}