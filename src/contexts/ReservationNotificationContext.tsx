'use client';

import React, { createContext, useContext, ReactNode } from 'react';
import { useReservationNotifications } from '../hooks/useReservationNotifications';

interface ReservationCounts {
  // <PERSON><PERSON> rezerv<PERSON> (rezerv<PERSON><PERSON>, ktoré si používateľ vytvoril)
  myPending: number;
  myConfirmed: number;
  myTotal: number;

  // Rezervácie mojich hračiek (požiadavky na moje hračky)
  ownerPending: number;
  ownerConfirmed: number;
  ownerTotal: number;

  // Celkové počty
  totalPending: number;
  totalConfirmed: number;
  total: number;
}

interface ReservationNotificationContextType {
  counts: ReservationCounts;
  loading: boolean;
  error: string | null;
  refresh: () => void;
  hasActiveReservations: boolean;
}

// Hook pre použitie kontextu rezervačných notifikácií
export function useReservationNotificationContext() {
  const context = useContext(ReservationNotificationContext);
  if (context === undefined) {
    throw new Error('useReservationNotificationContext must be used within a ReservationNotificationProvider');
  }
  return context;
}

const ReservationNotificationContext = createContext<ReservationNotificationContextType | undefined>(undefined);

export function ReservationNotificationProvider({ children }: { children: ReactNode }) {
  const notificationData = useReservationNotifications();

  return (
    <ReservationNotificationContext.Provider value={notificationData}>
      {children}
    </ReservationNotificationContext.Provider>
  );
}
