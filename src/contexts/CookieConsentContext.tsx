'use client';

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';

// Typy cookies, ktoré môžu byť povolené alebo zakázané
export type CookieType = 'necessary' | 'functional' | 'analytics' | 'marketing';

// Stav súhlasu s cookies
export interface CookieConsent {
  necessary: boolean; // Nevyhnutné cookies - vždy povolené
  functional: boolean; // Funkčné cookies
  analytics: boolean; // Analytické cookies
  marketing: boolean; // Marketingové cookies
}

// Rozhranie pre kontext
interface CookieConsentContextType {
  consent: CookieConsent;
  hasConsented: boolean;
  updateConsent: (consent: Partial<CookieConsent>) => void;
  acceptAll: () => void;
  rejectAll: () => void;
  savePreferences: () => void;
  openPreferences: () => void;
  isPreferencesOpen: boolean;
  closePreferences: () => void;
}

// Vytvorenie kontextu
const CookieConsentContext = createContext<CookieConsentContextType | undefined>(undefined);

// Kľúč pre localStorage
const COOKIE_CONSENT_KEY = 'cookie-consent';
const COOKIE_CONSENT_VERSION = '1.0'; // Verzia súhlasu - zvýšte pri zmenách v politike cookies

interface CookieConsentProviderProps {
  children: ReactNode;
}

export function CookieConsentProvider({ children }: CookieConsentProviderProps) {
  // Predvolené hodnoty súhlasu
  const defaultConsent: CookieConsent = {
    necessary: true, // Nevyhnutné cookies sú vždy povolené
    functional: false,
    analytics: false,
    marketing: false,
  };

  // Stav súhlasu
  const [consent, setConsent] = useState<CookieConsent>(defaultConsent);
  // Či používateľ už dal súhlas
  const [hasConsented, setHasConsented] = useState<boolean>(false);
  // Či je otvorené okno s nastaveniami cookies
  const [isPreferencesOpen, setIsPreferencesOpen] = useState<boolean>(false);

  // Načítanie súhlasu z localStorage pri inicializácii
  useEffect(() => {
    // Spustíme len na klientskej strane
    if (typeof window === 'undefined') return;

    try {
      const savedConsent = localStorage.getItem(COOKIE_CONSENT_KEY);
      if (savedConsent) {
        const parsedConsent = JSON.parse(savedConsent);
        
        // Kontrola verzie súhlasu - ak sa zmenila, vyžiadame nový súhlas
        if (parsedConsent.version !== COOKIE_CONSENT_VERSION) {
          setHasConsented(false);
          return;
        }
        
        setConsent(parsedConsent.consent);
        setHasConsented(true);
      }
    } catch (error) {
      console.error('Chyba pri načítaní súhlasu s cookies:', error);
    }
  }, []);

  // Aktualizácia súhlasu
  const updateConsent = (newConsent: Partial<CookieConsent>) => {
    setConsent(prev => ({
      ...prev,
      ...newConsent,
      necessary: true, // Nevyhnutné cookies sú vždy povolené
    }));
  };

  // Prijatie všetkých cookies
  const acceptAll = () => {
    const allAccepted: CookieConsent = {
      necessary: true,
      functional: true,
      analytics: true,
      marketing: true,
    };
    setConsent(allAccepted);
    saveConsentToStorage(allAccepted);
    setHasConsented(true);
    setIsPreferencesOpen(false);
    
    // Aktivácia Google Analytics, ak je povolené
    if (typeof window !== 'undefined') {
      document.documentElement.removeAttribute('data-google-analytics-opt-out');
    }
  };

  // Odmietnutie všetkých cookies okrem nevyhnutných
  const rejectAll = () => {
    const allRejected: CookieConsent = {
      necessary: true, // Nevyhnutné cookies sú vždy povolené
      functional: false,
      analytics: false,
      marketing: false,
    };
    setConsent(allRejected);
    saveConsentToStorage(allRejected);
    setHasConsented(true);
    setIsPreferencesOpen(false);
    
    // Deaktivácia Google Analytics
    if (typeof window !== 'undefined') {
      document.documentElement.setAttribute('data-google-analytics-opt-out', '');
    }
  };

  // Uloženie aktuálnych preferencií
  const savePreferences = () => {
    saveConsentToStorage(consent);
    setHasConsented(true);
    setIsPreferencesOpen(false);
    
    // Aktivácia/deaktivácia Google Analytics podľa nastavení
    if (typeof window !== 'undefined') {
      if (consent.analytics) {
        document.documentElement.removeAttribute('data-google-analytics-opt-out');
      } else {
        document.documentElement.setAttribute('data-google-analytics-opt-out', '');
      }
    }
  };

  // Uloženie súhlasu do localStorage
  const saveConsentToStorage = (consentToSave: CookieConsent) => {
    try {
      localStorage.setItem(COOKIE_CONSENT_KEY, JSON.stringify({
        consent: consentToSave,
        version: COOKIE_CONSENT_VERSION,
        timestamp: new Date().toISOString(),
      }));
    } catch (error) {
      console.error('Chyba pri ukladaní súhlasu s cookies:', error);
    }
  };

  // Otvorenie nastavení cookies
  const openPreferences = () => {
    setIsPreferencesOpen(true);
  };

  // Zatvorenie nastavení cookies
  const closePreferences = () => {
    setIsPreferencesOpen(false);
  };

  // Hodnoty poskytované kontextom
  const value = {
    consent,
    hasConsented,
    updateConsent,
    acceptAll,
    rejectAll,
    savePreferences,
    openPreferences,
    isPreferencesOpen,
    closePreferences,
  };

  return (
    <CookieConsentContext.Provider value={value}>
      {children}
    </CookieConsentContext.Provider>
  );
}

// Hook pre používanie kontextu
export function useCookieConsent() {
  const context = useContext(CookieConsentContext);
  if (context === undefined) {
    throw new Error('useCookieConsent must be used within a CookieConsentProvider');
  }
  return context;
}
