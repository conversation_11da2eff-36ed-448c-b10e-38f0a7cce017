-- Migration for adding <PERSON><PERSON><PERSON><PERSON> table for email notification monitoring
-- This table tracks all email sending attempts for admin monitoring and statistics

-- Create EmailLog table for tracking email notifications
CREATE TABLE IF NOT EXISTS `EmailLog` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `emailId` VARCHAR(255) NOT NULL COMMENT 'Unique identifier from email service',
  `emailType` VARCHAR(100) NOT NULL COMMENT 'RESERVATION_CREATED, RESERVATION_APPROVED, etc.',
  `recipientEmail` VARCHAR(255) NOT NULL COMMENT 'Anonymized recipient email for privacy',
  `recipientUserId` INT NULL COMMENT 'Reference to user if available',
  `subject` VARCHAR(500) NULL COMMENT 'Email subject line',
  `status` VARCHAR(50) NOT NULL COMMENT 'QUEUED, SENT, FAILED, RETRY',
  `attempt` INT NOT NULL DEFAULT 1 COMMENT 'Attempt number (1, 2, 3...)',
  `maxAttempts` INT NOT NULL DEFAULT 3 COMMENT 'Maximum retry attempts',
  `errorMessage` TEXT NULL COMMENT 'Error details if failed',
  `messageId` VARCHAR(255) NULL COMMENT 'External service message ID (e.g., Resend)',
  `queuedAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT 'When email was queued',
  `sentAt` DATETIME(3) NULL COMMENT 'When email was successfully sent',
  `failedAt` DATETIME(3) NULL COMMENT 'When email failed (final failure)',
  `processingTime` INT NULL COMMENT 'Processing time in milliseconds',
  `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
  `updatedAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3),
  PRIMARY KEY (`id`),
  INDEX `EmailLog_emailId_idx`(`emailId`),
  INDEX `EmailLog_emailType_idx`(`emailType`),
  INDEX `EmailLog_status_idx`(`status`),
  INDEX `EmailLog_recipientUserId_idx`(`recipientUserId`),
  INDEX `EmailLog_queuedAt_idx`(`queuedAt`),
  INDEX `EmailLog_sentAt_idx`(`sentAt`),
  INDEX `EmailLog_createdAt_idx`(`createdAt`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- Add foreign key constraint for recipientUserId if needed
-- ALTER TABLE `EmailLog` ADD CONSTRAINT `EmailLog_recipientUserId_fkey` 
-- FOREIGN KEY (`recipientUserId`) REFERENCES `User`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- Note: Foreign key constraint is commented out to avoid issues with soft-deleted users
-- The recipientUserId field will be used for reference but not enforced at database level
