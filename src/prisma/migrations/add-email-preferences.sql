-- Migration for adding email notification preferences to User table
-- This enables users to control their email notification settings for GDPR compliance

-- Add email notification preferences columns to User table
-- These are set to TRUE by default and users cannot disable them as they are essential for service functionality
ALTER TABLE `User` ADD COLUMN `emailNotificationsEnabled` B<PERSON><PERSON>EAN NOT NULL DEFAULT TRUE;
ALTER TABLE `User` ADD COLUMN `emailReservationCreated` BOOLEAN NOT NULL DEFAULT TRUE;
ALTER TABLE `User` ADD COLUMN `emailReservationApproved` BOOLEAN NOT NULL DEFAULT TRUE;
ALTER TABLE `User` ADD COLUMN `emailUnsubscribeToken` VARCHAR(255) NULL;

-- Create index for unsubscribe token for faster lookups
CREATE INDEX IF NOT EXISTS `idx_user_unsubscribe_token` ON `User`(`emailUnsubscribeToken`);

-- Create EmailUnsubscribe table for tracking unsubscribe events (GDPR compliance)
CREATE TABLE IF NOT EXISTS `EmailUnsubscribe` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `email` VARCHAR(191) NOT NULL,
  `token` VARCHAR(255) NOT NULL,
  `reason` VARCHAR(255) NULL,
  `unsubscribedAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
  `ipAddress` VARCHAR(45) NULL,
  `userAgent` TEXT NULL,
  PRIMARY KEY (`id`),
  UNIQUE INDEX `EmailUnsubscribe_token_key`(`token`),
  INDEX `EmailUnsubscribe_email_idx`(`email`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
