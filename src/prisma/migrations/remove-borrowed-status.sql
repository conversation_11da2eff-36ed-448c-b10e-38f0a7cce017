-- Migration script to remove BORROWED status from the application
-- This script handles existing toys with BORROWED status and removes the status from the database

-- 1. Update any existing toys with BORROWED status to RESERVED status
-- This ensures that toys currently being borrowed remain in a reserved state
UPDATE `Toy` 
SET `status` = 'RESERVED' 
WHERE `status` = 'BORROWED';

-- 2. Remove the BORROWED status from the ToyStatus table
-- This will prevent new toys from being set to BORROWED status
DELETE FROM `ToyStatus` 
WHERE `name` = 'BORROWED';

-- 3. Verify the changes
-- Check if any toys still have BORROWED status (should return 0 rows)
SELECT COUNT(*) as toys_with_borrowed_status 
FROM `Toy` 
WHERE `status` = 'BORROWED';

-- Check if BORROWED status was removed from ToyStatus table (should return 0 rows)
SELECT COUNT(*) as borrowed_status_exists 
FROM `ToyStatus` 
WHERE `name` = 'BORROWED';

-- 4. Display current toy status distribution for verification
SELECT `status`, COUNT(*) as count 
FROM `Toy` 
GROUP BY `status` 
ORDER BY count DESC;

-- 5. Display available toy statuses after migration
SELECT `name`, `label` 
FROM `ToyStatus` 
ORDER BY `name`;
