-- Migračn<PERSON> skript pre odstránenie tabuľky Location

-- 1. Záloha dát z tabuľky Location (ak by boli potrebné v budúcnosti)
CREATE TABLE IF NOT EXISTS LocationBackup (
  id INT PRIMARY KEY,
  city VARCHAR(255),
  postalCode VARCHAR(255)
);

INSERT INTO LocationBackup
SELECT * FROM Location;

-- 2. Odstránenie tabuľky Location
DROP TABLE IF EXISTS Location;

-- 3. Odstránenie tabuľky ToyLocationBackup (ak existuje)
DROP TABLE IF EXISTS ToyLocationBackup;
