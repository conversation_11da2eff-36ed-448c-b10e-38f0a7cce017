-- Manual SQL commands for database updates
-- Add isAnonymized column to User table if it doesn't exist
ALTER TABLE `User` ADD COLUMN IF NOT EXISTS `isAnonymized` BOOLEAN NOT NULL DEFAULT FALSE;

-- Create SystemLog table for audit purposes if it doesn't exist
CREATE TABLE IF NOT EXISTS `SystemLog` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `action` VARCHAR(255) NOT NULL,
  `targetId` INT NULL,
  `targetType` VARCHAR(255) NOT NULL,
  `adminId` INT NOT NULL,
  `details` TEXT NULL,
  `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
  PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- If you need to manually execute this in MySQL:
-- mysql -u your_username -p your_database < src/prisma/migrations/manual-db-update.sql
