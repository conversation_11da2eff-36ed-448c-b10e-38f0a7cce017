-- Migračn<PERSON> skript pre presun lokality z hračiek k používateľom

-- 1. Pridanie stĺpcov latitude a longitude do tabuľky User
ALTER TABLE User ADD COLUMN latitude FLOAT NULL;
ALTER TABLE User ADD COLUMN longitude FLOAT NULL;

-- 2. Aktualizácia používateľov - nastavenie lokality podľa lokality ich hračiek
-- Predpokladáme, že používateľ má všetky hračky v rovnakej lokalite
UPDATE User u
JOIN (
  SELECT DISTINCT t.userId, l.city, l.postalCode
  FROM Toy t
  JOIN Location l ON t.locationId = l.id
) AS toy_locations ON u.id = toy_locations.userId
SET 
  u.city = toy_locations.city,
  u.postalCode = toy_locations.postalCode
WHERE u.city IS NULL OR u.postalCode IS NULL;

-- 3. Odstránenie stĺpca locationId z tabuľky Toy
-- Tento krok sa vykoná automaticky pri aplikovaní Prisma migrácie
