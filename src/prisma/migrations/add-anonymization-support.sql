-- Migration for adding GDPR anonymization support
-- Add isAnonymized column to User table
ALTER TABLE `User` ADD COLUMN `isAnonymized` BOOLEAN NOT NULL DEFAULT FALSE;

-- Create SystemLog table for audit purposes
CREATE TABLE IF NOT EXISTS `SystemLog` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `action` VARCHAR(255) NOT NULL,
  `targetId` INT NULL,
  `targetType` VARCHAR(255) NOT NULL,
  `adminId` INT NOT NULL,
  `details` TEXT NULL,
  `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
  PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
