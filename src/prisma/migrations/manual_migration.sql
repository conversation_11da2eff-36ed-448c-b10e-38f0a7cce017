-- Manu<PERSON>lna migr<PERSON>cia pre odstránenie vzťahu medzi hračkami a lokalitami
-- a pridanie súradníc k používateľom

-- 1. Pridanie stĺpcov latitude a longitude do tabuľky User
ALTER TABLE User ADD COLUMN IF NOT EXISTS latitude FLOAT NULL;
ALTER TABLE User ADD COLUMN IF NOT EXISTS longitude FLOAT NULL;

-- 2. Aktualizácia používateľov - nastavenie lokality podľa lokality ich hračiek
-- Táto časť sa vykoná pomocou JavaScript skriptu

-- 3. Vytvorenie záložnej tabuľky pre uloženie vzťahov medzi hračkami a lokalitami
CREATE TABLE IF NOT EXISTS ToyLocationBackup (
  toyId INT NOT NULL,
  locationId INT NOT NULL,
  PRIMARY KEY (toyId)
);

-- 4. Uloženie existujúcich vzťahov do záložnej tabuľky
INSERT INTO ToyLocationBackup (toyId, locationId)
SELECT id, locationId FROM Toy;

-- 5. Odstránenie stĺpca locationId z tabuľky Toy
-- Najprv odstránime foreign key constraint
SET @constraintName = (
  SELECT CONSTRAINT_NAME
  FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE
  WHERE TABLE_NAME = 'Toy'
  AND COLUMN_NAME = 'locationId'
  AND REFERENCED_TABLE_NAME = 'Location'
  LIMIT 1
);

SET @dropForeignKeySQL = CONCAT('ALTER TABLE Toy DROP FOREIGN KEY ', @constraintName);
PREPARE stmt FROM @dropForeignKeySQL;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Potom odstránime stĺpec
ALTER TABLE Toy DROP COLUMN locationId;
