-- Migration for adding soft delete support to User, Toy, and Reservation tables
-- This enables cascading soft delete when users are anonymized

-- Add deletedAt column to User table for soft delete functionality
ALTER TABLE `User` ADD COLUMN `deletedAt` DATETIME(3) NULL;

-- Add deletedAt column to Toy table for cascading soft delete
ALTER TABLE `Toy` ADD COLUMN `deletedAt` DATETIME(3) NULL;

-- Add deletedAt column to Reservation table for cascading soft delete
ALTER TABLE `Reservation` ADD COLUMN `deletedAt` DATETIME(3) NULL;

-- Add indexes for better query performance on soft delete queries
CREATE INDEX `User_deletedAt_idx` ON `User`(`deletedAt`);
CREATE INDEX `Toy_deletedAt_idx` ON `Toy`(`deletedAt`);
CREATE INDEX `Reservation_deletedAt_idx` ON `Reservation`(`deletedAt`);

-- Add composite indexes for common query patterns
CREATE INDEX `User_status_deletedAt_idx` ON `User`(`status`, `deletedAt`);
CREATE INDEX `Toy_status_deletedAt_idx` ON `Toy`(`status`, `deletedAt`);
CREATE INDEX `Reservation_status_deletedAt_idx` ON `Reservation`(`status`, `deletedAt`);
