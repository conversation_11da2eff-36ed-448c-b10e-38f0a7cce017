-- <PERSON><PERSON><PERSON><PERSON><PERSON> tabu<PERSON>ky pre skryté hračky
CREATE TABLE IF NOT EXISTS `HiddenToy` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `userId` INT NOT NULL,
  `toyId` INT NOT NULL,
  `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
  <PERSON>IMAR<PERSON> KEY (`id`),
  UNIQUE INDEX `HiddenToy_userId_toyId_key`(`userId`, `toyId`),
  INDEX `HiddenToy_userId_idx`(`userId`),
  INDEX `HiddenToy_toyId_idx`(`toyId`),
  FOREIGN KEY (`userId`) REFERENCES `User`(`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  FOREIG<PERSON> KEY (`toyId`) REFERENCES `Toy`(`id`) ON DELETE CASCADE ON UPDATE CASCADE
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
