const { PrismaClient } = require('../app/generated/prisma');
const fs = require('fs');
const path = require('path');

const prisma = new PrismaClient();

async function runMigration() {
  try {
    // Načítanie SQL súboru
    const sql = fs.readFileSync(path.join(__dirname, 'migrations', 'rename_classic_to_user.sql'), 'utf8');

    // Spustenie SQL príkazu
    await prisma.$executeRawUnsafe(sql);

    console.log('Migr<PERSON>cia bola úspešne spustená');
  } catch (error) {
    console.error('Chyba pri spustení migrácie:', error);
  } finally {
    await prisma.$disconnect();
  }
}

runMigration();
