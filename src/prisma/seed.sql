-- Naplnenie datab<PERSON><PERSON> dátami

-- Používatelia
INSERT INTO `User` (`email`, `name`, `password`, `phone`, `address`, `city`, `postalCode`, `latitude`, `longitude`, `role`, `createdAt`, `updatedAt`)
VALUES
  ('<EMAIL>', 'Admin', '$2a$10$hACwQ5/HQI6FhbIISOUVeusy3sKyUDhSq36fF5d/54aAdiygJPFzm', '+421900000000', 'Námestie SNP 25', 'Bratislava', '81102', 48.145245, 17.112922, 'ADMIN', NOW(), NOW()),

-- Lokality boli odstránené, používame lokality používateľov

-- Hračky
INSERT INTO `Toy` (`name`, `description`, `type`, `price`, `deposit`, `status`, `createdAt`, `updatedAt`, `userId`)
VALUES
  ('Dr<PERSON><PERSON><PERSON> kocky', 'Sada 20 drevených kociek rôznych farieb a veľkostí.', 'EDUCATIONAL', 5, 15, 'AVAILABLE', NOW(), NOW(), 1),
  ('Plyšový medvedík', 'Mäkký plyšový medvedík, vhodný pre deti od 0 rokov.', 'PLUSH', 3, 10, 'AVAILABLE', NOW(), NOW(), 1),
  ('Lego Duplo sada', 'Základná sada Lego Duplo s 50 kockami.', 'BUILDING', 8, 25, 'AVAILABLE', NOW(), NOW(), 1),
  ('Detská kuchynka', 'Drevená detská kuchynka s príslušenstvom.', 'ROLEPLAY', 12, 40, 'AVAILABLE', NOW(), NOW(), 1),
  ('Autodráha', 'Elektrická autodráha s dvoma autíčkami.', 'CARS', 10, 30, 'AVAILABLE', NOW(), NOW(), 1),
  ('Bábika s oblečením', 'Bábika s kompletnou sadou oblečenia na prezliekanie.', 'DOLLS', 7, 20, 'AVAILABLE', NOW(), NOW(), 1);

-- Obrázky hračiek
INSERT INTO `ToyImage` (`url`, `toyId`, `createdAt`)
VALUES
  ('/toys/wooden-blocks.jpg', 1, NOW()),
  ('/toys/teddy-bear.jpg', 2, NOW()),
  ('/toys/lego-duplo.jpg', 3, NOW()),
  ('/toys/kitchen.jpg', 4, NOW()),
  ('/toys/race-track.jpg', 5, NOW()),
  ('/toys/doll.jpg', 6, NOW());
