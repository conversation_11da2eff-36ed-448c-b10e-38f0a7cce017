generator client {
  provider      = "prisma-client-js"
  output        = "../app/generated/prisma"
  binaryTargets = ["native", "debian-openssl-3.0.x", "linux-musl", "darwin-arm64"]
}

datasource db {
  provider = "mysql"
  url      = env("DATABASE_URL")
}

model User {
  id                        Int           @id @default(autoincrement())
  email                     String        @unique
  name                      String
  password                  String
  phone                     String?
  address                   String?       @db.VarChar(255)
  city                      String?
  postalCode                String?
  role                      String        @default("USER")
  createdAt                 DateTime      @default(now())
  updatedAt                 DateTime      @updatedAt
  status                    String        @default("ACTIVE")
  firebaseUid               String?       @unique @db.VarChar(255)
  latitude                  Float?        @db.Float
  longitude                 Float?        @db.Float
  termsAccepted             <PERSON>olean       @default(false)
  isAnonymized              Boolean       @default(false)
  deletedAt                 DateTime?     // Soft delete timestamp
  emailNotificationsEnabled Boolean       @default(true)
  emailReservationCreated   <PERSON><PERSON><PERSON>       @default(true)
  emailReservationApproved  Boolean       @default(true)
  emailUnsubscribeToken     String?       @db.Var<PERSON>har(255)
  reservations              Reservation[] @relation("UserReservations")
  toyReservations           Reservation[] @relation("ToyOwnerReservations")
  toys                      Toy[]
  hiddenToys                HiddenToy[]
  UserRole                  UserRole      @relation(fields: [role], references: [name], map: "User_ibfk_1")

  @@index([role], map: "role")
  @@index([deletedAt], map: "User_deletedAt_idx")
  @@index([status, deletedAt], map: "User_status_deletedAt_idx")
  @@index([emailUnsubscribeToken], map: "idx_user_unsubscribe_token")
}

model Toy {
  id                 Int           @id @default(autoincrement())
  name               String
  description        String        @db.Text
  type               String
  price              Float
  deposit            Float
  priceType          String        @default("PER_DAY") // "PER_DAY" or "PER_RENTAL"
  status             String        @default("AVAILABLE")
  maxReservationDays Int           @default(7)
  createdAt          DateTime      @default(now())
  updatedAt          DateTime      @updatedAt
  userId             Int
  deletedAt          DateTime?     // Soft delete timestamp
  reservations       Reservation[]
  hiddenBy           HiddenToy[]
  user               User          @relation(fields: [userId], references: [id], map: "Toy_ibfk_1")
  ToyType            ToyType       @relation(fields: [type], references: [name], map: "Toy_ibfk_3")
  ToyStatus          ToyStatus     @relation(fields: [status], references: [name], map: "Toy_ibfk_4")
  images             ToyImage[]

  @@index([status], map: "status")
  @@index([type], map: "type")
  @@index([userId], map: "userId")
  @@index([deletedAt], map: "Toy_deletedAt_idx")
  @@index([status, deletedAt], map: "Toy_status_deletedAt_idx")
}

model ToyImage {
  id               Int      @id @default(autoincrement())
  url              String
  toyId            Int
  createdAt        DateTime @default(now())
  originalFilename String?  // Store original filename for reference
  hashedFilename   String?  // Store hashed filename used in Cloudinary
  fileFormat       String?  // Store final format (e.g., 'webp')
  toy              Toy      @relation(fields: [toyId], references: [id], onDelete: Cascade, map: "ToyImage_ibfk_1")

  @@index([toyId], map: "toyId")
  @@index([hashedFilename], map: "hashedFilename")
}

// Model pre lokality bol odstránený, keďže lokality sú teraz súčasťou modelu User

model Reservation {
  id                Int               @id @default(autoincrement())
  startDate         DateTime
  endDate           DateTime
  status            String            @default("PENDING")
  position          Int               @default(0) // Position in the waiting list (0 = active reservation, 1+ = waiting list position)
  createdAt         DateTime          @default(now())
  updatedAt         DateTime          @updatedAt
  userId            Int
  toyId             Int
  ownerId           Int
  deletedAt         DateTime?         // Soft delete timestamp
  user              User              @relation("UserReservations", fields: [userId], references: [id], map: "Reservation_ibfk_1")
  toy               Toy               @relation(fields: [toyId], references: [id], map: "Reservation_ibfk_2")
  owner             User              @relation("ToyOwnerReservations", fields: [ownerId], references: [id], map: "Reservation_ibfk_3")
  ReservationStatus ReservationStatus @relation(fields: [status], references: [name], map: "Reservation_ibfk_4")

  @@index([ownerId], map: "ownerId")
  @@index([status], map: "status")
  @@index([toyId], map: "toyId")
  @@index([userId], map: "userId")
  @@index([deletedAt], map: "Reservation_deletedAt_idx")
  @@index([status, deletedAt], map: "Reservation_status_deletedAt_idx")
}

model ReservationStatus {
  name        String        @id
  Reservation Reservation[]
}

model ToyLocationBackup {
  toyId      Int @id
  locationId Int
}

model ToyStatus {
  name  String @id
  label String @default("")
  Toy   Toy[]
}

model ToyType {
  name  String @id
  label String @default("")
  Toy   Toy[]
}

model UserRole {
  name String @id
  User User[]
}

model HiddenToy {
  id        Int      @id @default(autoincrement())
  userId    Int
  toyId     Int
  createdAt DateTime @default(now())
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  toy       Toy      @relation(fields: [toyId], references: [id], onDelete: Cascade)

  @@unique([userId, toyId])
  @@index([userId])
  @@index([toyId])
}

model SystemLog {
  id          Int      @id @default(autoincrement())
  action      String   @db.VarChar(255)
  targetId    Int?
  targetType  String   @db.VarChar(255)
  adminId     Int
  details     String?  @db.Text
  createdAt   DateTime @default(now())
}

model EmailUnsubscribe {
  id             Int      @id @default(autoincrement())
  email          String
  token          String   @unique @db.VarChar(255)
  reason         String?  @db.VarChar(255)
  unsubscribedAt DateTime @default(now())
  ipAddress      String?  @db.VarChar(45)
  userAgent      String?  @db.Text

  @@index([email], map: "EmailUnsubscribe_email_idx")
}

model EmailLog {
  id              Int      @id @default(autoincrement())
  emailId         String   @db.VarChar(255) // Unique identifier from email service
  emailType       String   @db.VarChar(100) // RESERVATION_CREATED, RESERVATION_APPROVED, etc.
  recipientEmail  String   @db.VarChar(255) // Anonymized for privacy
  recipientUserId Int?     // Reference to user if available
  subject         String?  @db.VarChar(500)
  status          String   @db.VarChar(50)  // QUEUED, SENT, FAILED, RETRY
  attempt         Int      @default(1)      // Attempt number (1, 2, 3...)
  maxAttempts     Int      @default(3)      // Maximum retry attempts
  errorMessage    String?  @db.Text         // Error details if failed
  messageId       String?  @db.VarChar(255) // External service message ID (e.g., Resend)
  queuedAt        DateTime @default(now())  // When email was queued
  sentAt          DateTime?                 // When email was successfully sent
  failedAt        DateTime?                 // When email failed (final failure)
  processingTime  Int?     // Processing time in milliseconds
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  @@index([emailId], map: "EmailLog_emailId_idx")
  @@index([emailType], map: "EmailLog_emailType_idx")
  @@index([status], map: "EmailLog_status_idx")
  @@index([recipientUserId], map: "EmailLog_recipientUserId_idx")
  @@index([queuedAt], map: "EmailLog_queuedAt_idx")
  @@index([sentAt], map: "EmailLog_sentAt_idx")
  @@index([createdAt], map: "EmailLog_createdAt_idx")
}
