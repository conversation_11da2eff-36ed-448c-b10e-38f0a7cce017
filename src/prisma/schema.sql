-- Vyt<PERSON><PERSON><PERSON> tabuliek pre aplikáciu Swapka

-- Enum pre role používateľov
CREATE TABLE IF NOT EXISTS `UserRole` (
  `name` VARCHAR(191) NOT NULL,
  PRIMARY KEY (`name`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- Enum pre typy hračiek
CREATE TABLE IF NOT EXISTS `ToyType` (
  `name` VARCHAR(191) NOT NULL,
  `label` VARCHAR(191) NOT NULL DEFAULT '',
  PRIMARY KEY (`name`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- Enum pre status hračky
CREATE TABLE IF NOT EXISTS `ToyStatus` (
  `name` VARCHAR(191) NOT NULL,
  `label` VARCHAR(191) NOT NULL DEFAULT '',
  PRIMARY KEY (`name`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- Enum pre status rezervácie
CREATE TABLE IF NOT EXISTS `ReservationStatus` (
  `name` VARCHAR(191) NOT NULL,
  PRIMARY KEY (`name`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- Tabuľka používateľov
CREATE TABLE IF NOT EXISTS `User` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `email` VARCHAR(191) NOT NULL,
  `name` VARCHAR(191) NOT NULL,
  `password` VARCHAR(191) NOT NULL,
  `phone` VARCHAR(191) NULL,
  `city` VARCHAR(191) NULL,
  `postalCode` VARCHAR(191) NULL,
  `role` VARCHAR(191) NOT NULL DEFAULT 'USER',
  `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
  `updatedAt` DATETIME(3) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE INDEX `User_email_key`(`email`),
  FOREIGN KEY (`role`) REFERENCES `UserRole`(`name`) ON DELETE RESTRICT ON UPDATE CASCADE
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- Tabuľka lokalít
CREATE TABLE IF NOT EXISTS `Location` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `city` VARCHAR(191) NOT NULL,
  `postalCode` VARCHAR(191) NOT NULL,
  PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- Tabuľka hračiek
CREATE TABLE IF NOT EXISTS `Toy` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `name` VARCHAR(191) NOT NULL,
  `description` TEXT NOT NULL,
  `type` VARCHAR(191) NOT NULL,
  `price` DOUBLE NOT NULL,
  `deposit` DOUBLE NOT NULL,
  `status` VARCHAR(191) NOT NULL DEFAULT 'AVAILABLE',
  `maxReservationDays` INT NOT NULL DEFAULT 7,
  `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
  `updatedAt` DATETIME(3) NOT NULL,
  `userId` INT NOT NULL,
  `locationId` INT NOT NULL,
  PRIMARY KEY (`id`),
  FOREIGN KEY (`userId`) REFERENCES `User`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE,
  FOREIGN KEY (`locationId`) REFERENCES `Location`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE,
  FOREIGN KEY (`type`) REFERENCES `ToyType`(`name`) ON DELETE RESTRICT ON UPDATE CASCADE,
  FOREIGN KEY (`status`) REFERENCES `ToyStatus`(`name`) ON DELETE RESTRICT ON UPDATE CASCADE
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- Tabuľka obrázkov hračiek
CREATE TABLE IF NOT EXISTS `ToyImage` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `url` VARCHAR(191) NOT NULL,
  `toyId` INT NOT NULL,
  `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
  PRIMARY KEY (`id`),
  FOREIGN KEY (`toyId`) REFERENCES `Toy`(`id`) ON DELETE CASCADE ON UPDATE CASCADE
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- Tabuľka rezervácií
CREATE TABLE IF NOT EXISTS `Reservation` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `startDate` DATETIME(3) NOT NULL,
  `endDate` DATETIME(3) NOT NULL,
  `status` VARCHAR(191) NOT NULL DEFAULT 'PENDING',
  `position` INT NOT NULL DEFAULT 0,
  `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
  `updatedAt` DATETIME(3) NOT NULL,
  `userId` INT NOT NULL,
  `toyId` INT NOT NULL,
  `ownerId` INT NOT NULL,
  PRIMARY KEY (`id`),
  FOREIGN KEY (`userId`) REFERENCES `User`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE,
  FOREIGN KEY (`toyId`) REFERENCES `Toy`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE,
  FOREIGN KEY (`ownerId`) REFERENCES `User`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE,
  FOREIGN KEY (`status`) REFERENCES `ReservationStatus`(`name`) ON DELETE RESTRICT ON UPDATE CASCADE
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- Naplnenie enum hodnôt
INSERT IGNORE INTO `UserRole` (`name`) VALUES ('USER'), ('ADMIN');
INSERT IGNORE INTO `ToyType` (`name`, `label`) VALUES
('EDUCATIONAL', 'Vzdelávacie'),
('PLUSH', 'Plyšové'),
('BUILDING', 'Stavebnice'),
('ROLEPLAY', 'Na hranie rolí'),
('CARS', 'Autíčka'),
('DOLLS', 'Bábiky'),
('OUTDOOR', 'Vonkajšie'),
('BOARD_GAMES', 'Spoločenské hry'),
('ELECTRONIC', 'Elektronické'),
('OTHER', 'Iné');
INSERT IGNORE INTO `ToyStatus` (`name`, `label`) VALUES
('AVAILABLE', 'Dostupná'),
('RESERVED', 'Rezervovaná'),
('UNAVAILABLE', 'Nedostupná'),
('DRAFT', 'Koncept');
INSERT IGNORE INTO `ReservationStatus` (`name`) VALUES ('PENDING'), ('CONFIRMED'), ('ACTIVE'), ('COMPLETED'), ('CANCELLED');
