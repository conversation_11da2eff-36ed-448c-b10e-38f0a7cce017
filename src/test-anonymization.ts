// Simple test script for name anonymization
import { anonymizeName, isAnonymizedName, safeAnonymizeName } from './lib/nameUtils';

console.log('Testing name anonymization...');

// Test cases
const testCases = [
  '<PERSON><PERSON>',
  '<PERSON>', 
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '',
  '   ',
  '<PERSON><PERSON>',
  '<PERSON><PERSON><PERSON>ub<PERSON>rov<PERSON>'
];

console.log('\n=== anonymizeName tests ===');
testCases.forEach(name => {
  try {
    const result = anonymizeName(name);
    console.log(`"${name}" -> "${result}"`);
  } catch (error) {
    console.log(`"${name}" -> ERROR: ${(error as Error).message}`);
  }
});

console.log('\n=== isAnonymizedName tests ===');
const anonymizedNames = [
  '<PERSON><PERSON>***',
  '<PERSON>***', 
  'Nezná<PERSON> používateľ',
  'Anonymized User',
  '<PERSON><PERSON>',
  '<PERSON>'
];

anonymizedNames.forEach(name => {
  try {
    const result = isAnonymizedName(name);
    console.log(`"${name}" -> ${result}`);
  } catch (error) {
    console.log(`"${name}" -> ERROR: ${(error as Error).message}`);
  }
});

console.log('\n=== safeAnonymizeName tests ===');
const safeTestCases = [
  'Ján Novák',
  'Ján N***',
  'Neznámy používateľ'
];

safeTestCases.forEach(name => {
  try {
    const result = safeAnonymizeName(name);
    console.log(`"${name}" -> "${result}"`);
  } catch (error) {
    console.log(`"${name}" -> ERROR: ${(error as Error).message}`);
  }
});

console.log('\nTest completed!');
