/**
 * Test script for the user anonymization endpoint
 * 
 * This script tests the security implementation of the user anonymization endpoint:
 * 1. Tests that admin users can successfully access the endpoint
 * 2. Tests that non-admin users receive a 403 Forbidden response
 * 3. Tests that unauthenticated requests receive a 401 Unauthorized response
 * 
 * To run this test:
 * node tests/anonymize-endpoint-test.js
 */

// Import required modules
const fetch = require('node-fetch');

// Configuration
const API_BASE_URL = 'http://localhost:3000';
const USER_ID_TO_ANONYMIZE = 'REPLACE_WITH_VALID_USER_ID'; // Replace with a valid user ID to anonymize

// Test tokens (replace with actual tokens for testing)
const ADMIN_TOKEN = 'REPLACE_WITH_ADMIN_TOKEN'; // Replace with a valid admin token
const USER_TOKEN = 'REPLACE_WITH_USER_TOKEN';   // Replace with a valid non-admin user token

/**
 * Test the anonymization endpoint with different authentication scenarios
 */
async function testAnonymizationEndpoint() {
  console.log('Testing user anonymization endpoint security...');
  
  // Test 1: Unauthenticated request (no token)
  console.log('\n1. Testing unauthenticated request (should return 401)...');
  const unauthResult = await testEndpoint(`/api/users/${USER_ID_TO_ANONYMIZE}/anonymize`, null);
  console.log(`Status: ${unauthResult.status}`);
  console.log('Response:', unauthResult.data);
  console.log(`Test ${unauthResult.status === 401 ? 'PASSED' : 'FAILED'} - Expected 401 Unauthorized`);
  
  // Test 2: Non-admin user (should be forbidden)
  console.log('\n2. Testing non-admin user request (should return 403)...');
  const userResult = await testEndpoint(`/api/users/${USER_ID_TO_ANONYMIZE}/anonymize`, USER_TOKEN);
  console.log(`Status: ${userResult.status}`);
  console.log('Response:', userResult.data);
  console.log(`Test ${userResult.status === 403 ? 'PASSED' : 'FAILED'} - Expected 403 Forbidden`);
  
  // Test 3: Admin user (should succeed)
  console.log('\n3. Testing admin user request (should return 200)...');
  const adminResult = await testEndpoint(`/api/users/${USER_ID_TO_ANONYMIZE}/anonymize`, ADMIN_TOKEN);
  console.log(`Status: ${adminResult.status}`);
  console.log('Response:', adminResult.data);
  console.log(`Test ${adminResult.status === 200 ? 'PASSED' : 'FAILED'} - Expected 200 OK`);
  
  console.log('\nTest summary:');
  console.log(`- Unauthenticated request: ${unauthResult.status === 401 ? 'PASSED' : 'FAILED'}`);
  console.log(`- Non-admin user request: ${userResult.status === 403 ? 'PASSED' : 'FAILED'}`);
  console.log(`- Admin user request: ${adminResult.status === 200 ? 'PASSED' : 'FAILED'}`);
}

/**
 * Helper function to test the endpoint with different tokens
 */
async function testEndpoint(endpoint, token) {
  try {
    const headers = {
      'Content-Type': 'application/json'
    };
    
    if (token) {
      headers['Authorization'] = `Bearer ${token}`;
    }
    
    const response = await fetch(`${API_BASE_URL}${endpoint}`, {
      method: 'POST',
      headers
    });
    
    return {
      status: response.status,
      data: await response.json().catch(() => ({}))
    };
  } catch (error) {
    console.error(`Error testing endpoint ${endpoint}:`, error);
    return {
      status: 500,
      data: { error: error.message }
    };
  }
}

// Run the tests
testAnonymizationEndpoint().catch(error => {
  console.error('Test failed with error:', error);
});
