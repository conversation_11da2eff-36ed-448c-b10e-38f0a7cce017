/**
 * Test script for sitemap functionality
 * Tests the sitemap generation and robots.txt endpoints
 */

const fetch = require('node-fetch');

const API_BASE_URL = 'http://localhost:3000';

/**
 * Test helper function
 */
async function testEndpoint(endpoint, expectedStatus = 200) {
  try {
    const response = await fetch(`${API_BASE_URL}${endpoint}`);
    const contentType = response.headers.get('content-type');
    
    let data;
    if (contentType && contentType.includes('application/json')) {
      data = await response.json();
    } else {
      data = await response.text();
    }

    return {
      status: response.status,
      contentType,
      data,
      success: response.status === expectedStatus
    };
  } catch (error) {
    console.error(`Error testing endpoint ${endpoint}:`, error);
    return {
      status: 500,
      error: error.message,
      success: false
    };
  }
}

/**
 * Test sitemap XML generation
 */
async function testSitemapXml() {
  console.log('\n1. Testing sitemap.xml generation...');

  const result = await testEndpoint('/sitemap.xml');
  
  console.log(`Status: ${result.status}`);
  console.log(`Content-Type: ${result.contentType}`);
  
  if (result.success) {
    // Check if it's valid XML
    const isValidXml = result.data.includes('<?xml version="1.0"') && 
                      result.data.includes('<urlset') && 
                      result.data.includes('</urlset>');
    
    console.log(`Valid XML format: ${isValidXml ? 'YES' : 'NO'}`);
    
    // Count URLs
    const urlCount = (result.data.match(/<url>/g) || []).length;
    console.log(`Number of URLs: ${urlCount}`);
    
    // Check for different types of URLs
    const hasToyUrls = result.data.includes('/hracky/h-');
    const hasCategoryUrls = result.data.includes('?type=');
    const hasLocationUrls = result.data.includes('?location=');
    const hasStaticUrls = result.data.includes('http://localhost:3000/');
    
    console.log(`Contains toy URLs: ${hasToyUrls ? 'YES' : 'NO'}`);
    console.log(`Contains category URLs: ${hasCategoryUrls ? 'YES' : 'NO'}`);
    console.log(`Contains location URLs: ${hasLocationUrls ? 'YES' : 'NO'}`);
    console.log(`Contains static URLs: ${hasStaticUrls ? 'YES' : 'NO'}`);
    
    console.log(`Test ${result.success && isValidXml && urlCount > 0 ? 'PASSED' : 'FAILED'} ✅`);
  } else {
    console.log('Test FAILED ❌');
    console.log('Response:', result.data);
  }
  
  return result.success;
}

/**
 * Test robots.txt generation
 */
async function testRobotsTxt() {
  console.log('\n2. Testing robots.txt generation...');
  
  const result = await testEndpoint('/robots.txt');
  
  console.log(`Status: ${result.status}`);
  console.log(`Content-Type: ${result.contentType}`);
  
  if (result.success) {
    // Check if it contains expected content
    const hasUserAgent = result.data.includes('User-agent: *');
    const hasSitemap = result.data.includes('Sitemap: http://localhost:3000/sitemap.xml');
    const hasDisallowAdmin = result.data.includes('Disallow: /admin/');
    
    console.log(`Contains User-agent: ${hasUserAgent ? 'YES' : 'NO'}`);
    console.log(`Contains Sitemap URL: ${hasSitemap ? 'YES' : 'NO'}`);
    console.log(`Disallows admin routes: ${hasDisallowAdmin ? 'YES' : 'NO'}`);

    console.log(`Test ${result.success && hasUserAgent && hasSitemap ? 'PASSED' : 'FAILED'} ✅`);
  } else {
    console.log('Test FAILED ❌');
    console.log('Response:', result.data);
  }
  
  return result.success;
}

/**
 * Test sitemap caching
 */
async function testSitemapCaching() {
  console.log('\n3. Testing sitemap caching...');
  
  // First request
  const start1 = Date.now();
  const result1 = await testEndpoint('/sitemap.xml');
  const time1 = Date.now() - start1;

  // Second request (should be cached)
  const start2 = Date.now();
  const result2 = await testEndpoint('/sitemap.xml');
  const time2 = Date.now() - start2;
  
  console.log(`First request time: ${time1}ms`);
  console.log(`Second request time: ${time2}ms`);
  console.log(`Content identical: ${result1.data === result2.data ? 'YES' : 'NO'}`);
  console.log(`Cache working: ${time2 < time1 ? 'YES' : 'NO'} (second request should be faster)`);
  
  const cachingWorks = result1.success && result2.success && result1.data === result2.data;
  console.log(`Test ${cachingWorks ? 'PASSED' : 'FAILED'} ✅`);
  
  return cachingWorks;
}

/**
 * Test admin endpoint protection
 */
async function testAdminProtection() {
  console.log('\n4. Testing admin endpoint protection...');
  
  const result = await testEndpoint('/api/admin/sitemap', 401);
  
  console.log(`Status: ${result.status}`);
  console.log(`Expected 401 Unauthorized: ${result.status === 401 ? 'YES' : 'NO'}`);
  
  console.log(`Test ${result.success ? 'PASSED' : 'FAILED'} ✅`);
  
  return result.success;
}

/**
 * Main test function
 */
async function runSitemapTests() {
  console.log('🚀 Starting sitemap functionality tests...');
  console.log('='.repeat(50));
  
  const results = [];
  
  results.push(await testSitemapXml());
  results.push(await testRobotsTxt());
  results.push(await testSitemapCaching());
  results.push(await testAdminProtection());
  
  console.log('\n' + '='.repeat(50));
  console.log('📊 Test Summary:');
  console.log(`Total tests: ${results.length}`);
  console.log(`Passed: ${results.filter(r => r).length}`);
  console.log(`Failed: ${results.filter(r => !r).length}`);
  
  const allPassed = results.every(r => r);
  console.log(`\n${allPassed ? '✅ All tests PASSED!' : '❌ Some tests FAILED!'}`);
  
  return allPassed;
}

// Run tests if this script is executed directly
if (require.main === module) {
  runSitemapTests()
    .then(success => {
      process.exit(success ? 0 : 1);
    })
    .catch(error => {
      console.error('Error running tests:', error);
      process.exit(1);
    });
}

module.exports = { runSitemapTests };
