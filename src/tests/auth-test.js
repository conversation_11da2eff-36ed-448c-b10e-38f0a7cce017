// Test pre overenie autentifikácie na strane servera
// Tento skript testuje, či sa token správne overuje na strane servera

// Importy potrebných knižníc
const fetch = require('node-fetch');
const { getAuth, signInWithEmailAndPassword } = require('firebase/auth');
const { initializeApp } = require('firebase/app');

// Firebase konfigurácia - použite rovnakú konfiguráciu ako v aplikácii
const firebaseConfig = {
  apiKey: process.env.NEXT_PUBLIC_FIREBASE_API_KEY,
  authDomain: process.env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN,
  projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID,
  storageBucket: process.env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET,
  messagingSenderId: process.env.NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID,
  appId: process.env.NEXT_PUBLIC_FIREBASE_APP_ID
};

// Inicializácia Firebase
const app = initializeApp(firebaseConfig);
const auth = getAuth(app);

// Testovací používateľ - nahraďte skutočnými údajmi
const TEST_EMAIL = '<EMAIL>';
const TEST_PASSWORD = 'testpassword';
const TEST_USER_ID = ''; // Vyplňte hashované ID používateľa

// Funkcia pre získanie platného tokenu
async function getValidToken() {
  try {
    const userCredential = await signInWithEmailAndPassword(auth, TEST_EMAIL, TEST_PASSWORD);
    const token = await userCredential.user.getIdToken();
    return token;
  } catch (error) {
    console.error('Chyba pri získavaní tokenu:', error);
    throw error;
  }
}

// Funkcia pre vytvorenie neplatného tokenu
function getInvalidToken() {
  // Vytvoríme neplatný token zmenou niekoľkých znakov v platnom tokene
  return 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c';
}

// Funkcia pre testovanie API endpointu s rôznymi tokenmi
async function testEndpoint(endpoint, token) {
  try {
    const headers = {
      'Content-Type': 'application/json'
    };

    if (token) {
      headers['Authorization'] = `Bearer ${token}`;
    }

    const response = await fetch(`http://localhost:3000${endpoint}`, {
      method: 'GET',
      headers
    });

    return {
      status: response.status,
      data: await response.json().catch(() => ({}))
    };
  } catch (error) {
    console.error(`Chyba pri testovaní endpointu ${endpoint}:`, error);
    return {
      status: 500,
      data: { error: error.message }
    };
  }
}

// Hlavná testovacia funkcia
async function runTests() {
  console.log('Spúšťam testy autentifikácie na strane servera...');

  // Test 1: Volanie chráneného endpointu bez tokenu
  console.log('\nTest 1: Volanie chráneného endpointu bez tokenu');
  const test1Result = await testEndpoint('/api/users/' + TEST_USER_ID, null, TEST_USER_ID);
  console.log('Status:', test1Result.status);
  console.log('Odpoveď:', test1Result.data);
  console.log('Očakávaný výsledok: 401 Unauthorized');
  console.log('Test 1 ' + (test1Result.status === 401 ? 'PREŠIEL ✅' : 'ZLYHAL ❌'));

  // Test 2: Volanie chráneného endpointu s neplatným tokenom
  console.log('\nTest 2: Volanie chráneného endpointu s neplatným tokenom');
  const invalidToken = getInvalidToken();
  const test2Result = await testEndpoint('/api/users/' + TEST_USER_ID, invalidToken, TEST_USER_ID);
  console.log('Status:', test2Result.status);
  console.log('Odpoveď:', test2Result.data);
  console.log('Očakávaný výsledok: 401 Unauthorized');
  console.log('Test 2 ' + (test2Result.status === 401 ? 'PREŠIEL ✅' : 'ZLYHAL ❌'));

  // Test 3: Volanie chráneného endpointu s platným tokenom, ale bez ID používateľa
  console.log('\nTest 3: Volanie chráneného endpointu s platným tokenom, ale bez ID používateľa');
  const validToken = await getValidToken();
  const test3Result = await testEndpoint('/api/users/' + TEST_USER_ID, validToken, null);
  console.log('Status:', test3Result.status);
  console.log('Odpoveď:', test3Result.data);
  console.log('Očakávaný výsledok: 401 Unauthorized');
  console.log('Test 3 ' + (test3Result.status === 401 ? 'PREŠIEL ✅' : 'ZLYHAL ❌'));

  // Test 4: Volanie chráneného endpointu s platným tokenom a ID používateľa
  console.log('\nTest 4: Volanie chráneného endpointu s platným tokenom a ID používateľa');
  const test4Result = await testEndpoint('/api/users/' + TEST_USER_ID, validToken, TEST_USER_ID);
  console.log('Status:', test4Result.status);
  console.log('Odpoveď:', test4Result.data);
  console.log('Očakávaný výsledok: 200 OK');
  console.log('Test 4 ' + (test4Result.status === 200 ? 'PREŠIEL ✅' : 'ZLYHAL ❌'));

  // Test 5: Volanie admin endpointu s platným tokenom bežného používateľa
  console.log('\nTest 5: Volanie admin endpointu s platným tokenom bežného používateľa');
  const test5Result = await testEndpoint('/api/users', validToken, TEST_USER_ID);
  console.log('Status:', test5Result.status);
  console.log('Odpoveď:', test5Result.data);
  console.log('Očakávaný výsledok: 403 Forbidden (ak používateľ nie je admin)');
  console.log('Test 5 ' + (test5Result.status === 403 ? 'PREŠIEL ✅' : 'ZLYHAL ❌'));

  console.log('\nVšetky testy dokončené.');
}

// Spustenie testov
runTests().catch(error => {
  console.error('Chyba pri spúšťaní testov:', error);
});
