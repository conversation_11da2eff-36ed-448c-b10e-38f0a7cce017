/**
 * Debug skript pre testovanie mazania fotografií z Cloudinary pri úprave hračiek
 * Používa skutočné údaje z databázy
 */

const path = require('path');
const { prisma } = require(path.join(__dirname, '../lib/db'));
const { hashId } = require(path.join(__dirname, '../lib/hashUtils'));

/**
 * Získa hračku s fotografiami z databázy
 */
async function getToyWithImages() {
  try {
    console.log('🔍 Hľadám hračku s fotografiami v databáze...');
    
    const toy = await prisma.toy.findFirst({
      where: {
        images: {
          some: {} // Hračka musí mať aspoň jednu fotografiu
        }
      },
      include: {
        images: true,
        user: {
          select: {
            id: true,
            name: true,
            email: true
          }
        }
      }
    });

    if (!toy) {
      console.log('❌ Nenaš<PERSON> sa žiadna hračka s fotografiami');
      return null;
    }

    console.log('✅ Nájdená hračka:', {
      id: toy.id,
      hashedId: hashId(toy.id),
      name: toy.name,
      imageCount: toy.images.length,
      owner: toy.user.name
    });

    console.log('📸 Fotografie hračky:');
    toy.images.forEach((img, index) => {
      console.log(`  ${index + 1}. ID: ${img.id}, URL: ${img.url}`);
      console.log(`     hashedFilename: ${img.hashedFilename || 'NULL'}`);
      console.log(`     originalFilename: ${img.originalFilename || 'NULL'}`);
    });

    return toy;
  } catch (error) {
    console.error('❌ Chyba pri získavaní hračky:', error);
    return null;
  }
}

/**
 * Simuluje update hračky s odstránením fotografií
 */
async function simulateToyUpdate(toy) {
  const fetch = require('node-fetch');
  
  console.log('\n🧪 Simulujem update hračky s odstránením fotografií...');
  
  // Odstránime prvú fotografiu (ponecháme len zvyšné)
  const originalImageCount = toy.images.length;
  const updatedImages = toy.images.slice(1).map(img => img.url);
  
  console.log(`🗑️  Odstraňujem ${originalImageCount - updatedImages.length} fotografií`);
  console.log(`📸 Ponechávam ${updatedImages.length} fotografií`);

  const updateData = {
    id: hashId(toy.id),
    name: toy.name,
    description: toy.description,
    type: toy.type,
    price: toy.price.toString(),
    deposit: toy.deposit.toString(),
    images: updatedImages
  };

  console.log('\n📤 Posielané dáta na /api/toys/update:');
  console.log({
    id: updateData.id,
    name: updateData.name,
    originalImageCount: originalImageCount,
    newImageCount: updateData.images.length
  });

  try {
    // Simulácia autentifikácie - v skutočnosti by sme potrebovali Firebase token
    const response = await fetch('http://localhost:3001/api/toys/update', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer fake-token', // Toto bude zlyhávať, ale uvidíme logy
        'X-CSRF-Token': 'fake-csrf-token'
      },
      body: JSON.stringify(updateData)
    });

    console.log(`\n📥 Response status: ${response.status} ${response.statusText}`);
    
    const responseText = await response.text();
    console.log('📄 Response body:', responseText);

    return response.ok;
  } catch (error) {
    console.error('❌ Chyba pri volaní API:', error);
    return false;
  }
}

/**
 * Testuje priamo databázovú logiku bez API
 */
async function testDatabaseLogic(toy) {
  console.log('\n🔧 Testujem databázovú logiku priamo...');
  
  try {
    const toyId = toy.id;
    
    // 1. Získanie existujúcich obrázkov (ako v update endpointe)
    console.log(`📋 Získavam existujúce obrázky hračky ${toyId}...`);
    const existingImages = await prisma.toyImage.findMany({
      where: { toyId },
      select: {
        id: true,
        url: true,
        hashedFilename: true,
        originalFilename: true
      }
    });

    console.log(`✅ Nájdených ${existingImages.length} existujúcich obrázkov`);
    
    // 2. Simulácia mazania z Cloudinary (bez skutočného mazania)
    console.log('\n🧪 Simulujem mazanie z Cloudinary...');
    
    for (const image of existingImages) {
      console.log(`🗑️  Simulujem mazanie obrázku:`, {
        id: image.id,
        url: image.url,
        hashedFilename: image.hashedFilename,
        originalFilename: image.originalFilename
      });
      
      // Tu by sa volala deleteCloudinaryImage funkcia
      console.log(`   ✅ Simulované mazanie úspešné`);
    }

    console.log('\n✅ Databázová logika funguje správne');
    return true;
  } catch (error) {
    console.error('❌ Chyba pri testovaní databázovej logiky:', error);
    return false;
  }
}

/**
 * Testuje deleteCloudinaryImage funkciu priamo
 */
async function testCloudinaryFunction(toy) {
  console.log('\n🌩️  Testujem deleteCloudinaryImage funkciu priamo...');
  
  try {
    const { deleteCloudinaryImage } = require(path.join(__dirname, '../lib/cloudinary'));
    
    if (toy.images.length === 0) {
      console.log('⚠️  Hračka nemá žiadne fotografie na testovanie');
      return false;
    }

    const testImage = toy.images[0];
    console.log(`🧪 Testujem mazanie obrázku:`, {
      url: testImage.url,
      hashedFilename: testImage.hashedFilename,
      originalFilename: testImage.originalFilename
    });

    // POZOR: Toto skutočne zmaže obrázok z Cloudinary!
    // Pre bezpečnosť použijeme len dry-run
    console.log('⚠️  POZOR: Toto by skutočne zmazalo obrázok z Cloudinary!');
    console.log('   Pre bezpečnosť spúšťam len simuláciu...');
    
    // Namiesto skutočného mazania len overíme, či funkcia existuje
    if (typeof deleteCloudinaryImage === 'function') {
      console.log('✅ Funkcia deleteCloudinaryImage existuje a je dostupná');
      return true;
    } else {
      console.log('❌ Funkcia deleteCloudinaryImage nie je dostupná');
      return false;
    }
  } catch (error) {
    console.error('❌ Chyba pri testovaní Cloudinary funkcie:', error);
    return false;
  }
}

/**
 * Hlavná funkcia
 */
async function main() {
  console.log('🚀 Spúšťam debug test pre mazanie fotografií hračiek...\n');

  try {
    // 1. Získanie hračky s fotografiami
    const toy = await getToyWithImages();
    if (!toy) {
      console.log('\n❌ Nemožno pokračovať bez hračky s fotografiami');
      return;
    }

    // 2. Test databázovej logiky
    const dbTestResult = await testDatabaseLogic(toy);
    
    // 3. Test Cloudinary funkcie
    const cloudinaryTestResult = await testCloudinaryFunction(toy);
    
    // 4. Test API endpointu (bude zlyhávať kvôli autentifikácii, ale uvidíme logy)
    console.log('\n🌐 Testujem API endpoint (očakáva sa chyba autentifikácie)...');
    const apiTestResult = await simulateToyUpdate(toy);

    // Súhrn
    console.log('\n📊 SÚHRN TESTOV:');
    console.log(`  Databázová logika: ${dbTestResult ? '✅ OK' : '❌ CHYBA'}`);
    console.log(`  Cloudinary funkcia: ${cloudinaryTestResult ? '✅ OK' : '❌ CHYBA'}`);
    console.log(`  API endpoint: ${apiTestResult ? '✅ OK' : '⚠️  CHYBA (očakávaná kvôli autentifikácii)'}`);

    console.log('\n📋 Čo skontrolovať v server logoch:');
    console.log('  1. Logy z API volania (aj keď zlyhalo na autentifikácii)');
    console.log('  2. Akékoľvek error logy súvisiace s importom funkcií');
    console.log('  3. Logy z Cloudinary inicializácie');

  } catch (error) {
    console.error('❌ Kritická chyba:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Spustenie testov
if (require.main === module) {
  main().catch(error => {
    console.error('❌ Neočakávaná chyba:', error);
    process.exit(1);
  });
}

module.exports = {
  getToyWithImages,
  testDatabaseLogic,
  testCloudinaryFunction
};
