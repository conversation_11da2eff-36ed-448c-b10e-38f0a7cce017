/**
 * Test suite for cache prevention mechanisms
 * Tests cache headers, middleware, and cache busting functionality
 */

const http = require('http');
const https = require('https');

// Test configuration
const TEST_CONFIG = {
  baseUrl: process.env.TEST_BASE_URL || 'http://localhost:3000',
  timeout: 10000,
};

/**
 * Make HTTP request and return response with headers
 */
function makeRequest(url, options = {}) {
  return new Promise((resolve, reject) => {
    const isHttps = url.startsWith('https://');
    const client = isHttps ? https : http;
    
    const requestOptions = {
      timeout: TEST_CONFIG.timeout,
      headers: {
        'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1',
        ...options.headers,
      },
    };

    const req = client.get(url, requestOptions, (res) => {
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => {
        resolve({
          status: res.statusCode,
          headers: res.headers,
          data: data,
        });
      });
    });

    req.on('error', reject);
    req.on('timeout', () => {
      req.destroy();
      reject(new Error('Request timeout'));
    });
  });
}

/**
 * Test cache headers for different resource types
 */
async function testCacheHeaders() {
  console.log('\n=== TESTING CACHE HEADERS ===');
  
  const tests = [
    {
      name: 'HTML Page (Home)',
      url: `${TEST_CONFIG.baseUrl}/`,
      expectedHeaders: {
        'cache-control': /no-cache.*no-store.*must-revalidate/i,
        'pragma': 'no-cache',
        'expires': '0',
      },
    },
    {
      name: 'API Endpoint',
      url: `${TEST_CONFIG.baseUrl}/api/csrf`,
      expectedHeaders: {
        'cache-control': /no-cache.*no-store.*must-revalidate/i,
      },
    },
    {
      name: 'Robots.txt',
      url: `${TEST_CONFIG.baseUrl}/robots.txt`,
      expectedHeaders: {
        'cache-control': /public.*max-age=86400/i,
      },
    },
  ];

  let passed = 0;
  let failed = 0;

  for (const test of tests) {
    try {
      console.log(`\nTesting: ${test.name}`);
      console.log(`URL: ${test.url}`);
      
      const response = await makeRequest(test.url);
      console.log(`Status: ${response.status}`);
      
      // Check expected headers
      let testPassed = true;
      for (const [headerName, expectedValue] of Object.entries(test.expectedHeaders)) {
        const actualValue = response.headers[headerName.toLowerCase()];
        
        if (typeof expectedValue === 'string') {
          if (actualValue !== expectedValue) {
            console.log(`❌ ${headerName}: Expected "${expectedValue}", got "${actualValue}"`);
            testPassed = false;
          } else {
            console.log(`✅ ${headerName}: ${actualValue}`);
          }
        } else if (expectedValue instanceof RegExp) {
          if (!expectedValue.test(actualValue || '')) {
            console.log(`❌ ${headerName}: Expected pattern ${expectedValue}, got "${actualValue}"`);
            testPassed = false;
          } else {
            console.log(`✅ ${headerName}: ${actualValue}`);
          }
        }
      }
      
      // Check for security headers
      const securityHeaders = ['x-content-type-options', 'x-frame-options'];
      for (const header of securityHeaders) {
        if (response.headers[header]) {
          console.log(`✅ Security header ${header}: ${response.headers[header]}`);
        }
      }
      
      if (testPassed) {
        console.log(`✅ Test PASSED: ${test.name}`);
        passed++;
      } else {
        console.log(`❌ Test FAILED: ${test.name}`);
        failed++;
      }
      
    } catch (error) {
      console.log(`❌ Test ERROR: ${test.name} - ${error.message}`);
      failed++;
    }
  }

  console.log(`\n=== CACHE HEADERS TEST RESULTS ===`);
  console.log(`Passed: ${passed}`);
  console.log(`Failed: ${failed}`);
  console.log(`Total: ${passed + failed}`);
  
  return { passed, failed };
}

/**
 * Test cache busting API
 */
async function testCacheBustingAPI() {
  console.log('\n=== TESTING CACHE BUSTING API ===');
  
  try {
    const response = await makeRequest(`${TEST_CONFIG.baseUrl}/api/cache-bust`);
    
    if (response.status === 401) {
      console.log('✅ Cache busting API properly protected (401 Unauthorized)');
      return { passed: 1, failed: 0 };
    } else if (response.status === 200) {
      const data = JSON.parse(response.data);
      console.log('✅ Cache busting API accessible');
      console.log(`Build ID: ${data.buildId}`);
      console.log(`Version: ${data.version}`);
      console.log(`Environment: ${data.environment}`);
      return { passed: 1, failed: 0 };
    } else {
      console.log(`❌ Unexpected status: ${response.status}`);
      return { passed: 0, failed: 1 };
    }
  } catch (error) {
    console.log(`❌ Cache busting API test failed: ${error.message}`);
    return { passed: 0, failed: 1 };
  }
}

/**
 * Test mobile Safari specific headers
 */
async function testMobileSafariHeaders() {
  console.log('\n=== TESTING MOBILE SAFARI HEADERS ===');
  
  try {
    const response = await makeRequest(`${TEST_CONFIG.baseUrl}/`, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1',
      },
    });
    
    const mobileHeaders = [
      'x-ua-compatible',
      'x-mobile-cache-control',
      'x-build-version',
      'x-cache-timestamp',
    ];
    
    let found = 0;
    for (const header of mobileHeaders) {
      if (response.headers[header]) {
        console.log(`✅ Mobile header ${header}: ${response.headers[header]}`);
        found++;
      } else {
        console.log(`⚠️  Mobile header ${header}: Not found`);
      }
    }
    
    console.log(`Found ${found}/${mobileHeaders.length} mobile-specific headers`);
    return { passed: found > 0 ? 1 : 0, failed: found === 0 ? 1 : 0 };
    
  } catch (error) {
    console.log(`❌ Mobile Safari headers test failed: ${error.message}`);
    return { passed: 0, failed: 1 };
  }
}

/**
 * Run all tests
 */
async function runAllTests() {
  console.log('🧪 Starting Cache Prevention Tests');
  console.log(`Base URL: ${TEST_CONFIG.baseUrl}`);
  
  const results = {
    passed: 0,
    failed: 0,
  };
  
  // Run test suites
  const cacheHeadersResult = await testCacheHeaders();
  const cacheBustingResult = await testCacheBustingAPI();
  const mobileSafariResult = await testMobileSafariHeaders();
  
  // Aggregate results
  results.passed += cacheHeadersResult.passed + cacheBustingResult.passed + mobileSafariResult.passed;
  results.failed += cacheHeadersResult.failed + cacheBustingResult.failed + mobileSafariResult.failed;
  
  console.log('\n🏁 FINAL TEST RESULTS');
  console.log(`✅ Passed: ${results.passed}`);
  console.log(`❌ Failed: ${results.failed}`);
  console.log(`📊 Success Rate: ${((results.passed / (results.passed + results.failed)) * 100).toFixed(1)}%`);
  
  if (results.failed === 0) {
    console.log('🎉 All cache prevention tests passed!');
    process.exit(0);
  } else {
    console.log('⚠️  Some tests failed. Please check the implementation.');
    process.exit(1);
  }
}

// Run tests if this file is executed directly
if (require.main === module) {
  runAllTests().catch(error => {
    console.error('Test execution failed:', error);
    process.exit(1);
  });
}

module.exports = {
  runAllTests,
  testCacheHeaders,
  testCacheBustingAPI,
  testMobileSafariHeaders,
};
