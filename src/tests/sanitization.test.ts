/**
 * Testy pre sanitizačn<PERSON> funkcie
 * Overujú správne fungovanie všetkých sanitizačných a validačných funkcií
 */

import {
  sanitizeText,
  sanitizeEmail,
  sanitizeNumericInput,
  sanitizeSearchQuery,
  sanitizeHtml,
  validateFileUpload,
  sanitizeCoordinates,
  sanitizePhoneNumber,
  sanitizeUrl,
  sanitizeObject
} from '../lib/inputSanitization';

describe('Input Sanitization Tests', () => {
  
  describe('sanitizeText', () => {
    test('should sanitize basic text input', () => {
      const result = sanitizeText('Hello World', { maxLength: 50 });
      expect(result.isValid).toBe(true);
      expect(result.sanitizedValue).toBe('Hello World');
    });

    test('should remove HTML tags', () => {
      const result = sanitizeText('<script>alert("xss")</script>Hello', { removeHtml: true });
      expect(result.isValid).toBe(true);
      expect(result.sanitizedValue).toBe('Hello');
    });

    test('should trim whitespace', () => {
      const result = sanitizeText('  Hello World  ', { trim: true });
      expect(result.isValid).toBe(true);
      expect(result.sanitizedValue).toBe('Hello World');
    });

    test('should enforce max length', () => {
      const result = sanitizeText('Very long text that exceeds limit', { maxLength: 10 });
      expect(result.sanitizedValue).toBe('Very long ');
      expect(result.errors.length).toBeGreaterThan(0);
    });

    test('should remove special characters when not allowed', () => {
      const result = sanitizeText('Hello@World!', { allowSpecialChars: false });
      expect(result.sanitizedValue).toBe('HelloWorld');
    });
  });

  describe('sanitizeEmail', () => {
    test('should validate correct email', () => {
      const result = sanitizeEmail('<EMAIL>');
      expect(result.isValid).toBe(true);
      expect(result.sanitizedValue).toBe('<EMAIL>');
    });

    test('should reject invalid email format', () => {
      const result = sanitizeEmail('invalid-email');
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Neplatný formát emailu');
    });

    test('should convert to lowercase', () => {
      const result = sanitizeEmail('<EMAIL>');
      expect(result.sanitizedValue).toBe('<EMAIL>');
    });

    test('should reject too long email', () => {
      const longEmail = 'a'.repeat(250) + '@example.com';
      const result = sanitizeEmail(longEmail);
      expect(result.isValid).toBe(false);
    });
  });

  describe('sanitizeNumericInput', () => {
    test('should validate correct number', () => {
      const result = sanitizeNumericInput(42);
      expect(result.isValid).toBe(true);
      expect(result.sanitizedValue).toBe(42);
    });

    test('should parse string numbers', () => {
      const result = sanitizeNumericInput('42.5');
      expect(result.isValid).toBe(true);
      expect(result.sanitizedValue).toBe(42.5);
    });

    test('should enforce min/max limits', () => {
      const result = sanitizeNumericInput(150, { min: 0, max: 100 });
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Číslo nesmie byť väčšie ako 100');
    });

    test('should handle decimal places', () => {
      const result = sanitizeNumericInput(42.12345, { decimals: 2 });
      expect(result.sanitizedValue).toBe(42.12);
    });

    test('should reject negative numbers when not allowed', () => {
      const result = sanitizeNumericInput(-5, { allowNegative: false });
      expect(result.isValid).toBe(false);
    });
  });

  describe('sanitizeSearchQuery', () => {
    test('should sanitize basic search query', () => {
      const result = sanitizeSearchQuery('hello world');
      expect(result.isValid).toBe(true);
      expect(result.sanitizedValue).toBe('hello world');
    });

    test('should remove SQL injection attempts', () => {
      const result = sanitizeSearchQuery('hello; DROP TABLE users;', { preventSqlInjection: true });
      expect(result.sanitizedValue).not.toContain('DROP');
      expect(result.sanitizedValue).not.toContain(';');
    });

    test('should remove XSS attempts', () => {
      const result = sanitizeSearchQuery('<script>alert("xss")</script>');
      expect(result.sanitizedValue).not.toContain('<script>');
      expect(result.sanitizedValue).not.toContain('javascript:');
    });

    test('should enforce max length', () => {
      const longQuery = 'a'.repeat(150);
      const result = sanitizeSearchQuery(longQuery, { maxLength: 100 });
      expect(result.sanitizedValue?.length).toBe(100);
    });
  });

  describe('sanitizeHtml', () => {
    test('should allow safe HTML tags', () => {
      const result = sanitizeHtml('<p>Hello <strong>world</strong></p>', {
        allowedTags: ['p', 'strong']
      });
      expect(result.sanitizedValue).toContain('<p>');
      expect(result.sanitizedValue).toContain('<strong>');
    });

    test('should remove dangerous scripts', () => {
      const result = sanitizeHtml('<script>alert("xss")</script><p>Safe content</p>');
      expect(result.sanitizedValue).not.toContain('<script>');
      expect(result.sanitizedValue).toContain('Safe content');
    });

    test('should remove iframe tags', () => {
      const result = sanitizeHtml('<iframe src="evil.com"></iframe>');
      expect(result.sanitizedValue).not.toContain('<iframe>');
    });
  });

  describe('validateFileUpload', () => {
    test('should validate correct image file', () => {
      const mockFile = new File([''], 'test.jpg', { type: 'image/jpeg' });
      Object.defineProperty(mockFile, 'size', { value: 1024 * 1024 }); // 1MB
      
      const result = validateFileUpload(mockFile);
      expect(result.isValid).toBe(true);
    });

    test('should reject invalid file type', () => {
      const mockFile = new File([''], 'test.txt', { type: 'text/plain' });
      
      const result = validateFileUpload(mockFile);
      expect(result.isValid).toBe(false);
      expect(result.errors[0]).toContain('Nepovolený typ súboru');
    });

    test('should reject too large file', () => {
      const mockFile = new File([''], 'test.jpg', { type: 'image/jpeg' });
      Object.defineProperty(mockFile, 'size', { value: 10 * 1024 * 1024 }); // 10MB
      
      const result = validateFileUpload(mockFile, { maxSize: 5 * 1024 * 1024 });
      expect(result.isValid).toBe(false);
      expect(result.errors[0]).toContain('príliš veľký');
    });
  });

  describe('sanitizeCoordinates', () => {
    test('should validate correct coordinates', () => {
      const result = sanitizeCoordinates(48.1486, 17.1077); // Bratislava
      expect(result.isValid).toBe(true);
      expect(result.sanitizedValue?.latitude).toBe(48.1486);
      expect(result.sanitizedValue?.longitude).toBe(17.1077);
    });

    test('should reject invalid latitude', () => {
      const result = sanitizeCoordinates(95, 17.1077); // Invalid latitude
      expect(result.isValid).toBe(false);
    });

    test('should reject invalid longitude', () => {
      const result = sanitizeCoordinates(48.1486, 185); // Invalid longitude
      expect(result.isValid).toBe(false);
    });

    test('should handle precision', () => {
      const result = sanitizeCoordinates(48.123456789, 17.123456789, { precision: 4 });
      expect(result.sanitizedValue?.latitude).toBe(48.1235);
      expect(result.sanitizedValue?.longitude).toBe(17.1235);
    });
  });

  describe('sanitizePhoneNumber', () => {
    test('should validate correct phone number', () => {
      const result = sanitizePhoneNumber('+421 123 456 789');
      expect(result.isValid).toBe(true);
      expect(result.sanitizedValue).toBe('+421 123 456 789');
    });

    test('should remove invalid characters', () => {
      const result = sanitizePhoneNumber('+421abc123def456');
      expect(result.sanitizedValue).toBe('+421123456');
    });

    test('should handle empty input', () => {
      const result = sanitizePhoneNumber('');
      expect(result.isValid).toBe(true);
      expect(result.sanitizedValue).toBe('');
    });
  });

  describe('sanitizeUrl', () => {
    test('should validate correct URL', () => {
      const result = sanitizeUrl('https://example.com');
      expect(result.isValid).toBe(true);
      expect(result.sanitizedValue).toBe('https://example.com');
    });

    test('should add https protocol', () => {
      const result = sanitizeUrl('example.com');
      expect(result.sanitizedValue).toBe('https://example.com');
    });

    test('should reject dangerous protocols', () => {
      const result = sanitizeUrl('javascript:alert("xss")');
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Nebezpečný protokol v URL');
    });
  });

  describe('sanitizeObject', () => {
    test('should sanitize object with multiple fields', () => {
      const input = {
        name: 'John Doe',
        email: '<EMAIL>',
        age: '25'
      };

      const schema = {
        name: (value: any) => sanitizeText(value, { maxLength: 50 }),
        email: (value: any) => sanitizeEmail(value),
        age: (value: any) => sanitizeNumericInput(value, { min: 0, max: 120 })
      };

      const result = sanitizeObject(input, schema);
      
      expect(result.isValid).toBe(true);
      expect(result.sanitizedData.name).toBe('John Doe');
      expect(result.sanitizedData.email).toBe('<EMAIL>');
      expect(result.sanitizedData.age).toBe(25);
    });

    test('should handle validation errors', () => {
      const input = {
        name: '',
        email: 'invalid-email'
      };

      const schema = {
        name: (value: any) => sanitizeText(value, { minLength: 1 }),
        email: (value: any) => sanitizeEmail(value)
      };

      const result = sanitizeObject(input, schema);
      
      expect(result.isValid).toBe(false);
      expect(Object.keys(result.errors)).toContain('name');
      expect(Object.keys(result.errors)).toContain('email');
    });
  });
});

// Testy pre edge cases a bezpečnostné scenáre
describe('Security Tests', () => {
  test('should prevent XSS in text inputs', () => {
    const xssAttempts = [
      '<script>alert("xss")</script>',
      'javascript:alert("xss")',
      '<img src="x" onerror="alert(1)">',
      '<svg onload="alert(1)">',
      '"><script>alert("xss")</script>'
    ];

    xssAttempts.forEach(attempt => {
      const result = sanitizeText(attempt, { removeHtml: true });
      expect(result.sanitizedValue).not.toContain('<script>');
      expect(result.sanitizedValue).not.toContain('javascript:');
      expect(result.sanitizedValue).not.toContain('onerror');
      expect(result.sanitizedValue).not.toContain('onload');
    });
  });

  test('should prevent SQL injection in search queries', () => {
    const sqlAttempts = [
      "'; DROP TABLE users; --",
      "' OR '1'='1",
      "UNION SELECT * FROM users",
      "; DELETE FROM toys WHERE 1=1; --"
    ];

    sqlAttempts.forEach(attempt => {
      const result = sanitizeSearchQuery(attempt, { preventSqlInjection: true });
      expect(result.sanitizedValue).not.toContain('DROP');
      expect(result.sanitizedValue).not.toContain('DELETE');
      expect(result.sanitizedValue).not.toContain('UNION');
      expect(result.sanitizedValue).not.toContain('--');
    });
  });
});
