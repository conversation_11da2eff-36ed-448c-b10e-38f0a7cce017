/**
 * <PERSON>y pre podporu slovenských diakritick<PERSON>ch znakov
 * <PERSON>uj<PERSON>, že sanitizačné funkcie správne zachovávajú slovenské znaky
 */

import {
  sanitizeText,
  sanitizeSearchQuery
} from '../lib/inputSanitization';

describe('Slovak Diacritics Support Tests', () => {
  const slovakText = 'Ján Novák z Bratislavy má krásnu hračku';
  const slovakTextWithDiacritics = 'áčďéíľňóôŕšťúýžÁČĎÉÍĽŇÓÔŔŠŤÚÝŽ';
  const slovakCityNames = ['Bratislava', 'Košice', 'Prešov', 'Žilina', 'Banská Bystrica', 'Nitra', 'Trenčín', 'Trnava'];

  describe('sanitizeText with Slovak diacritics', () => {
    test('should preserve Slovak diacritics when allowSlovakDiacritics is true', () => {
      const result = sanitizeText(slovakText, { 
        allowSpecialChars: false, 
        allowSlovakDiacritics: true 
      });
      
      expect(result.isValid).toBe(true);
      expect(result.sanitizedValue).toBe(slovakText);
    });

    test('should preserve all Slovak diacritical characters', () => {
      const result = sanitizeText(slovakTextWithDiacritics, { 
        allowSpecialChars: false, 
        allowSlovakDiacritics: true 
      });
      
      expect(result.isValid).toBe(true);
      expect(result.sanitizedValue).toBe(slovakTextWithDiacritics);
    });

    test('should remove Slovak diacritics when allowSlovakDiacritics is false', () => {
      const result = sanitizeText(slovakText, { 
        allowSpecialChars: false, 
        allowSlovakDiacritics: false 
      });
      
      expect(result.isValid).toBe(true);
      expect(result.sanitizedValue).not.toBe(slovakText);
      expect(result.sanitizedValue).toBe('Jn Novk z Bratislavy m krsnu hracu');
    });

    test('should work with Slovak city names', () => {
      slovakCityNames.forEach(city => {
        const result = sanitizeText(city, { 
          allowSpecialChars: false, 
          allowSlovakDiacritics: true 
        });
        
        expect(result.isValid).toBe(true);
        expect(result.sanitizedValue).toBe(city);
      });
    });

    test('should still remove dangerous characters while preserving Slovak diacritics', () => {
      const dangerousText = 'Ján<script>alert("xss")</script>Novák';
      const result = sanitizeText(dangerousText, { 
        allowSpecialChars: false, 
        allowSlovakDiacritics: true 
      });
      
      expect(result.isValid).toBe(true);
      expect(result.sanitizedValue).toBe('JnNovk');
      expect(result.sanitizedValue).not.toContain('<script>');
    });
  });

  describe('sanitizeSearchQuery with Slovak diacritics', () => {
    test('should preserve Slovak diacritics in search queries', () => {
      const searchQuery = 'hračka pre dieťa';
      const result = sanitizeSearchQuery(searchQuery, { 
        allowSlovakDiacritics: true 
      });
      
      expect(result.isValid).toBe(true);
      expect(result.sanitizedValue).toBe(searchQuery);
    });

    test('should remove Slovak diacritics when allowSlovakDiacritics is false', () => {
      const searchQuery = 'hračka pre dieťa';
      const result = sanitizeSearchQuery(searchQuery, { 
        allowSlovakDiacritics: false 
      });
      
      expect(result.isValid).toBe(true);
      expect(result.sanitizedValue).not.toBe(searchQuery);
      expect(result.sanitizedValue).toBe('hraka pre diea');
    });

    test('should work with wildcards and Slovak diacritics', () => {
      const searchQuery = 'hračka*';
      const result = sanitizeSearchQuery(searchQuery, { 
        allowSlovakDiacritics: true,
        allowWildcards: true
      });
      
      expect(result.isValid).toBe(true);
      expect(result.sanitizedValue).toBe(searchQuery);
    });

    test('should still prevent SQL injection while preserving Slovak diacritics', () => {
      const maliciousQuery = 'hračka; DROP TABLE users;';
      const result = sanitizeSearchQuery(maliciousQuery, { 
        allowSlovakDiacritics: true,
        preventSqlInjection: true
      });
      
      expect(result.isValid).toBe(true);
      expect(result.sanitizedValue).toBe('hračka   ;');
      expect(result.sanitizedValue).not.toContain('DROP');
      expect(result.sanitizedValue).not.toContain('TABLE');
    });
  });

  describe('User profile field sanitization', () => {
    test('should work for user names with Slovak diacritics', () => {
      const userName = 'Ján Kováč';
      const result = sanitizeText(userName, { 
        maxLength: 100, 
        allowSpecialChars: false, 
        allowSlovakDiacritics: true 
      });
      
      expect(result.isValid).toBe(true);
      expect(result.sanitizedValue).toBe(userName);
    });

    test('should work for city names with Slovak diacritics', () => {
      const cityName = 'Žilina';
      const result = sanitizeText(cityName, { 
        maxLength: 100, 
        allowSpecialChars: false, 
        allowSlovakDiacritics: true 
      });
      
      expect(result.isValid).toBe(true);
      expect(result.sanitizedValue).toBe(cityName);
    });

    test('should work for addresses with Slovak diacritics', () => {
      const address = 'Hlavná ulica 123, Bratislava';
      const result = sanitizeText(address, { 
        maxLength: 200, 
        allowSpecialChars: true  // addresses allow special chars
      });
      
      expect(result.isValid).toBe(true);
      expect(result.sanitizedValue).toBe(address);
    });
  });

  describe('Toy type and status names with Slovak diacritics', () => {
    test('should work for toy type names with Slovak diacritics', () => {
      const toyTypeName = 'Stavebnica';
      const result = sanitizeText(toyTypeName, { 
        maxLength: 50, 
        allowSpecialChars: false, 
        allowSlovakDiacritics: true 
      });
      
      expect(result.isValid).toBe(true);
      expect(result.sanitizedValue).toBe(toyTypeName);
    });

    test('should work for toy status names with Slovak diacritics', () => {
      const toyStatusName = 'Dostupná';
      const result = sanitizeText(toyStatusName, { 
        maxLength: 50, 
        allowSpecialChars: false, 
        allowSlovakDiacritics: true 
      });
      
      expect(result.isValid).toBe(true);
      expect(result.sanitizedValue).toBe(toyStatusName);
    });
  });
});
