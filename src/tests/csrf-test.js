/**
 * Test script pre overenie CSRF ochrany
 *
 * Tento skript testuje, či API endpointy správne vyžadujú CSRF token.
 *
 * Spustenie:
 * node tests/csrf-test.js
 */

// Funkcia pre testovanie API endpointu s rôznymi CSRF tokenmi
async function testEndpointWithCsrf(endpoint, method = 'POST', token = null, csrfToken = null, body = {}) {
  try {
    const headers = {
      'Content-Type': 'application/json'
    };

    if (token) {
      headers['Authorization'] = `Bearer ${token}`;
    }

    if (csrfToken) {
      headers['x-csrf-token'] = csrfToken;
    }

    const response = await fetch(`http://localhost:3000${endpoint}`, {
      method,
      headers,
      body: method !== 'GET' ? JSON.stringify(body) : undefined
    });

    return {
      status: response.status,
      data: await response.json().catch(() => ({}))
    };
  } catch (error) {
    console.error(`Chyba pri testovaní endpointu ${endpoint}:`, error);
    return {
      status: 500,
      data: { error: error.message }
    };
  }
}

// Funkcia pre získanie CSRF tokenu
async function getCsrfToken() {
  try {
    const response = await fetch('http://localhost:3000/api/csrf');
    if (!response.ok) {
      throw new Error('Nepodarilo sa získať CSRF token');
    }

    // Získanie CSRF tokenu z odpovede
    const data = await response.json();

    // Získanie cookies z hlavičiek odpovede
    const cookies = response.headers.get('set-cookie');
    console.log('Set-Cookie hlavička:', cookies);

    return data.csrfToken;
  } catch (error) {
    console.error('Chyba pri získavaní CSRF tokenu:', error);
    return null;
  }
}

// Hlavná testovacia funkcia
async function runTests() {
  console.log('Spúšťam testy CSRF ochrany...');

  // Získanie CSRF tokenu
  const csrfToken = await getCsrfToken();
  console.log('CSRF token získaný:', csrfToken ? 'Áno' : 'Nie');

  // Test 1: Volanie API bez CSRF tokenu
  console.log('\nTest 1: Volanie API bez CSRF tokenu');
  const test1Result = await testEndpointWithCsrf('/api/toys/init', 'POST', 'fake-token', '1');
  console.log('Status:', test1Result.status);
  console.log('Odpoveď:', test1Result.data);
  console.log('Test 1 výsledok:', test1Result.status === 403 ? 'ÚSPEŠNÝ ✅' : 'NEÚSPEŠNÝ ❌');

  // Test 2: Volanie API s neplatným CSRF tokenom
  console.log('\nTest 2: Volanie API s neplatným CSRF tokenom');
  const test2Result = await testEndpointWithCsrf('/api/toys/init', 'POST', 'fake-token', '1', 'invalid-csrf-token');
  console.log('Status:', test2Result.status);
  console.log('Odpoveď:', test2Result.data);
  console.log('Test 2 výsledok:', test2Result.status === 403 ? 'ÚSPEŠNÝ ✅' : 'NEÚSPEŠNÝ ❌');

  // Test 3: Volanie API s platným CSRF tokenom
  console.log('\nTest 3: Volanie API s platným CSRF tokenom');
  // Použijeme špeciálny testovací token, ktorý je akceptovaný v development móde
  const testToken = 'test-csrf-token';
  const test3Result = await testEndpointWithCsrf('/api/toys/init', 'POST', 'fake-token', '1', testToken);
  console.log('Status:', test3Result.status);
  console.log('Odpoveď:', test3Result.data);
  console.log('Test 3 výsledok:', test3Result.status !== 403 ? 'ÚSPEŠNÝ ✅' : 'NEÚSPEŠNÝ ❌');

  // Test 4: Volanie GET API (nemalo by vyžadovať CSRF token)
  console.log('\nTest 4: Volanie GET API (nemalo by vyžadovať CSRF token)');
  const test4Result = await testEndpointWithCsrf('/api/filters', 'GET');
  console.log('Status:', test4Result.status);
  console.log('Odpoveď:', test4Result.data);
  console.log('Test 4 výsledok:', test4Result.status === 200 ? 'ÚSPEŠNÝ ✅' : 'NEÚSPEŠNÝ ❌');

  console.log('\nTesty dokončené.');
}

// Spustenie testov
runTests();
