/**
 * Test script for email notification system
 * This script tests the email notification functionality without requiring database changes
 */

const {
  queueReservationCreatedEmail,
  queueReservationApprovedEmail,
  queueReservationRequestEmail,
  getEmailQueueStatus,
  clearEmailQueue,
  forceProcessEmailQueue
} = require('../lib/emailNotificationService');

const { validateEmailConfig } = require('../lib/emailService');

// Test data - skutočný email pre testovanie
const testReservationCreatedData = {
  userId: 1,
  userName: 'Mi<PERSON>',
  userEmail: '<EMAIL>',
  toyName: 'LEGO Technic Bugatti Chiron',
  reservationId: 'hash_test_123456',
  position: 0,
  queueLength: 1
};

const testReservationApprovedData = {
  userId: 1,
  userName: 'Michal <PERSON>',
  userEmail: '<EMAIL>',
  toyName: 'LEGO Technic Buga<PERSON>',
  reservationId: 'hash_test_123456',
  ownerName: '<PERSON><PERSON>',
  ownerEmail: '<EMAIL>',
  ownerPhone: '+421901234567',
  ownerCity: 'Bratislava'
};

const testReservationRequestData = {
  ownerId: 2,
  ownerName: 'Ján Novák',
  ownerEmail: '<EMAIL>', // Using same email for testing
  toyName: 'LEGO Technic Bugatti Chiron',
  reservationId: 'hash_test_123456',
  requesterName: 'Michal Gašparík',
  requesterEmail: '<EMAIL>',
  requesterPhone: '+421901234567',
  requesterCity: 'Bratislava',
  position: 0,
  queueLength: 1
};

async function runEmailTests() {
  console.log('🧪 Spúšťam testy emailového notifikačného systému...\n');

  // Test 1: Validate email configuration
  console.log('1️⃣ Test konfigurácie emailu:');
  const configValid = validateEmailConfig();
  console.log(`   Konfigurácia je ${configValid ? '✅ platná' : '❌ neplatná'}\n`);

  if (!configValid) {
    console.log('❌ Emailová konfigurácia nie je platná. Skontrolujte .env súbor.');
    return;
  }

  // Test 2: Clear queue and check initial status
  console.log('2️⃣ Test počiatočného stavu fronty:');
  clearEmailQueue();
  const initialStatus = getEmailQueueStatus();
  console.log(`   Počiatočný stav fronty: ${initialStatus.queueLength} emailov`);
  console.log(`   Spracováva sa: ${initialStatus.isProcessing ? 'Áno' : 'Nie'}\n`);

  // Test 3: Queue reservation created email
  console.log('3️⃣ Test pridania emailu o vytvorení rezervácie:');
  const createdEmailId = queueReservationCreatedEmail(testReservationCreatedData);
  console.log(`   Email ID: ${createdEmailId}`);
  
  const statusAfterCreated = getEmailQueueStatus();
  console.log(`   Stav fronty po pridaní: ${statusAfterCreated.queueLength} emailov\n`);

  // Test 4: Queue reservation approved email
  console.log('4️⃣ Test pridania emailu o schválení rezervácie:');
  const approvedEmailId = queueReservationApprovedEmail(testReservationApprovedData);
  console.log(`   Email ID: ${approvedEmailId}`);

  const statusAfterApproved = getEmailQueueStatus();
  console.log(`   Stav fronty po pridaní: ${statusAfterApproved.queueLength} emailov\n`);

  // Test 5: Queue reservation request email (to toy owner)
  console.log('5️⃣ Test pridania emailu o požiadavke na rezerváciu (pre vlastníka):');
  const requestEmailId = queueReservationRequestEmail(testReservationRequestData);
  console.log(`   Email ID: ${requestEmailId}`);

  const statusAfterRequest = getEmailQueueStatus();
  console.log(`   Stav fronty po pridaní: ${statusAfterRequest.queueLength} emailov\n`);

  // Test 6: Display queue contents
  console.log('6️⃣ Obsah fronty emailov:');
  statusAfterRequest.pendingEmails.forEach((email, index) => {
    console.log(`   ${index + 1}. ${email.type} (ID: ${email.id})`);
    console.log(`      Počet pokusov: ${email.retryCount}`);
    console.log(`      Naplánované na: ${email.scheduledAt}`);
  });
  console.log();

  // Test 7: Process email queue (this will actually send emails if RESEND_API_KEY is valid)
  console.log('7️⃣ Test spracovania fronty emailov:');
  console.log('   📧 Odosielam testovacie <NAME_EMAIL>...');
  console.log('   📧 Typy emailov: potvrdenie rezervácie, schválenie rezervácie, požiadavka na rezerváciu');

  console.log('   Spracovávam frontu...');
  await forceProcessEmailQueue();
  
  const finalStatus = getEmailQueueStatus();
  console.log(`   Finálny stav fronty: ${finalStatus.queueLength} emailov`);
  console.log(`   Spracováva sa: ${finalStatus.isProcessing ? 'Áno' : 'Nie'}\n`);

  console.log('✅ Testy emailového systému dokončené!');
}

// Test email preferences functions (mock implementation for testing)
async function testEmailPreferences() {
  console.log('\n📧 Test emailových preferencií:');
  
  // Mock user preferences check
  console.log('   Simulujem kontrolu používateľských preferencií...');
  
  // Simulate different preference scenarios
  const scenarios = [
    { userId: 1, type: 'reservation_created', expected: true, description: 'Aktívny používateľ s povolenými notifikáciami' },
    { userId: 2, type: 'reservation_created', expected: false, description: 'Používateľ s vypnutými notifikáciami' },
    { userId: 3, type: 'reservation_approved', expected: true, description: 'Používateľ s povolenými schváleniami' },
    { userId: 4, type: 'reservation_approved', expected: false, description: 'Anonymizovaný používateľ' },
    { userId: 5, type: 'reservation_request', expected: true, description: 'Vlastník hračky s povolenými požiadavkami' },
    { userId: 6, type: 'reservation_request', expected: false, description: 'Neaktívny vlastník hračky' }
  ];

  scenarios.forEach((scenario, index) => {
    console.log(`   ${index + 1}. ${scenario.description}`);
    console.log(`      Typ: ${scenario.type}`);
    console.log(`      Očakávaný výsledok: ${scenario.expected ? 'Odoslať' : 'Neodosielať'}`);
  });

  console.log('\n   ℹ️  Pre plnú funkčnosť je potrebné spustiť SQL migráciu:');
  console.log('      mysql -u username -p database < prisma/migrations/add-email-preferences.sql');
}

// Test unsubscribe functionality
async function testUnsubscribe() {
  console.log('\n🚫 Test odhlásenia z emailov:');
  
  console.log('   Simulujem generovanie unsubscribe tokenu...');
  const mockToken = 'test_unsubscribe_token_' + Date.now();
  console.log(`   Mock token: ${mockToken}`);
  
  console.log('   Unsubscribe URL by bol:');
  const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'https://swapka.sk';
  console.log(`   ${baseUrl}/api/email/unsubscribe?token=${mockToken}`);
  
  console.log('\n   ℹ️  Pre testovanie unsubscribe funkcionality navštívte URL vyššie');
  console.log('      (po spustení migrácie databázy)');
}

// Main test runner
async function main() {
  try {
    await runEmailTests();
    await testEmailPreferences();
    await testUnsubscribe();
  } catch (error) {
    console.error('❌ Chyba pri testovaní:', error);
  }
}

// Run tests if this file is executed directly
if (require.main === module) {
  main();
}

module.exports = {
  runEmailTests,
  testEmailPreferences,
  testUnsubscribe
};
