/**
 * Test pre overenie mazania fotografií z Cloudinary pri úprave hračiek
 *
 * Tento test overuje, že pri úprave hračky sa staré fotografie automaticky
 * odstránia z Cloudinary úložiska, nie len z databázy.
 */

console.log('🧪 Spúšťam test mazania fotografií z Cloudinary pri úprave hračiek...\n');

// Simulácia testu - overenie, že kód obsahuje správne volania
function testPhotoDeleteionImplementation() {
  const fs = require('fs');
  const path = require('path');

  console.log('=== Test 1: Overenie implementácie v /api/toys/update ===');

  try {
    const updateRoutePath = path.join(__dirname, '../app/api/toys/update/route.ts');
    const updateRouteContent = fs.readFileSync(updateRoutePath, 'utf8');

    // Kontrola importu deleteCloudinaryImage
    const hasImport = updateRouteContent.includes('import { deleteCloudinaryImage }');
    console.log(`✓ Import deleteCloudinaryImage: ${hasImport ? '✅ NÁJDENÝ' : '❌ CHÝBA'}`);

    // Kontrola získania existujúcich obrázkov
    const hasImageFetch = updateRouteContent.includes('prisma.toyImage.findMany');
    console.log(`✓ Získanie existujúcich obrázkov: ${hasImageFetch ? '✅ NÁJDENÉ' : '❌ CHÝBA'}`);

    // Kontrola volania deleteCloudinaryImage
    const hasCloudinaryDelete = updateRouteContent.includes('deleteCloudinaryImage(');
    console.log(`✓ Volanie deleteCloudinaryImage: ${hasCloudinaryDelete ? '✅ NÁJDENÉ' : '❌ CHÝBA'}`);

    // Kontrola error handling
    const hasErrorHandling = updateRouteContent.includes('catch (imageError)');
    console.log(`✓ Error handling pre Cloudinary: ${hasErrorHandling ? '✅ NÁJDENÉ' : '❌ CHÝBA'}`);

    const test1Passed = hasImport && hasImageFetch && hasCloudinaryDelete && hasErrorHandling;
    console.log(`\nTest 1 výsledok: ${test1Passed ? '✅ PREŠIEL' : '❌ ZLYHAL'}\n`);

    return test1Passed;
  } catch (error) {
    console.log(`❌ Chyba pri čítaní súboru: ${error.message}\n`);
    return false;
  }
}

function testAdminPhotoDeleteionImplementation() {
  const fs = require('fs');
  const path = require('path');

  console.log('=== Test 2: Overenie implementácie v /api/admin/toys/update ===');

  try {
    const adminUpdateRoutePath = path.join(__dirname, '../app/api/admin/toys/update/route.ts');
    const adminUpdateRouteContent = fs.readFileSync(adminUpdateRoutePath, 'utf8');

    // Kontrola importu deleteCloudinaryImage
    const hasImport = adminUpdateRouteContent.includes('import { deleteCloudinaryImage }');
    console.log(`✓ Import deleteCloudinaryImage: ${hasImport ? '✅ NÁJDENÝ' : '❌ CHÝBA'}`);

    // Kontrola získania existujúcich obrázkov
    const hasImageFetch = adminUpdateRouteContent.includes('prisma.toyImage.findMany');
    console.log(`✓ Získanie existujúcich obrázkov: ${hasImageFetch ? '✅ NÁJDENÉ' : '❌ CHÝBA'}`);

    // Kontrola volania deleteCloudinaryImage
    const hasCloudinaryDelete = adminUpdateRouteContent.includes('deleteCloudinaryImage(');
    console.log(`✓ Volanie deleteCloudinaryImage: ${hasCloudinaryDelete ? '✅ NÁJDENÉ' : '❌ CHÝBA'}`);

    // Kontrola error handling
    const hasErrorHandling = adminUpdateRouteContent.includes('catch (imageError)');
    console.log(`✓ Error handling pre Cloudinary: ${hasErrorHandling ? '✅ NÁJDENÉ' : '❌ CHÝBA'}`);

    // Kontrola admin logov
    const hasAdminLogs = adminUpdateRouteContent.includes('Admin:');
    console.log(`✓ Admin špecifické logy: ${hasAdminLogs ? '✅ NÁJDENÉ' : '❌ CHÝBA'}`);

    const test2Passed = hasImport && hasImageFetch && hasCloudinaryDelete && hasErrorHandling && hasAdminLogs;
    console.log(`\nTest 2 výsledok: ${test2Passed ? '✅ PREŠIEL' : '❌ ZLYHAL'}\n`);

    return test2Passed;
  } catch (error) {
    console.log(`❌ Chyba pri čítaní súboru: ${error.message}\n`);
    return false;
  }
}

function testCloudinaryFunctionExists() {
  const fs = require('fs');
  const path = require('path');

  console.log('=== Test 3: Overenie existencie deleteCloudinaryImage funkcie ===');

  try {
    const cloudinaryPath = path.join(__dirname, '../lib/cloudinary.ts');
    const cloudinaryContent = fs.readFileSync(cloudinaryPath, 'utf8');

    // Kontrola existencie funkcie
    const hasFunction = cloudinaryContent.includes('export async function deleteCloudinaryImage');
    console.log(`✓ Funkcia deleteCloudinaryImage: ${hasFunction ? '✅ EXISTUJE' : '❌ NEEXISTUJE'}`);

    // Kontrola parametrov funkcie
    const hasCorrectParams = cloudinaryContent.includes('imageIdentifier: string, hashedFilename?: string');
    console.log(`✓ Správne parametre funkcie: ${hasCorrectParams ? '✅ ÁNO' : '❌ NIE'}`);

    // Kontrola Cloudinary API volania
    const hasCloudinaryCall = cloudinaryContent.includes('cloudinary.uploader.destroy');
    console.log(`✓ Cloudinary API volanie: ${hasCloudinaryCall ? '✅ NÁJDENÉ' : '❌ CHÝBA'}`);

    const test3Passed = hasFunction && hasCorrectParams && hasCloudinaryCall;
    console.log(`\nTest 3 výsledok: ${test3Passed ? '✅ PREŠIEL' : '❌ ZLYHAL'}\n`);

    return test3Passed;
  } catch (error) {
    console.log(`❌ Chyba pri čítaní súboru: ${error.message}\n`);
    return false;
  }
}

// Hlavná testovacia funkcia
async function runTests() {
  console.log('🧪 Spúšťam všetky testy...\n');

  const test1Result = testPhotoDeleteionImplementation();
  const test2Result = testAdminPhotoDeleteionImplementation();
  const test3Result = testCloudinaryFunctionExists();

  const allTestsPassed = test1Result && test2Result && test3Result;

  console.log('=== SÚHRN TESTOV ===');
  console.log(`Test 1 (User update endpoint): ${test1Result ? '✅ PREŠIEL' : '❌ ZLYHAL'}`);
  console.log(`Test 2 (Admin update endpoint): ${test2Result ? '✅ PREŠIEL' : '❌ ZLYHAL'}`);
  console.log(`Test 3 (Cloudinary function): ${test3Result ? '✅ PREŠIEL' : '❌ ZLYHAL'}`);
  console.log(`\n🎯 CELKOVÝ VÝSLEDOK: ${allTestsPassed ? '✅ VŠETKY TESTY PREŠLI' : '❌ NIEKTORÉ TESTY ZLYHALI'}`);

  if (allTestsPassed) {
    console.log('\n🎉 Implementácia mazania fotografií z Cloudinary je správna!');
    console.log('✓ Fotografie sa budú automaticky mazať z Cloudinary pri úprave hračiek');
    console.log('✓ Implementácia je konzistentná pre používateľov aj administrátorov');
    console.log('✓ Error handling zabezpečuje stabilitu aplikácie');
  } else {
    console.log('\n⚠️  Niektoré časti implementácie chýbajú alebo sú nesprávne.');
    console.log('   Skontrolujte výsledky testov vyššie a opravte chyby.');
  }

  return allTestsPassed;
}

// Spustenie testov
runTests().catch(error => {
  console.error('❌ Chyba pri spúšťaní testov:', error);
  process.exit(1);
}).then(success => {
  process.exit(success ? 0 : 1);
});
