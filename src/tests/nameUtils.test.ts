/**
 * Tests for name anonymization utility functions
 */

import { anonymizeName, isAnonymizedName, safeAnonymizeName } from '../lib/nameUtils';

describe('Name Anonymization Tests', () => {
  describe('anonymizeName', () => {
    test('should anonymize full name correctly', () => {
      expect(anonymizeName('<PERSON><PERSON>')).toBe('<PERSON><PERSON> N***');
      expect(anonymizeName('<PERSON>')).toBe('<PERSON>***');
      expect(anonymizeName('<PERSON>')).toBe('<PERSON> S***');
    });

    test('should handle single name', () => {
      expect(anonymizeName('<PERSON>')).toBe('<PERSON>');
      expect(anonymizeName('Anna')).toBe('Anna');
    });

    test('should handle empty or invalid input', () => {
      expect(anonymizeName('')).toBe('Neznámy použí<PERSON>');
      expect(anonymizeName('   ')).toBe('Nezná<PERSON> používateľ');
      expect(anonymizeName(null as any)).toBe('Neznámy používateľ');
      expect(anonymizeName(undefined as any)).toBe('Neznámy používateľ');
    });

    test('should handle names with multiple spaces', () => {
      expect(anonymizeName('Ján   Novák')).toBe('Ján N***');
      expect(anonymizeName('  Peter  S<PERSON>boda  ')).toBe('Peter S***');
    });

    test('should handle names with diacritics', () => {
      expect(anonymizeName('Ján Čierny')).toBe('Ján Č***');
      expect(anonymizeName('Žofia Ľubomírová')).toBe('Žofia Ľ***');
    });
  });

  describe('isAnonymizedName', () => {
    test('should detect anonymized names', () => {
      expect(isAnonymizedName('Ján N***')).toBe(true);
      expect(isAnonymizedName('Peter S***')).toBe(true);
      expect(isAnonymizedName('Neznámy používateľ')).toBe(true);
      expect(isAnonymizedName('Anonymized User')).toBe(true);
    });

    test('should detect non-anonymized names', () => {
      expect(isAnonymizedName('Ján Novák')).toBe(false);
      expect(isAnonymizedName('Peter')).toBe(false);
      expect(isAnonymizedName('Anna Mária Svobodová')).toBe(false);
    });

    test('should handle invalid input', () => {
      expect(isAnonymizedName('')).toBe(false);
      expect(isAnonymizedName(null as any)).toBe(false);
      expect(isAnonymizedName(undefined as any)).toBe(false);
    });
  });

  describe('safeAnonymizeName', () => {
    test('should anonymize non-anonymized names', () => {
      expect(safeAnonymizeName('Ján Novák')).toBe('Ján N***');
      expect(safeAnonymizeName('Peter Svoboda')).toBe('Peter S***');
    });

    test('should not double-anonymize already anonymized names', () => {
      expect(safeAnonymizeName('Ján N***')).toBe('Ján N***');
      expect(safeAnonymizeName('Neznámy používateľ')).toBe('Neznámy používateľ');
      expect(safeAnonymizeName('Anonymized User')).toBe('Anonymized User');
    });
  });
});
