/**
 * Debug skript pre testovanie mazania fotografií z Cloudinary pri úprave hračiek
 * 
 * Tento skript simuluje update hračky s odstránením fotografií a sleduje logy
 */

const fetch = require('node-fetch');

// Konfigurácia
const BASE_URL = 'http://localhost:3001';
const TEST_USER_ID = 1; // ID testovacieho používateľa
const TEST_TOY_ID = 'hashed123'; // Hashované ID testovej hračky

// Simulácia autentifikačných údajov
const AUTH_TOKEN = 'test-token'; // V skutočnosti by to bol Firebase token
const CSRF_TOKEN = 'test-csrf-token';

/**
 * Simuluje update hračky s odstránením fotografií
 */
async function testToyPhotoUpdate() {
  console.log('🧪 Začínam test mazania fotografií pri úprave hračky...\n');

  try {
    // 1. Najprv získame aktuálne údaje hračky
    console.log('📋 Krok 1: Získavam aktuálne údaje hračky...');
    
    const getToyResponse = await fetch(`${BASE_URL}/api/toys/${TEST_TOY_ID}`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${AUTH_TOKEN}`,
        'Content-Type': 'application/json'
      }
    });

    if (!getToyResponse.ok) {
      console.log(`❌ Chyba pri získavaní hračky: ${getToyResponse.status} ${getToyResponse.statusText}`);
      const errorText = await getToyResponse.text();
      console.log('Error response:', errorText);
      return;
    }

    const toyData = await getToyResponse.json();
    console.log('✅ Hračka získaná:', {
      id: toyData.id,
      name: toyData.name,
      imageCount: toyData.images?.length || 0
    });

    if (!toyData.images || toyData.images.length === 0) {
      console.log('⚠️  Hračka nemá žiadne fotografie na odstránenie. Ukončujem test.');
      return;
    }

    console.log('📸 Aktuálne fotografie hračky:');
    toyData.images.forEach((img, index) => {
      console.log(`  ${index + 1}. ${img.url}`);
    });

    // 2. Simulujeme update s odstránením niektorých fotografií
    console.log('\n📝 Krok 2: Simulujem update hračky s odstránením fotografií...');
    
    // Odstránime prvú fotografiu (ponecháme len zvyšné)
    const updatedImages = toyData.images.slice(1).map(img => img.url);
    
    console.log(`🗑️  Odstraňujem ${toyData.images.length - updatedImages.length} fotografií`);
    console.log(`📸 Ponechávam ${updatedImages.length} fotografií`);

    const updateData = {
      id: TEST_TOY_ID,
      name: toyData.name,
      description: toyData.description,
      type: toyData.type,
      price: toyData.price?.toString() || '0',
      deposit: toyData.deposit?.toString() || '0',
      images: updatedImages
    };

    console.log('\n🚀 Volám /api/toys/update endpoint...');
    console.log('📤 Posielané dáta:', {
      id: updateData.id,
      name: updateData.name,
      imageCount: updateData.images.length
    });

    const updateResponse = await fetch(`${BASE_URL}/api/toys/update`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${AUTH_TOKEN}`,
        'Content-Type': 'application/json',
        'X-CSRF-Token': CSRF_TOKEN
      },
      body: JSON.stringify(updateData)
    });

    console.log(`📥 Response status: ${updateResponse.status} ${updateResponse.statusText}`);

    if (!updateResponse.ok) {
      console.log('❌ Chyba pri update hračky:');
      const errorText = await updateResponse.text();
      console.log('Error response:', errorText);
      return;
    }

    const updateResult = await updateResponse.json();
    console.log('✅ Update hračky úspešný:', {
      id: updateResult.id,
      name: updateResult.name,
      newImageCount: updateResult.images?.length || 0
    });

    // 3. Overenie výsledku
    console.log('\n🔍 Krok 3: Overujem výsledok...');
    
    const verifyResponse = await fetch(`${BASE_URL}/api/toys/${TEST_TOY_ID}`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${AUTH_TOKEN}`,
        'Content-Type': 'application/json'
      }
    });

    if (verifyResponse.ok) {
      const verifiedToy = await verifyResponse.json();
      console.log('✅ Overenie úspešné:', {
        id: verifiedToy.id,
        name: verifiedToy.name,
        finalImageCount: verifiedToy.images?.length || 0
      });

      console.log('📸 Finálne fotografie hračky:');
      if (verifiedToy.images && verifiedToy.images.length > 0) {
        verifiedToy.images.forEach((img, index) => {
          console.log(`  ${index + 1}. ${img.url}`);
        });
      } else {
        console.log('  (žiadne fotografie)');
      }
    }

    console.log('\n🎯 Test dokončený. Skontrolujte server logy pre detaily o mazaní z Cloudinary.');

  } catch (error) {
    console.error('❌ Chyba počas testu:', error);
  }
}

/**
 * Testuje endpoint s neexistujúcou hračkou
 */
async function testNonExistentToy() {
  console.log('\n🧪 Test s neexistujúcou hračkou...');

  try {
    const updateData = {
      id: 'nonexistent123',
      name: 'Test Toy',
      description: 'Test description',
      type: 'EDUCATIONAL',
      price: '10.00',
      deposit: '5.00',
      images: ['https://example.com/test.jpg']
    };

    const response = await fetch(`${BASE_URL}/api/toys/update`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${AUTH_TOKEN}`,
        'Content-Type': 'application/json',
        'X-CSRF-Token': CSRF_TOKEN
      },
      body: JSON.stringify(updateData)
    });

    console.log(`📥 Response status: ${response.status} ${response.statusText}`);
    
    if (!response.ok) {
      const errorText = await response.text();
      console.log('✅ Očakávaná chyba pre neexistujúcu hračku:', errorText);
    } else {
      console.log('⚠️  Neočakávané: Update neexistujúcej hračky bol úspešný');
    }

  } catch (error) {
    console.error('❌ Chyba pri teste neexistujúcej hračky:', error);
  }
}

/**
 * Hlavná funkcia
 */
async function main() {
  console.log('🚀 Spúšťam debug test pre mazanie fotografií hračiek...\n');
  console.log(`🌐 Server URL: ${BASE_URL}`);
  console.log(`🎯 Test toy ID: ${TEST_TOY_ID}`);
  console.log(`👤 Test user ID: ${TEST_USER_ID}\n`);

  // Test 1: Normálny update s odstránením fotografií
  await testToyPhotoUpdate();

  // Test 2: Update neexistujúcej hračky
  await testNonExistentToy();

  console.log('\n✅ Všetky testy dokončené.');
  console.log('\n📋 Čo skontrolovať v server logoch:');
  console.log('  1. Logy "Aktualizujem obrázky hračky [ID]"');
  console.log('  2. Logy "Nájdených X existujúcich obrázkov"');
  console.log('  3. Logy "Odstraňujem obrázok z Cloudinary"');
  console.log('  4. Logy "Výsledok odstránenia obrázku: úspešné/neúspešné"');
  console.log('  5. Akékoľvek error logy súvisiace s Cloudinary API');
}

// Spustenie testov
if (require.main === module) {
  main().catch(error => {
    console.error('❌ Kritická chyba:', error);
    process.exit(1);
  });
}

module.exports = {
  testToyPhotoUpdate,
  testNonExistentToy
};
