import { NextRequest, NextResponse } from 'next/server';
import { rateLimitMiddleware } from './middleware/rateLimit';
import { isHashedId } from './lib/hashUtils';

/**
 * Global middleware for cache control, security headers, and rate limiting
 * Specifically designed to prevent aggressive caching issues on mobile Safari/iPhone
 * and implement comprehensive rate limiting for API endpoints
 */
export function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;

  // Handle toy detail URL redirects (legacy format to SEF format)
  if (pathname.startsWith('/hracky/') && pathname !== '/hracky') {
    const urlParts = pathname.split('/');
    if (urlParts.length === 3) {
      const toyParam = urlParts[2];

      // Check if it's a legacy hashed ID format (h-xxxxxxxxxx)
      if (isHashedId(toyParam)) {
        // This is a legacy URL, but we need the toy name to create SEF URL
        // For now, let the page handle it with backward compatibility
        // The page will detect it's legacy and could potentially redirect
        // after fetching toy data, but for now we'll let it pass through
      }
    }
  }

  // Apply rate limiting first for API routes and attack patterns
  const rateLimitResponse = rateLimitMiddleware(request);
  if (rateLimitResponse) {
    // Rate limit exceeded, return the rate limit response
    return rateLimitResponse;
  }

  const response = NextResponse.next();

  // Get current timestamp for cache busting
  const timestamp = Date.now();
  
  // Determine cache strategy based on path
  if (pathname.startsWith('/_next/static/')) {
    // Static assets - allow caching but with validation
    if (pathname.includes('/chunks/')) {
      // JavaScript chunks - critical for preventing iPhone cache issues
      response.headers.set('Cache-Control', 'public, max-age=300, must-revalidate, stale-while-revalidate=60');
      response.headers.set('ETag', `"${timestamp}"`);
      response.headers.set('Vary', 'Accept-Encoding');
    } else {
      // Other static assets (CSS, images, etc.)
      response.headers.set('Cache-Control', 'public, max-age=3600, must-revalidate');
      response.headers.set('ETag', `"static-${timestamp}"`);
    }
  } else if (pathname.startsWith('/api/')) {
    // API routes - no caching
    response.headers.set('Cache-Control', 'no-cache, no-store, must-revalidate, max-age=0');
    response.headers.set('Pragma', 'no-cache');
    response.headers.set('Expires', '0');
  } else {
    // HTML pages - prevent aggressive caching
    response.headers.set('Cache-Control', 'no-cache, no-store, must-revalidate, max-age=0');
    response.headers.set('Pragma', 'no-cache');
    response.headers.set('Expires', '0');
    response.headers.set('ETag', `"page-${timestamp}"`);
  }

  // Security headers for all responses
  response.headers.set('X-Content-Type-Options', 'nosniff');
  response.headers.set('X-Frame-Options', 'DENY');
  response.headers.set('X-XSS-Protection', '1; mode=block');
  response.headers.set('Referrer-Policy', 'strict-origin-when-cross-origin');
  
  // Mobile Safari specific headers
  response.headers.set('X-UA-Compatible', 'IE=edge');
  response.headers.set('X-Mobile-Cache-Control', 'no-cache');
  
  // Add build version header for debugging
  response.headers.set('X-Build-Version', process.env.BUILD_ID || 'dev');
  response.headers.set('X-Cache-Timestamp', timestamp.toString());

  return response;
}

/**
 * Middleware configuration
 * Apply to all routes except specific exclusions
 */
export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/webpack-hmr (hot module replacement)
     * - _next/static (static files that need different cache rules)
     * - favicon.ico (favicon)
     * - robots.txt (robots file)
     * - sitemap.xml (sitemap file)
     */
    '/((?!_next/webpack-hmr|favicon.ico|robots.txt|sitemap.xml).*)',
  ],
};
