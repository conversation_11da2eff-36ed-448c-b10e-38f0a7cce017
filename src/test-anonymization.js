// Simple test script for name anonymization
const { anonymizeName, isAnonymizedName, safeAnonymizeName } = require('./lib/nameUtils.ts');

console.log('Testing name anonymization...');

// Test cases
const testCases = [
  '<PERSON><PERSON>',
  '<PERSON>', 
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '',
  '   ',
  '<PERSON><PERSON>',
  'Ž<PERSON><PERSON>ub<PERSON>írov<PERSON>'
];

console.log('\n=== anonymizeName tests ===');
testCases.forEach(name => {
  try {
    const result = anonymizeName(name);
    console.log(`"${name}" -> "${result}"`);
  } catch (error) {
    console.log(`"${name}" -> ERROR: ${error.message}`);
  }
});

console.log('\n=== isAnonymizedName tests ===');
const anonymizedNames = [
  '<PERSON><PERSON>***',
  '<PERSON>***', 
  '<PERSON>ez<PERSON><PERSON>',
  'Anonymized User',
  '<PERSON><PERSON>',
  '<PERSON>'
];

anonymizedNames.forEach(name => {
  try {
    const result = isAnonymizedName(name);
    console.log(`"${name}" -> ${result}`);
  } catch (error) {
    console.log(`"${name}" -> ERROR: ${error.message}`);
  }
});

console.log('\nTest completed!');
