# Swapka - Aplikácia na požičiavanie detských hračiek (src)

Toto je zdrojový kód aplik<PERSON>cie Swapka, ktorá umožňuje rodičom zdieľať a požičiavať detské hračky.

## Spustenie vývojového servera

Pred spustením servera sa uistite, že máte správne nastavené prostredie:

1. **Inštalácia závislostí**

```bash
npm install
```

2. **Nastavenie premenných prostredia**

Vytvorte súbor `.env` v tomto adresári s nasledujúcim obsahom:

```
DATABASE_URL="mysql://swapka_d5das4651:<EMAIL>/swapka_5336164"
```

3. **Generovanie Prisma klienta**

```bash
npx prisma generate
```

4. **Spustenie vývojového servera**

```bash
npm run dev
```

Aplikácia bude dostupná na adrese [http://localhost:3000](http://localhost:3000).

## Produkčné nasadenie s PM2

Pre produkčné nasadenie odporúčame použiť PM2 process manager s Next.js standalone režimom:

### Rýchle spustenie

```bash
# Z root adresára projektu
./pm2-setup.sh
```

### Manuálne nastavenie

```bash
# Inštalácia PM2
npm install -g pm2

# Build aplikácie
npm run build

# Spustenie produkčnej aplikácie
pm2 start ../ecosystem.config.js --only swapka-prod

# Uloženie konfigurácie pre auto-start
pm2 save
pm2 startup
```

### Správa PM2 procesov

```bash
# Zobrazenie stavu
pm2 list

# Sledovanie logov
pm2 logs swapka-prod

# Monitoring
pm2 monit

# Zero-downtime restart
pm2 reload swapka-prod
```

**Dokumentácia:**
- [docs/pm2-deployment.md](docs/pm2-deployment.md) - PM2 deployment guide
- [docs/standalone-deployment.md](docs/standalone-deployment.md) - Next.js standalone deployment guide

## Štruktúra projektu

- `/app` - Next.js App Router
  - `/api` - API endpointy
  - `/hracky` - stránka s hračkami
  - `/ako-to-funguje` - stránka s vysvetlením fungovania aplikácie
  - `/kontakt` - kontaktná stránka
- `/components` - React komponenty
- `/lib` - pomocné funkcie a knižnice
- `/prisma` - Prisma schéma a migrácie

## Riešenie problémov

### Chyba: @prisma/client did not initialize yet

Ak sa stretnete s touto chybou, spustite:

```bash
npx prisma generate
```

A potom reštartujte vývojový server.

### Problémy s importom modulov

Ak sa stretnete s chybou `Module not found: Can't resolve '@/lib/db'`, upravte importy na relatívne cesty, napríklad:

```typescript
// Namiesto
import { getAllToys } from '@/lib/db';

// Použite
import { getAllToys } from '../../../lib/db';
```
