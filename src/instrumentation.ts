/**
 * Next.js Instrumentation Hook for New Relic
 * 
 * This file is automatically called by Next.js when the application starts.
 * It's the recommended way to initialize monitoring tools like New Relic.
 * 
 * @see https://nextjs.org/docs/app/building-your-application/optimizing/instrumentation
 */

export async function register() {
  if (process.env.NEXT_RUNTIME === 'nodejs') {
    // Dynamically import 'newrelic' only on the server-side
    // This prevents attempting to load it in the browser or edge runtimes
    try {
      await import('newrelic');
      if (process.env.NODE_ENV !== 'production') {
        console.log('✅ New Relic instrumentation loaded successfully.');
      }
    } catch (error) {
      console.error('❌ Failed to load New Relic instrumentation:', error);
    }
  } else {
    if (process.env.NODE_ENV !== 'production') {
      console.log(`ℹ️  Instrumentation hook loaded in ${process.env.NEXT_RUNTIME} runtime - New Relic not initialized here.`);
    }
  }

  // Future monitoring integrations can be added here
}
