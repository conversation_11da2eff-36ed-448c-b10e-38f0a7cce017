/**
 * Test script to verify that the hash function improvements are working
 */

const { hashId, dehashId, toNumericId, isHashedId } = require('./lib/hashUtils');

console.log('=== TESTING HASH FUNCTION IMPROVEMENTS ===\n');

// Test the specific hashed ID from the error
const testHashedId = 'h-cd70730da161';
console.log(`Testing hashed ID: ${testHashedId}`);
console.log(`Is valid hashed ID: ${isHashedId(testHashedId)}`);

// Test with increased limit
console.log('\n--- Testing dehashId with increased limit ---');
const numericId = dehashId(testHashedId, 100000);
console.log(`Numeric ID result: ${numericId}`);

// Test toNumericId function
console.log('\n--- Testing toNumericId function ---');
const numericIdFromToNumeric = toNumericId(testHashedId);
console.log(`toNumericId result: ${numericIdFromToNumeric}`);

// Test some known ID ranges to see what works
console.log('\n--- Testing known ID ranges ---');
for (let i = 1; i <= 50; i++) {
  const hash = hashId(i);
  if (hash === testHashedId) {
    console.log(`✅ FOUND MATCH! ID ${i} generates hash ${hash}`);
    break;
  }
}

// Test higher ranges
console.log('\n--- Testing higher ID ranges (10000-10100) ---');
for (let i = 10000; i <= 10100; i++) {
  const hash = hashId(i);
  if (hash === testHashedId) {
    console.log(`✅ FOUND MATCH! ID ${i} generates hash ${hash}`);
    break;
  }
}

// Test even higher ranges
console.log('\n--- Testing even higher ID ranges (50000-50100) ---');
for (let i = 50000; i <= 50100; i++) {
  const hash = hashId(i);
  if (hash === testHashedId) {
    console.log(`✅ FOUND MATCH! ID ${i} generates hash ${hash}`);
    break;
  }
}

console.log('\n=== TEST COMPLETED ===');
