import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  output: 'standalone',
  poweredByHeader: false,
  reactStrictMode: true,
  eslint: {
    ignoreDuringBuilds: true,
  },


  // External packages for server components
  serverExternalPackages: ['sharp'],

  // Cache busting and optimization configuration
  generateBuildId: async () => {
    // Generate unique build ID with timestamp to ensure cache invalidation
    return `build-${Date.now()}-${Math.random().toString(36).substring(2, 15)}`;
  },

  // Asset optimization and cache control
  assetPrefix: process.env.NODE_ENV === 'production' ? undefined : undefined,

  // Turbopack configuration (stable in Next.js 15+)
  turbopack: {
    rules: {
      // Ignore problematic file types that cause issues with New Relic
      '*.proto': {
        loaders: ['ignore-loader'],
        as: '*.js',
      },
    },
  },

  // Webpack configuration for production builds (when not using Turbopack)
  webpack: (config, { buildId, dev, isServer }) => {
    // Only apply webpack config when not using Turbopack (production builds)
    if (dev) {
      // In development with Turbopack, skip webpack modifications
      return config;
    }

    // Fix libheif-js dependency extraction issue
    config.externals = config.externals || [];
    if (isServer) {
      // Externalize problematic packages to avoid webpack bundling issues
      config.externals.push({
        'libheif-js': 'commonjs libheif-js',
        'heic-convert': 'commonjs heic-convert'
      });
    }

    // Ignore problematic dynamic requires in client bundles
    config.module = config.module || {};
    config.module.rules = config.module.rules || [];
    config.module.rules.push({
      test: /libheif-js/,
      use: 'ignore-loader'
    });

    // Add build ID to chunk names for better cache busting
    if (!isServer) {
      config.output.filename = `static/chunks/[name]-[contenthash]-${buildId}.js`;
      config.output.chunkFilename = `static/chunks/[name]-[contenthash]-${buildId}.js`;
    }

    // Optimize for mobile Safari cache behavior
    config.optimization = {
      ...config.optimization,
      splitChunks: {
        ...config.optimization.splitChunks,
        cacheGroups: {
          ...config.optimization.splitChunks?.cacheGroups,
          // Create separate chunks for better cache invalidation
          vendor: {
            test: /[\\/]node_modules[\\/]/,
            name: 'vendors',
            chunks: 'all',
            priority: 10,
          },
          common: {
            name: 'common',
            minChunks: 2,
            chunks: 'all',
            priority: 5,
            reuseExistingChunk: true,
          },
        },
      },
    };

    return config;
  },

  // Headers configuration for cache control
  async headers() {
    return [
      // Static assets - aggressive caching with proper invalidation
      {
        source: '/_next/static/:path*',
        headers: [
          {
            key: 'Cache-Control',
            value: 'public, max-age=31536000, immutable',
          },
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff',
          },
        ],
      },
      // JavaScript chunks - short cache with validation
      {
        source: '/_next/static/chunks/:path*',
        headers: [
          {
            key: 'Cache-Control',
            value: 'public, max-age=3600, must-revalidate',
          },
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff',
          },
        ],
      },
      // Main application files - no cache for critical files
      {
        source: '/((?!_next/static|favicon.ico).*)',
        headers: [
          {
            key: 'Cache-Control',
            value: 'no-cache, no-store, must-revalidate, max-age=0',
          },
          {
            key: 'Pragma',
            value: 'no-cache',
          },
          {
            key: 'Expires',
            value: '0',
          },
        ],
      },
    ];
  },
};

export default nextConfig;
