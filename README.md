# Swapka - Aplikácia na požičiavanie detských hračiek

Swapka je webová aplikácia, ktorá umožňuje rodičom zdieľať a požičiavať detské hračky. Cieľom je podporiť udržateľnosť, znížiť plytvanie a umožniť deťom prístup k väčšiemu množstvu hračiek bez nutnosti stáleho kupovania nových.

## Technológie

- **Frontend**: Next.js 15, React 19, TypeScript, Tailwind CSS
- **Backend**: Next.js API Routes
- **Databáza**: MySQL
- **ORM**: Prisma

## Inštalácia a spustenie

### Požiadavky

- Node.js 20.x alebo novší
- npm 10.x alebo novší
- MySQL databáza

### Kroky pre spustenie aplikácie

1. **Klonovanie repozitára**

```bash
git clone <URL_repozitára>
cd swapka
```

2. **Inštalácia závislostí**

```bash
# Inštalácia závislostí v adresári src
cd src
npm install
```

3. **Nastavenie premenných prostredia**

Vytvorte súbor `.env` v koreňovom adresári aj v adresári `src` s nasledujúcim obsahom:

```
DATABASE_URL="mysql://<používateľ>:<heslo>@<host>/<názov_databázy>"
```

Napríklad:

```
DATABASE_URL="mysql://swapka_d5das4651:<EMAIL>/swapka_5336164"
```

4. **Generovanie Prisma klienta**

```bash
# V adresári src
npx prisma generate
```

5. **Vytvorenie a naplnenie databázy**

```bash
# V koreňovom adresári
node scripts/setup-db.js
```

6. **Spustenie vývojového servera**

```bash
# V adresári src
npm run dev
```

Aplikácia bude dostupná na adrese [http://localhost:3000](http://localhost:3000).

## Produkčné nasadenie

### PM2 Process Manager (Odporúčané)

Pre produkčné nasadenie odporúčame použiť PM2 process manager s Next.js standalone režimom, ktorý poskytuje:
- Cluster mode s využitím všetkých CPU jadier
- Automatické reštartovanie pri chybách
- Zero-downtime deployment
- Monitoring a logovanie
- Optimalizovanú výkonnosť cez standalone server
- Pokročilé monitoring a logovanie

#### Rýchle spustenie

```bash
# Automatizované nastavenie
./pm2-setup.sh

# Alebo manuálne
npm install -g pm2
pm2 start ecosystem.config.js --only swapka-prod
pm2 save && pm2 startup
```

#### Správa aplikácie

```bash
pm2 list              # Zobrazenie stavu
pm2 logs swapka-prod  # Sledovanie logov
pm2 monit             # Real-time monitoring
pm2 reload swapka-prod # Zero-downtime restart
```

**Dokumentácia:**
- [src/docs/pm2-deployment.md](src/docs/pm2-deployment.md) - PM2 deployment guide
- [src/docs/standalone-deployment.md](src/docs/standalone-deployment.md) - Next.js standalone deployment guide

### Riešenie problémov

Ak sa stretnete s chybou `@prisma/client did not initialize yet`, uistite sa, že ste spustili príkaz `npx prisma generate` v adresári `src`.

## Štruktúra projektu

- `/src` - zdrojový kód aplikácie
  - `/app` - Next.js App Router
    - `/api` - API endpointy
    - `/hracky` - stránka s hračkami
    - `/ako-to-funguje` - stránka s vysvetlením fungovania aplikácie
    - `/kontakt` - kontaktná stránka
  - `/components` - React komponenty
  - `/lib` - pomocné funkcie a knižnice
  - `/prisma` - Prisma schéma a migrácie
- `/scripts` - skripty pre nastavenie a správu aplikácie
- `/prisma-test` - testovací adresár pre Prisma

## Databázový model

Aplikácia používa nasledujúce tabuľky:

- `User` - používatelia aplikácie
- `Toy` - hračky na požičanie
- `ToyImage` - obrázky hračiek
- `Location` - lokality
- `Reservation` - rezervácie hračiek

## Docker

Aplikácia je pripravená na spustenie v Docker kontajneri. Môžete ju spustiť pomocou Docker Compose.

### Požiadavky

- Docker
- Docker Compose

### Spustenie pomocou Docker Compose

1. **Build a spustenie aplikácie**

```bash
docker-compose up -d --build
```

Aplikácia bude dostupná na adrese [http://localhost:3000](http://localhost:3000).

2. **Zastavenie aplikácie**

```bash
docker-compose down
```

### Manuálne buildovanie Docker obrazu

Ak chcete manuálne buildovať Docker obraz:

```bash
# Build Docker obrazu
docker build -t swapka .

# Spustenie kontajnera
docker run -p 3000:3000 -e DATABASE_URL="mysql://swapka_d5das4651:<EMAIL>/swapka_5336164" swapka
```

## Autori

- Váš tím

## Licencia

Tento projekt je licencovaný pod [MIT licenciou](LICENSE).
