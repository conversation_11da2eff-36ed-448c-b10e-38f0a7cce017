#!/bin/bash

# Skript pre spustenie Docker kontajnera

# Farby pre výstup
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

echo -e "${YELLOW}Spúšťam Docker Compose pre Swapka aplikáciu...${NC}"

# Kontrola, či je Docker nainštalovaný
if ! command -v docker &> /dev/null; then
    echo -e "${RED}Docker nie je nain<PERSON>talovaný. Prosím, nainštalujte Docker a skúste to znova.${NC}"
    exit 1
fi

# Kontrola, či je Docker Compose nainštalovaný
if ! command -v docker-compose &> /dev/null; then
    echo -e "${RED}Docker Compose nie je nainštalovaný. Prosím, nainštalujte Docker Compose a skúste to znova.${NC}"
    exit 1
fi

# Spustenie Docker Compose
echo -e "${YELLOW}Buildovanie a spustenie kontajnerov...${NC}"
docker-compose up -d --build

# Kontrola, či sa kontajnery spustili
if [ $? -eq 0 ]; then
    echo -e "${GREEN}Kontajnery boli úspešne spustené!${NC}"
    echo -e "${GREEN}Aplikácia je dostupná na adrese: http://localhost:3000${NC}"
else
    echo -e "${RED}Nastala chyba pri spúšťaní kontajnerov.${NC}"
    exit 1
fi

echo -e "${YELLOW}Pre zastavenie kontajnerov použite príkaz: docker-compose down${NC}"
