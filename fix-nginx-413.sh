#!/bin/bash

# Skript na opravu 413 "Request Entity Too Large" chyby v Nginx pre swapka.sk
# Aplikuje novú konfiguráciu s podporou veľkých súborov

# Farby pre výstup
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}=== Oprava Nginx 413 chyby pre swapka.sk ===${NC}"
echo -e "${YELLOW}Aplikovanie konfigurácie s podporou veľkých súborov${NC}"
echo ""

# Kontrola, či je skript spustený ako root
if [ "$EUID" -ne 0 ]; then
    echo -e "${RED}Tento skript musí byť spustený ako root (sudo)${NC}"
    echo "Použite: sudo ./fix-nginx-413.sh"
    exit 1
fi

# <PERSON><PERSON><PERSON><PERSON>, či existuje nová konfigurácia
if [ ! -f "nginx-swapka-fixed.conf" ]; then
    echo -e "${RED}Súbor nginx-swapka-fixed.conf nebol nájdený!${NC}"
    echo "Uistite sa, že ste v správnom adresári."
    exit 1
fi

# Nájdenie aktuálnej Nginx konfigurácie pre swapka.sk
NGINX_SITES_DIR="/etc/nginx/sites-available"
NGINX_ENABLED_DIR="/etc/nginx/sites-enabled"
CURRENT_CONFIG=""

# Hľadanie existujúcej konfigurácie
for config_file in "$NGINX_SITES_DIR"/*; do
    if [ -f "$config_file" ] && grep -q "swapka.sk" "$config_file"; then
        CURRENT_CONFIG="$config_file"
        break
    fi
done

if [ -z "$CURRENT_CONFIG" ]; then
    echo -e "${YELLOW}Existujúca konfigurácia pre swapka.sk nebola nájdená.${NC}"
    echo -e "${YELLOW}Vytváram novú konfiguráciu...${NC}"
    CURRENT_CONFIG="$NGINX_SITES_DIR/swapka"
else
    echo -e "${GREEN}Nájdená existujúca konfigurácia: $CURRENT_CONFIG${NC}"
fi

# Backup existujúcej konfigurácie
if [ -f "$CURRENT_CONFIG" ]; then
    BACKUP_FILE="${CURRENT_CONFIG}.backup.$(date +%Y%m%d_%H%M%S)"
    echo -e "${YELLOW}Vytváram backup: $BACKUP_FILE${NC}"
    cp "$CURRENT_CONFIG" "$BACKUP_FILE"
fi

# Kopírovanie novej konfigurácie
echo -e "${YELLOW}Aplikujem novú konfiguráciu...${NC}"
cp nginx-swapka-fixed.conf "$CURRENT_CONFIG"

# Aktivácia konfigurácie (ak nie je aktivovaná)
CONFIG_NAME=$(basename "$CURRENT_CONFIG")
if [ ! -L "$NGINX_ENABLED_DIR/$CONFIG_NAME" ]; then
    echo -e "${YELLOW}Aktivujem konfiguráciu...${NC}"
    ln -s "$CURRENT_CONFIG" "$NGINX_ENABLED_DIR/$CONFIG_NAME"
fi

# Vytvorenie potrebných log súborov
echo -e "${YELLOW}Vytváram log súbory...${NC}"
touch /var/log/nginx/swapka-access.log
touch /var/log/nginx/swapka-error.log
touch /var/log/nginx/swapka-upload.log
touch /var/log/nginx/swapka-upload-error.log

# Nastavenie oprávnení pre logy
chown www-data:www-data /var/log/nginx/swapka-*.log
chmod 644 /var/log/nginx/swapka-*.log

# Vytvorenie temp adresára pre nginx client body
mkdir -p /tmp/nginx_client_body_temp
chown www-data:www-data /tmp/nginx_client_body_temp
chmod 755 /tmp/nginx_client_body_temp

# Testovanie konfigurácie
echo -e "${YELLOW}Testovanie Nginx konfigurácie...${NC}"
if nginx -t; then
    echo -e "${GREEN}✓ Nginx konfigurácia je v poriadku${NC}"
    
    # Reload Nginx
    echo -e "${YELLOW}Reloadujem Nginx...${NC}"
    systemctl reload nginx
    
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✓ Nginx úspešne reloadovaný${NC}"
    else
        echo -e "${RED}✗ Chyba pri reloade Nginx${NC}"
        systemctl status nginx
        exit 1
    fi
else
    echo -e "${RED}✗ Chyba v Nginx konfigurácii!${NC}"
    nginx -t
    echo ""
    echo -e "${YELLOW}Obnovujem pôvodnú konfiguráciu...${NC}"
    if [ -f "$BACKUP_FILE" ]; then
        cp "$BACKUP_FILE" "$CURRENT_CONFIG"
        nginx -t && systemctl reload nginx
    fi
    exit 1
fi

echo ""
echo -e "${GREEN}=== Oprava 413 chyby dokončená! ===${NC}"
echo ""
echo -e "${BLUE}Kľúčové zmeny:${NC}"
echo -e "  ✓ client_max_body_size: 20M (25M pre upload endpointy)"
echo -e "  ✓ client_body_timeout: 60s (120s pre upload)"
echo -e "  ✓ proxy timeouts: 60s (120s pre upload)"
echo -e "  ✓ proxy_request_buffering: off pre upload endpointy"
echo -e "  ✓ Špeciálne error handling pre 413 chyby"
echo ""
echo -e "${BLUE}Nové log súbory:${NC}"
echo -e "  • Hlavné logy: /var/log/nginx/swapka-access.log"
echo -e "  • Error logy: /var/log/nginx/swapka-error.log"
echo -e "  • Upload logy: /var/log/nginx/swapka-upload.log"
echo -e "  • Upload error logy: /var/log/nginx/swapka-upload-error.log"
echo ""
echo -e "${YELLOW}Testovanie:${NC}"
echo -e "  1. Skúste nahrať obrázok na swapka.sk"
echo -e "  2. Sledujte logy: tail -f /var/log/nginx/swapka-upload*.log"
echo -e "  3. Ak stále máte problémy, skontrolujte PM2 a Next.js logy"
echo ""
echo -e "${GREEN}413 chyba by mala byť vyriešená!${NC}"
