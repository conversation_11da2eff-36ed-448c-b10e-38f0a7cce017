# Production Deployment Fix - Static Assets 404 & MIME Type Issues

## Problem Summary

The Swapka Next.js application was experiencing critical production deployment issues:

1. **404 Errors for Static Assets**: All Next.js chunks and CSS files returning 404 errors
2. **MIME Type Mismatches**: JavaScript files served with incorrect Content-Type headers
3. **Cache-busting Failures**: Assets with build timestamps not accessible
4. **Prisma Client Platform Mismatch**: Prisma Client generated for "darwin-arm64" but production needs "debian-openssl-3.0.x"

## Root Cause Analysis

**Primary Issues**:

1. **Static Assets**: Next.js standalone mode doesn't automatically copy static files to the standalone directory.
   - `.next/static/` directory was not copied to `.next/standalone/.next/static/`
   - `public/` directory was not copied to `.next/standalone/public/`
   - Production server was trying to serve files from standalone directory but files were missing

2. **Prisma Platform Mismatch**: Prisma Client was generated for development platform (macOS) instead of production platform (Linux).
   - Schema missing `binaryTargets` configuration
   - Prisma binaries not copied to standalone directory
   - Production server couldn't find the correct Prisma Query Engine

## Solution Implemented

### 1. Fixed Prisma Schema Configuration

Updated `src/prisma/schema.prisma` to include multiple binary targets:
```prisma
generator client {
  provider      = "prisma-client-js"
  output        = "../app/generated/prisma"
  binaryTargets = ["native", "debian-openssl-3.0.x", "linux-musl", "darwin-arm64"]
}
```

### 2. Enhanced Asset Copying Script

Created `src/scripts/copy-standalone-assets.js` that:
- Copies `.next/static/` to `.next/standalone/.next/static/`
- Copies `public/` to `.next/standalone/public/`
- Copies `app/generated/prisma/` to `.next/standalone/app/generated/prisma/`
- Verifies all critical files exist
- Provides detailed logging and error handling

### 3. Updated Build Process

Modified `src/package.json` scripts:
```json
{
  "build": "node scripts/version-info.js && next build && node scripts/copy-standalone-assets.js",
  "postbuild": "node scripts/copy-standalone-assets.js"
}
```

### 4. Enhanced Nginx Configuration

Created `nginx-swapka-production-fixed.conf` with:
- Proper MIME type handling for JavaScript and CSS files
- Specific location blocks for static assets
- Detailed logging for debugging
- Correct Content-Type headers to prevent X-Content-Type-Options conflicts

### 5. Updated Deployment Scripts

Enhanced PM2 and deployment scripts to:
- Regenerate Prisma client for production platform
- Automatically run asset copying after build
- Verify standalone assets and Prisma binaries exist
- Provide fallback manual copying if needed

## Deployment Instructions

### For Immediate Fix (Production)

1. **Fix Prisma and copy assets to existing build**:
```bash
cd /path/to/swapka/src
npx prisma generate  # Regenerate for production platform
node scripts/copy-standalone-assets.js
```

**OR use the automated fix script**:
```bash
cd /path/to/swapka
chmod +x fix-production-assets.sh
./fix-production-assets.sh
```

2. **Update Nginx configuration**:
```bash
# Backup current config
sudo cp /etc/nginx/sites-available/swapka.conf /etc/nginx/sites-available/swapka.conf.backup

# Copy new configuration
sudo cp nginx-swapka-production-fixed.conf /etc/nginx/sites-available/swapka.conf

# Test and reload
sudo nginx -t
sudo systemctl reload nginx
```

3. **Restart application**:
```bash
pm2 restart swapka-prod
```

### For New Deployments

1. **Build with automatic asset copying**:
```bash
cd src
npm install
npx prisma generate
npm run build  # Now includes automatic asset copying
```

2. **Deploy using PM2**:
```bash
pm2 start ecosystem.config.js --only swapka-prod --env production
```

## Verification Steps

### 1. Check Static Files Exist
```bash
ls -la .next/standalone/.next/static/css/
ls -la .next/standalone/.next/static/chunks/
ls -la .next/standalone/public/
```

### 2. Test Asset Loading
```bash
# Test CSS files
curl -I https://www.swapka.sk/_next/static/css/f81f63c98c96cb1a.css

# Test JavaScript chunks
curl -I https://www.swapka.sk/_next/static/chunks/webpack-[hash].js
```

### 3. Check MIME Types
Verify responses include correct Content-Type headers:
- JavaScript files: `application/javascript; charset=utf-8`
- CSS files: `text/css; charset=utf-8`
- Font files: `font/woff2`

## Files Modified/Created

### New Files:
- `src/scripts/copy-standalone-assets.js` - Asset copying script
- `nginx-swapka-production-fixed.conf` - Fixed Nginx configuration
- `PRODUCTION_DEPLOYMENT_FIX.md` - This documentation

### Modified Files:
- `src/package.json` - Updated build scripts
- `ecosystem.config.js` - Enhanced deployment process
- `pm2-setup.sh` - Added asset verification
- `src/docs/standalone-deployment.md` - Updated documentation

## Monitoring & Maintenance

### Log Files to Monitor:
- `/var/log/nginx/swapka-static.log` - Static file requests
- `/var/log/nginx/swapka-static-error.log` - Static file errors
- `/var/log/nginx/swapka-404.log` - 404 errors

### Regular Checks:
1. Verify static assets exist after each deployment
2. Monitor 404 error logs for missing assets
3. Check MIME type headers in browser developer tools

## Troubleshooting

### If Static Assets Still Missing:
```bash
cd src
node scripts/copy-standalone-assets.js
pm2 restart swapka-prod
```

### If MIME Type Issues Persist:
1. Check Nginx configuration is active
2. Verify location block order (most specific first)
3. Test with curl to see actual headers

### If Build Fails:
```bash
# Manual build and copy
npm run build:only
node scripts/copy-standalone-assets.js
```

## Success Metrics

After implementing this fix:
- ✅ All static assets load without 404 errors
- ✅ Correct MIME types served for all file types
- ✅ Cache-busting mechanism works properly
- ✅ No X-Content-Type-Options conflicts
- ✅ Improved page load performance

## Next Steps

1. Monitor production logs for 24-48 hours
2. Verify all pages load correctly
3. Test on different devices/browsers
4. Consider implementing automated deployment testing
5. Document any additional edge cases discovered
