#!/bin/bash

# Skript na aplikovanie minimálnej Nginx konfigurácie
# Pôvodná konfigurácia + len 413 fix pre upload fotografií

# Farby pre výstup
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}=== Aplikovanie minimálnej Nginx konfigurácie pre swapka.sk ===${NC}"
echo -e "${YELLOW}Pôvodná konfigurácia + 413 fix pre upload fotografií${NC}"
echo ""

# Kontrola, či je skript spustený ako root
if [ "$EUID" -ne 0 ]; then
    echo -e "${RED}Tento skript musí byť spustený ako root (sudo)${NC}"
    echo "Použite: sudo ./apply-minimal-nginx-fix.sh"
    exit 1
fi

# <PERSON><PERSON><PERSON><PERSON>, či existuje nová konfigurácia
if [ ! -f "nginx-swapka-minimal-fix.conf" ]; then
    echo -e "${RED}Súbor nginx-swapka-minimal-fix.conf nebol nájdený!${NC}"
    echo "Uistite sa, že ste v správnom adresári."
    exit 1
fi

# Nájdenie aktuálnej Nginx konfigurácie pre swapka.sk
NGINX_SITES_DIR="/etc/nginx/sites-available"
NGINX_ENABLED_DIR="/etc/nginx/sites-enabled"
CURRENT_CONFIG=""

# Hľadanie existujúcej konfigurácie
for config_file in "$NGINX_SITES_DIR"/*; do
    if [ -f "$config_file" ] && grep -q "swapka.sk" "$config_file"; then
        CURRENT_CONFIG="$config_file"
        break
    fi
done

if [ -z "$CURRENT_CONFIG" ]; then
    echo -e "${RED}Existujúca konfigurácia pre swapka.sk nebola nájdená!${NC}"
    echo "Skontrolujte /etc/nginx/sites-available/"
    exit 1
else
    echo -e "${GREEN}Nájdená existujúca konfigurácia: $CURRENT_CONFIG${NC}"
fi

# Backup existujúcej konfigurácie
BACKUP_FILE="${CURRENT_CONFIG}.minimal-backup.$(date +%Y%m%d_%H%M%S)"
echo -e "${YELLOW}Vytváram backup: $BACKUP_FILE${NC}"
cp "$CURRENT_CONFIG" "$BACKUP_FILE"

# Kopírovanie novej konfigurácie
echo -e "${YELLOW}Aplikujem minimálnu konfiguráciu...${NC}"
cp nginx-swapka-minimal-fix.conf "$CURRENT_CONFIG"

# Vytvorenie potrebných log súborov
echo -e "${YELLOW}Vytváram log súbory pre upload...${NC}"
touch /var/log/nginx/swapka-upload.log
touch /var/log/nginx/swapka-upload-error.log

# Nastavenie oprávnení pre logy
chown www-data:www-data /var/log/nginx/swapka-upload*.log
chmod 644 /var/log/nginx/swapka-upload*.log

# Testovanie konfigurácie
echo -e "${YELLOW}Testovanie Nginx konfigurácie...${NC}"
if nginx -t; then
    echo -e "${GREEN}✓ Nginx konfigurácia je v poriadku${NC}"
    
    # Reload Nginx
    echo -e "${YELLOW}Reloadujem Nginx...${NC}"
    systemctl reload nginx
    
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✓ Nginx úspešne reloadovaný${NC}"
    else
        echo -e "${RED}✗ Chyba pri reloade Nginx${NC}"
        echo -e "${YELLOW}Obnovujem backup konfiguráciu...${NC}"
        cp "$BACKUP_FILE" "$CURRENT_CONFIG"
        nginx -t && systemctl reload nginx
        exit 1
    fi
else
    echo -e "${RED}✗ Chyba v Nginx konfigurácii!${NC}"
    nginx -t
    echo ""
    echo -e "${YELLOW}Obnovujem backup konfiguráciu...${NC}"
    cp "$BACKUP_FILE" "$CURRENT_CONFIG"
    nginx -t && systemctl reload nginx
    exit 1
fi

echo ""
echo -e "${GREEN}=== Minimálna konfigurácia aplikovaná! ===${NC}"
echo ""
echo -e "${BLUE}Zmeny:${NC}"
echo -e "  ✓ Zachovaná pôvodná štruktúra location blokov"
echo -e "  ✓ Pridané: client_max_body_size 20M (25M pre upload)"
echo -e "  ✓ Pridané: location = /api/cloudinary/upload s proxy_request_buffering off"
echo -e "  ✓ Pridané: zvýšené timeouty pre upload"
echo -e "  ✓ Pridané: custom JSON error handling pre 413 chyby"
echo ""
echo -e "${YELLOW}Testovanie:${NC}"
echo -e "  1. Skúste pristúpiť na https://www.swapka.sk/ako-to-funguje"
echo -e "  2. Skúste nahrať obrázok (upload by mal fungovať bez 413 chyby)"
echo -e "  3. Sledujte upload logy: tail -f /var/log/nginx/swapka-upload*.log"
echo ""
echo -e "${GREEN}Konfigurácia by mala zachovať routing a opraviť upload!${NC}"
