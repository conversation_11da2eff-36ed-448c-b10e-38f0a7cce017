FROM node:20-alpine

# Nastavenie pracovného adresára
WORKDIR /app

# Kop<PERSON>rovanie package.json a package-lock.json
COPY src/package.json src/package-lock.json ./

# Inštalácia závislostí
RUN npm ci

# Kopírovanie zdrojových súborov
COPY src/ ./

# Vytvorenie .env súboru s potrebnými premennými
RUN echo "DATABASE_URL=mysql://swapka_d5das4651:<EMAIL>/swapka_5336164" > .env && \
    echo "NEXT_PUBLIC_FIREBASE_API_KEY=AIzaSyDWeYOYXJmT4y5tkRaaPsEpMK9yPz6W4jc" >> .env && \
    echo "NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=swapka-b9171.firebaseapp.com" >> .env && \
    echo "NEXT_PUBLIC_FIREBASE_PROJECT_ID=swapka-b9171" >> .env && \
    echo "NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET=swapka-b9171.firebasestorage.app" >> .env && \
    echo "NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=178237532103" >> .env && \
    echo "NEXT_PUBLIC_FIREBASE_APP_ID=1:178237532103:web:7608883b895e5da20a9be2" >> .env

# Generovanie Prisma klienta
RUN npx prisma generate

# Build aplikácie
RUN NEXT_DISABLE_ESLINT=1 npm run build

# Nastavenie prostredia
ENV NODE_ENV=production
ENV PORT=3000
ENV HOSTNAME="0.0.0.0"

# Nastavenie používateľa pre bezpečnosť
RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs
RUN chown -R nextjs:nodejs /app
USER nextjs

# Nastavenie portu
EXPOSE 3000

# Spustenie aplikácie
CMD ["node", ".next/standalone/server.js"]
