# Docker inštrukcie pre Swapka aplikáciu

Tento dokument obsahuje podrobné inštrukcie pre spustenie Swapka aplikácie v Docker kontajneri.

## Požiadavky

- Docker (verzia 20.10.0 alebo novšia)
- Docker Compose (verzia 2.0.0 alebo novšia)

## Kontrola súborov

Pred spustením Docker buildu môžete skontrolovať, či sú všetky potrebné súbory na správnom mieste:

```bash
./check-docker-files.sh
```

## Spustenie aplikácie pomocou Docker Compose

Docker Compose je najjednoduchší spôsob, ako spustiť aplikáciu. Vykoná všetky potrebné kroky automaticky.

### Krok 1: Spustenie aplikácie

```bash
# Spustenie pomocou skriptu (odporúčané)
./docker-run.sh

# ALEBO manuálne spustenie
docker-compose up -d --build
```

Aplikácia bude dostupná na adrese [http://localhost:3000](http://localhost:3000).

### Krok 2: Zastavenie aplikácie

```bash
docker-compose down
```

## Manuálne buildovanie a spustenie Docker obrazu

Ak chcete mať väčšiu kontrolu nad procesom, môžete manuálne buildovať a spustiť Docker obraz.

### Krok 1: Buildovanie Docker obrazu

```bash
docker build -t swapka .
```

### Krok 2: Spustenie kontajnera

```bash
docker run -p 3000:3000 \
  -e DATABASE_URL="mysql://swapka_d5das4651:<EMAIL>/swapka_5336164" \
  -e NODE_ENV=production \
  --name swapka-app \
  swapka
```

### Krok 3: Zastavenie a odstránenie kontajnera

```bash
# Zastavenie kontajnera
docker stop swapka-app

# Odstránenie kontajnera
docker rm swapka-app
```

## Riešenie problémov

### Problém: Kontajner sa nespustí

Skontrolujte logy kontajnera:

```bash
docker logs swapka-app
```

### Problém: Aplikácia nie je dostupná

Skontrolujte, či je kontajner spustený:

```bash
docker ps
```

Skontrolujte, či je port 3000 otvorený:

```bash
docker-compose ps
```

### Problém: Problémy s databázou

Skontrolujte, či je správne nastavená premenná prostredia `DATABASE_URL`.

### Problém: Failed to deploy a stack

Ak sa stretnete s chybou "Failed to deploy a stack" alebo "failed to solve: failed to compute cache key", skontrolujte:

1. Či sú všetky súbory na správnom mieste pomocou skriptu `./check-docker-files.sh`
2. Či je Dockerfile správne nakonfigurovaný
3. Či je docker-compose.yml správne nakonfigurovaný (bez zastaralých atribútov)

Môžete skúsiť vyčistiť Docker cache:

```bash
docker builder prune -f
```

A potom znova spustiť build:

```bash
docker-compose up -d --build
```

### Problém: ESLint chyby počas buildu

Ak sa stretnete s ESLint chybami počas buildu, môžete ich ignorovať pomocou:

```bash
NEXT_DISABLE_ESLINT=1 npm run build
```

Alebo môžete upraviť `next.config.ts` a pridať:

```typescript
eslint: {
  ignoreDuringBuilds: true,
},
```

## Ďalšie informácie

Pre viac informácií o Docker a Docker Compose navštívte:

- [Docker dokumentácia](https://docs.docker.com/)
- [Docker Compose dokumentácia](https://docs.docker.com/compose/)
