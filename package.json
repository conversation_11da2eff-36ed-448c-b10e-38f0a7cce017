{"name": "swapka-deployment", "version": "1.0.0", "description": "Swapka production deployment automation", "private": true, "scripts": {"deploy:prod": "node scripts/deploy-production.js", "deploy:prod:force": "node scripts/deploy-production.js --force", "deploy:prod:create-tag": "node scripts/deploy-production.js --create-tag", "deploy:check": "node scripts/deploy-production.js --check-only", "deploy:shell": "bash scripts/deploy-production.sh", "deploy:shell:check": "bash scripts/deploy-production.sh --check-only", "deploy:shell:create-tag": "bash scripts/deploy-production.sh --create-tag", "test:deploy": "echo 'Testing deployment scripts...' && npm run deploy:check", "tag:create": "node -e \"const pkg=require('./src/package.json'); const tag='v'+pkg.version; console.log('Creating tag:',tag); require('child_process').execSync('git tag '+tag+' && git push origin '+tag, {stdio:'inherit'});\""}}