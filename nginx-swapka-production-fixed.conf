# Nginx konfigurácia pre swapka.sk - PRODUCTION FIXED
# R<PERSON>šenie 404 chýb pre statické súbory a MIME type problémov

# HTTP (port 80) – Let's Encrypt validation a presmerovanie na HTTPS
server {
    listen 80;
    server_name swapka.sk www.swapka.sk;

    # Let's Encrypt validation
    location /.well-known/acme-challenge/ {
        root /var/www/html;
    }

    # Presmerovanie všetkého ostatného na HTTPS
    location / {
        return 301 https://www.swapka.sk$request_uri;
    }
}

# HTTPS pre hlavný doménový variant (s www)
server {
    listen 443 ssl http2;
    server_name www.swapka.sk;

    ssl_certificate     /etc/letsencrypt/live/swapka.sk/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/swapka.sk/privkey.pem;

    # SSL optimalizácie
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-RSA-CHACHA20-POLY1305;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;

    # HSTS
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;

    # Bezpečnostné hlavičky (bez X-Content-Type-Options pre statické súbory)
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;

    # ===== KRITICKÉ NASTAVENIA PRE FILE UPLOADS =====
    client_max_body_size 20M;
    client_body_buffer_size 128k;
    client_body_temp_path /tmp/nginx_client_body_temp;
    client_body_timeout 60s;
    client_header_timeout 60s;
    send_timeout 60s;

    # Proxy buffer nastavenia
    proxy_buffering on;
    proxy_buffer_size 4k;
    proxy_buffers 8 4k;
    proxy_busy_buffers_size 8k;
    proxy_max_temp_file_size 1024m;
    proxy_temp_file_write_size 8k;
    proxy_connect_timeout 60s;
    proxy_send_timeout 60s;
    proxy_read_timeout 60s;

    # Gzip kompresácia
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/json
        application/javascript
        application/xml+rss
        application/atom+xml
        image/svg+xml;

    # ===== PRIORITNÉ LOCATION BLOKY =====

    # 1. PRESNÝ MATCH pre upload endpoint (najvyššia priorita)
    location = /api/cloudinary/upload {
        client_max_body_size 25M;
        client_body_timeout 120s;
        proxy_request_buffering off;
        
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_cache_bypass $http_upgrade;
        
        proxy_connect_timeout 120s;
        proxy_send_timeout 120s;
        proxy_read_timeout 120s;

        access_log /var/log/nginx/swapka-upload.log;
        error_log /var/log/nginx/swapka-upload-error.log;
    }

    # 2. Next.js statické súbory - KRITICKÉ PRE RIEŠENIE 404 CHÝB
    location /_next/static/ {
        proxy_pass http://localhost:3000;
        
        # Cache nastavenia pre statické súbory
        expires 1y;
        add_header Cache-Control "public, immutable";
        
        # MIME type handling - KRITICKÉ PRE RIEŠENIE MIME PROBLÉMOV
        location ~* \.(js|mjs)$ {
            proxy_pass http://localhost:3000;
            add_header Content-Type "application/javascript; charset=utf-8";
            expires 1y;
            add_header Cache-Control "public, immutable";
        }
        
        location ~* \.(css)$ {
            proxy_pass http://localhost:3000;
            add_header Content-Type "text/css; charset=utf-8";
            expires 1y;
            add_header Cache-Control "public, immutable";
        }
        
        location ~* \.(woff|woff2|ttf|eot)$ {
            proxy_pass http://localhost:3000;
            add_header Content-Type "font/woff2";
            expires 1y;
            add_header Cache-Control "public, immutable";
            add_header Access-Control-Allow-Origin "*";
        }
        
        # Gzip pre statické súbory
        gzip_static on;
        
        # Logging pre debugging
        access_log /var/log/nginx/swapka-static.log;
        error_log /var/log/nginx/swapka-static-error.log;
    }

    # 3. Presné matches pre špeciálne súbory
    location = /favicon.ico {
        proxy_pass http://localhost:3000;
        expires 1d;
        add_header Cache-Control "public";
        add_header Content-Type "image/x-icon";
    }
    
    location = /robots.txt {
        proxy_pass http://localhost:3000;
        expires 1d;
        add_header Cache-Control "public";
        add_header Content-Type "text/plain; charset=utf-8";
    }
    
    location = /sitemap.xml {
        proxy_pass http://localhost:3000;
        expires 1d;
        add_header Cache-Control "public";
        add_header Content-Type "application/xml; charset=utf-8";
    }

    # 4. API endpointy (okrem upload)
    location /api/ {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_cache_bypass $http_upgrade;
        
        # Bezpečnostné hlavičky pre API
        add_header X-Content-Type-Options "nosniff" always;
    }

    # 5. VŠEOBECNÝ LOCATION BLOK - NAJNIŽŠIA PRIORITA
    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_cache_bypass $http_upgrade;
        
        # Bezpečnostné hlavičky pre HTML stránky
        add_header X-Content-Type-Options "nosniff" always;
    }

    # Custom error handling
    error_page 413 @413_error;
    location @413_error {
        return 413 '{"error": "Súbor je príliš veľký", "details": ["Maximálna povolená veľkosť je 20MB"], "code": 413}';
        add_header Content-Type application/json always;
    }
    
    # Error page pre 404 chyby statických súborov
    error_page 404 @404_error;
    location @404_error {
        access_log /var/log/nginx/swapka-404.log;
        return 404 '{"error": "Súbor nebol nájdený", "code": 404}';
        add_header Content-Type application/json always;
    }
}

# HTTPS pre doménu bez www (presmerovanie na www)
server {
    listen 443 ssl http2;
    server_name swapka.sk;

    ssl_certificate     /etc/letsencrypt/live/swapka.sk/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/swapka.sk/privkey.pem;

    return 301 https://www.swapka.sk$request_uri;
}
