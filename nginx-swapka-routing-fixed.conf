# Nginx konfigurácia pre swapka.sk - OPRAVENÉ ROUTOVANIE
# Riešenie 413 chyby + zachovanie Next.js routing funkcionality

# HTTP (port 80) – Let's Encrypt validation a presmerovanie na HTTPS
server {
    listen 80;
    server_name swapka.sk www.swapka.sk;

    # Let's Encrypt validation
    location /.well-known/acme-challenge/ {
        root /var/www/html;
    }

    # Presmerovanie všetkého ostatného na HTTPS
    location / {
        return 301 https://www.swapka.sk$request_uri;
    }
}

# HTTPS pre hlavný doménový variant (s www)
server {
    listen 443 ssl http2;
    server_name www.swapka.sk;

    # SSL certifikáty
    ssl_certificate     /etc/letsencrypt/live/swapka.sk/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/swapka.sk/privkey.pem;

    # SSL optimalizácie
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-RSA-CHACHA20-POLY1305;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;

    # HSTS
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;

    # Bezpečnostné hlavičky
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;

    # ===== KRITICKÉ NASTAVENIA PRE FILE UPLOADS =====
    # Maximálna veľkosť requestu - HLAVNÝ FIX PRE 413 CHYBU
    client_max_body_size 20M;
    
    # Buffer nastavenia pre veľké súbory
    client_body_buffer_size 128k;
    client_body_temp_path /tmp/nginx_client_body_temp;
    
    # Timeout nastavenia
    client_body_timeout 60s;
    client_header_timeout 60s;
    send_timeout 60s;
    
    # Proxy buffer nastavenia
    proxy_buffering on;
    proxy_buffer_size 4k;
    proxy_buffers 8 4k;
    proxy_busy_buffers_size 8k;
    proxy_max_temp_file_size 1024m;
    proxy_temp_file_write_size 8k;
    
    # Proxy timeout nastavenia
    proxy_connect_timeout 60s;
    proxy_send_timeout 60s;
    proxy_read_timeout 60s;

    # ===== LOCATION BLOKY V SPRÁVNOM PORADÍ PRE NGINX =====
    
    # 1. PRESNÝ MATCH pre upload endpoint (najvyššia priorita)
    location = /api/cloudinary/upload {
        # Zvýšené limity špecificky pre upload
        client_max_body_size 25M;
        client_body_timeout 120s;
        
        # Vypnutie proxy bufferovanie pre streaming uploads
        proxy_request_buffering off;
        
        # Proxy nastavenia
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_cache_bypass $http_upgrade;
        
        # Zvýšené timeout pre upload
        proxy_connect_timeout 120s;
        proxy_send_timeout 120s;
        proxy_read_timeout 120s;

        # Špeciálne logovanie pre upload debugging
        access_log /var/log/nginx/swapka-upload.log;
        error_log /var/log/nginx/swapka-upload-error.log;
    }

    # 2. Next.js statické súbory (prefix match)
    location /_next/static/ {
        proxy_pass http://localhost:3000;
        expires 1y;
        add_header Cache-Control "public, immutable";
        gzip_static on;
    }

    # 3. Presné matches pre špeciálne súbory
    location = /favicon.ico {
        proxy_pass http://localhost:3000;
        expires 1d;
        add_header Cache-Control "public";
    }
    
    location = /robots.txt {
        proxy_pass http://localhost:3000;
        expires 1d;
        add_header Cache-Control "public";
    }
    
    location = /sitemap.xml {
        proxy_pass http://localhost:3000;
        expires 1d;
        add_header Cache-Control "public";
    }

    # 4. VŠEOBECNÝ LOCATION BLOK - NAJNIŽŠIA PRIORITA
    # Toto zachytí VŠETKY ostatné requesty vrátane:
    # - /hracky/h-c514910b9a5e (toy detail pages)
    # - /profil/ (user profiles)  
    # - /ako-to-funguje (static pages)
    # - /api/* (všetky API endpointy okrem upload)
    # - Všetky Next.js App Router routes
    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_cache_bypass $http_upgrade;
    }

    # Custom error handling pre 413 chybu
    error_page 413 @413_error;
    location @413_error {
        return 413 '{"error": "Súbor je príliš veľký", "details": ["Maximálna povolená veľkosť je 20MB"], "code": 413}';
        add_header Content-Type application/json always;
    }

    # Error handling pre server chyby
    error_page 502 503 504 @50x_error;
    location @50x_error {
        return 502 '{"error": "Server je dočasne nedostupný", "details": ["Skúste to znovu neskôr"], "code": 502}';
        add_header Content-Type application/json always;
    }

    # Logovanie
    access_log /var/log/nginx/swapka-access.log;
    error_log /var/log/nginx/swapka-error.log;
}

# Presmerovanie z https://swapka.sk na https://www.swapka.sk
server {
    listen 443 ssl http2;
    server_name swapka.sk;

    # SSL certifikáty
    ssl_certificate     /etc/letsencrypt/live/swapka.sk/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/swapka.sk/privkey.pem;

    # SSL nastavenia (rovnaké ako hlavný server)
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-RSA-CHACHA20-POLY1305;
    ssl_prefer_server_ciphers off;

    # Presmerovanie na www variant
    return 301 https://www.swapka.sk$request_uri;
}
