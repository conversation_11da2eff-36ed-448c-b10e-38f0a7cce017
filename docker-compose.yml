services:
  swapka:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - DATABASE_URL=mysql://swapka_d5das4651:<EMAIL>/swapka_5336164
      - NEXT_PUBLIC_FIREBASE_API_KEY=AIzaSyDWeYOYXJmT4y5tkRaaPsEpMK9yPz6W4jc
      - NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=swapka-b9171.firebaseapp.com
      - NEXT_PUBLIC_FIREBASE_PROJECT_ID=swapka-b9171
      - NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET=swapka-b9171.firebasestorage.app
      - NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=178237532103
      - NEXT_PUBLIC_FIREBASE_APP_ID=1:178237532103:web:7608883b895e5da20a9be2
      - FIREBASE_PROJECT_ID=swapka-b9171
      - FIREBASE_CLIENT_EMAIL=<EMAIL>
      - FIREBASE_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\nMIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQC7VJTUt9Us8cKj\nMzEfYyjiWA4R4/M2bS1GB4t7NXp98C3SC6dVMvDuictGeurT8jNbvJZHtCSuYEvu\nNMoSfm76oqFvAp8Gy0iz5sxjZmSnXyCdPEovGhLa0VzMaQ8s+CLOyS56YyCFGeJZ\n3Afo+2Ghku1WYIh4zmFoRfpug7gQIL5TtC6z7lhVnVMD0DpGM9LDIWb+3t1MKC1A\n4AD7+0UJOcZOUEuEcc7Wf/VrXG8hwYFm2nJZLy7nNvxQsRDM/3xZKZ6nQvXnCJxj\n/cFYCikPdPDv3xduSjCLLXu5Iw9Nf6J7uQHEzamRGZA4kIvpS3pAB9/eqUPQIt1G\nGQe9j1IzAgMBAAECggEADxMTyQpuXjrBL0HOSSTC6U3CiEAQ9xQoe9MrAi52XnEE\nJRY0UWQcqBgMQXmqzCTOvDD9ySBpjRiNHqgJePdKDMnCETJVQNxBMhRKwj2jYX+q\nZFgPQ4fTGFP+fhQNAhBUlGIAnP6G+bXQnQAKX7Lf8tSqknTg8FuI5K2U77+vLB2b\nz1BokirLgYHh2+uIVGYdFZ7GDlzUMfQPdL1oqTRGkWYRtDjFKLHn8qcLzU5OfUeL\nJYyXk1KxZp7R7wZXzqGH/YClWun0Wu8Q3Bj9txW0P+TUxEkS5GYO8o7Qvz75T6dv\nzEz3T0yRPZiwpkQjZkRuKDEH8GpM+7WDiAMZ0A8QAQKBgQDm2V7fNXrjzUFwpcFI\nLGGwLnLYZ77tLE9JhFMbOu0qpgpy28uEE53HbLoz9FQJfoZMJCBcnZl2eHjQxZ2V\nPqtOyQNpGTmN5JCbJz9QIXa8oAMCmFcjVpD0FxMxCS6YvUPO5JHaZIHSR2QLzGXq\nGg+x9A/KN2JqUcka8eoKqwOfgQKBgQDPz+KRY2X2WwTzkPKrZkND8IPyPwUMmOcX\nEPRwGjDH5jeZyf5XQX0dcmprJpKzMDchN4LNjwYmEISuxIlUMnZR6JUSJmMKvt/k\nXbfX5F5fWp9hnlhb85Eoo/1Fj0WIbRiJkk/Qxnxp3JhwbdPcIFkOuCNxjkCu4hRG\nCJVzs8SjMwKBgCo7HWU7z0Lb0/Pxz+Z+4ZFBVJTGKgYLsK2un8QVG4qAXD0YZOFV\nPZXYNrxOL4huycjYzTEeQMbDC+jUUxpMELM/Xl3rLX8U1pGQOFb0wRdVTOHp5c0C\nKHcwAdZZ0cFpRkZmqwvqmvMnpyAzNtCL3U4t2My2DBPnZ1O0PkFW3YeBAoGBAKt4\nxXr20rBnk9WmZ4vVQdP3ABHzAFYGOVTz5W7FTyRvMEQKFSp+Esm6WT+QEBYYiT1J\nQLEKpR8qgP5lMxwW4U9SPj2QEXptNJ+SgZEGfp5ZGMYpUDPrc5Z4Dh0Z7VvxtCVx\nKgDAqxhM5YzQs4I8SZEpGqVrM6kNlYZJGmEnx6UHAoGAQTb4q3LwGwA0zxiDZ3lP\nAFKrNjgKhtyK9FQpkLhQUwTMkOxQqn5TQYVbdJSi6qsQSRDCwuPyfhb0RtPW6wAF\nQRHW5wPyD6EjRhG2UZl5PV7rR/8sMnOJqK+QKWcZkFDvkL2wDQtIQNQFjKKOjzjn\nRXaUSSsrR4VRzKyX4/ATsaI=\n-----END PRIVATE KEY-----\n"
      # Cloudinary Configuration
      - CLOUDINARY_CLOUD_NAME=dejdfe6aq
      - CLOUDINARY_API_KEY=176456215174513
      - CLOUDINARY_API_SECRET=jXRaVQblj0J5KnIsVWpYNmEBzME
      - NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME=dejdfe6aq
      # Security Configuration
      - CSRF_SECRET=2ba1e426d73cc3a53e40d96fcc441c39
      - CSP_DYNAMIC_ENABLED=true
      - CSP_CACHE_MAX_AGE=300000
      - CSP_LOG_LEVEL=info
      - CSP_BUILD_DIR=.next
      - CSP_HASH_FILE=.next/csp-hashes.json
      # New Relic Configuration
      - NEW_RELIC_APP_NAME=swapka-docker
      - NEW_RELIC_LICENSE_KEY=eu01xxc543fb0e74830dd4a3ed746d8bFFFFNRAL
      - NEW_RELIC_AI_MONITORING_ENABLED=true
      - NEW_RELIC_CUSTOM_INSIGHTS_EVENTS_MAX_SAMPLES_STORED=100k
      - NEW_RELIC_SPAN_EVENTS_MAX_SAMPLES_STORED=10k
      # Rate Limiting Configuration
      - RATE_LIMIT_AUTH_MAX=5
      - RATE_LIMIT_AUTH_WINDOW_MS=60000
      - RATE_LIMIT_GENERAL_MAX=100
      - RATE_LIMIT_GENERAL_WINDOW_MS=60000
      - RATE_LIMIT_ADMIN_MAX=20
      - RATE_LIMIT_ADMIN_WINDOW_MS=60000
      - RATE_LIMIT_UPLOAD_MAX=10
      - RATE_LIMIT_UPLOAD_WINDOW_MS=60000
      - RATE_LIMIT_ENABLED=true
      - RATE_LIMIT_SKIP_SUCCESSFUL_REQUESTS=false
      - RATE_LIMIT_SKIP_FAILED_REQUESTS=false
      - RATE_LIMIT_WHITELIST_IPS=""
      # Email Configuration (NEW in v0.20)
      - RESEND_API_KEY=re_AShf6Fz1_4bivz5PVs9chLJSLRfNU4JVY
      - EMAIL_FROM=<EMAIL>
      - EMAIL_FROM_NAME="Swapka - Zdieľanie hračiek"
    restart: always
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:3000"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 10s

  # Ak by ste chceli pridať lokálnu databázu pre vývoj, môžete odkomentovať nasledujúci blok
  # db:
  #   image: mysql:8.0
  #   ports:
  #     - "3306:3306"
  #   environment:
  #     - MYSQL_ROOT_PASSWORD=rootpassword
  #     - MYSQL_DATABASE=swapka
  #     - MYSQL_USER=swapka_user
  #     - MYSQL_PASSWORD=swapka_password
  #   volumes:
  #     - mysql_data:/var/lib/mysql
  #   restart: always
  #   healthcheck:
  #     test: ["CMD", "mysqladmin", "ping", "-h", "localhost", "-u", "root", "-p$$MYSQL_ROOT_PASSWORD"]
  #     interval: 10s
  #     timeout: 5s
  #     retries: 5
  #     start_period: 30s

# volumes:
#   mysql_data:
