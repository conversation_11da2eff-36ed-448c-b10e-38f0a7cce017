# Riešenie Nginx Routing Problémov po 413 Fixe

## Problém
Po aplikovaní Nginx konfigurácie na riešenie 413 "Request Entity Too Large" chyby sa pokazilo routovanie aplikácie. Nefungujú URL s path segmentmi:

- `/hracky/h-c514910b9a5e` (toy detail pages s hashovanými ID)
- `/profil/` (user profile pages)
- `/ako-to-funguje` (how it works page)
- Ostatné Next.js App Router routes

## Príčina
Problém bol v **nesprávnom poradí a type location blokov** v Nginx konfigurácii:

### Pôvodná (funkčná) konfigurácia:
```nginx
location / {
    proxy_pass http://localhost:3000;
    # ... headers
}
```

### Problematická konfigurácia po 413 fixe:
```nginx
location /api/cloudinary/upload { ... }
location /api/ { ... }                    # ← PROBLÉM!
location /_next/static/ { ... }
location ~ ^/(favicon\.ico|robots\.txt|sitemap\.xml)$ { ... }
location / { ... }
```

**Problém:** `location /api/` blok zachytával všetky requesty začínajúce `/api/`, čo interferovalo s Next.js API routes a mohlo ovplyvniť aj iné routes.

## Riešenie

### Správne poradie location blokov v Nginx:

Nginx vyhodnocuje location bloky v tomto poradí:
1. **Presné matches** (`location = /path`)
2. **Prefix matches** (`location /path`)
3. **Regex matches** (`location ~ regex`)
4. **Všeobecný catch-all** (`location /`)

### Opravená konfigurácia:

```nginx
# 1. PRESNÝ MATCH pre upload endpoint (najvyššia priorita)
location = /api/cloudinary/upload {
    client_max_body_size 25M;
    proxy_request_buffering off;
    # ... upload specific settings
}

# 2. Next.js statické súbory (prefix match)
location /_next/static/ {
    proxy_pass http://localhost:3000;
    expires 1y;
    add_header Cache-Control "public, immutable";
}

# 3. Presné matches pre špeciálne súbory
location = /favicon.ico { ... }
location = /robots.txt { ... }
location = /sitemap.xml { ... }

# 4. VŠEOBECNÝ CATCH-ALL (najnižšia priorita)
# Zachytí VŠETKY ostatné requesty vrátane:
# - /hracky/h-c514910b9a5e
# - /profil/
# - /ako-to-funguje
# - /api/* (všetky API endpointy okrem upload)
location / {
    proxy_pass http://localhost:3000;
    # ... standard proxy settings
}
```

## Automatická oprava

```bash
# Spustite skript na opravu routing
sudo chmod +x fix-nginx-routing.sh
sudo ./fix-nginx-routing.sh
```

## Manuálna oprava

### Krok 1: Backup aktuálnej konfigurácie
```bash
sudo cp /etc/nginx/sites-available/swapka /etc/nginx/sites-available/swapka.routing-backup.$(date +%Y%m%d)
```

### Krok 2: Nahradenie konfigurácie
```bash
sudo cp nginx-swapka-routing-fixed.conf /etc/nginx/sites-available/swapka
```

### Krok 3: Testovanie a reload
```bash
sudo nginx -t
sudo systemctl reload nginx
```

## Testovanie

### 1. Testovanie Next.js routes
```bash
# Toy detail pages
curl -I https://www.swapka.sk/hracky/h-c514910b9a5e

# User profiles
curl -I https://www.swapka.sk/profil/

# Static pages
curl -I https://www.swapka.sk/ako-to-funguje
```

### 2. Testovanie upload funkcionality
- Otvorte swapka.sk
- Skúste nahrať obrázok
- Upload by mal stále fungovať bez 413 chyby

### 3. Sledovanie logov
```bash
# Všeobecné logy
sudo tail -f /var/log/nginx/swapka-access.log

# Upload logy
sudo tail -f /var/log/nginx/swapka-upload.log
```

## Kľúčové zmeny

### ✅ Zachované (z 413 fixu):
- `client_max_body_size 20M` (25M pre upload)
- `proxy_request_buffering off` pre upload endpoint
- Zvýšené timeouty pre upload
- Custom JSON error handling

### ✅ Opravené (routing):
- Odstránený problematický `location /api/` blok
- Použitý presný match `location = /api/cloudinary/upload`
- Všeobecný `location /` catch-all pre Next.js routes
- Správne poradie location blokov

## Next.js App Router štruktúra

Aplikácia používa Next.js 13+ App Router s touto štruktúrou:

```
src/app/
├── hracky/[id]/page.tsx          # /hracky/h-c514910b9a5e
├── profil/[userHash]/page.tsx    # /profil/u-abc123
├── ako-to-funguje/page.tsx       # /ako-to-funguje
├── api/
│   ├── cloudinary/upload/route.ts
│   ├── toy/[id]/route.ts
│   └── toys/[id]/route.ts
└── ...
```

Všetky tieto routes musia byť správne proxované na Next.js server na porte 3000.

## Riešenie problémov

### Ak routing stále nefunguje:

1. **Skontrolujte Nginx error logy:**
   ```bash
   sudo tail -f /var/log/nginx/error.log
   sudo tail -f /var/log/nginx/swapka-error.log
   ```

2. **Skontrolujte Next.js logy:**
   ```bash
   pm2 logs swapka-prod
   ```

3. **Testujte priamo Next.js server:**
   ```bash
   curl -I http://localhost:3000/hracky/h-c514910b9a5e
   ```

4. **Skontrolujte Nginx konfiguráciu:**
   ```bash
   sudo nginx -t
   sudo nginx -T | grep -A 20 "server_name www.swapka.sk"
   ```

### Ak upload prestane fungovať:

1. **Skontrolujte upload endpoint:**
   ```bash
   curl -X POST -F "file=@test.jpg" https://www.swapka.sk/api/cloudinary/upload
   ```

2. **Skontrolujte upload logy:**
   ```bash
   sudo tail -f /var/log/nginx/swapka-upload*.log
   ```

## Súvisiace súbory

- `nginx-swapka-routing-fixed.conf` - Opravená konfigurácia
- `fix-nginx-routing.sh` - Automatický opravný skript
- `nginx-swapka-fixed.conf` - Pôvodný 413 fix (s routing problémami)
- `/var/log/nginx/swapka-*.log` - Nginx logy
