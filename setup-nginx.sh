#!/bin/bash

# Setup skript pre Nginx konfiguráciu pre Swapka aplikáciu
# Riešenie 413 "Request Entity Too Large" chyby

# Farby pre výstup
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}=== Nginx Setup pre Swapka aplikáciu ===${NC}"
echo -e "${YELLOW}Riešenie 413 'Request Entity Too Large' chyby${NC}"
echo ""

# Kontrola, či je skript spustený ako root
if [ "$EUID" -ne 0 ]; then
    echo -e "${RED}Tento skript musí byť spustený ako root (sudo)${NC}"
    exit 1
fi

# Kontrola, či je Nginx nainštalovaný
if ! command -v nginx &> /dev/null; then
    echo -e "${YELLOW}Nginx nie je nainštalovaný. Inštalujem...${NC}"
    
    # Detekcia distribúcie
    if [ -f /etc/debian_version ]; then
        apt update
        apt install -y nginx
    elif [ -f /etc/redhat-release ]; then
        yum install -y nginx || dnf install -y nginx
    else
        echo -e "${RED}Nepodporovaná distribúcia. Nainštalujte Nginx manuálne.${NC}"
        exit 1
    fi
fi

echo -e "${GREEN}Nginx je nainštalovaný${NC}"

# Backup existujúcej konfigurácie
if [ -f /etc/nginx/nginx.conf ]; then
    echo -e "${YELLOW}Vytváram backup existujúcej konfigurácie...${NC}"
    cp /etc/nginx/nginx.conf /etc/nginx/nginx.conf.backup.$(date +%Y%m%d_%H%M%S)
fi

# Kopírovanie hlavnej konfigurácie
echo -e "${YELLOW}Kopírujem hlavnú Nginx konfiguráciu...${NC}"
if [ -f "nginx.conf" ]; then
    cp nginx.conf /etc/nginx/nginx.conf
    echo -e "${GREEN}Hlavná konfigurácia skopírovaná${NC}"
else
    echo -e "${RED}Súbor nginx.conf nebol nájdený v aktuálnom adresári${NC}"
    exit 1
fi

# Kopírovanie site konfigurácie
echo -e "${YELLOW}Kopírujem site konfiguráciu...${NC}"
if [ -f "nginx-swapka.conf" ]; then
    cp nginx-swapka.conf /etc/nginx/sites-available/swapka
    
    # Vytvorenie symlinku pre aktiváciu
    if [ ! -L /etc/nginx/sites-enabled/swapka ]; then
        ln -s /etc/nginx/sites-available/swapka /etc/nginx/sites-enabled/swapka
        echo -e "${GREEN}Site konfigurácia aktivovaná${NC}"
    fi
else
    echo -e "${RED}Súbor nginx-swapka.conf nebol nájdený v aktuálnom adresári${NC}"
    exit 1
fi

# Odstránenie default site (voliteľné)
read -p "Chcete odstrániť default Nginx site? (y/n): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    if [ -L /etc/nginx/sites-enabled/default ]; then
        rm /etc/nginx/sites-enabled/default
        echo -e "${GREEN}Default site odstránený${NC}"
    fi
fi

# Vytvorenie log adresárov
echo -e "${YELLOW}Vytváram log adresáre...${NC}"
mkdir -p /var/log/nginx
touch /var/log/nginx/swapka-access.log
touch /var/log/nginx/swapka-error.log
touch /var/log/nginx/swapka-upload.log
touch /var/log/nginx/swapka-upload-error.log

# Nastavenie oprávnení
chown -R www-data:www-data /var/log/nginx
chmod 644 /var/log/nginx/swapka-*.log

# Testovanie konfigurácie
echo -e "${YELLOW}Testovanie Nginx konfigurácie...${NC}"
if nginx -t; then
    echo -e "${GREEN}Nginx konfigurácia je v poriadku${NC}"
    
    # Reštart Nginx
    echo -e "${YELLOW}Reštartujem Nginx...${NC}"
    systemctl restart nginx
    
    if systemctl is-active --quiet nginx; then
        echo -e "${GREEN}Nginx úspešne reštartovaný${NC}"
        
        # Povolenie autostart
        systemctl enable nginx
        echo -e "${GREEN}Nginx autostart povolený${NC}"
    else
        echo -e "${RED}Chyba pri reštarte Nginx${NC}"
        systemctl status nginx
        exit 1
    fi
else
    echo -e "${RED}Chyba v Nginx konfigurácii${NC}"
    nginx -t
    exit 1
fi

echo ""
echo -e "${GREEN}=== Nginx setup dokončený ===${NC}"
echo -e "${BLUE}Kľúčové nastavenia:${NC}"
echo -e "  • client_max_body_size: 20M (25M pre upload endpointy)"
echo -e "  • client_body_timeout: 60s (120s pre upload)"
echo -e "  • proxy timeouts: 60s (120s pre upload)"
echo -e "  • proxy_request_buffering: off pre upload endpointy"
echo ""
echo -e "${YELLOW}Logy:${NC}"
echo -e "  • Hlavné logy: /var/log/nginx/swapka-*.log"
echo -e "  • Upload logy: /var/log/nginx/swapka-upload*.log"
echo ""
echo -e "${YELLOW}Ďalšie kroky:${NC}"
echo -e "  1. Upravte server_name v /etc/nginx/sites-available/swapka"
echo -e "  2. Skontrolujte, či Next.js aplikácia beží na porte 3000"
echo -e "  3. Testujte nahrávanie súborov"
echo ""
echo -e "${GREEN}Nginx je pripravený na riešenie 413 chýb!${NC}"
