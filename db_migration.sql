-- Pridanie stĺpca position do tabuľky Reservation
ALTER TABLE `Reservation` ADD COLUMN `position` INT NOT NULL DEFAULT 0;

-- Inicializácia pozícií pre existujúce rezervácie
-- Nastavenie všetkých na 0 (aktívna rezervácia)
UPDATE `Reservation` SET `position` = 0;

-- Nastavenie pozícií pre rezervácie podľa hračiek a dátumu vytvorenia
-- Toto je komplexný príkaz, ktorý nastaví pozície pre všetky hračky naraz

-- Najprv vytvoríme dočasnú tabuľku s poradím rezervácií
CREATE TEMPORARY TABLE IF NOT EXISTS temp_reservation_positions AS
SELECT 
    r.id,
    r.toyId,
    r.status,
    @row_number := IF(@current_toy = r.toyId, @row_number + 1, 0) AS position,
    @current_toy := r.toyId
FROM 
    (SELECT * FROM `Reservation` 
     WHERE `status` IN ('PENDING', 'CONFIRMED', 'ACTIVE')
     ORDER BY `toyId`, `createdAt`) r,
    (SELECT @row_number := -1, @current_toy := 0) t;

-- Potom aktualizujeme pozície v hlavnej tabuľke
UPDATE `Reservation` r
JOIN temp_reservation_positions t ON r.id = t.id
SET r.position = t.position
WHERE r.status IN ('PENDING', 'CONFIRMED', 'ACTIVE');

-- Odstránime dočasnú tabuľku
DROP TEMPORARY TABLE IF EXISTS temp_reservation_positions;

-- Alternatívny jednoduchší prístup (ak by horný nefungoval):
-- Tento prístup vyžaduje manuálne spustenie pre každú hračku s viacerými rezerváciami

-- Príklad pre konkrétnu hračku (nahraďte TOY_ID skutočným ID hračky):
/*
SET @row_number = -1;
UPDATE `Reservation` 
SET `position` = (@row_number := @row_number + 1)
WHERE `toyId` = TOY_ID AND `status` IN ('PENDING', 'CONFIRMED', 'ACTIVE')
ORDER BY `createdAt` ASC;
*/
