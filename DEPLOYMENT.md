# Swapka Production Deployment

This document describes the automated production deployment process for the Swapka application.

## Overview

The deployment automation replaces the manual 6-step process with a single command that:

1. Pulls the latest changes from the git repository
2. Finds and checks out the latest version tag (following semantic versioning v0.19, v0.20, v0.21, etc.)
3. Navigates to the src directory and runs the build process
4. Navigates back to the root directory
5. Manages PM2 processes (graceful reload for zero-downtime deployment)
6. Verifies deployment status

**Important:** This deployment system now uses **git tags only** (not branches). You must create version tags before deployment.

## Prerequisites

- Node.js and npm installed
- PM2 installed globally (`npm install -g pm2`)
- Git repository with proper version tags (e.g., v0.22, v0.23)
- Proper file permissions for the deployment scripts
- Working directory should be `/home/<USER>/www/swapka`

## Creating Version Tags

Before deployment, you need to create version tags. You can do this in several ways:

### Method 1: Automatic tag creation from package.json
```bash
npm run tag:create
```

### Method 2: Manual tag creation
```bash
git tag v0.22
git push origin v0.22
```

### Method 3: Create tag during deployment
```bash
npm run deploy:prod:create-tag
# or
npm run deploy:shell:create-tag
```

## Installation

1. Ensure you're in the correct working directory:
   ```bash
   cd /home/<USER>/www/swapka
   ```

2. Install deployment dependencies (if using Node.js version):
   ```bash
   npm install
   ```

3. Make shell script executable (if using shell version):
   ```bash
   chmod +x scripts/deploy-production.sh
   ```

## Usage

### Option 1: Node.js Script (Recommended)

```bash
# Full production deployment (requires existing tags)
npm run deploy:prod

# Check prerequisites only (dry run)
npm run deploy:check

# Create tag from package.json version and deploy
npm run deploy:prod:create-tag

# Force deployment (bypass some checks)
npm run deploy:prod:force

# Create version tag only (without deployment)
npm run tag:create
```

### Option 2: Shell Script

```bash
# Full production deployment (requires existing tags)
npm run deploy:shell
# or directly:
./scripts/deploy-production.sh

# Check prerequisites only
./scripts/deploy-production.sh --check-only

# Create tag and deploy
npm run deploy:shell:create-tag
# or directly:
./scripts/deploy-production.sh --create-tag
```

## Features

### Zero-Downtime Deployment
- Uses PM2's `reload` command for existing processes
- Graceful process management with health checks
- Automatic fallback to restart if reload fails

### Error Handling
- Comprehensive error checking at each step
- Detailed error messages with context
- Automatic rollback on critical failures
- Prerequisites validation before deployment

### Logging
- Color-coded console output
- Step-by-step progress tracking
- Deployment timing information
- PM2 process status verification

### Version Management
- Automatic detection of latest version tags
- **Tags only** - no branch fallback (cleaner deployment process)
- Support for semantic versioning (v0.19, v0.20, v0.21, etc.)
- Automatic tag creation from package.json version
- Tag validation and sorting

## Configuration

The deployment scripts use the following configuration (can be modified in the script files):

```javascript
const CONFIG = {
  workingDir: '/home/<USER>/www/swapka',
  srcDir: 'src',
  pm2AppName: 'swapka-prod',
  pm2ConfigFile: 'ecosystem.config.js'
};
```

## Troubleshooting

### Common Issues

1. **Permission Denied**
   ```bash
   chmod +x scripts/deploy-production.sh
   ```

2. **Wrong Directory**
   - Ensure you're running from `/home/<USER>/www/swapka`
   - Update CONFIG.workingDir if needed

3. **PM2 Process Issues**
   ```bash
   pm2 status
   pm2 logs swapka-prod
   pm2 restart swapka-prod
   ```

4. **Build Failures**
   - Check Node.js version compatibility
   - Verify all environment variables are set
   - Check disk space and memory

5. **Git Issues**
   ```bash
   git status
   git fetch --all --tags
   git tag --list
   ```

6. **No Tags Found**
   ```bash
   # Create a tag from current package.json version
   npm run tag:create

   # Or create manually
   git tag v0.22
   git push origin v0.22
   ```

### Manual Recovery

If deployment fails, you can manually recover:

```bash
# Check current status
pm2 status

# View logs
pm2 logs swapka-prod

# Manual restart
pm2 restart swapka-prod

# Or full reset
pm2 delete all
pm2 start ecosystem.config.js --only swapka-prod
```

## Monitoring

After deployment, monitor the application:

```bash
# Check PM2 status
pm2 status

# View real-time logs
pm2 logs swapka-prod

# Monitor resources
pm2 monit

# Check application health
curl -I http://localhost:3000
```

## Security Considerations

- Scripts validate working directory and prerequisites
- No sensitive information is logged
- Proper error handling prevents partial deployments
- PM2 process isolation and resource limits

## Customization

To customize the deployment process:

1. **Modify Configuration**: Update the CONFIG object in the scripts
2. **Add Steps**: Insert additional steps in the main deployment function
3. **Change PM2 Behavior**: Modify the PM2 management section
4. **Add Notifications**: Integrate with Slack, email, or other notification systems

## Support

For issues or questions:
1. Check the logs: `pm2 logs swapka-prod`
2. Verify prerequisites: `npm run deploy:check`
3. Review this documentation
4. Check PM2 and Node.js versions
