#!/bin/bash

# Skript na opravu Nginx routing problémov po 413 fixe
# Aplikuje konfigu<PERSON>, <PERSON><PERSON><PERSON> upload fix ale opraví routovanie

# Farby pre výstup
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}=== Oprava Nginx routing problémov pre swapka.sk ===${NC}"
echo -e "${YELLOW}Zachovanie 413 fixu + obnovenie Next.js routing funkcionality${NC}"
echo ""

# Ko<PERSON><PERSON><PERSON>, či je skript spustený ako root
if [ "$EUID" -ne 0 ]; then
    echo -e "${RED}Tento skript musí byť spustený ako root (sudo)${NC}"
    echo "Použite: sudo ./fix-nginx-routing.sh"
    exit 1
fi

# <PERSON><PERSON><PERSON><PERSON>, či existuje nová konfigurácia
if [ ! -f "nginx-swapka-routing-fixed.conf" ]; then
    echo -e "${RED}Súbor nginx-swapka-routing-fixed.conf nebol nájdený!${NC}"
    echo "Uistite sa, že ste v správnom adresári."
    exit 1
fi

# Nájdenie aktuálnej Nginx konfigurácie pre swapka.sk
NGINX_SITES_DIR="/etc/nginx/sites-available"
NGINX_ENABLED_DIR="/etc/nginx/sites-enabled"
CURRENT_CONFIG=""

# Hľadanie existujúcej konfigurácie
for config_file in "$NGINX_SITES_DIR"/*; do
    if [ -f "$config_file" ] && grep -q "swapka.sk" "$config_file"; then
        CURRENT_CONFIG="$config_file"
        break
    fi
done

if [ -z "$CURRENT_CONFIG" ]; then
    echo -e "${RED}Existujúca konfigurácia pre swapka.sk nebola nájdená!${NC}"
    echo "Skontrolujte /etc/nginx/sites-available/"
    exit 1
else
    echo -e "${GREEN}Nájdená existujúca konfigurácia: $CURRENT_CONFIG${NC}"
fi

# Backup existujúcej konfigurácie
BACKUP_FILE="${CURRENT_CONFIG}.routing-backup.$(date +%Y%m%d_%H%M%S)"
echo -e "${YELLOW}Vytváram backup: $BACKUP_FILE${NC}"
cp "$CURRENT_CONFIG" "$BACKUP_FILE"

# Kopírovanie novej konfigurácie
echo -e "${YELLOW}Aplikujem opravenou konfiguráciu...${NC}"
cp nginx-swapka-routing-fixed.conf "$CURRENT_CONFIG"

# Testovanie konfigurácie
echo -e "${YELLOW}Testovanie Nginx konfigurácie...${NC}"
if nginx -t; then
    echo -e "${GREEN}✓ Nginx konfigurácia je v poriadku${NC}"
    
    # Reload Nginx
    echo -e "${YELLOW}Reloadujem Nginx...${NC}"
    systemctl reload nginx
    
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✓ Nginx úspešne reloadovaný${NC}"
    else
        echo -e "${RED}✗ Chyba pri reloade Nginx${NC}"
        echo -e "${YELLOW}Obnovujem backup konfiguráciu...${NC}"
        cp "$BACKUP_FILE" "$CURRENT_CONFIG"
        nginx -t && systemctl reload nginx
        exit 1
    fi
else
    echo -e "${RED}✗ Chyba v Nginx konfigurácii!${NC}"
    nginx -t
    echo ""
    echo -e "${YELLOW}Obnovujem backup konfiguráciu...${NC}"
    cp "$BACKUP_FILE" "$CURRENT_CONFIG"
    nginx -t && systemctl reload nginx
    exit 1
fi

echo ""
echo -e "${GREEN}=== Routing oprava dokončená! ===${NC}"
echo ""
echo -e "${BLUE}Kľúčové zmeny v location blokoch:${NC}"
echo -e "  ✓ location = /api/cloudinary/upload (presný match pre upload)"
echo -e "  ✓ location /_next/static/ (Next.js statické súbory)"
echo -e "  ✓ location = /favicon.ico, /robots.txt, /sitemap.xml (presné matches)"
echo -e "  ✓ location / (všeobecný catch-all pre Next.js routes)"
echo ""
echo -e "${BLUE}Zachované upload nastavenia:${NC}"
echo -e "  ✓ client_max_body_size: 20M (25M pre upload endpoint)"
echo -e "  ✓ proxy_request_buffering: off pre upload"
echo -e "  ✓ Zvýšené timeouty pre upload"
echo ""
echo -e "${YELLOW}Testovanie routing:${NC}"
echo -e "  1. Otvorte https://www.swapka.sk/hracky/h-c514910b9a5e"
echo -e "  2. Otvorte https://www.swapka.sk/profil/"
echo -e "  3. Otvorte https://www.swapka.sk/ako-to-funguje"
echo -e "  4. Skúste nahrať obrázok (upload by mal stále fungovať)"
echo ""
echo -e "${GREEN}Routing by mal byť obnovený!${NC}"
