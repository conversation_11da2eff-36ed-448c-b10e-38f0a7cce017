# Riešenie 413 "Request Entity Too Large" chyby v Nginx

## Problém
Pri nahrávaní obrázkov v Swapka aplikácii sa objavuje chyba:
- **HTTP 413 "Request Entity Too Large"** - server odmietne súbor kvôli veľkosti
- **SyntaxError: "The string did not match the expected pattern"** - klient sa pokúša parsovať HTML error stránku ako JSON

## Príčina
Nginx má defaultne veľmi nízke limity pre veľkosť requestov (zvyčajne 1MB). Keď používateľ nahráva obrázok väčší ako tento limit, Nginx vráti 413 chybu ešte predtým, ako sa request dostane k Next.js aplikácii.

## Riešenie

### 1. Automatická oprava (Odporúčané)

```bash
# Spustite skript na opravu
sudo chmod +x fix-nginx-413.sh
sudo ./fix-nginx-413.sh
```

### 2. Manuálna oprava

#### Krok 1: Backup existujúcej konfigurácie
```bash
sudo cp /etc/nginx/sites-available/swapka /etc/nginx/sites-available/swapka.backup.$(date +%Y%m%d)
```

#### Krok 2: Nahradenie konfigurácie
```bash
sudo cp nginx-swapka-fixed.conf /etc/nginx/sites-available/swapka
```

#### Krok 3: Testovanie a reload
```bash
sudo nginx -t
sudo systemctl reload nginx
```

## Kľúčové zmeny v konfigurácii

### Globálne nastavenia pre server
```nginx
# Maximálna veľkosť requestu
client_max_body_size 20M;

# Buffer nastavenia
client_body_buffer_size 128k;
client_body_timeout 60s;
client_header_timeout 60s;
send_timeout 60s;

# Proxy timeouts
proxy_connect_timeout 60s;
proxy_send_timeout 60s;
proxy_read_timeout 60s;
```

### Špeciálne nastavenia pre upload endpointy
```nginx
location /api/cloudinary/upload {
    # Zvýšené limity pre upload
    client_max_body_size 25M;
    client_body_timeout 120s;
    
    # Vypnutie proxy bufferovanie
    proxy_request_buffering off;
    
    # Zvýšené timeouts
    proxy_connect_timeout 120s;
    proxy_send_timeout 120s;
    proxy_read_timeout 120s;
    
    # Špeciálne logovanie
    access_log /var/log/nginx/swapka-upload.log;
    error_log /var/log/nginx/swapka-upload-error.log;
}
```

### Custom error handling
```nginx
# JSON odpoveď pre 413 chybu
error_page 413 @413_error;
location @413_error {
    return 413 '{"error": "Súbor je príliš veľký", "details": ["Maximálna povolená veľkosť je 20MB"], "code": 413}';
    add_header Content-Type application/json always;
}
```

## Testovanie

### 1. Kontrola Nginx konfigurácie
```bash
sudo nginx -t
```

### 2. Sledovanie upload logov
```bash
# Sledovanie upload logov v reálnom čase
sudo tail -f /var/log/nginx/swapka-upload.log
sudo tail -f /var/log/nginx/swapka-upload-error.log
```

### 3. Test nahrávania
1. Otvorte swapka.sk
2. Skúste nahrať obrázok (ideálne väčší ako 5MB)
3. Sledujte logy a browser console

## Riešenie problémov

### Ak stále dostávate 413 chybu

1. **Skontrolujte Nginx konfiguráciu:**
   ```bash
   sudo nginx -t
   grep -r "client_max_body_size" /etc/nginx/
   ```

2. **Skontrolujte, či je konfigurácia aktivovaná:**
   ```bash
   ls -la /etc/nginx/sites-enabled/
   ```

3. **Reštartujte Nginx (nie len reload):**
   ```bash
   sudo systemctl restart nginx
   ```

### Ak dostávate iné chyby

1. **Skontrolujte Nginx error logy:**
   ```bash
   sudo tail -f /var/log/nginx/error.log
   sudo tail -f /var/log/nginx/swapka-error.log
   ```

2. **Skontrolujte PM2 logy:**
   ```bash
   pm2 logs swapka-prod
   ```

3. **Skontrolujte Next.js logy:**
   ```bash
   tail -f ./logs/swapka-combined.log
   ```

## Dodatočné optimalizácie

### Pre veľmi veľké súbory (>20MB)
Ak potrebujete podporu pre ešte väčšie súbory, upravte:

```nginx
client_max_body_size 50M;  # alebo podľa potreby
```

### Pre pomalé pripojenia
Zvýšte timeouts:

```nginx
client_body_timeout 300s;  # 5 minút
proxy_read_timeout 300s;
```

## Bezpečnostné poznámky

- Veľké limity môžu byť zneužité na DoS útoky
- Odporúčame implementovať rate limiting
- Monitorujte využitie diskovej kapacity
- Pravidelne čistite temp súbory

## Súvisiace súbory

- `nginx-swapka-fixed.conf` - Nová Nginx konfigurácia
- `fix-nginx-413.sh` - Automatický inštalačný skript
- `/var/log/nginx/swapka-upload*.log` - Upload logy
- `src/app/api/cloudinary/upload/config.ts` - Next.js API konfigurácia
