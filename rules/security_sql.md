# Bezpečnostné pravidlá a SQL ochrana

Tento dokument definuje centrálne bezpečnostné pravidlá pre Swapka aplikáciu, vrátane ochrany proti SQL injection, XSS útokom a implementácie rate limiting.

## 1. SQL Injection ochrana

### 1.1 Používanie Prisma type-safe metód
**PRAVIDLO**: Vždy používajte Prisma type-safe metódy pre štandardné databázové operácie.

```typescript
// ✅ SPRÁVNE - Type-safe Prisma metódy
const user = await prisma.user.findUnique({
  where: { id: userId }
});

const toys = await prisma.toy.findMany({
  where: {
    type: toyType,
    status: 'AVAILABLE'
  }
});

// ❌ NESPRÁVNE - Priame SQL bez sanitizácie
const result = await prisma.$executeRawUnsafe(`SELECT * FROM User WHERE id = ${userId}`);
```

### 1.2 Raw SQL queries s template literal syntax
**PRAVIDLO**: Ak musíte použiť raw SQL, vždy používajte `$executeRaw` template literal syntax.

```typescript
// ✅ SPRÁVNE - Template literal syntax s parametrami
const result = await prisma.$executeRaw`
  UPDATE Toy 
  SET status = ${newStatus} 
  WHERE id = ${toyId} AND userId = ${userId}
`;

const toys = await prisma.$queryRaw`
  SELECT t.*, u.name as ownerName 
  FROM Toy t 
  JOIN User u ON t.userId = u.id 
  WHERE t.type = ${toyType}
`;

// ❌ NESPRÁVNE - $executeRawUnsafe
const result = await prisma.$executeRawUnsafe(
  `UPDATE Toy SET status = '${newStatus}' WHERE id = ${toyId}`
);
```

### 1.3 Input sanitizácia
**PRAVIDLO**: Všetky používateľské vstupy musia byť validované a sanitizované pomocou centrálnej sanitizačnej knižnice.

```typescript
import {
  sanitizeText,
  sanitizeEmail,
  sanitizeSearchQuery,
  sanitizeNumericInput,
  validateFileUpload,
  sanitizeHtml
} from '../lib/inputSanitization';

// ✅ SPRÁVNE - Použitie centrálnych sanitizačných funkcií
const searchQuery = sanitizeSearchQuery(request.nextUrl.searchParams.get('search') || '');
const email = sanitizeEmail(data.email);
const name = sanitizeText(data.name, { maxLength: 100, allowSpecialChars: false });
const description = sanitizeHtml(data.description, { maxLength: 1000 });

// ✅ SPRÁVNE - Validácia číselných vstupov
const price = sanitizeNumericInput(data.price, { min: 0, max: 10000, decimals: 2 });
const toyId = sanitizeNumericInput(params.id, { min: 1, max: 999999, decimals: 0 });

// ✅ SPRÁVNE - Validácia súborov
const fileValidation = validateFileUpload(file, {
  allowedTypes: ['image/jpeg', 'image/png', 'image/gif'],
  maxSize: 5 * 1024 * 1024, // 5MB
  maxDimensions: { width: 2048, height: 2048 }
});
```

### 1.4 Sanitizačné funkcie pre rôzne typy vstupov

**PRAVIDLO**: Používajte špecifické sanitizačné funkcie pre každý typ vstupu.

```typescript
// ✅ SPRÁVNE - Textové vstupy
const userName = sanitizeText(input.name, {
  maxLength: 50,
  allowSpecialChars: false,
  allowNumbers: true,
  trim: true
});

// ✅ SPRÁVNE - HTML obsah (popisy hračiek)
const toyDescription = sanitizeHtml(input.description, {
  maxLength: 2000,
  allowedTags: ['p', 'br', 'strong', 'em'],
  stripScripts: true
});

// ✅ SPRÁVNE - Vyhľadávacie dotazy
const searchTerm = sanitizeSearchQuery(input.search, {
  maxLength: 100,
  removeSpecialChars: true,
  preventSqlInjection: true
});

// ✅ SPRÁVNE - Geografické súradnice
const coordinates = sanitizeCoordinates(input.latitude, input.longitude, {
  validateRange: true,
  precision: 6
});
```

## 2. XSS ochrana

### 2.1 Output encoding
**PRAVIDLO**: Všetky používateľské dáta musia byť správne enkódované pred zobrazením.

```typescript
// ✅ SPRÁVNE - React automaticky enkóduje
<div>{user.name}</div>
<input value={toy.description} />

// ❌ NESPRÁVNE - Priame vloženie HTML
<div dangerouslySetInnerHTML={{__html: userInput}} />
```

### 2.2 API response sanitizácia
**PRAVIDLO**: API odpovede musia filtrovať citlivé údaje.

```typescript
// ✅ SPRÁVNE - Filtrovanie citlivých údajov
function sanitizeUserForResponse(user: any) {
  return {
    id: user.hashedUserId,
    name: user.name,
    city: user.city,
    // Vynechanie: password, firebaseUid, email (ak nie je potrebný)
  };
}
```

## 3. Rate Limiting

### 3.1 Konfigurácia limitov
**PRAVIDLO**: Rôzne typy endpointov majú rôzne limity.

| Typ endpointu | Limit | Okno | Popis | URL Patterns |
|---------------|-------|------|-------|--------------|
| Autentifikačné | 5 req/min | 60s | Login, registrácia, reset hesla | `/api/auth/`, `/api/csrf/`, `/api/users/route` (POST) |
| Všeobecné API | 100 req/min | 60s | CRUD operácie, vyhľadávanie | Všetky ostatné `/api/*` endpointy |
| Admin operácie | 20 req/min | 60s | Admin-only endpointy | `/api/admin/`, `/anonymize`, `/api/toy-types/`, `/api/cache-bust/` |
| Upload súborov | 10 req/min | 60s | Nahrávanie obrázkov | `/api/cloudinary/upload`, `/api/toys/create` |
| **Ochrana proti útokom** | **10 req/min** | **60s** | **WordPress a iné attack patterns** | **`/wp-admin`, `/phpmyadmin`, `/.env`, `/config.php`, atď.** |

### 3.2 Implementácia
**PRAVIDLO**: Rate limiting sa aplikuje automaticky cez global middleware.

```typescript
// ✅ SPRÁVNE - Automatické aplikovanie cez middleware
// Žiadne manuálne zmeny v API endpointoch nie sú potrebné
export const POST = withAuth(handleLogin); // Automaticky dostane rate limiting

// ✅ SPRÁVNE - Admin monitoring endpoint
GET /api/admin/rate-limit // Získanie štatistík
DELETE /api/admin/rate-limit?ip=******* // Vyčistenie pre IP
DELETE /api/admin/rate-limit?all=true // Vyčistenie všetkých
```

### 3.3 Environment konfigurácia
**PRAVIDLO**: Všetky rate limiting nastavenia sú konfigurovateľné cez environment variables.

```env
# Rate Limiting Configuration
RATE_LIMIT_ENABLED=true
RATE_LIMIT_AUTH_MAX=5
RATE_LIMIT_AUTH_WINDOW_MS=60000
RATE_LIMIT_GENERAL_MAX=100
RATE_LIMIT_GENERAL_WINDOW_MS=60000
RATE_LIMIT_ADMIN_MAX=20
RATE_LIMIT_ADMIN_WINDOW_MS=60000
RATE_LIMIT_UPLOAD_MAX=10
RATE_LIMIT_UPLOAD_WINDOW_MS=60000
RATE_LIMIT_ATTACK_MAX=10
RATE_LIMIT_ATTACK_WINDOW_MS=60000
RATE_LIMIT_WHITELIST_IPS="127.0.0.1,::1,*************"
```

### 3.4 HTTP Headers a monitoring
**PRAVIDLO**: Základné rate limit informácie v headers, ale bez citlivých údajov v error správach.

```http
X-RateLimit-Limit: 5
X-RateLimit-Remaining: 3
# Odstránené pre bezpečnosť:
# X-RateLimit-Reset: 2025-01-15T10:30:00.000Z
# X-RateLimit-Type: auth
# Retry-After: 60
```

**Bezpečné error správy:**
```json
{
  "error": "Príliš veľa požiadaviek",
  "message": "Príliš veľa pokusov o prihlásenie. Skúste to znovu neskôr."
}

// Pre attack patterns
{
  "error": "Príliš veľa požiadaviek",
  "message": "Príliš veľa podozrivých požiadaviek. Prístup bol dočasne obmedzený."
}
```

### 3.5 Testovanie
**PRAVIDLO**: Používajte test endpointy pre overenie funkcionality.

```bash
# General rate limit test (100 req/min)
curl http://localhost:3000/api/test/rate-limit

# Auth rate limit test (5 req/min)
curl -X POST http://localhost:3000/api/test/rate-limit

# Attack pattern test (10 req/min) - testuje WordPress attack detection
curl http://localhost:3000/wp-admin/setup-config.php
curl http://localhost:3000/wordpress/wp-admin/
curl http://localhost:3000/phpmyadmin
curl http://localhost:3000/.env
```

### 3.6 Ochrana proti útokom
**PRAVIDLO**: Automatická detekcia a blokovanie známych attack patterns.

**Detekované attack patterns:**
- **WordPress patterns**: `/wp-admin`, `/wp-login.php`, `/wp-config.php`, `/xmlrpc.php`
- **WordPress setup**: `/wp-admin/setup-config.php`, `/wordpress/wp-admin/setup-config.php`
- **CMS patterns**: `/admin.php`, `/administrator`, `/phpmyadmin`, `/phpMyAdmin`
- **Exploit patterns**: `/.env`, `/.git`, `/config.php`, `/database.php`, `/backup`
- **API abuse**: `/api/v1`, `/api/v2`, `/rest/api`, `/graphql`

**Implementácia:**
```typescript
// Automatická detekcia v middleware
if (isAttackPattern(pathname)) {
  return { type: 'attack', ...rateLimitConfig.attack };
}

// Attack patterns majú najvyššiu prioritu
export function isAttackPattern(pathname: string): boolean {
  const attackPatterns = ['/wp-admin', '/phpmyadmin', '/.env', ...];
  return attackPatterns.some(pattern =>
    pathname.toLowerCase().includes(pattern.toLowerCase())
  );
}
```

**Monitoring:**
- Všetky attack attempts sa logujú s IP adresou (sanitizovanou)
- Progresívne blokovanie pre opakované pokusy
- Admin monitoring cez `/api/admin/rate-limit`

## 4. Autentifikácia a autorizácia

### 4.1 Token verifikácia
**PRAVIDLO**: Všetky chránené endpointy musia overiť Firebase token.

```typescript
// ✅ SPRÁVNE - Použitie withAuth middleware
export const GET = withAuth(handleGetUserData);
export const POST = withAdminAuth(handleAdminOperation);

// ❌ NESPRÁVNE - Spoliehanie sa na client-side údaje
const userId = request.headers.get('x-user-id'); // NIKDY!
```

### 4.2 Role-based access control
**PRAVIDLO**: Admin operácie musia používať `withAdminAuth` middleware.

```typescript
// ✅ SPRÁVNE - Admin middleware
export const DELETE = withAdminAuth(handleDeleteUser);
export const PUT = withAdminAuth(handleUpdateToyType);
```

## 5. CSRF ochrana

### 5.1 Kritické operácie
**PRAVIDLO**: Operácie meniace stav musia mať CSRF ochranu.

```typescript
// ✅ SPRÁVNE - CSRF ochrana pre kritické operácie
export const POST = withAuthAndCsrf(handleCreateToy);
export const DELETE = withAdminAuthAndCsrf(handleDeleteUser);
```

## 6. Logging a monitoring

### 6.1 Bezpečné loggovanie
**PRAVIDLO**: Nikdy nelogujte citlivé údaje.

```typescript
// ✅ SPRÁVNE - Bezpečné loggovanie
console.log('User login attempt', {
  userId: user.hashedUserId,
  timestamp: new Date().toISOString(),
  ip: ip.substring(0, 8) + '***' // Čiastočne skryť IP
});

// ❌ NESPRÁVNE - Loggovanie citlivých údajov
console.log('User data:', user); // Môže obsahovať heslo, token, atď.
```

### 6.2 Rate limit monitoring
**PRAVIDLO**: Prekročenia rate limitov sa logujú pre monitoring.

```typescript
// Automaticky implementované v rateLimiting.ts
console.warn('Rate limit exceeded', {
  type: 'auth',
  ip: sanitizedIP,
  timestamp: new Date().toISOString()
});
```

## 7. Environment variables

### 7.1 Citlivé konfigurácie
**PRAVIDLO**: Všetky citlivé konfigurácie musia byť v environment variables.

```env
# Databáza
DATABASE_URL="mysql://..."

# Firebase
FIREBASE_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----..."

# Rate limiting
RATE_LIMIT_ENABLED=true
RATE_LIMIT_AUTH_MAX=5
```

## 8. Compliance checklist

### Pre každý nový API endpoint:
- [ ] Používa type-safe Prisma metódy alebo správnu `$executeRaw` syntax
- [ ] Má implementovaný rate limiting
- [ ] **Validuje a sanitizuje všetky vstupy pomocou centrálnej sanitizačnej knižnice**
- [ ] **Používa špecifické sanitizačné funkcie pre každý typ vstupu**
- [ ] Používa správny authentication middleware (`withAuth`, `withAdminAuth`)
- [ ] Filtruje citlivé údaje v odpovediach
- [ ] Má CSRF ochranu pre state-changing operácie
- [ ] Loguje bezpečne bez citlivých údajov
- [ ] **Validuje súbory pri nahrávaní (typ, veľkosť, obsah)**

### Pre frontend komponenty:
- [ ] Neposiela citlivé údaje v headers (napr. x-user-id)
- [ ] Používa iba Authorization Bearer token
- [ ] **Validuje používateľské vstupy na client-side pred odoslaním**
- [ ] **Sanitizuje vstupy na server-side bez ohľadu na client-side validáciu**
- [ ] Enkóduje output správne (React default)

### Pre databázové operácie:
- [ ] **Nikdy nepoužíva `$executeRawUnsafe` pre používateľské vstupy**
- [ ] **Používa `$executeRaw` template literal syntax pre raw SQL**
- [ ] **Preferuje type-safe Prisma metódy pred raw SQL**
- [ ] **Sanitizuje všetky parametre pred použitím v databázových dotazoch**

## 9. Incident response

### Pri podozrení na bezpečnostný incident:
1. **Okamžite** zakázať podozrivé IP adresy cez rate limiting whitelist
2. Skontrolovať logy pre neobvyklé vzory
3. Overiť integritu databázových dát
4. Aktualizovať Firebase tokeny ak je potrebné
5. Dokumentovať incident a zlepšenia

## 10. Pravidelné bezpečnostné kontroly

### Mesačne:
- [ ] Kontrola rate limiting štatistík
- [ ] Audit logov pre podozrivé aktivity
- [ ] Overenie environment variables
- [ ] Testovanie backup a recovery procesov

### Kvartálne:
- [ ] Penetračné testovanie API endpointov
- [ ] Audit používateľských oprávnení
- [ ] Aktualizácia bezpečnostných závislostí
- [ ] Revízia tohto dokumentu
