# Admin Sitemap Management Documentation

## Overview

The admin sitemap management interface provides administrators with comprehensive tools to monitor and manage the sitemap cache system. This feature integrates seamlessly with the existing admin interface and follows established security and UX patterns.

## Features

### 🎯 **Sitemap Statistics Dashboard**
- **Real-time URL counts**: Display current counts for toys, categories, locations, user profiles, and static pages
- **Total URL tracking**: Monitor overall sitemap size and composition
- **Last generation timestamp**: Track when sitemap was last updated
- **Cache status monitoring**: View current cache state and statistics

### 🔧 **Cache Management Controls**
- **Warm Up Cache**: Pre-generate sitemap and store in cache for faster access
- **Clear Cache**: Remove all cached sitemap data to force fresh generation
- **Refresh Statistics**: Reload current sitemap and cache statistics
- **Confirmation dialogs**: Prevent accidental cache clearing with user confirmation

### 🔒 **Security & Authentication**
- **Admin-only access**: Requires admin role authentication via withAdminAuth middleware
- **Protected API endpoints**: All management operations require valid admin tokens
- **Secure error handling**: Proper error responses without sensitive information exposure

### 📱 **User Experience**
- **Responsive design**: Works on desktop, tablet, and mobile devices
- **Loading states**: Visual feedback during operations with spinner animations
- **Toast notifications**: Success/error messages using existing toast system
- **Consistent styling**: Follows established admin interface design patterns

## User Interface

### **Navigation Integration**
The sitemap management is accessible via a new "Sitemap" tab in the admin navigation:

```
Admin Tabs: Používatelia | Hračky | Typy hračiek | Rezervačné dni | Sitemap
```

### **Dashboard Layout**
The interface is organized into two main sections:

#### **Left Panel: Sitemap Statistics**
- **Total URLs**: Large number display with breakdown by type
- **Category breakdown**: Color-coded cards for each URL type:
  - 🔵 Total URLs (blue)
  - 🟢 Toys (green) 
  - 🟣 Categories (purple)
  - 🟠 Locations (orange)
  - 🩷 User Profiles (pink)
  - ⚪ Static Pages (gray)
- **Last updated timestamp**: Slovak locale formatted date/time
- **Refresh button**: Manual statistics reload with loading state

#### **Right Panel: Cache Management**
- **Cache size indicator**: Number of items currently cached
- **Action buttons**:
  - **Warm Up Cache** (Primary blue button): Pre-generates sitemap
  - **Clear Cache** (Danger red button): Removes all cached data
- **Help text**: Explanations of each action's purpose
- **Confirmation modal**: Prevents accidental cache clearing

## API Endpoints

### **GET /api/admin/sitemap**
Retrieves current sitemap statistics and cache information.

**Authentication**: Required (Admin role)

**Response Format**:
```json
{
  "sitemap": {
    "totalUrls": 150,
    "toyUrls": 100,
    "categoryUrls": 10,
    "locationUrls": 25,
    "userProfileUrls": 8,
    "staticUrls": 7,
    "lastGenerated": "2024-01-01T12:00:00.000Z"
  },
  "cache": {
    "size": 1,
    "entries": ["sitemap-xml"]
  },
  "timestamp": "2024-01-01T12:00:00.000Z"
}
```

### **POST /api/admin/sitemap**
Executes cache management actions.

**Authentication**: Required (Admin role)

**Request Body**:
```json
{
  "action": "clear" | "warmup"
}
```

**Response Format**:
```json
{
  "message": "Cache action completed successfully",
  "timestamp": "2024-01-01T12:00:00.000Z"
}
```

**Supported Actions**:
- `clear`: Removes all cached sitemap data
- `warmup`: Generates and caches fresh sitemap data

### **DELETE /api/admin/sitemap** (Legacy)
Legacy endpoint for cache clearing (maintained for backward compatibility).

**Authentication**: Required (Admin role)

## File Structure

```
src/
├── app/
│   ├── admin/
│   │   └── sitemap/
│   │       └── page.tsx              # Main admin sitemap page
│   └── api/
│       └── admin/
│           └── sitemap/
│               └── route.ts          # Enhanced API with POST support
├── components/
│   └── admin/
│       └── AdminTabs.tsx             # Updated with sitemap tab
├── app/
│   └── globals.css                   # Added btn-danger and btn-sm styles
├── tests/
│   └── admin-sitemap-test.js         # Comprehensive test suite
└── docs/
    └── ADMIN_SITEMAP.md              # This documentation
```

## Implementation Details

### **State Management**
The admin page uses React hooks for state management:

```typescript
const [sitemapData, setSitemapData] = useState<SitemapData | null>(null);
const [isLoading, setIsLoading] = useState(true);
const [isClearing, setIsClearing] = useState(false);
const [isWarmingUp, setIsWarmingUp] = useState(false);
const [isRefreshing, setIsRefreshing] = useState(false);
const [showClearConfirm, setShowClearConfirm] = useState(false);
```

### **Authentication Flow**
1. Page checks user authentication status
2. Redirects non-admin users to home page
3. Loads sitemap statistics for authenticated admins
4. All API calls include Bearer token authentication

### **Error Handling**
- **Network errors**: Displayed via toast notifications
- **Authentication errors**: Automatic redirect to login
- **API errors**: User-friendly error messages in Slovak
- **Loading states**: Visual feedback during operations

### **Responsive Design**
- **Desktop**: Two-column grid layout
- **Tablet**: Stacked layout with full-width cards
- **Mobile**: Single column with optimized button sizes
- **Touch-friendly**: Adequate button sizes and spacing

## CSS Classes Added

```css
.btn-danger {
  background-color: #dc2626;
  color: white;
}

.btn-danger:hover {
  background-color: #b91c1c;
}

.btn-sm {
  padding: 0.25rem 0.75rem;
  font-size: 0.875rem;
}
```

## Testing

### **Automated Tests**
Run the comprehensive test suite:

```bash
cd src
node tests/admin-sitemap-test.js
```

**Test Coverage**:
- ✅ Admin API endpoint protection
- ✅ Authentication requirement verification
- ✅ Proper error responses
- ✅ Page accessibility
- ✅ API response structure validation
- ✅ Security headers verification

### **Manual Testing Checklist**

#### **Authentication & Access**
- [ ] Non-admin users cannot access /admin/sitemap
- [ ] Admin users can access the page successfully
- [ ] Page redirects properly for unauthenticated users

#### **Statistics Display**
- [ ] Statistics load correctly on page load
- [ ] All URL counts display properly
- [ ] Last updated timestamp shows correct format
- [ ] Refresh button updates statistics

#### **Cache Management**
- [ ] Warm up cache button works and shows loading state
- [ ] Clear cache shows confirmation dialog
- [ ] Cache clearing works after confirmation
- [ ] Cancel button in confirmation dialog works
- [ ] Toast notifications appear for all actions

#### **Responsive Design**
- [ ] Layout works on desktop (1920x1080)
- [ ] Layout works on tablet (768x1024)
- [ ] Layout works on mobile (375x667)
- [ ] Buttons are touch-friendly on mobile
- [ ] Text is readable on all screen sizes

#### **Error Handling**
- [ ] Network errors show appropriate messages
- [ ] API errors display user-friendly text
- [ ] Loading states work correctly
- [ ] Error recovery works after network issues

## Security Considerations

### **Authentication**
- All admin endpoints require valid Firebase admin tokens
- User role verification prevents privilege escalation
- Automatic session timeout and redirect handling

### **Input Validation**
- POST request body validation for action parameters
- Proper error responses for invalid actions
- No sensitive information in error messages

### **Rate Limiting**
- Inherits rate limiting from existing admin middleware
- Cache operations are naturally rate-limited by user interaction

## Performance Considerations

### **Caching Strategy**
- Statistics are fetched on-demand, not cached in frontend
- Cache operations provide immediate feedback
- Background cache warming doesn't block UI

### **Network Optimization**
- Minimal API calls (only on user action)
- Efficient data structures in API responses
- Proper loading states prevent multiple requests

## Troubleshooting

### **Common Issues**

**Page shows 404 or redirects:**
- Verify user has admin role
- Check authentication token validity
- Ensure admin middleware is working

**Statistics not loading:**
- Check network connectivity
- Verify API endpoint is accessible
- Check browser console for errors

**Cache operations failing:**
- Verify admin authentication
- Check server logs for errors
- Ensure database connectivity

**Toast notifications not appearing:**
- Verify react-toastify is properly configured
- Check for JavaScript errors in console
- Ensure ToastContainer is rendered in layout

### **Debug Commands**

```bash
# Test admin API access
curl -H "Authorization: Bearer YOUR_TOKEN" http://localhost:3000/api/admin/sitemap

# Test cache clearing
curl -X POST -H "Content-Type: application/json" -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"action":"clear"}' http://localhost:3000/api/admin/sitemap

# Run automated tests
node tests/admin-sitemap-test.js

# Check page accessibility
curl http://localhost:3000/admin/sitemap
```

## Future Enhancements

### **Potential Improvements**
- **Real-time updates**: WebSocket integration for live statistics
- **Historical data**: Track sitemap generation history and trends
- **Performance metrics**: Cache hit rates and generation times
- **Bulk operations**: Multiple cache management actions
- **Export functionality**: Download sitemap statistics as CSV/JSON

### **Integration Opportunities**
- **Monitoring alerts**: Integration with New Relic for cache failures
- **Automated cache warming**: Scheduled cache refresh
- **Analytics integration**: Track admin usage patterns
- **Audit logging**: Record all cache management actions
