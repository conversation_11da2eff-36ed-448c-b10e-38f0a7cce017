# Analýza aplikácie Swapka

## Základné informácie
Swapka je webová aplikácia určená na zdieľanie a požičiavanie detských hračiek medzi rodičmi. Cieľom aplikácie je podporiť udržateľnosť, znížiť plytvanie a umožniť deťom prístup k väčšiemu množstvu hračiek bez nutnosti stáleho kupovania nových.

## Technologický stack
- **Frontend**: Next.js 15, React 19, TypeScript, Tailwind CSS
- **Backend**: Next.js API Routes
- **Databáza**: MySQL
- **ORM**: Prisma
- **Autentifikácia**: Firebase Authentication (Google a email/heslo)
- **Kontajnerizácia**: Docker

## Štruktúra projektu
- `/src/app` - Next.js App Router s jednotlivými stránkami aplikácie
- `/src/components` - React komponenty
- `/src/contexts` - React kontexty (napr. AuthContext pre autentifikáciu)
- `/src/lib` - pomocné funkcie a knižnice
- `/src/prisma` - Prisma schéma a konfigurácia
- `/src/scripts` - skripty pre správu aplikácie
- `/public` - statické súbory

## Databázový model

### User (Používateľ)
- **id**: Primárny kľúč
- **firebaseUid**: ID používateľa z Firebase (voliteľné)
- **email**: Email používateľa (unikátny)
- **name**: Meno používateľa
- **password**: Heslo (prázdne pre Firebase používateľov)
- **phone**: Telefónne číslo (voliteľné)
- **city**: Mesto (voliteľné)
- **postalCode**: PSČ (voliteľné)
- **role**: Rola používateľa (USER, NONE, ADMIN)
- **status**: Stav používateľa (ACTIVE, BLOCKED)
- **createdAt**: Dátum vytvorenia
- **updatedAt**: Dátum aktualizácie
- **Vzťahy**: toys, reservations, toyReservations

### Toy (Hračka)
- **id**: Primárny kľúč
- **name**: Názov hračky
- **description**: Popis hračky
- **type**: Typ hračky (enum ToyType)
- **price**: Cena za deň požičania
- **deposit**: Záloha
- **status**: Stav hračky (AVAILABLE, RESERVED, UNAVAILABLE, DRAFT)
- **createdAt**: Dátum vytvorenia
- **updatedAt**: Dátum aktualizácie
- **userId**: ID vlastníka hračky
- **locationId**: ID lokality
- **Vzťahy**: user, location, images, reservations

### ToyImage (Obrázok hračky)
- **id**: Primárny kľúč
- **url**: URL obrázka
- **toyId**: ID hračky
- **createdAt**: Dátum vytvorenia
- **Vzťahy**: toy

### Location (Lokalita)
- **id**: Primárny kľúč
- **city**: Mesto
- **postalCode**: PSČ
- **Vzťahy**: toys

### Reservation (Rezervácia)
- **id**: Primárny kľúč
- **startDate**: Dátum začiatku rezervácie
- **endDate**: Dátum konca rezervácie
- **status**: Stav rezervácie (PENDING, CONFIRMED, ACTIVE, COMPLETED, CANCELLED)
- **createdAt**: Dátum vytvorenia
- **updatedAt**: Dátum aktualizácie
- **userId**: ID používateľa, ktorý si rezervuje
- **toyId**: ID hračky
- **ownerId**: ID vlastníka hračky
- **Vzťahy**: user, toy, owner

## Hlavné funkcie aplikácie

### 1. Autentifikácia a autorizácia
- Prihlásenie cez Google alebo email/heslo pomocou Firebase Authentication
- Roly používateľov: USER (bežný používateľ), ADMIN (administrátor), NONE (bez oprávnení)
- Prvý prihlásený používateľ dostáva automaticky rolu ADMIN
- Možnosť zablokovania používateľov administrátorom

### 2. Prehliadanie hračiek
- Zobrazenie zoznamu dostupných hračiek
- Filtrovanie podľa typu hračky a lokality
- Vyhľadávanie hračiek podľa názvu alebo popisu
- Zobrazenie detailov hračky vrátane informácií o vlastníkovi

### 3. Správa vlastných hračiek
- Zobrazenie vlastných hračiek v profile používateľa
- Pridávanie nových hračiek
- Úprava existujúcich hračiek
- Deaktivácia hračiek pri zablokovaní používateľa

### 4. Administrácia
- Správa používateľov - zobrazenie zoznamu, možnosť zablokovania/odblokovania
- Správa typov hračiek - pridávanie, odstraňovanie
- Správa lokalít - pridávanie, odstraňovanie

### 5. Domovská stránka
- Zobrazenie carousel s nedávno pridanými hračkami (max. 10)
- Sekcie "Prečo používať Swapku?" a "Ako to funguje"

## Autentifikačný proces
1. Používateľ sa prihlási cez Google alebo email/heslo pomocou Firebase
2. Aplikácia skontroluje, či používateľ existuje v databáze
3. Ak áno, načíta jeho rolu a ďalšie informácie
4. Ak nie, vytvorí nového používateľa v databáze
   - Prvý používateľ dostane rolu ADMIN, ostatní USER
5. Používateľ je presmerovaný na domovskú stránku

## API endpointy

### Používatelia
- `GET /api/users` - Získanie zoznamu používateľov (len pre admina)
- `POST /api/users` - Vytvorenie nového používateľa
- `GET /api/users/[id]` - Získanie detailu používateľa
- `PUT /api/users/[id]/status` - Zmena stavu používateľa (blokovanie/odblokovanie)
- `GET /api/users/email/[email]` - Vyhľadanie používateľa podľa emailu
- `GET /api/users/count` - Získanie počtu používateľov

### Hračky
- `GET /api/toys` - Získanie zoznamu hračiek (s možnosťou filtrovania)
- `POST /api/toys/create` - Vytvorenie novej hračky
- `GET /api/toy/[id]` - Získanie detailu hračky

### Typy hračiek a lokality
- `GET /api/toy-types` - Získanie zoznamu typov hračiek
- `POST /api/toy-types` - Vytvorenie nového typu hračky
- `DELETE /api/toy-types/[name]` - Odstránenie typu hračky
- `GET /api/locations` - Získanie zoznamu lokalít
- `GET /api/admin/locations` - Správa lokalít (len pre admina)
- `POST /api/admin/locations` - Vytvorenie novej lokality
- `DELETE /api/admin/locations/[id]` - Odstránenie lokality

## Dockerizácia
Aplikácia je pripravená na spustenie v Docker kontajneri:
- Dockerfile definuje proces buildu a spustenia aplikácie
- docker-compose.yml konfiguruje služby a prostredie
- Aplikácia je nakonfigurovaná na pripojenie k externej MySQL databáze

## Verzia aplikácie
- Aktuálna verzia: 0.1.2 (podľa package.json)
- Pri štarte aplikácie sa zobrazuje informácia o verzii v konzole

## Poznámky k implementácii
- Aplikácia používa Prisma ORM pre prácu s databázou
- Firebase Authentication pre správu používateľov
- Tailwind CSS pre štýlovanie
- Next.js App Router pre routing a API endpointy
- Responzívny dizajn pre mobilné aj desktopové zariadenia
