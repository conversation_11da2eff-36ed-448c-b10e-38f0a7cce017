# Bezpečnostná analýza API endpointov

## Úvod
Tento dokument poskytuje komplexnú bezpečnostnú analýzu všetkých API endpointov v aplikácii Swapka. Analýza sa zameriava na požiadavky autentifikácie, kontroly oprávnení, validáciu vstupov a potenciálne bezpečnostné zraniteľnosti.

## Metodológia
Analýza bola vykonaná preskúmaním zdrojového kódu, konkrétne so zameraním na:
- Definície API trás
- Autentifikačné middleware
- Kontroly oprávnení
- Validáciu vstupov
- Použitie identifikátorov (číselné ID vs. hashované ID)
- Kontroly prístupu k dátam

## Autentifikačné mechanizmy
Aplikácia používa nasledujúce autentifikačné mechanizmy:
- **Firebase Authentication**: Pre autentifikáciu použ<PERSON>ľov (email/heslo a Google OAuth)
- **JWT Tokeny**: Bearer tokeny v hlavičke Authorization
- **Verifikácia ID používateľa**: hlavička x-user-id obsahujúca číselné alebo hashované ID používateľa
- **Riadenie prístupu na základe rolí**: Role USER a ADMIN
- **CSRF ochrana**: Ochrana proti Cross-Site Request Forgery pomocou tokenov v hlavičke x-csrf-token

## API Endpointy

### Správa používateľov

#### Endpoint: /api/users
- Metóda: GET
- Prístup: Len admin
- Identifikátor: N/A
- Kontrola oprávnení: ✅ Používa withAdminAuth middleware
- Validácia vstupov: N/A
- Poznámky: Vracia zoznam všetkých používateľov s citlivými informáciami

#### Endpoint: /api/users
- Metóda: POST
- Prístup: Verejný
- Identifikátor: N/A
- Kontrola oprávnení: ❌ Nevyžaduje autentifikáciu (používa sa na registráciu používateľov)
- Validácia vstupov: ✅ Kontroluje povinné polia (email, meno, rola)
- Poznámky: Vytvára nového používateľa v databáze

#### Endpoint: /api/users/[id]
- Metóda: GET
- Prístup: Prihlásený používateľ
- Identifikátor: ID používateľa (hashované ID)
- Kontrola oprávnení: ✅ Používa withAuth middleware a kontroluje, či používateľ pristupuje k vlastnému profilu alebo je admin
- Validácia vstupov: ✅ Validuje formát ID
- Poznámky: Vracia detaily používateľa

#### Endpoint: /api/users/[id]
- Metóda: PUT
- Prístup: Prihlásený používateľ
- Identifikátor: ID používateľa (hashované ID)
- Kontrola oprávnení: ✅ Používa withAuth middleware a kontroluje, či používateľ aktualizuje vlastný profil alebo je admin
- Validácia vstupov: ✅ Validuje formát ID a vstupné dáta
- Poznámky: Aktualizuje detaily používateľa

#### Endpoint: /api/users/[id]/status
- Metóda: PUT
- Prístup: Len admin
- Identifikátor: ID používateľa (hashované ID)
- Kontrola oprávnení: ✅ Používa withAdminAuth middleware
- Validácia vstupov: ✅ Validuje, či používateľ existuje a nie je admin
- Poznámky: Aktualizuje stav používateľa (blokovanie/odblokovanie)

#### Endpoint: /api/users/public/[userHash]
- Metóda: GET
- Prístup: Verejný
- Identifikátor: ID používateľa (hashované)
- Kontrola oprávnení: N/A (vracia len verejné informácie)
- Validácia vstupov: ✅ Validuje formát hashovaného ID
- Poznámky: Vracia obmedzené verejné informácie o používateľovi (meno, mesto)

#### Endpoint: /api/users/count
- Metóda: GET
- Prístup: Verejný
- Identifikátor: N/A
- Kontrola oprávnení: N/A
- Validácia vstupov: N/A
- Poznámky: Vracia celkový počet používateľov

#### Endpoint: /api/users/email/[email]
- Metóda: GET
- Prístup: Verejný
- Identifikátor: Emailová adresa
- Kontrola oprávnení: N/A
- Validácia vstupov: ✅ Validuje formát emailu
- Poznámky: Používa sa počas autentifikácie na kontrolu, či používateľ existuje

### Správa hračiek

#### Endpoint: /api/toys
- Metóda: GET
- Prístup: Verejný (s dodatočnými dátami pre prihlásených používateľov)
- Identifikátor: N/A
- Kontrola oprávnení: ✅ Kontroluje autentifikáciu pre filtrovanie skrytých hračiek
- Validácia vstupov: ✅ Validuje parametre dotazu
- Poznámky: Vracia hračky na základe filtrov, vylučuje skryté hračky pre prihlásených používateľov

#### Endpoint: /api/toys/create
- Metóda: POST
- Prístup: Prihlásený používateľ
- Identifikátor: N/A
- Kontrola oprávnení: ✅ Používa withAuth middleware a overuje, či používateľ vytvára hračku pre seba
- Validácia vstupov: ✅ Validuje povinné polia a typ hračky
- Poznámky: Vytvára novú hračku

#### Endpoint: /api/toys/[id]
- Metóda: GET
- Prístup: Verejný
- Identifikátor: ID hračky (hashované ID)
- Kontrola oprávnení: ✅ Kontroluje autentifikáciu pre zahrnutie informácií o vlastníkovi
- Validácia vstupov: ✅ Validuje formát ID
- Poznámky: Vracia detaily hračky

#### Endpoint: /api/toy/[id]
- Metóda: GET
- Prístup: Verejný
- Identifikátor: ID hračky (hashované ID)
- Kontrola oprávnení: N/A
- Validácia vstupov: ✅ Validuje formát ID
- Poznámky: Alternatívny endpoint pre detaily hračky (legacy)

#### Endpoint: /api/toys/update
- Metóda: PUT
- Prístup: Prihlásený používateľ
- Identifikátor: ID hračky (hashované ID)
- Kontrola oprávnení: ✅ Používa withAuth middleware a overuje, či používateľ vlastní hračku
- Validácia vstupov: ✅ Validuje povinné polia a typ hračky
- Poznámky: Aktualizuje existujúcu hračku

#### Endpoint: /api/toys/delete
- Metóda: DELETE
- Prístup: Prihlásený používateľ
- Identifikátor: ID hračky (hashované ID)
- Kontrola oprávnení: ✅ Používa withAuth middleware a overuje, či používateľ vlastní hračku
- Validácia vstupov: ✅ Validuje formát ID
- Poznámky: Odstraňuje hračku

#### Endpoint: /api/toys/init
- Metóda: POST
- Prístup: Prihlásený používateľ
- Identifikátor: N/A
- Kontrola oprávnení: ✅ Používa withAuth middleware
- Validácia vstupov: ✅ Validuje povinné polia
- Poznámky: Inicializuje vzorové hračky (pravdepodobne pre testovanie)

### Skryté hračky

#### Endpoint: /api/toys/hide
- Metóda: POST
- Prístup: Prihlásený používateľ
- Identifikátor: ID hračky (hashované ID)
- Kontrola oprávnení: ✅ Používa withAuth middleware a overuje, či používateľ neskrýva vlastnú hračku
- Validácia vstupov: ✅ Validuje formát ID a kontroluje, či hračka existuje
- Poznámky: Skrýva hračku pre prihláseného používateľa

#### Endpoint: /api/toys/hide
- Metóda: DELETE
- Prístup: Prihlásený používateľ
- Identifikátor: ID hračky (hashované ID)
- Kontrola oprávnení: ✅ Používa withAuth middleware
- Validácia vstupov: ✅ Validuje formát ID
- Poznámky: Zviditeľňuje predtým skrytú hračku

### Rezervácie

#### Endpoint: /api/reservations
- Metóda: POST
- Prístup: Prihlásený používateľ
- Identifikátor: ID hračky (hashované ID)
- Kontrola oprávnení: ✅ Používa withAuth middleware
- Validácia vstupov: ✅ Validuje povinné polia a kontroluje, či hračka existuje
- Poznámky: Vytvára novú rezerváciu

#### Endpoint: /api/reservations
- Metóda: GET
- Prístup: Prihlásený používateľ
- Identifikátor: N/A
- Kontrola oprávnení: ✅ Používa withAuth middleware
- Validácia vstupov: N/A
- Poznámky: Vracia rezervácie používateľa

#### Endpoint: /api/reservations/owner
- Metóda: GET
- Prístup: Prihlásený používateľ
- Identifikátor: N/A
- Kontrola oprávnení: ✅ Používa withAuth middleware
- Validácia vstupov: N/A
- Poznámky: Vracia rezervácie pre hračky vlastnené používateľom

### Typy hračiek

#### Endpoint: /api/toy-types
- Metóda: GET
- Prístup: Len admin
- Identifikátor: N/A
- Kontrola oprávnení: ✅ Používa withAdminAuth middleware
- Validácia vstupov: N/A
- Poznámky: Vracia všetky typy hračiek s počtami

#### Endpoint: /api/toy-types
- Metóda: POST
- Prístup: Len admin
- Identifikátor: N/A
- Kontrola oprávnení: ✅ Používa withAdminAuth middleware
- Validácia vstupov: ✅ Validuje povinné polia
- Poznámky: Vytvára nový typ hračky

#### Endpoint: /api/toy-types/[name]
- Metóda: PUT
- Prístup: Len admin
- Identifikátor: Názov typu hračky
- Kontrola oprávnení: ✅ Používa withAdminAuth middleware
- Validácia vstupov: ✅ Validuje povinné polia
- Poznámky: Aktualizuje typ hračky

#### Endpoint: /api/toy-types/[name]
- Metóda: DELETE
- Prístup: Len admin
- Identifikátor: Názov typu hračky
- Kontrola oprávnení: ✅ Používa withAdminAuth middleware
- Validácia vstupov: ✅ Validuje, či typ hračky existuje
- Poznámky: Odstraňuje typ hračky

### Filtre

#### Endpoint: /api/filters
- Metóda: GET
- Prístup: Verejný
- Identifikátor: N/A
- Kontrola oprávnení: N/A
- Validácia vstupov: N/A
- Poznámky: Vracia dostupné filtre (typy hračiek a lokality)

### Admin endpointy

#### Endpoint: /api/admin/reservation-days
- Metóda: GET
- Prístup: Len admin
- Identifikátor: N/A
- Kontrola oprávnení: ✅ Používa withAdminAuth middleware
- Validácia vstupov: N/A
- Poznámky: Vracia nastavenia rezervačných dní

#### Endpoint: /api/admin/reservation-days
- Metóda: POST
- Prístup: Len admin
- Identifikátor: N/A
- Kontrola oprávnení: ✅ Používa withAdminAuth middleware
- Validácia vstupov: ✅ Validuje povinné polia
- Poznámky: Vytvára nové nastavenie rezervačných dní

#### Endpoint: /api/admin/toys/update
- Metóda: PUT
- Prístup: Len admin
- Identifikátor: ID hračky (hashované ID)
- Kontrola oprávnení: ✅ Používa withAdminAuth middleware
- Validácia vstupov: ✅ Validuje povinné polia a existenciu hračky
- Poznámky: Umožňuje adminom aktualizovať akúkoľvek hračku bez ohľadu na vlastníctvo

### Nahrávanie médií

#### Endpoint: /api/cloudinary/upload
- Metóda: POST
- Prístup: Prihlásený používateľ
- Identifikátor: N/A
- Kontrola oprávnení: ✅ Používa withAuth middleware
- Validácia vstupov: ✅ Validuje veľkosť a typ súboru
- Poznámky: Nahráva obrázok do Cloudinary

### CSRF ochrana

#### Endpoint: /api/csrf
- Metóda: GET
- Prístup: Verejný
- Identifikátor: N/A
- Kontrola oprávnení: N/A
- Validácia vstupov: N/A
- Poznámky: Generuje a vracia CSRF token, ktorý je potrebný pre všetky operácie meniace stav (POST, PUT, DELETE)

## Bezpečnostné zistenia a odporúčania

### Silné stránky
1. Konzistentné používanie autentifikačných middleware (withAuth, withAdminAuth)
2. Validácia vstupov na väčšine endpointov
3. Konzistentné používanie hashovaných ID pre všetky endpointy
4. Správne kontroly oprávnení pre vlastníctvo zdrojov
5. Obmedzené zverejňovanie dát vo verejných endpointoch
6. Implementovaná ochrana proti CSRF útokom pre všetky operácie meniace stav

### Oblasti na zlepšenie
1. Žiadne obmedzenie počtu požiadaviek (rate limiting) na žiadnom endpointe
2. Chýba explicitná sanitizácia dát pred operáciami s databázou

### Odporúčania
1. Implementovať obmedzenie počtu požiadaviek (rate limiting) na všetkých endpointoch, najmä tých súvisiacich s autentifikáciou
2. Implementovať správne overenie tokenu na strane servera pomocou Firebase Admin SDK
3. ✅ Pridať ochranu proti CSRF pre všetky operácie meniace stav (implementované)
4. Implementovať sanitizáciu vstupov pre všetky používateľské vstupy
5. Zvážiť pridanie logovania požiadaviek pre bezpečnostné monitorovanie
6. Implementovať HTTP bezpečnostné hlavičky (Content-Security-Policy, X-XSS-Protection, atď.)
