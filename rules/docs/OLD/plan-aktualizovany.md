# 🎠 Aplikácia na požičiavanie detských hračiek

Táto aplikácia je určená na zdieľanie a požičiavanie detských hračiek medzi rodičmi. Cieľom je podporiť udržateľnosť, znížiť plytvanie a umožniť deťom prístup k väčšiemu množstvu hračiek bez nutnosti stáleho kupovania nových. Používatelia môžu ponúkať svoje hračky na požičanie alebo predaj, prehliadať dostupné hračky v okolí a rezervovať si ich na určitý čas.

Aplikácia bude mať tri typy používateľov:
- **Neprihlásený návštevník** – môže prezerať hračky a filtrovať podľa lokality a textu.
- **Registrovaný používateľ** – môže spravovať vlastné <PERSON>, rezervovať cudzie a komunikovať s ostatnými používateľmi.
- **Administrátor** – má prístup ku všetkým dátam a môže spravovať číselníky, používateľov a ponuky.

Dizajn aplikácie bude moderný, intuitívny, minimalistický s jemnými farbami, prispôsobený aj mobilným zariadeniam. Dátové úložisko je založené na MySQL databáze, s možnosťou integrácie s cloudovým úložiskom na fotografie v neskoršej fáze.

---

# 📦 Plán vývoja aplikácie na požičiavanie detských hračiek

Po dokončení každej úlohy **označ zelenou fajkou** (`✅`) konkrétnu úlohu aby si mal prehľad o priebehu vývoja.


## 🎯 Ciele aplikácie
- Moderná, intuitívna a minimalistická aplikácia s jemným dizajnom a farbami.
- Backend: MySQL databáza.
- Rôzne role: neprihlásený návštevník, registrovaný používateľ, administrátor.
- Fotografie budú riešené neskôr (Cloudflare, ilustrácie zatiaľ stačia).

---

## 🧱 Funkčné celky a úlohy

### 1. 🖼️ Základný návrh UI a UX
- [✅] Navrhnúť moderné a jemné farebné rozhranie
- [✅] Vytvoriť základné rozhranie pre neprihláseného používateľa
- [✅] Navrhnúť responzívny dizajn (desktop/mobil)

---

### 2. 🗄️ Nastavenie databázy (MySQL)
- [✅] Návrh databázového modelu (hracky, pouzivatelia, rezervacie, ciselniky...)
- [✅] Implementácia databázovej štruktúry
- [✅] Prepojenie backendu s databázou

---

### 3. 🧍 Neprihlásený používateľ
- [✅] Zobraziť úvodnú stránku s výpisom hračiek na požičanie
- [ ] Možnosť zobraziť detail hračky
- [✅] Filtrovanie na základe:
  - [✅] Fulltextové vyhľadávanie
  - [✅] Lokalita (mesto, PSČ)
  - [ ] Radius (v km)

---

### 4. 🔐 Prihlásený používateľ
- [✅] Prihlásenie cez Google a email/heslo
- [✅] Implementácia Firebase Authentication
- [✅] Zobrazenie profilu používateľa po prihlásení
- [✅] Implementácia rolí používateľov (classic, none, admin)
- [✅] Automatické priradenie role admin prvému prihlásenému používateľovi
- [ ] Používateľský profil a správa hračiek:
  - [ ] Vytváranie novej hračky
  - [ ] Editovanie existujúcich hračiek
  - [ ] Polia pri vytváraní/editácii:
    - [ ] Názov
    - [ ] Ilustračný obrázok
    - [ ] Typ hračky (číselník)
    - [ ] Doba výpožičky (číselník)
    - [ ] Možnosť prenájmu / predaja
    - [ ] Lokalita (mesto, PSČ, Slovensko, radius)
    - [ ] Cena
    - [ ] Záloha (ak ide o prenájom)
    - [ ] Stav (voľné, požičané, rezervované)
    - [ ] Dĺžka požičania / rezervácie
  - [ ] Možnosť mať záujem o hračku iného používateľa
  - [ ] Potvrdenie rezervácie a dátumu

---

### 5. 🛠️ Rola administrátora
- [✅] Prístup ku všetkému ako používateľ
- [✅] Implementácia administrátorského rozhrania
- [✅] Zobrazenie zoznamu používateľov
- [ ] Správa číselníkov (typy hračiek, doby výpožičky)
- [ ] Správa používateľov (zmena rolí)
- [ ] Správa hračiek

---

### 6. ☁️ Správa fotografií (budúca fáza)
- [ ] Implementácia nahrávania fotografií cez Cloudflare alebo alternatívu
- [ ] Napojenie na frontend

---

## 🔄 Aktuálny stav projektu (aktualizované 2023-07-10)

### Dokončené úlohy
- Základný návrh UI a UX
- Nastavenie databázy MySQL
- Prepojenie backendu s databázou pomocou Prisma ORM
- Základné zobrazenie hračiek pre neprihláseného používateľa
- Filtrovanie hračiek podľa textu a lokality
- Implementácia Firebase Authentication (Google a email/heslo)
- Implementácia používateľských rolí (classic, none, admin)
- Zobrazenie profilu používateľa po prihlásení
- Vytvorenie administrátorského rozhrania so zoznamom používateľov

### Aktuálne problémy a ich riešenia
- Problém s inicializáciou Prisma klienta - riešenie: spustiť `npx prisma generate` v adresári src
- Problémy s importom modulov - riešenie: používať relatívne cesty namiesto aliasov (@/lib/db)
- Konfigurácia Firebase - riešenie: nastaviť správne hodnoty v .env súbore a povoliť poskytovateľov autentifikácie v Firebase konzole

### Ďalšie kroky
- Implementácia detailu hračky
- Implementácia správy hračiek pre prihlásených používateľov
- Rozšírenie administrátorského rozhrania o správu číselníkov a používateľov
- Implementácia rezervácie hračiek
