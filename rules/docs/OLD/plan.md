# 🎠 Aplikácia na požičiavanie detských hračiek

Táto aplikácia je určená na zdieľanie a požičiavanie detských hračiek medzi rodičmi. Cieľom je podporiť udržateľnosť, znížiť plytvanie a umožniť deťom prístup k väčšiemu množstvu hračiek bez nutnosti stáleho kupovania nových. Používatelia môžu ponúkať svoje hračky na požičanie alebo predaj, prehliadať dostupné hračky v okolí a rezervovať si ich na určitý čas.

Aplikácia bude mať tri typy používateľov:
- **Neprihlásený návštevník** – môže prezerať hračky a filtrovať podľa lokality a textu.
- **Registrovaný používateľ** – môže spravovať vlastné <PERSON>, rezervovať cudzie a komunikovať s ostatnými používateľmi.
- **Administrátor** – má prístup ku všetkým dátam a môže spravovať číselníky, používateľov a ponuky.

Dizajn aplikácie bude moderný, intuitívny, minimalistický s jemnými farbami, prispôsobený aj mobilným zariadeniam. Dátové úložisko bude založené na MySQL databáze, s možnosťou integrácie s cloudovým úložiskom na fotografie v neskoršej fáze.

---

# 📦 Plán vývoja aplikácie na požičiavanie detských hračiek

Po dokončení každej úlohy **označ zelenou fajkou** (`✅`) konkrétnu úlohu aby si mal prehľad o priebehu vývoja.


## 🎯 Ciele aplikácie
- Moderná, intuitívna a minimalistická aplikácia s jemným dizajnom a farbami.
- Backend: MySQL databáza.
- Rôzne role: neprihlásený návštevník, registrovaný používateľ, administrátor.
- Fotografie budú riešené neskôr (Cloudflare, ilustrácie zatiaľ stačia).

---

## 🧱 Funkčné celky a úlohy

### 1. 🖼️ Základný návrh UI a UX
- [✅] Navrhnúť moderné a jemné farebné rozhranie
- [✅] Vytvoriť základné rozhranie pre neprihláseného používateľa
- [✅] Navrhnúť responzívny dizajn (desktop/mobil)

---

### 2. 🗄️ Nastavenie databázy (MySQL)
- [ ] Návrh databázového modelu (hracky, pouzivatelia, rezervacie, ciselniky...)
- [ ] Implementácia databázovej štruktúry
- [ ] Prepojenie backendu s databázou

---

### 3. 🧍 Neprihlásený používateľ
- [✅] Zobraziť úvodnú stránku s výpisom hračiek na požičanie
- [ ] Možnosť zobraziť detail hračky
- [✅] Filtrovanie na základe:
  - [✅] Fulltextové vyhľadávanie
  - [✅] Lokalita (mesto, PSČ)
  - [ ] Radius (v km)

---

### 4. 🔐 Prihlásený používateľ
- [ ] Prihlásenie cez Google / Facebook
- [ ] Možnosť neskôr pridať vlastné prihlásenie
- [ ] Používateľský profil a správa hračiek:
  - [ ] Vytváranie novej hračky
  - [ ] Editovanie existujúcich hračiek
  - [ ] Polia pri vytváraní/editácii:
    - [ ] Názov
    - [ ] Ilustračný obrázok
    - [ ] Typ hračky (číselník)
    - [ ] Doba výpožičky (číselník)
    - [ ] Možnosť prenájmu / predaja
    - [ ] Lokalita (mesto, PSČ, Slovensko, radius)
    - [ ] Cena
    - [ ] Záloha (ak ide o prenájom)
    - [ ] Stav (voľné, požičané, rezervované)
    - [ ] Dĺžka požičania / rezervácie
  - [ ] Možnosť mať záujem o hračku iného používateľa
  - [ ] Potvrdenie rezervácie a dátumu

---

### 5. 🛠️ Rola administrátora
- [ ] Prístup ku všetkému ako používateľ
- [ ] Správa číselníkov (typy hračiek, doby výpožičky)
- [ ] Správa používateľov
- [ ] Správa hračiek

---

### 6. ☁️ Správa fotografií (budúca fáza)
- [ ] Implementácia nahrávania fotografií cez Cloudflare alebo alternatívu
- [ ] Napojenie na frontend

---
