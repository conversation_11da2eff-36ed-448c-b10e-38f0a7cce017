# PM2 Deployment a Process Management pre Swapka

Tento dokument popisuje, ako používať PM2 pre správu procesov aplikácie Swapka v produkčnom aj vývojovom prostredí.

## Prehľad

PM2 (Process Manager 2) je p<PERSON><PERSON><PERSON><PERSON> process manager pre Node.js ap<PERSON><PERSON><PERSON>, k<PERSON><PERSON> poskytuje:
- **Cluster mode**: Využitie všetkých CPU jadier
- **Auto-restart**: <PERSON><PERSON><PERSON> reštartovanie pri zlyhaní
- **Load balancing**: Rozloženie záťaže medzi procesy
- **Monitoring**: Real-time sledovanie výkonu
- **Log management**: Centralizované logovanie
- **Zero-downtime deployment**: Nasadenie bez výpadku

## Prečo PM2 pre Swapka

PM2 je odporúčané riešenie pre produkčné nasadenie Next.js aplik<PERSON>cií, pretože:
1. **Stabilita**: <PERSON><PERSON><PERSON> reštartovanie pri chybách alebo vysokom využití pamäte
2. **Výkon**: Cluster mode využíva všetky dostupné CPU jadrá
3. **Monitoring**: Integrované sledovanie výkonu a New Relic podpora
4. **Jednoduchost**: Jednoduchá správa cez PM2 príkazy
5. **Integrácia**: Bezproblémová integrácia s existujúcou štruktúrou projektu

## Inštalácia a nastavenie

### Požiadavky

- Node.js 20.x alebo novší
- npm 10.x alebo novší
- Prístup k MySQL databáze
- Správne nastavené environment variables v `src/.env`

### Krok 1: Inštalácia PM2

```bash
# Globálna inštalácia PM2
npm install -g pm2

# Overenie inštalácie
pm2 --version
```

### Krok 2: Príprava aplikácie

```bash
# Inštalácia závislostí
cd src
npm install

# Generovanie Prisma klienta
npx prisma generate

# Build pre produkciu (len pre produkčné nasadenie)
npm run build
```

### Krok 3: Vytvorenie logs adresára

```bash
# Z root adresára projektu
mkdir -p logs
```

## Konfigurácie aplikácií

Ecosystem konfigurácia definuje tri typy aplikácií:

### 🚀 **swapka-prod** (Produkcia)
- **Účel**: Produkčné nasadenie s maximálnym výkonom
- **Instances**: `max` (využíva všetky CPU jadrá)
- **Exec mode**: `cluster` (load balancing)
- **Memory limit**: 1GB s auto-restart
- **Watch**: Vypnuté (pre výkon)
- **Monitoring**: Integrované PM2 monitoring a logovanie

### 🛠️ **swapka-dev** (Vývoj)
- **Účel**: Vývojové prostredie s file watching
- **Instances**: 1 (single instance)
- **Exec mode**: `fork`
- **Memory limit**: 500MB s auto-restart
- **Watch**: Sleduje zmeny v `app/`, `components/`, `lib/`
- **Auto-restart**: Pri zmenách súborov

### 🔨 **swapka-build** (Build)
- **Účel**: Spustenie build procesu
- **Instances**: 1
- **Auto-restart**: Vypnuté
- **Použitie**: CI/CD alebo manuálne buildy

## Základné PM2 príkazy

### Spustenie aplikácií

```bash
# Produkčná aplikácia (odporúčané pre produkciu)
pm2 start ecosystem.config.js --only swapka-prod

# Vývojová aplikácia (s file watching)
pm2 start ecosystem.config.js --only swapka-dev

# Všetky aplikácie naraz
pm2 start ecosystem.config.js

# S konkrétnym prostredím
pm2 start ecosystem.config.js --env production
```

### Správa aplikácií

```bash
# Zobrazenie bežiacich aplikácií
pm2 list
pm2 status

# Zastavenie aplikácií
pm2 stop swapka-prod
pm2 stop swapka-dev
pm2 stop all

# Reštartovanie aplikácií
pm2 restart swapka-prod
pm2 restart swapka-dev
pm2 restart all

# Zero-downtime restart (len pre cluster mode)
pm2 reload swapka-prod

# Odstránenie aplikácií z PM2
pm2 delete swapka-prod
pm2 delete swapka-dev
pm2 delete all
```

### Monitoring a logy

```bash
# Zobrazenie logov v reálnom čase
pm2 logs
pm2 logs swapka-prod
pm2 logs swapka-dev

# Monitoring dashboard
pm2 monit

# Detailné informácie o aplikácii
pm2 show swapka-prod
pm2 describe swapka-prod

# Vymazanie logov
pm2 flush
```

## Produkčné nasadenie

### Prvotné nastavenie

1. **Príprava aplikácie:**
   ```bash
   cd src
   npm install
   npx prisma generate
   npm run build
   ```

2. **Spustenie produkčnej aplikácie:**
   ```bash
   pm2 start ecosystem.config.js --only swapka-prod --env production
   ```

3. **Uloženie PM2 konfigurácie:**
   ```bash
   pm2 save
   pm2 startup
   ```

### Automatizované nastavenie

Použite pripravený setup skript:

```bash
# Automatické nastavenie pre produkciu
./pm2-setup.sh

# Pre vývojové prostredie
./pm2-setup.sh --mode development

# Preskočenie build kroku
./pm2-setup.sh --skip-build

# Bez nastavenia auto-startup
./pm2-setup.sh --no-startup
```

### Zero-downtime deployment

```bash
# Aktualizácia kódu a reload aplikácie
git pull origin main
cd src
npm install
npx prisma generate
npm run build  # Vytvorí .next/standalone/server.js
pm2 reload swapka-prod
```

**Poznámka**: Build krok je povinný pre standalone režim, pretože vytvára optimalizovaný server súbor.

## Vývojový workflow

### Spustenie development servera

```bash
# Spustenie s file watching
pm2 start ecosystem.config.js --only swapka-dev

# Sledovanie logov
pm2 logs swapka-dev
```

### File watching

Development konfigurácia sleduje zmeny v:
- `./src/app` - Next.js stránky a API routes
- `./src/components` - React komponenty
- `./src/lib` - Pomocné funkcie a knižnice

Ignoruje:
- `node_modules` - NPM balíčky
- `.next` - Next.js build súbory
- `logs` - Log súbory
- `*.log` - Všetky log súbory
- `.git` - Git súbory
- `prisma/migrations` - Prisma migrácie

## Environment variables

Všetky konfigurácie automaticky načítavaju environment variables z `./src/.env`:

### Databáza
- `DATABASE_URL` - MySQL connection string

### Firebase Authentication
- `NEXT_PUBLIC_FIREBASE_API_KEY`
- `NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN`
- `NEXT_PUBLIC_FIREBASE_PROJECT_ID`
- `FIREBASE_PROJECT_ID`
- `FIREBASE_CLIENT_EMAIL`
- `FIREBASE_PRIVATE_KEY`

### Cloudinary (obrázky)
- `CLOUDINARY_CLOUD_NAME`
- `CLOUDINARY_API_KEY`
- `CLOUDINARY_API_SECRET`

### Bezpečnosť
- `CSRF_SECRET`
- `CSP_DYNAMIC_ENABLED`

## Logovanie

Logy sú uložené v `./logs` adresári:

### Produkčné logy
- `swapka-combined.log` - Kombinované output a error logy
- `swapka-out.log` - Štandardný output
- `swapka-error.log` - Error logy

### Vývojové logy
- `swapka-dev-combined.log` - Kombinované logy
- `swapka-dev-out.log` - Štandardný output
- `swapka-dev-error.log` - Error logy

### Log rotácia

```bash
# Inštalácia PM2 log rotate modulu
pm2 install pm2-logrotate

# Konfigurácia rotácie
pm2 set pm2-logrotate:max_size 10M
pm2 set pm2-logrotate:retain 30
pm2 set pm2-logrotate:compress true
```

## Správa pamäte

### Automatické reštartovanie
- **Produkcia**: Restart pri prekročení 1GB pamäte
- **Vývoj**: Restart pri prekročení 500MB pamäte

### Monitoring pamäte
```bash
# Zobrazenie využitia pamäte
pm2 monit

# Detailné informácie o procese
pm2 show swapka-prod
```

## Clustering a škálovanie

### Automatické clustering
Produkčná konfigurácia používa `instances: 'max'` pre využitie všetkých CPU jadier.

### Manuálne škálovanie
```bash
# Škálovanie na konkrétny počet inštancií
pm2 scale swapka-prod 4

# Pridanie inštancií
pm2 scale swapka-prod +2

# Odobratie inštancií
pm2 scale swapka-prod -1
```

## Integrácia so systémovými službami

### Auto-start pri reštarte systému

```bash
# Generovanie startup skriptu
pm2 startup

# Uloženie aktuálnej PM2 konfigurácie
pm2 save

# Odstránenie auto-startup
pm2 unstartup
```

Toto zabezpečí, že sa Swapka aplikácia automaticky spustí pri reštarte servera.

## Integrácia s existujúcou štruktúrou projektu

### Štruktúra súborov

PM2 konfigurácia rešpektuje existujúcu štruktúru projektu:

```
swapka/
├── ecosystem.config.js          # PM2 konfigurácia
├── pm2-setup.sh               # Automatizovaný setup skript
├── PM2_GUIDE.md               # Detailný návod (anglicky)
├── PM2_QUICK_REFERENCE.md     # Rýchla referencia
├── logs/                      # PM2 logy
│   ├── swapka-combined.log
│   ├── swapka-out.log
│   └── swapka-error.log
└── src/                       # Hlavná aplikácia
    ├── package.json           # NPM závislosti
    ├── .env                   # Environment variables
    ├── next.config.ts         # Next.js konfigurácia
    ├── prisma/                # Prisma schéma
    ├── app/                   # Next.js App Router
    ├── components/            # React komponenty
    ├── lib/                   # Pomocné funkcie
    └── docs/                  # Dokumentácia
        └── pm2-deployment.md  # Tento dokument
```

### Integrácia s Prisma

PM2 automaticky spúšťa aplikáciu z `src` adresára, kde sa nachádza Prisma konfigurácia:

```bash
# PM2 automaticky používa správny working directory
cwd: './src'

# Pred spustením aplikácie vždy spustite:
cd src
npx prisma generate
```

### Integrácia s Next.js

PM2 konfigurácia využíva standalone režim Next.js pre optimálnu produkčnú výkonnosť:

```json
{
  "scripts": {
    "dev": "node scripts/version-info.js && next dev --turbopack",
    "build": "node scripts/version-info.js && next build",
    "start": "node scripts/version-info.js && node .next/standalone/server.js"
  }
}
```

PM2 spúšťa:
- **Produkcia**: `node .next/standalone/server.js` (standalone server pre optimálnu výkonnosť)
- **Vývoj**: `npm run dev` (zobrazí verziu a spustí dev server s Turbopack)

**Poznámka**: Standalone režim vytvára samostatný server súbor, ktorý obsahuje všetky potrebné závislosti a poskytuje lepšiu výkonnosť v produkčnom prostredí.

## Riešenie problémov

### Časté problémy

#### 1. **Aplikácia sa nespustí**

**Príznaky:**
- PM2 zobrazuje status "errored" alebo "stopped"
- Chybové hlášky v logoch

**Riešenie:**
```bash
# Skontrolujte logy
pm2 logs swapka-prod --lines 50

# Overenie závislostí
cd src && npm install
npx prisma generate

# Overenie environment variables
cat src/.env

# Manuálne testovanie
cd src && npm run build && npm start
```

#### 2. **Databázové pripojenie zlyhá**

**Príznaky:**
- Chyby typu "Can't connect to MySQL server"
- Prisma connection errors

**Riešenie:**
```bash
# Testovanie databázového pripojenia
cd src
npx prisma db pull

# Overenie DATABASE_URL v .env
echo $DATABASE_URL

# Testovanie pripojenia
mysql -h db.dw155.nameserver.sk -u swapka_d5das4651 -p swapka_5336164
```

#### 3. **Vysoké využitie pamäte**

**Príznaky:**
- Časté reštarty kvôli memory limit
- Pomalý výkon aplikácie

**Riešenie:**
```bash
# Monitoring pamäte
pm2 monit

# Zvýšenie memory limitu
pm2 stop swapka-prod
# Upravte max_memory_restart v ecosystem.config.js
pm2 start ecosystem.config.js --only swapka-prod

# Optimalizácia Next.js
cd src
npm run build -- --analyze
```

#### 4. **File watching nefunguje**

**Príznaky:**
- Zmeny v kóde sa neprejavujú automaticky
- Development server sa nereštartuje

**Riešenie:**
```bash
# Overenie watch konfigurácie
pm2 describe swapka-dev

# Reštart s watch
pm2 stop swapka-dev
pm2 start ecosystem.config.js --only swapka-dev

# Manuálny restart
pm2 restart swapka-dev
```

#### 5. **Port už používaný**

**Príznaky:**
- Chyba "EADDRINUSE: address already in use :::3000"

**Riešenie:**
```bash
# Zastavenie všetkých PM2 procesov
pm2 stop all

# Nájdenie procesu na porte 3000
lsof -i :3000
kill -9 <PID>

# Alebo zmena portu v environment variables
echo "PORT=3001" >> src/.env
```

### Debug príkazy

```bash
# Detailné informácie o procese
pm2 describe swapka-prod

# Zobrazenie posledných 100 riadkov logov
pm2 logs swapka-prod --lines 100

# Real-time monitoring
pm2 monit

# Testovanie aplikácie
curl http://localhost:3000
curl http://localhost:3000/api/health

# Overenie PM2 daemon
pm2 ping

# Reset restart počítadla
pm2 reset swapka-prod
```

## Výkonnostné optimalizácie

### Cluster mode nastavenia

```bash
# Optimálny počet inštancií (zvyčajne počet CPU jadier)
pm2 scale swapka-prod max

# Pre servery s obmedzenou pamäťou
pm2 scale swapka-prod 2
```

### Memory management

```bash
# Monitoring memory usage
pm2 monit

# Nastavenie memory limitu podľa servera
# V ecosystem.config.js upravte max_memory_restart
```

### Log management

```bash
# Automatická rotácia logov
pm2 install pm2-logrotate

# Konfigurácia rotácie
pm2 set pm2-logrotate:max_size 10M
pm2 set pm2-logrotate:retain 30
pm2 set pm2-logrotate:compress true
pm2 set pm2-logrotate:dateFormat YYYY-MM-DD_HH-mm-ss
```

## Monitoring a alerting

### PM2 Plus (voliteľné)

```bash
# Pripojenie k PM2 Plus pre pokročilé monitoring
pm2 link <secret_key> <public_key>

# Odpojenie
pm2 unlink
```

### Základné health check

```bash
# Jednoduchý health check skript
#!/bin/bash
response=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:3000)
if [ $response != "200" ]; then
    echo "Application is down, restarting..."
    pm2 restart swapka-prod
fi
```

## Bezpečnostné úvahy

### Process isolation

PM2 spúšťa procesy s obmedzenými oprávneniami:

```bash
# Spustenie pod špecifickým používateľom
pm2 start ecosystem.config.js --uid swapka --gid swapka
```

### Environment variables

```bash
# Zabezpečenie .env súboru
chmod 600 src/.env
chown swapka:swapka src/.env
```

### Log security

```bash
# Zabezpečenie log súborov
chmod 640 logs/*.log
chown swapka:swapka logs/*.log
```

## Zálohovanie a obnova

### Zálohovanie PM2 konfigurácie

```bash
# Uloženie aktuálnej konfigurácie
pm2 save

# Zálohovanie ecosystem súboru
cp ecosystem.config.js ecosystem.config.js.backup
```

### Obnova po výpadku

```bash
# Obnovenie uložených procesov
pm2 resurrect

# Alebo úplné reštartovanie
pm2 start ecosystem.config.js --only swapka-prod
```

## Ďalšie zdroje

### Dokumentácia
- [PM2 oficiálna dokumentácia](https://pm2.keymetrics.io/docs/)
- [Next.js deployment dokumentácia](https://nextjs.org/docs/deployment)
- [PM2_GUIDE.md](../../PM2_GUIDE.md) - Detailný návod v angličtine
- [PM2_QUICK_REFERENCE.md](../../PM2_QUICK_REFERENCE.md) - Rýchla referencia

### Súvisiace dokumenty
- [FIREBASE_ADMIN_SETUP.md](./FIREBASE_ADMIN_SETUP.md) - Nastavenie Firebase autentifikácie
- [SITEMAP.md](./SITEMAP.md) - Konfigurácia sitemap generátora
- [TOKEN_EXPIRATION.md](./TOKEN_EXPIRATION.md) - Nastavenie platnosti tokenov

### Podpora
Pre technickú podporu alebo otázky týkajúce sa PM2 konfigurácie kontaktujte vývojový tím.
