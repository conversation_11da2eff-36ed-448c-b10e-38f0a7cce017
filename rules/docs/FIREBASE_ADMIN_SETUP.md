# Nastavenie Firebase Admin SDK pre server-side autentifikáciu

Tento dokument popisuje, ako nastaviť Firebase Admin SDK pre server-side overenie tokenov v aplikácii Swapka.

## Prečo je to dôležité

Predchádzajúca implementácia sa spoliehala len na hlavičky na strane klienta bez overenia tokenu na strane servera, čo predstavovalo bezpečnostné riziko. Nová implementácia overuje platnosť tokenov pomocou Firebase Admin SDK, čím zvyšuje bezpečnosť aplikácie.

## Kroky pre nastavenie

### 1. Získanie Service Account Credentials

Pre správne fungovanie Firebase Admin SDK potrebujete nastaviť service account credentials:

1. Prejdite do Firebase konzoly: https://console.firebase.google.com/
2. Vyberte váš projekt (swapka-b9171)
3. Prejdite do Project settings > Service accounts
4. Kliknite na "Generate new private key"
5. Stiahnite a uložte JSON súbor s credentials

### 2. Nastavenie credentials

Máte dve možnosti:

#### A) Použiť Google Application Default Credentials (ADC)

```bash
export GOOGLE_APPLICATION_CREDENTIALS="/path/to/service-account.json"
```

Pridajte tento príkaz do vášho startup skriptu alebo ho spustite pred spustením aplikácie.

#### B) Nastaviť credentials priamo v .env súbore

Otvorte stiahnutý JSON súbor a pridajte nasledujúce premenné do `.env` súboru:

```
FIREBASE_PROJECT_ID=project-id-z-json-suboru
FIREBASE_CLIENT_EMAIL=client-email-z-json-suboru
FIREBASE_PRIVATE_KEY="private-key-z-json-suboru"
```

Poznámka: `FIREBASE_PRIVATE_KEY` musí byť v úvodzovkách a zachovať všetky znaky nového riadku (`\\n`).

### 3. Overenie nastavenia

Môžete spustiť pomocný skript pre overenie nastavenia:

```bash
node src/scripts/setup-firebase-admin.js
```

## Ako to funguje

1. Keď klient pošle požiadavku na API endpoint, musí zahrnúť:
   - Bearer token v hlavičke Authorization
   - ID používateľa v hlavičke x-user-id (pre spätnú kompatibilitu)

2. Server overí token pomocou Firebase Admin SDK
3. Server overí, či ID používateľa v hlavičke zodpovedá používateľovi v databáze
4. Server overí, či email v tokene zodpovedá emailu používateľa v databáze

Ak ktorýkoľvek z týchto krokov zlyhá, požiadavka je zamietnutá s chybou 401 Unauthorized.

## Testovanie

Pre overenie, že server-side autentifikácia funguje správne, môžete vykonať nasledujúce testy:

1. Prihláste sa do aplikácie a získajte platný token
2. Použite tento token pre prístup k chránenému API endpointu
3. Skúste použiť neplatný alebo expirovaný token - mali by ste dostať chybu 401
4. Skúste použiť platný token s nesprávnym ID používateľa - mali by ste dostať chybu 401

## Riešenie problémov

Ak sa vyskytne chyba pri inicializácii Firebase Admin SDK:

1. Skontrolujte, či sú správne nastavené credentials
2. Skontrolujte, či má service account správne oprávnenia v Firebase projekte
3. Skontrolujte logy servera pre detailnejšie informácie o chybe

## Ďalšie zdroje

- [Firebase Admin SDK dokumentácia](https://firebase.google.com/docs/admin/setup)
- [Firebase Authentication dokumentácia](https://firebase.google.com/docs/auth)
