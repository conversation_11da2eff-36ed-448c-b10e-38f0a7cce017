# Sitemap Implementation Documentation

## Overview

The Swapka application includes an automated sitemap generator that dynamically creates XML sitemaps based on current database content. This implementation follows SEO best practices and includes proper caching mechanisms.

## Features

### 🎯 **Dynamic Content Generation**
- **Toy Listings**: All publicly available toys with hashed IDs
- **Categories**: All toy types from the ToyType table
- **Locations**: All cities with active toys
- **User Profiles**: Public user profiles with active toys
- **Static Pages**: Important static pages (home, about, contact, etc.)

### 🌐 **SEO-Friendly Root Location**
- **Accessible at `/sitemap.xml`**: Standard location expected by search engines
- **Proper URL routing**: Direct root-level access without API prefix
- **Search engine optimization**: Follows SEO best practices for sitemap placement

### 🚀 **Performance & Caching**
- **In-memory caching**: 15-minute cache in production, 5-minute in development
- **Cache invalidation**: Manual cache clearing via admin endpoint
- **URL limits**: Maximum 50,000 URLs per sitemap (Google recommendation)
- **Performance monitoring**: Generation time tracking and logging

### 🔒 **Security & Privacy**
- **Public content only**: Excludes draft toys and blocked users
- **Hashed IDs**: Uses secure hashed IDs for toys and user profiles
- **Input validation**: URL validation and sanitization
- **Admin protection**: Admin endpoints require proper authentication

## API Endpoints

### Public Endpoints

#### `GET /api/sitemap.xml`
Returns the XML sitemap with all publicly accessible URLs.

**Response Headers:**
- `Content-Type: application/xml; charset=utf-8`
- `Cache-Control: public, max-age=900, s-maxage=900`
- `X-Robots-Tag: noindex`
- `X-Generation-Time: {time}ms`
- `Last-Modified: {timestamp}`

#### `GET /robots.txt`
Returns robots.txt with environment-aware sitemap URL.

**Features:**
- Dynamic sitemap URL based on environment
- Proper disallow rules for private areas
- Allow rules for public content

### Admin Endpoints

#### `GET /api/admin/sitemap`
Returns sitemap statistics and cache information.

**Requires:** Admin authentication

**Response:**
```json
{
  "sitemap": {
    "totalUrls": 150,
    "toyUrls": 100,
    "categoryUrls": 10,
    "locationUrls": 25,
    "userProfileUrls": 8,
    "staticUrls": 7
  },
  "cache": {
    "size": 1,
    "entries": ["sitemap-xml"]
  },
  "timestamp": "2024-01-01T12:00:00.000Z"
}
```

#### `DELETE /api/admin/sitemap`
Clears the sitemap cache to force regeneration.

**Requires:** Admin authentication

## File Structure

```
src/
├── app/
│   ├── sitemap.xml/
│   │   └── route.ts              # Main sitemap endpoint (root-level)
│   ├── robots.txt/
│   │   └── route.ts              # Dynamic robots.txt
│   └── api/
│       └── admin/
│           └── sitemap/
│               └── route.ts      # Admin management endpoint
├── lib/
│   ├── sitemapGenerator.ts       # Core sitemap generation logic
│   └── sitemapCache.ts          # Caching mechanism
├── tests/
│   └── sitemap-test.js          # Comprehensive test suite
└── docs/
    └── SITEMAP.md               # This documentation
```

## URL Structure

### Toy Listings
- Format: `/hracky/{hashedId}`
- Example: `/hracky/h-fcd6dfd6675c`
- Priority: 0.7
- Change frequency: weekly

### Categories
- Format: `/hracky?type={categoryName}`
- Example: `/hracky?type=EDUCATIONAL`
- Priority: 0.8
- Change frequency: daily

### Locations
- Format: `/hracky?location={cityName}`
- Example: `/hracky?location=Bratislava`
- Priority: 0.6
- Change frequency: daily

### User Profiles
- Format: `/profil/{userHash}`
- Example: `/profil/h-abc123def456`
- Priority: 0.6
- Change frequency: weekly

### Static Pages
- Home (`/`): Priority 1.0, daily
- Toys listing (`/hracky`): Priority 0.9, daily
- How it works (`/ako-to-funguje`): Priority 0.6, monthly
- Contact (`/kontakt`): Priority 0.5, monthly
- Terms (`/podmienky-pouzitia`): Priority 0.4, yearly
- Privacy (`/ochrana-osobnych-udajov`): Priority 0.4, yearly
- Cookies (`/cookies`): Priority 0.3, yearly

## Configuration

### Environment Variables
- `NEXT_PUBLIC_BASE_URL`: Production base URL (default: https://swapka.sk)
- `NODE_ENV`: Environment mode (affects caching duration)

### Cache Settings
- **Production**: 15-minute cache TTL
- **Development**: 5-minute cache TTL
- **Cleanup**: Expired entries cleaned every 30 minutes

## Testing

Run the comprehensive test suite:

```bash
cd src
node tests/sitemap-test.js
```

**Test Coverage:**
- ✅ XML sitemap generation and validation
- ✅ Robots.txt generation with correct URLs
- ✅ Caching functionality
- ✅ Admin endpoint protection
- ✅ URL validation and sanitization
- ✅ Performance monitoring

## SEO Best Practices

### ✅ **Implemented**
- Valid XML sitemap format
- Proper priority and change frequency values
- Last modification dates
- URL validation and sanitization
- Robots.txt with sitemap reference
- Cache headers for performance
- No indexing of sitemap itself

### 📋 **Compliance**
- Google Sitemap Protocol 0.9
- Maximum 50,000 URLs per sitemap
- UTF-8 encoding
- Proper XML escaping
- Valid URL formats

## Monitoring & Maintenance

### Performance Metrics
- Generation time tracking
- Cache hit/miss statistics
- URL count monitoring
- Error logging

### Cache Management
- Automatic cache expiration
- Manual cache invalidation via admin
- Cache statistics monitoring

### Error Handling
- Graceful degradation on database errors
- Proper error responses
- Comprehensive logging

## Future Enhancements

### Potential Improvements
- **Sitemap Index**: Support for multiple sitemaps if URL count exceeds limits
- **Image Sitemaps**: Include toy images in dedicated image sitemap
- **Internationalization**: Multi-language sitemap support
- **Compression**: Gzip compression for large sitemaps
- **Analytics**: Integration with Google Search Console

### Monitoring Integration
- **Performance**: PM2 process monitoring
- **Alerts**: Application health monitoring
- **Metrics**: Sitemap generation frequency and performance

## Troubleshooting

### Common Issues

**Sitemap not updating:**
- Check cache expiration (15 minutes in production)
- Clear cache via admin endpoint
- Verify database connectivity

**Missing URLs:**
- Check toy/user status (must be ACTIVE/AVAILABLE)
- Verify URL validation logic
- Check URL limits (50,000 max)

**Performance issues:**
- Monitor generation time
- Check database query performance
- Verify cache functionality

### Debug Commands

```bash
# Test sitemap generation
curl http://localhost:3000/sitemap.xml

# Check robots.txt
curl http://localhost:3000/robots.txt

# Run test suite
node tests/sitemap-test.js

# Check headers
curl -I http://localhost:3000/sitemap.xml
```
