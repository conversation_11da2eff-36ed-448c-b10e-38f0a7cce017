# Dokumentácia Swapka aplikácie

Tento adresár obsahuje technickú dokumentáciu pre aplikáciu Swapka.

## 📚 Dostupná dokumentácia

### 🚀 **Deployment a Process Management**
- **[pm2-deployment.md](pm2-deployment.md)** - Kompletný návod na PM2 deployment a správu procesov
  - Produkčné nasadenie s PM2
  - Vývojový workflow
  - Troubleshooting a optimalizácie
  - Integrácia s existujúcou štruktúrou projektu

### 🔐 **Autentifikácia a bezpečnosť**
- **[FIREBASE_ADMIN_SETUP.md](FIREBASE_ADMIN_SETUP.md)** - Nastavenie Firebase Admin SDK
  - Server-side overenie tokenov
  - Service account credentials
  - Bezpečnostné implementácie

- **[TOKEN_EXPIRATION.md](TOKEN_EXPIRATION.md)** - Nastavenie platnosti Firebase tokenov
  - Konfigurácia 7-dňovej platnosti tokenov
  - Bezpečnostn<PERSON>
  - Testovanie a troubleshooting

### 🗺️ **SEO a sitemap**
- **[SITEMAP.md](SITEMAP.md)** - Implementácia automatického sitemap generátora
  - Dynamické generovanie XML sitemap
  - Caching a performance optimalizácie
  - SEO best practices

- **[ADMIN_SITEMAP.md](ADMIN_SITEMAP.md)** - Admin rozhranie pre správu sitemap
  - Admin endpointy pre sitemap management
  - Monitoring a cache management
  - Bezpečnostné aspekty

### 📁 **Archívne dokumenty**
- **[OLD/](OLD/)** - Staršie verzie dokumentácie a plánov
  - Pôvodné plány vývoja
  - Historické poznámky
  - Endpoint dokumentácia

## 🔗 **Súvisiace dokumenty**

### Root adresár
- **[../../PM2_GUIDE.md](../../PM2_GUIDE.md)** - Detailný PM2 návod (anglicky)
- **[../../PM2_QUICK_REFERENCE.md](../../PM2_QUICK_REFERENCE.md)** - Rýchla PM2 referencia
- **[../../README.md](../../README.md)** - Hlavná dokumentácia projektu
- **[../README.md](../README.md)** - Dokumentácia src adresára

### Konfiguračné súbory
- **[../../ecosystem.config.js](../../ecosystem.config.js)** - PM2 konfigurácia
- **[../../docker-compose.yml](../../docker-compose.yml)** - Docker Compose konfigurácia
- **[../../Dockerfile](../../Dockerfile)** - Docker build konfigurácia

## 🛠️ **Technické informácie**

### Technologický stack
- **Frontend**: Next.js 15, React 19, TypeScript, Tailwind CSS
- **Backend**: Next.js API Routes
- **Databáza**: MySQL s Prisma ORM
- **Autentifikácia**: Firebase Authentication
- **Process Management**: PM2
- **Kontajnerizácia**: Docker

### Štruktúra projektu
```
src/
├── app/                    # Next.js App Router
│   ├── api/               # API endpointy
│   ├── hracky/            # Stránka s hračkami
│   ├── ako-to-funguje/    # Informačná stránka
│   └── kontakt/           # Kontaktná stránka
├── components/            # React komponenty
├── contexts/              # React kontexty
├── lib/                   # Pomocné funkcie a knižnice
├── prisma/                # Prisma schéma a migrácie
├── docs/                  # Dokumentácia (tento adresár)
└── tests/                 # Testy
```

## 📋 **Návody pre rôzne scenáre**

### Pre vývojárov
1. **Prvé spustenie**: Začnite s [../README.md](../README.md)
2. **Produkčné nasadenie**: Pokračujte s [pm2-deployment.md](pm2-deployment.md)
3. **Firebase setup**: Nastavte autentifikáciu podľa [FIREBASE_ADMIN_SETUP.md](FIREBASE_ADMIN_SETUP.md)

### Pre administrátorov
1. **Process management**: [pm2-deployment.md](pm2-deployment.md)
2. **Monitoring**: Sekcia o New Relic v PM2 dokumentácii
3. **Sitemap správa**: [ADMIN_SITEMAP.md](ADMIN_SITEMAP.md)

### Pre DevOps
1. **Deployment pipeline**: [pm2-deployment.md](pm2-deployment.md)
2. **Docker setup**: [../../DOCKER.md](../../DOCKER.md)
3. **Environment variables**: Sekcie o .env v jednotlivých dokumentoch

## 🆘 **Podpora a troubleshooting**

### Časté problémy
- **PM2 problémy**: [pm2-deployment.md#riešenie-problémov](pm2-deployment.md#riešenie-problémov)
- **Firebase autentifikácia**: [FIREBASE_ADMIN_SETUP.md#riešenie-problémov](FIREBASE_ADMIN_SETUP.md#riešenie-problémov)
- **Prisma problémy**: [../README.md#riešenie-problémov](../README.md#riešenie-problémov)

### Kontakt
Pre technickú podporu alebo otázky týkajúce sa dokumentácie kontaktujte vývojový tím.

---

**Posledná aktualizácia:** December 2024  
**Verzia aplikácie:** 0.9.001
