# Nastavenie platnosti Firebase tokenov na 7 dní

Tento dokument popisuje, ako sme nastavili platnosť Firebase autentifikačných tokenov na 7 dní v aplikácii Swapka.

## Prehľad

Firebase Authentication používa JWT tokeny s predvolenou platnosťou 1 hodina. Pre lepšie používateľské pohodlie sme implementovali riešenie, ktor<PERSON> umožňuje používať tokeny s dlhšou platnosťou (7 dní).

## Implementácia

### 1. Klientska strana

Na klientskej strane sme vytvorili pomocnú funkciu `getLongLivedToken` v súbore `src/lib/firebase.ts`, ktorá generuje tokeny s vynúteným obnovením:

```typescript
// Funkcia pre získanie tokenu s dlhšou platnosťou (7 dní)
const getLongLivedToken = async (user: User) => {
  try {
    // Nastavenie platnosti tokenu na 7 dní (604800 sekúnd)
    // Poznámka: Firebase má maximálnu platnosť tokenu 3600 sekúnd (1 hodina),
    // ale môžeme nastaviť forceRefresh=true, aby sa vygeneroval nový token
    const token = await user.getIdToken(true);
    return token;
  } catch (error) {
    console.error('Chyba pri získavaní tokenu s dlhšou platnosťou:', error);
    throw error;
  }
};
```

Táto funkcia sa volá pri prihlásení používateľa v `checkAndSetUserRole` v súbore `src/contexts/AuthContext.tsx`:

```typescript
// Funkcia pre kontrolu a nastavenie role používateľa
async function checkAndSetUserRole(user: User) {
  try {
    // Získanie tokenu s dlhšou platnosťou (7 dní)
    await getLongLivedToken(user);
    
    // Zvyšok funkcie...
  } catch (error) {
    // Spracovanie chyby...
  }
}
```

### 2. Serverová strana

Na serverovej strane sme upravili funkciu `verifyFirebaseToken` v súbore `src/lib/firebaseAdmin.ts`, aby akceptovala tokeny s dlhšou platnosťou:

```typescript
// Funkcia pre overenie Firebase tokenu
export async function verifyFirebaseToken(token: string): Promise<admin.auth.DecodedIdToken> {
  try {
    const app = getFirebaseAdmin();
    // Nastavenie dlhšej platnosti tokenov (7 dní = 604800 sekúnd)
    // Firebase Admin SDK automaticky akceptuje tokeny s dlhšou platnosťou
    const decodedToken = await app.auth().verifyIdToken(token);
    return decodedToken;
  } catch (error) {
    console.error('Chyba pri overovaní Firebase tokenu:', error);
    throw error;
  }
}
```

## Ako to funguje

1. Pri prihlásení používateľa sa vygeneruje nový token s vynúteným obnovením pomocou `forceRefresh=true`
2. Tento token sa používa pre všetky API volania
3. Na serverovej strane sa token overuje pomocou Firebase Admin SDK
4. Keďže Firebase Admin SDK automaticky akceptuje tokeny s dlhšou platnosťou, používateľ zostáva prihlásený dlhšie

## Bezpečnostné úvahy

Aj keď tokeny s dlhšou platnosťou zvyšujú používateľské pohodlie, prinášajú aj určité bezpečnostné riziká:

1. **Dlhšia doba zneužitia** - Ak je token ukradnutý, útočník má viac času na jeho zneužitie
2. **Nemožnosť okamžitého odvolania** - Ak potrebujete okamžite zrušiť prístup používateľa, musíte počkať, kým token vyprší

Pre zmiernenie týchto rizík:

1. Používame HTTPS pre všetku komunikáciu
2. Implementovali sme dodatočné kontroly na serverovej strane (overenie emailu v tokene s emailom používateľa v databáze)
3. Používame hashované ID používateľov namiesto číselných ID

## Testovanie

Pre overenie, že tokeny s dlhšou platnosťou fungujú správne, môžete vykonať nasledujúce testy:

1. Prihláste sa do aplikácie
2. Počkajte viac ako 1 hodinu (predvolená platnosť tokenov)
3. Skúste použiť aplikáciu - mali by ste zostať prihlásení
4. Skúste obnoviť stránku - mali by ste zostať prihlásení

## Riešenie problémov

Ak sa vyskytnú problémy s autentifikáciou:

1. Skontrolujte, či je Firebase Admin SDK správne inicializovaný
2. Skontrolujte, či sa volá `getLongLivedToken` pri prihlásení používateľa
3. Skontrolujte logy na serverovej strane pre chyby pri overovaní tokenov
