# PM2 Configuration Guide for Swapka Application

This guide explains how to use PM2 to manage the Swapka Next.js application using the provided `ecosystem.config.js` configuration. The application uses Next.js standalone mode for optimal production performance.

## Prerequisites

1. **Install PM2 globally:**
   ```bash
   npm install -g pm2
   ```

2. **Install application dependencies:**
   ```bash
   cd src
   npm install
   npx prisma generate
   ```

3. **Create logs directory:**
   ```bash
   mkdir -p logs
   ```

## Available Configurations

The ecosystem configuration provides three different app configurations:

- **swapka-prod**: Production environment with clustering (uses standalone server)
- **swapka-dev**: Development environment with file watching
- **swapka-build**: Build process configuration

**Important**: The production configuration uses Next.js standalone mode (`node .next/standalone/server.js`) instead of `npm start` for better performance and reduced memory footprint.

## Basic PM2 Commands

### Starting Applications

```bash
# Start production application (recommended for production)
pm2 start ecosystem.config.js --only swapka-prod

# Start development application (with file watching)
pm2 start ecosystem.config.js --only swapka-dev

# Start all applications defined in ecosystem
pm2 start ecosystem.config.js

# Start with specific environment
pm2 start ecosystem.config.js --env production
```

### Managing Applications

```bash
# View running applications
pm2 list
pm2 status

# Stop applications
pm2 stop swapka-prod
pm2 stop swapka-dev
pm2 stop all

# Restart applications
pm2 restart swapka-prod
pm2 restart swapka-dev
pm2 restart all

# Reload applications (zero-downtime restart)
pm2 reload swapka-prod

# Delete applications from PM2
pm2 delete swapka-prod
pm2 delete swapka-dev
pm2 delete all
```

### Monitoring and Logs

```bash
# View real-time logs
pm2 logs
pm2 logs swapka-prod
pm2 logs swapka-dev

# View application monitoring dashboard
pm2 monit

# View detailed application information
pm2 show swapka-prod
pm2 describe swapka-prod

# Flush logs
pm2 flush
```

## Production Deployment

### Initial Setup

1. **Prepare the application:**
   ```bash
   cd src
   npm install
   npx prisma generate
   npm run build
   ```

2. **Start production application:**
   ```bash
   pm2 start ecosystem.config.js --only swapka-prod --env production
   ```

3. **Save PM2 configuration:**
   ```bash
   pm2 save
   pm2 startup
   ```

### Zero-Downtime Deployment

```bash
# Update code and reload application
git pull origin main
cd src
npm install
npx prisma generate
npm run build
pm2 reload swapka-prod
```

## Development Workflow

### Starting Development Server

```bash
# Start development server with file watching
pm2 start ecosystem.config.js --only swapka-dev

# View logs in real-time
pm2 logs swapka-dev
```

### File Watching

The development configuration watches these directories:
- `./src/app`
- `./src/components` 
- `./src/lib`

And ignores:
- `node_modules`
- `.next`
- `logs`
- `*.log`
- `.git`
- `prisma/migrations`

## Environment Variables

Environment variables are automatically loaded from `./src/.env`. The configuration includes:

- Database connection (MySQL)
- Firebase authentication
- Cloudinary image storage
- New Relic monitoring
- CSRF protection
- CSP (Content Security Policy)

## Logging

Logs are stored in the `./logs` directory:

- `swapka-combined.log`: Combined output and error logs (production)
- `swapka-out.log`: Standard output logs (production)
- `swapka-error.log`: Error logs (production)
- `swapka-dev-*.log`: Development logs

## Memory Management

- **Production**: Restarts if memory usage exceeds 1GB
- **Development**: Restarts if memory usage exceeds 500MB

## Clustering

Production configuration uses `instances: 'max'` to utilize all available CPU cores for optimal performance.

## Health Monitoring

The configuration includes:
- Automatic restart on crashes
- Memory limit monitoring
- Minimum uptime requirements
- Grace period for health checks

## Troubleshooting

### Common Issues

1. **Port already in use:**
   ```bash
   pm2 stop all
   # Or change PORT in environment variables
   ```

2. **Application won't start:**
   ```bash
   # Check logs
   pm2 logs swapka-prod
   
   # Verify dependencies
   cd src && npm install
   npx prisma generate
   ```

3. **Database connection issues:**
   ```bash
   # Verify DATABASE_URL in src/.env
   # Test database connection
   cd src && npx prisma db pull
   ```

### Performance Monitoring

```bash
# View resource usage
pm2 monit

# View process information
pm2 show swapka-prod

# Check application health
pm2 ping
```

## Advanced Configuration

### Custom Environment Variables

You can override environment variables when starting:

```bash
pm2 start ecosystem.config.js --only swapka-prod --env production --update-env
```

### Process Scaling

```bash
# Scale to specific number of instances
pm2 scale swapka-prod 4

# Scale up/down
pm2 scale swapka-prod +2
pm2 scale swapka-prod -1
```

### Log Rotation

```bash
# Install PM2 log rotate module
pm2 install pm2-logrotate

# Configure log rotation
pm2 set pm2-logrotate:max_size 10M
pm2 set pm2-logrotate:retain 30
pm2 set pm2-logrotate:compress true
```

## Integration with System Services

### Auto-start on System Boot

```bash
# Generate startup script
pm2 startup

# Save current PM2 configuration
pm2 save

# To remove auto-startup
pm2 unstartup
```

This ensures your Swapka application automatically starts when the server reboots.
