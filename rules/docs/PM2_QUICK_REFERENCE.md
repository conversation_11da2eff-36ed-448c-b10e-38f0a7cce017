# PM2 Quick Reference for Swapka

## Quick Start

```bash
# 1. Install PM2 globally
npm install -g pm2

# 2. Setup and start production
./pm2-setup.sh

# 3. Or start manually
pm2 start ecosystem.config.js --only swapka-prod
```

## Essential Commands

### Application Management
```bash
# Start applications
pm2 start ecosystem.config.js --only swapka-prod    # Production
pm2 start ecosystem.config.js --only swapka-dev     # Development

# Stop/Restart
pm2 stop swapka-prod
pm2 restart swapka-prod
pm2 reload swapka-prod      # Zero-downtime restart

# Delete
pm2 delete swapka-prod
```

### Monitoring
```bash
pm2 list                    # List all processes
pm2 logs                    # View logs (all apps)
pm2 logs swapka-prod        # View logs (specific app)
pm2 monit                   # Real-time monitoring dashboard
pm2 show swapka-prod        # Detailed app info
```

### Process Management
```bash
pm2 save                    # Save current process list
pm2 startup                 # Generate startup script
pm2 unstartup               # Remove startup script
pm2 resurrect               # Restore saved processes
```

## Application Configurations

| Name | Purpose | Instances | Watch | Memory Limit |
|------|---------|-----------|-------|--------------|
| `swapka-prod` | Production | max (all CPUs) | No | 1GB |
| `swapka-dev` | Development | 1 | Yes | 500MB |
| `swapka-build` | Build only | 1 | No | - |

## Environment Variables

All configurations load from `./src/.env`:
- Database, Firebase, Cloudinary credentials
- CSRF and CSP settings

## Log Files

```
./logs/
├── swapka-combined.log      # Production combined logs
├── swapka-out.log           # Production stdout
├── swapka-error.log         # Production stderr
├── swapka-dev-combined.log  # Development combined logs
├── swapka-dev-out.log       # Development stdout
└── swapka-dev-error.log     # Development stderr
```

## Common Workflows

### Development
```bash
pm2 start ecosystem.config.js --only swapka-dev
pm2 logs swapka-dev
# Edit files - app auto-restarts on changes
```

### Production Deployment
```bash
git pull origin main
cd src && npm install && npx prisma generate && npm run build
pm2 reload swapka-prod
```

### Troubleshooting
```bash
pm2 logs swapka-prod --lines 100    # View last 100 log lines
pm2 describe swapka-prod            # Detailed process info
pm2 restart swapka-prod             # Force restart
```

## Performance Tuning

### Scale Instances
```bash
pm2 scale swapka-prod 4      # Scale to 4 instances
pm2 scale swapka-prod +2     # Add 2 instances
pm2 scale swapka-prod -1     # Remove 1 instance
```

### Memory Management
```bash
pm2 set pm2-logrotate:max_size 10M    # Rotate logs at 10MB
pm2 set pm2-logrotate:retain 30       # Keep 30 rotated files
```

## Emergency Commands

```bash
pm2 kill                     # Kill PM2 daemon (stops all apps)
pm2 flush                    # Clear all logs
pm2 reset swapka-prod        # Reset restart counter
pm2 stop all                 # Stop all applications
```

## Health Checks

```bash
pm2 ping                     # Check PM2 daemon health
pm2 list                     # Check app status
curl http://localhost:3000   # Test application response
```
