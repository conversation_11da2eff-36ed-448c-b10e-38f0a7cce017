# Cache Prevention Strategy

Tento dokument popisuje komplexnú stratégiu pre predchádzanie problémom s cache-ovaním, najmä na iPhone/mobile Safari zariadeniach.

## Problém

Po nasadení novej verzie aplikácie sa používatelia na iPhone zariadeniach stretávajú s problémami, kde prehliadač nemôže stiahnuť špecifické JavaScript chunks, pretože sú agresívne cache-ované staré verzie súborov.

## Riešenie

### 1. Next.js Konfigur<PERSON> (`next.config.ts`)

**Cache Busting:**
- Generovanie unikátneho build ID s timestamp
- Pridanie build ID do názvov chunks
- Optimalizácia webpack konfigurácie pre lepšie cache invalidation

**HTTP Headers:**
- Statické assets: `public, max-age=31536000, immutable`
- JavaScript chunks: `public, max-age=3600, must-revalidate`
- <PERSON>TM<PERSON> stránky: `no-cache, no-store, must-revalidate, max-age=0`

### 2. Globálny Middleware (`middleware.ts`)

**Funkcie:**
- Nastavenie cache headers pre všetky požiadavky
- Špecifické pravidlá pre rôzne typy súborov
- Mobile Safari špecifické headers
- Security headers

**Cache Stratégie:**
- **JavaScript chunks**: 5 minút s povinnou validáciou
- **API routes**: Žiadne cache-ovanie
- **HTML stránky**: Žiadne cache-ovanie

### 3. Cache Header Utilities (`lib/cacheHeaders.ts`)

**Predefinované konfigurácie:**
- `NO_CACHE`: Pre HTML stránky a kritické súbory
- `JS_CHUNKS`: Pre JavaScript chunks s krátkou cache
- `ASSETS`: Pre CSS a ostatné assets
- `STATIC_IMMUTABLE`: Pre verzované statické súbory
- `API`: Pre API odpovede

**Utility funkcie:**
- Generovanie Cache-Control headers
- ETag generovanie
- Mobile Safari špecifické headers
- Cache busting parametre

### 4. Cache Invalidation API (`api/cache-bust/route.ts`)

**Endpointy:**
- `GET`: Získanie informácií o cache a verzii
- `POST`: Vynútenie cache invalidation
- `DELETE`: Vymazanie všetkých cache

**Akcie:**
- `invalidate-all`: Invalidácia všetkých cache
- `invalidate-js`: Invalidácia len JavaScript chunks
- `invalidate-assets`: Invalidácia statických assets
- `get-version`: Získanie informácií o verzii

### 5. Client-side Cache Manager (`components/CacheManager.tsx`)

**Funkcie:**
- Automatická detekcia zmien verzie
- Vymazanie browser cache
- Mobile Safari špecifické cache clearing
- Development mode debugging

**Hook `useCacheManager`:**
- Online/offline detekcia
- Cache busting utility
- Local cache management

### 6. Version Management (`scripts/version-info.js`)

**Funkcie:**
- Generovanie unikátneho build ID
- Uloženie version info do JSON súboru
- Runtime prístup k version informáciám

## Implementácia

### Automatické Cache Busting

```javascript
// Build ID generovanie
generateBuildId: async () => {
  return `build-${Date.now()}-${Math.random().toString(36).substring(2, 15)}`;
}
```

### Mobile Safari Headers

```javascript
// Špecifické headers pre Mobile Safari
'X-UA-Compatible': 'IE=edge',
'X-Mobile-Cache-Control': 'no-cache',
'X-Webkit-Cache-Control': 'no-cache',
```

### Client-side Detection

```javascript
// Detekcia zmeny verzie
const storedVersion = localStorage.getItem('app-version');
if (storedVersion && storedVersion !== currentVersion) {
  await clearBrowserCaches();
}
```

## Testovanie

### Development Mode

V development móde je dostupný CacheManager komponent, ktorý umožňuje:
- Zobrazenie aktuálnej verzie a build ID
- Manuálne vymazanie cache
- Debugging cache problémov

### Production Testing

1. **Nasadenie novej verzie**
2. **Kontrola cache headers** pomocou Developer Tools
3. **Testovanie na rôznych zariadeniach**, najmä iPhone/Safari
4. **Overenie cache invalidation** po nasadení

## Monitoring

### Headers pre Debugging

- `X-Build-Version`: Aktuálne build ID
- `X-Cache-Timestamp`: Timestamp cache operácie
- `X-Cache-Invalidation`: Timestamp invalidácie

### Logy

```javascript
console.log('App version changed, clearing caches...');
console.log('Build ID changed, clearing caches...');
```

## Riešenie Problémov

### iPhone Cache Issues

1. **Kontrola headers** v Network tab
2. **Vymazanie Safari cache** manuálne
3. **Použitie cache-bust parametrov**
4. **Reštart Safari** aplikácie

### Debugging

1. **Zapnutie CacheManager** v development mode
2. **Kontrola version-info.json** súboru
3. **Monitoring API calls** v Network tab
4. **Kontrola localStorage** pre version tracking

## Bezpečnosť

Všetky cache prevention mechanizmy sú navrhnuté s ohľadom na bezpečnosť:
- Admin-only prístup k cache invalidation API
- Bezpečné headers pre všetky responses
- Ochrana proti cache poisoning útokom

## Záver

Táto komplexná stratégia zabezpečuje, že používatelia vždy dostanú najnovšiu verziu aplikácie, najmä na problematických zariadeniach ako iPhone s Mobile Safari prehliadačom.
