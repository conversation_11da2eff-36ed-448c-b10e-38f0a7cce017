# 🎠 Swapka - Aplikácia na požičiavanie detských hračiek

Swapka je webová aplikácia určená na zdieľanie a požičiavanie detských hračiek medzi rodičmi. Cieľom je podporiť udržateľnosť, znížiť plytvanie a umožniť deťom prístup k väčšiemu množstvu hračiek bez nutnosti stáleho kupovania nových. Používatelia môžu ponúkať svoje hračky na požičanie alebo predaj, prehliadať dostupné hračky v okolí a rezervovať si ich na určitý čas.

## 👥 Typy používateľov

Aplikácia má tri typy používateľov:
- **Neprihlásený návštevník** – môže prezerať hračky a filtrovať podľa lokality a textu
- **Registrovaný používateľ** – môže spravovať vlastn<PERSON>, rezervovať cudzie a komunikovať s ostatnými používateľmi
- **Administrátor** – má prístup ku všetkým dátam a môže spravovať číselníky, používateľov a ponuky

## 🛠️ Technológie

- **Frontend**: Next.js 15, React 19, TypeScript, Tailwind CSS
- **Backend**: Next.js API Routes
- **Databáza**: MySQL s Prisma ORM
- **Autentifikácia**: Firebase Authentication (Google, email/heslo)
- **Fotografie**: Cloudinary
- **Deployment**: PM2 process manager

Dizajn aplikácie je moderný, intuitívny a minimalistický s jemnými farbami, plne prispôsobený mobilným zariadeniam.

---

# 📦 Plán vývoja aplikácie na požičiavanie detských hračiek

**Aktuálna verzia**: 0.13  
**Posledná aktualizácia plánu**: December 2024

Po dokončení každej úlohy **označ zelenou fajkou** (`✅`) konkrétnu úlohu aby si mal prehľad o priebehu vývoja.

---

## 🧱 Funkčné celky a úlohy

### 1. 🖼️ Základný návrh UI a UX
- [✅] Navrhnúť moderné a jemné farebné rozhranie
- [✅] Vytvoriť základné rozhranie pre neprihláseného používateľa
- [✅] Navrhnúť responzívny dizajn (desktop/mobil)
- [✅] Implementovať mobilný header s optimalizovaným layoutom
- [✅] Vytvoriť minimalistický a kompaktný dizajn

---

### 2. 🗄️ Nastavenie databázy (MySQL)
- [✅] Návrh databázového modelu (hračky, používatelia, rezervácie, číselníky...)
- [✅] Implementácia databázovej štruktúry pomocou Prisma
- [✅] Prepojenie backendu s databázou
- [✅] Migrácie databázy a schéma aktualizácie
- [✅] Optimalizácia databázových dotazov

---

### 3. 🧍 Neprihlásený používateľ
- [✅] Zobraziť úvodnú stránku s výpisom hračiek na požičanie
- [✅] Možnosť zobraziť detail hračky
- [✅] Filtrovanie na základe:
  - [✅] Fulltextové vyhľadávanie
  - [✅] Lokalita (mesto, PSČ)
  - [✅] Multiselect filtre s predvolenými hodnotami
  - [✅] Stav hračky (dostupné, rezervované)
  - [ ] Radius (v km) - plánované vylepšenie

---

### 4. 🔐 Autentifikácia a používateľský profil
- [✅] Implementácia Firebase Authentication
- [✅] Prihlásenie cez Google
- [✅] Prihlásenie cez email/heslo
- [✅] Registrácia nových používateľov
- [✅] Zobrazenie profilu používateľa po prihlásení
- [✅] Implementácia rolí používateľov (USER, ADMIN)
- [✅] Automatické priradenie role ADMIN prvému prihlásenému používateľovi
- [✅] Bezpečnostné middleware (withAuth, withAdminAuth)
- [✅] Token verification a session management
- [✅] Konsolidovaný login systém s menu-based prístupom

---

### 5. 🧸 Správa hračiek pre registrovaných používateľov
- [✅] Vytváranie novej hračky
- [✅] Editovanie existujúcich hračiek
- [✅] Mazanie hračiek
- [✅] Polia pri vytváraní/editácii:
  - [✅] Názov a popis
  - [✅] Typ hračky (číselník)
  - [✅] Cena a záloha
  - [✅] Stav (dostupné, rezervované, nedostupné)
  - [✅] Maximálny počet dní rezervácie
  - [✅] Fotografie (až 5 obrázkov)
- [✅] Dashboard pre správu vlastných hračiek
- [✅] Bezpečné nahrávanie fotografií do Cloudinary
- [✅] Automatické mazanie fotografií pri odstránení hračky

---

### 6. 📅 Rezervačný systém
- [✅] Vytvorenie rezervácie hračky
- [✅] Systém čakacieho zoznamu (queue management)
- [✅] Schvaľovanie/zamietanie rezervácií majiteľom
- [✅] Zrušenie rezervácie používateľom
- [✅] Zobrazenie kontaktných údajov po schválení rezervácie
- [✅] Dashboard rezervácií pre používateľov
- [✅] Notifikácie o stave rezervácie
- [✅] Automatická aktualizácia stavu hračiek

---

### 7. 🛠️ Administrátorské rozhranie
- [✅] Prístup ku všetkému ako používateľ
- [✅] Implementácia administrátorského rozhrania
- [✅] Zobrazenie zoznamu používateľov
- [✅] Správa hračiek (všetky hračky vrátane DRAFT)
- [✅] Správa rezervácií
- [✅] Správa stavov hračiek
- [✅] Administrátorské API endpointy
- [✅] Bezpečnostné kontroly pre admin operácie
- [ ] Správa číselníkov (typy hračiek, doby výpožičky)
- [ ] Zmena rolí používateľov
- [ ] Systémové logy a audit trail

---

### 8. 📸 Správa fotografií
- [✅] Implementácia nahrávania fotografií cez Cloudinary
- [✅] Napojenie na frontend s drag & drop rozhraním
- [✅] Bezpečné hashovanie názvov súborov
- [✅] Automatické mazanie fotografií pri odstránení hračky
- [✅] Optimalizácia obrázkov (HEIC konverzia, Sharp processing)
- [✅] Validácia formátov a veľkosti súborov
- [✅] Mazanie fotografií pri úprave hračiek
- [✅] Podpora až 5 obrázkov na hračku

---

### 9. 🔒 Bezpečnosť a ochrana dát
- [✅] Input sanitization pre všetky používateľské vstupy
- [✅] SQL injection protection pomocou Prisma
- [✅] Rate limiting pre API endpointy
- [✅] CSRF protection
- [✅] HTTP security headers
- [✅] Bezpečné token handling
- [✅] Filtrovanie citlivých dát v API odpovediach
- [✅] Secure filename hashing pre fotografie
- [✅] SSL/TLS certificate validation
- [ ] Automated CSP hash management
- [ ] Enhanced monitoring a logging

---

### 10. 🌍 SEO a optimalizácia
- [✅] Automatický sitemap generator (/sitemap.xml)
- [✅] Robots.txt konfigurácia
- [✅] Meta tags pre sociálne siete
- [✅] Responzívny dizajn pre všetky zariadenia
- [✅] Cache-busting mechanizmy
- [✅] Optimalizácia pre mobilné zariadenia
- [ ] Structured data markup
- [ ] Performance optimizations

---

### 11. 🔄 GDPR a správa používateľov
- [✅] Export používateľských dát do XML/JSON
- [✅] Anonymizácia používateľských dát
- [✅] Automatické mazanie fotografií pri odstránení používateľa
- [✅] Bezpečné mazanie citlivých informácií
- [✅] Audit trail pre admin operácie
- [ ] Cookie consent management
- [ ] Data retention policies

---

### 12. 🚀 Deployment a monitoring
- [✅] PM2 process management konfigurácia
- [✅] Environment variables management
- [✅] Production build optimizations
- [✅] Error handling a logging
- [✅] Database migration scripts
- [✅] Backup strategies
- [ ] Health checks a monitoring
- [ ] Performance metrics

---

## 📊 Aktuálny stav projektu

### ✅ Dokončené funkcionality

**Základná infraštruktúra:**
- Kompletná databázová schéma s Prisma ORM
- Firebase Authentication s Google a email/heslo prihlásením
- Responzívny UI dizajn s Tailwind CSS
- PM2 deployment konfigurácia

**Správa hračiek:**
- CRUD operácie pre hračky
- Nahrávanie a správa fotografií cez Cloudinary
- Filtrovanie a vyhľadávanie hračiek
- Detailné zobrazenie hračiek

**Rezervačný systém:**
- Vytvorenie a správa rezervácií
- Systém čakacieho zoznamu
- Schvaľovanie/zamietanie rezervácií
- Kontaktné informácie po schválení

**Administrácia:**
- Kompletné admin rozhranie
- Správa používateľov, hračiek a rezervácií
- Bezpečnostné kontroly a audit

**Bezpečnosť:**
- Input sanitization a SQL injection protection
- Rate limiting a CSRF protection
- Secure file handling a token management

### 🔧 Známe problémy a ich riešenia

**Databázové pripojenie:**
- Riešenie: Spustiť `npx prisma generate` v src adresári
- Overenie DATABASE_URL v .env súbore

**Import modulov:**
- Riešenie: Používať relatívne cesty namiesto aliasov
- Príklad: `../../../lib/db` namiesto `@/lib/db`

**Firebase konfigurácia:**
- Riešenie: Nastaviť správne environment variables
- Povoliť poskytovateľov autentifikácie v Firebase konzole

**Memory management:**
- Riešenie: Monitoring cez `pm2 monit`
- Úprava memory limitov v ecosystem.config.js

---

## 🎯 Prioritné úlohy na dokončenie

### Vysoká priorita
1. **Správa číselníkov** - Administrátorské rozhranie pre typy hračiek
2. **Zmena rolí používateľov** - Admin možnosť meniť role
3. **Radius filtrovanie** - Geografické vyhľadávanie v okruhu
4. **Automated CSP hash management** - Bezpečnostné vylepšenie

### Stredná priorita
1. **Systémové logy** - Audit trail pre všetky operácie
2. **Cookie consent** - GDPR compliance vylepšenie
3. **Performance optimizations** - Rýchlosť a efektivita
4. **Health checks** - Monitoring a alerting

### Nízka priorita
1. **Structured data markup** - SEO vylepšenia
2. **Data retention policies** - Automatické čistenie starých dát
3. **Enhanced monitoring** - Detailné metriky a reporting

---

## 🔮 Budúci vývoj a vylepšenia

### Fáza 1: Dokončenie základných funkcií (Q1 2025)
- Správa číselníkov a rolí používateľov
- Radius filtrovanie s geolokáciou
- Vylepšené bezpečnostné mechanizmy

### Fáza 2: Pokročilé funkcie (Q2 2025)
- Notifikačný systém (email/push)
- Hodnotenie a recenzie používateľov
- Pokročilé vyhľadávanie a odporúčania

### Fáza 3: Škálovanie a optimalizácia (Q3 2025)
- Performance optimizations
- Caching strategies
- Mobile aplikácia (React Native)

### Fáza 4: Rozšírené funkcie (Q4 2025)
- Platobný systém
- Integrácia s externými službami
- Analytics a reporting dashboard

---

## 📝 Poznámky pre vývojárov

### Štruktúra projektu
- `/src/app` - Next.js App Router a API endpointy
- `/src/components` - React komponenty
- `/src/lib` - Pomocné funkcie a knižnice
- `/src/prisma` - Databázová schéma a migrácie
- `/rules/docs` - Projektová dokumentácia

### Dôležité príkazy
```bash
# Spustenie vývojového servera
npm run dev

# Generovanie Prisma klienta
npx prisma generate

# Databázové migrácie
npx prisma db push

# Production build
npm run build

# PM2 deployment
pm2 start ecosystem.config.js
```

### Bezpečnostné pravidlá
- Vždy používať Prisma pre databázové operácie
- Implementovať input sanitization pre všetky vstupy
- Používať withAuth/withAdminAuth middleware
- Validovať všetky API parametre
- Logovať bezpečnostné udalosti

---

**Posledná aktualizácia:** December 2024
**Verzia dokumentu:** 1.0
**Zodpovedný:** Development Team
