#!/bin/bash

# Production Assets Fix Script for Swapka
# Fixes 404 errors and MIME type issues in Next.js standalone deployment

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${BLUE}================================${NC}"
    echo -e "${BLUE}$1${NC}"
    echo -e "${BLUE}================================${NC}"
}

# Check if running as root for nginx operations
check_permissions() {
    if [[ $EUID -eq 0 ]]; then
        SUDO=""
    else
        SUDO="sudo"
        print_warning "Some operations may require sudo privileges"
    fi
}

# Backup current nginx configuration
backup_nginx_config() {
    print_status "Backing up current Nginx configuration..."
    
    if [ -f "/etc/nginx/sites-available/swapka.conf" ]; then
        $SUDO cp /etc/nginx/sites-available/swapka.conf /etc/nginx/sites-available/swapka.conf.backup.$(date +%Y%m%d_%H%M%S)
        print_success "Nginx configuration backed up"
    else
        print_warning "No existing Nginx configuration found at /etc/nginx/sites-available/swapka.conf"
    fi
}

# Copy static assets to standalone directory
fix_static_assets() {
    print_status "Fixing static assets and Prisma binaries in standalone directory..."

    if [ ! -d "src" ]; then
        print_error "src directory not found. Please run this script from the project root."
        exit 1
    fi

    cd src

    # Check if standalone directory exists
    if [ ! -d ".next/standalone" ]; then
        print_error "Standalone directory not found. Please run 'npm run build' first."
        exit 1
    fi

    # Regenerate Prisma client for production platform
    print_status "Regenerating Prisma client for production platform..."
    if npx prisma generate; then
        print_success "Prisma client regenerated for production"
    else
        print_error "Failed to regenerate Prisma client"
        exit 1
    fi

    # Run the asset copying script
    if node scripts/copy-standalone-assets.js; then
        print_success "Static assets and Prisma binaries copied successfully"
    else
        print_error "Failed to copy static assets"
        exit 1
    fi

    cd ..
}

# Update nginx configuration
update_nginx_config() {
    print_status "Updating Nginx configuration..."
    
    if [ ! -f "nginx-swapka-production-fixed.conf" ]; then
        print_error "Fixed Nginx configuration file not found: nginx-swapka-production-fixed.conf"
        exit 1
    fi
    
    # Copy new configuration
    $SUDO cp nginx-swapka-production-fixed.conf /etc/nginx/sites-available/swapka.conf
    
    # Test nginx configuration
    if $SUDO nginx -t; then
        print_success "Nginx configuration is valid"
    else
        print_error "Nginx configuration test failed"
        print_status "Restoring backup configuration..."
        $SUDO cp /etc/nginx/sites-available/swapka.conf.backup.* /etc/nginx/sites-available/swapka.conf 2>/dev/null || true
        exit 1
    fi
}

# Reload nginx
reload_nginx() {
    print_status "Reloading Nginx..."
    
    if $SUDO systemctl reload nginx; then
        print_success "Nginx reloaded successfully"
    else
        print_error "Failed to reload Nginx"
        exit 1
    fi
}

# Restart PM2 application
restart_application() {
    print_status "Restarting application..."
    
    # Check if PM2 is running the application
    if pm2 list | grep -q "swapka-prod"; then
        if pm2 restart swapka-prod; then
            print_success "Application restarted successfully"
        else
            print_error "Failed to restart application"
            exit 1
        fi
    else
        print_warning "PM2 application 'swapka-prod' not found"
        print_status "Starting application..."
        if pm2 start ecosystem.config.js --only swapka-prod --env production; then
            print_success "Application started successfully"
        else
            print_error "Failed to start application"
            exit 1
        fi
    fi
}

# Verify the fix
verify_fix() {
    print_status "Verifying the fix..."
    
    # Check if static files exist
    if [ -d "src/.next/standalone/.next/static" ] && [ -d "src/.next/standalone/public" ]; then
        print_success "Static assets verified in standalone directory"
    else
        print_error "Static assets missing in standalone directory"
        return 1
    fi

    # Check if Prisma binaries exist
    if [ -d "src/.next/standalone/app/generated/prisma" ]; then
        print_success "Prisma binaries verified in standalone directory"
    else
        print_error "Prisma binaries missing in standalone directory"
        return 1
    fi
    
    # Check if application is running
    if pm2 list | grep -q "online.*swapka-prod"; then
        print_success "Application is running"
    else
        print_warning "Application may not be running properly"
        return 1
    fi
    
    # Test if nginx is serving correctly
    if curl -s -o /dev/null -w "%{http_code}" http://localhost:3000/ | grep -q "200\|301\|302"; then
        print_success "Application is responding"
    else
        print_warning "Application may not be responding correctly"
        return 1
    fi
    
    return 0
}

# Main execution
main() {
    print_header "Swapka Production Assets Fix"
    
    print_status "Starting production assets and Prisma fix..."
    print_status "This script will:"
    echo "  1. Regenerate Prisma client for production platform"
    echo "  2. Copy static assets and Prisma binaries to standalone directory"
    echo "  3. Update Nginx configuration with proper MIME types"
    echo "  4. Reload Nginx"
    echo "  5. Restart the application"
    echo ""
    
    read -p "Do you want to continue? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        print_status "Operation cancelled"
        exit 0
    fi
    
    # Check permissions
    check_permissions
    
    # Step 1: Fix static assets
    print_header "Step 1: Fixing Static Assets"
    fix_static_assets
    
    # Step 2: Backup and update nginx config
    print_header "Step 2: Updating Nginx Configuration"
    backup_nginx_config
    update_nginx_config
    
    # Step 3: Reload nginx
    print_header "Step 3: Reloading Nginx"
    reload_nginx
    
    # Step 4: Restart application
    print_header "Step 4: Restarting Application"
    restart_application
    
    # Step 5: Verify fix
    print_header "Step 5: Verifying Fix"
    if verify_fix; then
        print_success "All verification checks passed!"
    else
        print_warning "Some verification checks failed. Please check the logs."
    fi
    
    print_header "Fix Complete"
    print_success "Production assets fix has been applied successfully!"
    print_status "Next steps:"
    echo "  1. Test the website: https://www.swapka.sk"
    echo "  2. Check browser developer tools for any remaining 404 errors"
    echo "  3. Monitor logs: tail -f /var/log/nginx/swapka-static-error.log"
    echo "  4. Monitor PM2: pm2 logs swapka-prod"
}

# Run main function
main "$@"
