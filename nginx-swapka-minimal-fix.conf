# Minimálna Nginx konfigurácia pre swapka.sk
# Pôvodná konfigurácia + pridané nastavenia pre upload fotografií (413 fix)

# HTTP (port 80) – Let's Encrypt validation a presmerovanie na HTTPS
server {
    listen 80;
    server_name swapka.sk www.swapka.sk;

    location /.well-known/acme-challenge/ {
        root /var/www/html;
    }

    location / {
        return 301 https://www.swapka.sk$request_uri;
    }
}

# HTTPS pre hlavný doménový variant (s www)
server {
    listen 443 ssl;
    server_name www.swapka.sk;

    ssl_certificate     /etc/letsencrypt/live/swapka.sk/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/swapka.sk/privkey.pem;

    # SSL optimalizácie
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-RSA-CHACHA20-POLY1305;
    ssl_prefer_server_ciphers off;

    # HSTS
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;

    # ===== KRITICKÉ NASTAVENIA PRE FILE UPLOADS - 413 FIX =====
    # Maximálna veľkosť requestu - HLAVNÝ FIX PRE 413 CHYBU
    client_max_body_size 20M;
    
    # Buffer nastavenia pre veľké súbory
    client_body_buffer_size 128k;
    client_body_timeout 60s;
    client_header_timeout 60s;
    send_timeout 60s;
    
    # Proxy timeout nastavenia
    proxy_connect_timeout 60s;
    proxy_send_timeout 60s;
    proxy_read_timeout 60s;

    # ===== ŠPECIÁLNE NASTAVENIA PRE UPLOAD ENDPOINT =====
    location = /api/cloudinary/upload {
        # Zvýšené limity špecificky pre upload
        client_max_body_size 25M;
        client_body_timeout 120s;
        
        # Vypnutie proxy bufferovanie pre streaming uploads
        proxy_request_buffering off;
        
        # Proxy nastavenia (rovnaké ako pôvodné)
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # Zvýšené timeout pre upload
        proxy_connect_timeout 120s;
        proxy_send_timeout 120s;
        proxy_read_timeout 120s;

        # Špeciálne logovanie pre upload debugging
        access_log /var/log/nginx/swapka-upload.log;
        error_log /var/log/nginx/swapka-upload-error.log;
    }

    # ===== PÔVODNÝ VŠEOBECNÝ LOCATION BLOK =====
    # Zachytí VŠETKY ostatné requesty vrátane Next.js routes
    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # Custom error handling pre 413 chybu
    error_page 413 @413_error;
    location @413_error {
        return 413 '{"error": "Súbor je príliš veľký", "details": ["Maximálna povolená veľkosť je 20MB"], "code": 413}';
        add_header Content-Type application/json always;
    }
}

# Presmerovanie z https://swapka.sk na https://www.swapka.sk
server {
    listen 443 ssl;
    server_name swapka.sk;

    ssl_certificate     /etc/letsencrypt/live/swapka.sk/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/swapka.sk/privkey.pem;

    return 301 https://www.swapka.sk$request_uri;
}
