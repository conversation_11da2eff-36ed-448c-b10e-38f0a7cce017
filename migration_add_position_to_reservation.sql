-- Pridanie stĺpca position do tabuľky Reservation
ALTER TABLE `Reservation` ADD COLUMN `position` INT NOT NULL DEFAULT 0;

-- Aktualizácia existujúcich rezervácií - nastavenie pozície podľa poradia vytvorenia
-- Najprv nastavíme všetky na vysokú hodnotu, aby sme predišli konfliktom
UPDATE `Reservation` SET `position` = 9999 WHERE `status` IN ('PENDING', 'CONFIRMED', 'ACTIVE');

-- Potom aktualizujeme pozície pre každú hračku samostatne
-- Toto je potrebné spustiť manuálne pre každú hračku s viacerými rezerváciami
-- Príklad SQL pre aktualizáciu pozícií pre konkrétnu hračku:
/*
SET @row_number = 0;
UPDATE `Reservation` 
SET `position` = (@row_number := @row_number + 1) - 1
WHERE `toyId` = [ID_HRACKY] AND `status` IN ('PENDING', 'CONFIRMED', 'ACTIVE')
ORDER BY `createdAt` ASC;
*/

-- Poznámka: Nahraďte [ID_HRACKY] skutočným ID hračky
-- Tento skript je potrebné spustiť pre každú hračku, ktorá má viacero aktívnych rezervácií
