# Nginx konfigur<PERSON>cia pre Swapka aplikáciu
# Riešenie 413 "Request Entity Too Large" chyby pre nahrávanie obr<PERSON>zkov

# <PERSON><PERSON><PERSON> konfigurácia
user nginx;
worker_processes auto;
error_log /var/log/nginx/error.log warn;
pid /var/run/nginx.pid;

events {
    worker_connections 1024;
    use epoll;
    multi_accept on;
}

http {
    include /etc/nginx/mime.types;
    default_type application/octet-stream;

    # Logging formát
    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for"';

    access_log /var/log/nginx/access.log main;

    # Základné nastavenia
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    types_hash_max_size 2048;

    # KRITICKÉ NASTAVENIA PRE FILE UPLOADS
    # Zvýšenie limitov pre nahrávanie súborov
    client_max_body_size 20M;          # <PERSON><PERSON>lna veľkosť requestu (20MB)
    client_body_buffer_size 128k;      # <PERSON><PERSON><PERSON> pre request body
    client_body_timeout 60s;           # Timeout pre čítanie request body
    client_header_timeout 60s;         # Timeout pre čítanie headerov
    send_timeout 60s;                  # Timeout pre odosielanie odpovede
    
    # Proxy buffer nastavenia pre veľké súbory
    proxy_buffering on;
    proxy_buffer_size 4k;
    proxy_buffers 8 4k;
    proxy_busy_buffers_size 8k;
    proxy_max_temp_file_size 1024m;
    proxy_temp_file_write_size 8k;
    
    # Proxy timeout nastavenia
    proxy_connect_timeout 60s;
    proxy_send_timeout 60s;
    proxy_read_timeout 60s;

    # Gzip kompresácia
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/json
        application/javascript
        application/xml+rss
        application/atom+xml
        image/svg+xml;

    # Server konfigurácia pre Swapka
    server {
        listen 80;
        server_name localhost swapka.local;  # Upravte podľa vašej domény

        # Bezpečnostné hlavičky
        add_header X-Frame-Options "SAMEORIGIN" always;
        add_header X-XSS-Protection "1; mode=block" always;
        add_header X-Content-Type-Options "nosniff" always;
        add_header Referrer-Policy "strict-origin-when-cross-origin" always;

        # Špeciálne nastavenia pre upload endpointy
        location /api/cloudinary/upload {
            # Zvýšené limity špecificky pre upload
            client_max_body_size 25M;
            client_body_timeout 120s;
            proxy_request_buffering off;  # Vypnutie bufferovanie pre streaming uploads
            
            # Proxy nastavenia
            proxy_pass http://127.0.0.1:3000;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection 'upgrade';
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_cache_bypass $http_upgrade;
            
            # Timeout nastavenia pre upload
            proxy_connect_timeout 120s;
            proxy_send_timeout 120s;
            proxy_read_timeout 120s;
        }

        # Všetky ostatné API endpointy
        location /api/ {
            proxy_pass http://127.0.0.1:3000;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection 'upgrade';
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_cache_bypass $http_upgrade;
        }

        # Statické súbory Next.js
        location /_next/static/ {
            proxy_pass http://127.0.0.1:3000;
            expires 1y;
            add_header Cache-Control "public, immutable";
        }

        # Všetky ostatné requesty
        location / {
            proxy_pass http://127.0.0.1:3000;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection 'upgrade';
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_cache_bypass $http_upgrade;
        }

        # Error pages
        error_page 413 /413.html;
        location = /413.html {
            root /usr/share/nginx/html;
            internal;
        }
    }
}
