#!/bin/bash

# Farby pre výstup
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

echo -e "${YELLOW}Kontrolujem súbory potrebné pre Docker...${NC}"

# Kontrola Dockerfile
if [ -f "Dockerfile" ]; then
    echo -e "${GREEN}✓ Dockerfile existuje${NC}"
else
    echo -e "${RED}✗ Dockerfile neexistuje${NC}"
    exit 1
fi

# Kontrola docker-compose.yml
if [ -f "docker-compose.yml" ]; then
    echo -e "${GREEN}✓ docker-compose.yml existuje${NC}"
else
    echo -e "${RED}✗ docker-compose.yml neexistuje${NC}"
    exit 1
fi

# Kontrola .dockerignore
if [ -f ".dockerignore" ]; then
    echo -e "${GREEN}✓ .dockerignore existuje${NC}"
else
    echo -e "${RED}✗ .dockerignore neexistuje${NC}"
    exit 1
fi

# Kontrola package.json v src adresári
if [ -f "src/package.json" ]; then
    echo -e "${GREEN}✓ src/package.json existuje${NC}"
else
    echo -e "${RED}✗ src/package.json neexistuje${NC}"
    exit 1
fi

# Kontrola package-lock.json v src adresári
if [ -f "src/package-lock.json" ]; then
    echo -e "${GREEN}✓ src/package-lock.json existuje${NC}"
else
    echo -e "${RED}✗ src/package-lock.json neexistuje${NC}"
    exit 1
fi

# Kontrola next.config.ts v src adresári
if [ -f "src/next.config.ts" ]; then
    echo -e "${GREEN}✓ src/next.config.ts existuje${NC}"
else
    echo -e "${RED}✗ src/next.config.ts neexistuje${NC}"
    exit 1
fi

echo -e "${GREEN}Všetky potrebné súbory existujú.${NC}"
echo -e "${YELLOW}Môžete spustiť Docker build pomocou:${NC}"
echo -e "${YELLOW}docker-compose up -d --build${NC}"
