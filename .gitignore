# dependencies
/node_modules
/src/node_modules
/.pnp
.pnp.js
.yarn/install-state.gz

# testing
/coverage

# next.js
/.next/
/out/
/src/.next/
/src/out/

# production
/build
/src/build

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# local env files
.env*.local
.env
/src/.env*.local
/src/.env

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts

# IDE
.idea/
.vscode/
*.swp
*.swo

# Logs
logs
*.log

# Cache
.cache/
.npm/

# Temporary files
tmp/
temp/

/src/version-info.json