#!/bin/bash

# PM2 Setup Script for Swapka Application
# This script automates the initial setup and deployment of the Swapka app with PM2

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to check if PM2 is installed
check_pm2() {
    if command_exists pm2; then
        print_success "PM2 is already installed"
        pm2 --version
        return 0
    else
        print_warning "PM2 is not installed"
        return 1
    fi
}

# Function to install PM2
install_pm2() {
    print_status "Installing PM2 globally..."
    if npm install -g pm2; then
        print_success "PM2 installed successfully"
    else
        print_error "Failed to install PM2"
        exit 1
    fi
}

# Function to setup application dependencies
setup_dependencies() {
    print_status "Setting up application dependencies..."
    
    if [ ! -d "src" ]; then
        print_error "src directory not found. Please run this script from the project root."
        exit 1
    fi
    
    cd src
    
    print_status "Installing npm dependencies..."
    if npm install; then
        print_success "Dependencies installed successfully"
    else
        print_error "Failed to install dependencies"
        exit 1
    fi
    
    print_status "Generating Prisma client for production..."
    if npx prisma generate; then
        print_success "Prisma client generated successfully"
    else
        print_error "Failed to generate Prisma client"
        exit 1
    fi
    
    cd ..
}

# Function to create logs directory
setup_logs() {
    print_status "Creating logs directory..."
    mkdir -p logs
    print_success "Logs directory created"
}

# Function to build the application
build_application() {
    print_status "Building the application..."
    cd src

    if npm run build; then
        print_success "Application built successfully"
        print_success "Static assets copied to standalone directory"
    else
        print_error "Failed to build application"
        exit 1
    fi

    # Verify standalone assets
    if [ -d ".next/standalone/.next/static" ] && [ -d ".next/standalone/public" ]; then
        print_success "Standalone assets verified"
    else
        print_warning "Standalone assets may be missing - running manual copy"
        if node scripts/copy-standalone-assets.js; then
            print_success "Manual asset copy completed"
        else
            print_error "Failed to copy standalone assets"
            exit 1
        fi
    fi

    cd ..
}

# Function to start PM2 application
start_pm2() {
    local mode=$1
    
    print_status "Starting PM2 application in $mode mode..."
    
    case $mode in
        "production")
            pm2 start ecosystem.config.js --only swapka-prod --env production
            ;;
        "development")
            pm2 start ecosystem.config.js --only swapka-dev
            ;;
        *)
            print_error "Invalid mode. Use 'production' or 'development'"
            exit 1
            ;;
    esac
    
    if [ $? -eq 0 ]; then
        print_success "PM2 application started successfully"
        pm2 list
    else
        print_error "Failed to start PM2 application"
        exit 1
    fi
}

# Function to setup PM2 startup
setup_startup() {
    print_status "Setting up PM2 auto-startup..."
    
    pm2 save
    pm2 startup
    
    print_success "PM2 startup configuration completed"
    print_warning "You may need to run the generated startup command with sudo"
}

# Function to show usage
show_usage() {
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  -m, --mode MODE     Set mode (production|development) [default: production]"
    echo "  -s, --skip-build    Skip building the application"
    echo "  -n, --no-startup    Skip PM2 startup configuration"
    echo "  -h, --help          Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0                           # Setup for production"
    echo "  $0 --mode development        # Setup for development"
    echo "  $0 --skip-build             # Skip build step"
    echo "  $0 --no-startup             # Skip startup configuration"
}

# Default values
MODE="production"
SKIP_BUILD=false
NO_STARTUP=false

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -m|--mode)
            MODE="$2"
            shift 2
            ;;
        -s|--skip-build)
            SKIP_BUILD=true
            shift
            ;;
        -n|--no-startup)
            NO_STARTUP=true
            shift
            ;;
        -h|--help)
            show_usage
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
done

# Validate mode
if [[ "$MODE" != "production" && "$MODE" != "development" ]]; then
    print_error "Invalid mode: $MODE. Use 'production' or 'development'"
    exit 1
fi

# Main execution
print_status "Starting PM2 setup for Swapka application..."
print_status "Mode: $MODE"

# Check and install PM2
if ! check_pm2; then
    install_pm2
fi

# Setup dependencies
setup_dependencies

# Create logs directory
setup_logs

# Build application (unless skipped)
if [ "$SKIP_BUILD" = false ] && [ "$MODE" = "production" ]; then
    build_application
fi

# Start PM2 application
start_pm2 "$MODE"

# Setup startup (unless skipped)
if [ "$NO_STARTUP" = false ] && [ "$MODE" = "production" ]; then
    setup_startup
fi

print_success "PM2 setup completed successfully!"
print_status "You can now manage your application with PM2 commands:"
echo "  pm2 list          # View running applications"
echo "  pm2 logs          # View application logs"
echo "  pm2 monit         # Monitor applications"
echo "  pm2 restart all   # Restart applications"
echo ""
print_status "For more information, see PM2_GUIDE.md"
