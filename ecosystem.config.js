module.exports = {
  apps: [
    {
      // Production configuration
      name: 'swapka-prod',
      script: 'node',
      args: '.next/standalone/server.js',
      cwd: './src',
      instances: 1, // Use all available CPU cores
      exec_mode: 'fork',
      
      // Environment variables
      env: {
        NODE_ENV: 'production',
        PORT: 3000,
        HOSTNAME: '0.0.0.0'
      },
      env_file: './src/.env',
      
      // Restart policies
      autorestart: true,
      watch: false, // Disable in production for performance
      max_memory_restart: '1G',
      restart_delay: 4000,
      max_restarts: 10,
      min_uptime: '10s',
      
      // Logging configuration
      log_file: './logs/swapka-combined.log',
      out_file: './logs/swapka-out.log',
      error_file: './logs/swapka-error.log',
      log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
      merge_logs: true,
      
      // Process management
      kill_timeout: 5000,
      listen_timeout: 3000,
      
      // Health monitoring
      health_check_grace_period: 3000
    },
    
    {
      // Development configuration
      name: 'swapka-dev',
      script: 'npm',
      args: 'run dev',
      cwd: './src',
      instances: 1, // Single instance for development
      exec_mode: 'fork',
      
      // Environment variables
      env: {
        NODE_ENV: 'development',
        PORT: 3000,
        HOSTNAME: 'localhost'
      },
      env_file: './src/.env',
      
      // Restart policies
      autorestart: true,
      watch: ['./src/app', './src/components', './src/lib'], // Watch for file changes
      watch_delay: 1000,
      ignore_watch: [
        'node_modules',
        '.next',
        'logs',
        '*.log',
        '.git',
        'prisma/migrations'
      ],
      max_memory_restart: '500M',
      restart_delay: 2000,
      
      // Logging configuration
      log_file: './logs/swapka-dev-combined.log',
      out_file: './logs/swapka-dev-out.log',
      error_file: './logs/swapka-dev-error.log',
      log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
      merge_logs: true,
      
      // Process management
      kill_timeout: 3000,
      listen_timeout: 3000
    },
    
    {
      // Build configuration (for CI/CD or manual builds)
      name: 'swapka-build',
      script: 'npm',
      args: 'run build',
      cwd: './src',
      instances: 1,
      exec_mode: 'fork',
      autorestart: false, // Don't restart build processes
      
      // Environment variables
      env: {
        NODE_ENV: 'production'
      },
      env_file: './src/.env',
      
      // Logging configuration
      log_file: './logs/swapka-build-combined.log',
      out_file: './logs/swapka-build-out.log',
      error_file: './logs/swapka-build-error.log',
      log_date_format: 'YYYY-MM-DD HH:mm:ss Z'
    }
  ],
  
  // Global PM2 configuration
  deploy: {
    production: {
      user: 'deploy',
      host: ['your-production-server.com'],
      ref: 'origin/main',
      repo: 'your-git-repository-url',
      path: '/var/www/swapka',
      'post-deploy': 'cd src && npm install && npx prisma generate && npm run build && pm2 reload ecosystem.config.js --env production',
      'pre-setup': 'mkdir -p /var/www/swapka/logs'
    }
  }
};
