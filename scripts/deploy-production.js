#!/usr/bin/env node

const { execSync, spawn } = require('child_process');
const fs = require('fs');
const path = require('path');

// ANSI color codes for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

// Configuration
const CONFIG = {
  workingDir: '/home/<USER>/www/swapka',
  srcDir: 'src',
  pm2AppName: 'swapka-prod',
  pm2ConfigFile: 'ecosystem.config.js'
};

// Utility functions
function log(message, color = colors.cyan) {
  console.log(`${color}${colors.bright}[DEPLOY]${colors.reset} ${message}`);
}

function logSuccess(message) {
  log(`✅ ${message}`, colors.green);
}

function logError(message) {
  log(`❌ ${message}`, colors.red);
}

function logWarning(message) {
  log(`⚠️  ${message}`, colors.yellow);
}

function logInfo(message) {
  log(`ℹ️  ${message}`, colors.blue);
}

function execCommand(command, options = {}) {
  try {
    const result = execSync(command, {
      stdio: 'pipe',
      encoding: 'utf8',
      cwd: options.cwd || CONFIG.workingDir,
      ...options
    });
    return result.trim();
  } catch (error) {
    throw new Error(`Command failed: ${command}\n${error.message}`);
  }
}

function checkPrerequisites() {
  log('Checking prerequisites...');
  
  // Check if we're in the correct directory
  const currentDir = process.cwd();
  if (currentDir !== CONFIG.workingDir) {
    logWarning(`Current directory: ${currentDir}`);
    logWarning(`Expected directory: ${CONFIG.workingDir}`);
    logWarning('Please run this script from the correct directory or update CONFIG.workingDir');
  }
  
  // Check if git is available
  try {
    execCommand('git --version');
    logSuccess('Git is available');
  } catch (error) {
    throw new Error('Git is not available');
  }
  
  // Check if PM2 is available
  try {
    execCommand('pm2 --version');
    logSuccess('PM2 is available');
  } catch (error) {
    throw new Error('PM2 is not available');
  }
  
  // Check if src directory exists
  if (!fs.existsSync(path.join(CONFIG.workingDir, CONFIG.srcDir))) {
    throw new Error(`Source directory not found: ${CONFIG.srcDir}`);
  }
  logSuccess('Source directory exists');
  
  // Check if ecosystem.config.js exists
  if (!fs.existsSync(path.join(CONFIG.workingDir, CONFIG.pm2ConfigFile))) {
    throw new Error(`PM2 config file not found: ${CONFIG.pm2ConfigFile}`);
  }
  logSuccess('PM2 config file exists');
}

function pullLatestChanges() {
  log('Pulling latest changes from git repository...');

  try {
    // Check git status first
    try {
      const status = execCommand('git status --porcelain');
      if (status.trim()) {
        logWarning('Working directory has uncommitted changes:');
        console.log(status);
        logInfo('These changes will be handled during checkout...');
      }
    } catch (error) {
      logWarning('Could not check git status');
    }

    // Fetch all branches and tags
    execCommand('git fetch --all --tags');
    logSuccess('Fetched latest changes');

    // Get current branch
    const currentBranch = execCommand('git branch --show-current');
    logInfo(`Current branch: ${currentBranch}`);

    return currentBranch;
  } catch (error) {
    throw new Error(`Failed to pull latest changes: ${error.message}`);
  }
}

function getLatestVersionTag() {
  log('Finding latest version tag...');

  try {
    // Get all tags
    const tags = execCommand('git tag --list').split('\n').filter(tag => tag.trim());

    if (tags.length === 0) {
      throw new Error('No git tags found. Please create a version tag first (e.g., git tag v0.22)');
    }

    // Filter and sort tags by semantic version
    const versionTags = tags
      .filter(tag => tag.match(/^v\d+\.\d+(\.\d+)?$/))
      .sort((a, b) => {
        const aVersion = a.replace('v', '').split('.').map(Number);
        const bVersion = b.replace('v', '').split('.').map(Number);

        // Compare major version
        if (aVersion[0] !== bVersion[0]) return bVersion[0] - aVersion[0];
        // Compare minor version
        if (aVersion[1] !== bVersion[1]) return bVersion[1] - aVersion[1];
        // Compare patch version (if exists)
        const aPatch = aVersion[2] || 0;
        const bPatch = bVersion[2] || 0;
        return bPatch - aPatch;
      });

    if (versionTags.length === 0) {
      throw new Error('No semantic version tags found. Tags should follow format v0.22, v0.23, etc.');
    }

    const latestTag = versionTags[0];
    logSuccess(`Latest version tag found: ${latestTag}`);
    logInfo(`Available tags: ${versionTags.slice(0, 5).join(', ')}${versionTags.length > 5 ? '...' : ''}`);
    return latestTag;
  } catch (error) {
    throw new Error(`Failed to get latest version: ${error.message}`);
  }
}

function createVersionTag() {
  log('Creating new version tag...');

  try {
    // Get current version from package.json
    const packageJsonPath = path.join(CONFIG.workingDir, CONFIG.srcDir, 'package.json');
    if (!fs.existsSync(packageJsonPath)) {
      throw new Error('package.json not found in src directory');
    }

    const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
    const currentVersion = packageJson.version;

    if (!currentVersion) {
      throw new Error('No version found in package.json');
    }

    const tagName = `v${currentVersion}`;
    logInfo(`Creating tag: ${tagName}`);

    // Check if tag already exists
    try {
      execCommand(`git rev-parse ${tagName}`);
      logWarning(`Tag ${tagName} already exists`);
      return tagName;
    } catch (error) {
      // Tag doesn't exist, create it
    }

    // Create the tag
    execCommand(`git tag ${tagName}`);
    logSuccess(`Created tag: ${tagName}`);

    // Push the tag to remote
    try {
      execCommand(`git push origin ${tagName}`);
      logSuccess(`Pushed tag to remote: ${tagName}`);
    } catch (error) {
      logWarning(`Failed to push tag to remote: ${error.message}`);
    }

    return tagName;
  } catch (error) {
    throw new Error(`Failed to create version tag: ${error.message}`);
  }
}

function checkoutVersion(version) {
  log(`Checking out version: ${version}...`);

  try {
    // Handle potential conflicts with generated files
    logInfo('Cleaning up generated files that might cause conflicts...');

    // Remove version-info.json if it exists (it's generated during build)
    const versionInfoPath = path.join(CONFIG.workingDir, CONFIG.srcDir, 'version-info.json');
    if (fs.existsSync(versionInfoPath)) {
      fs.unlinkSync(versionInfoPath);
      logInfo('Removed existing version-info.json');
    }

    // Discard any local changes to tracked files that might conflict
    try {
      execCommand('git checkout -- .');
      logInfo('Discarded local changes to tracked files');
    } catch (error) {
      logWarning('Could not discard local changes (this is usually fine)');
    }

    // Now checkout the version
    execCommand(`git checkout ${version}`);
    logSuccess(`Checked out version: ${version}`);
  } catch (error) {
    throw new Error(`Failed to checkout version ${version}: ${error.message}`);
  }
}

function buildApplication() {
  log('Building application...');
  
  const srcPath = path.join(CONFIG.workingDir, CONFIG.srcDir);
  
  try {
    // Check if package.json exists in src directory
    if (!fs.existsSync(path.join(srcPath, 'package.json'))) {
      throw new Error('package.json not found in src directory');
    }
    
    // Install dependencies (in case there are new ones)
    logInfo('Installing dependencies...');
    execCommand('npm ci', { cwd: srcPath });
    logSuccess('Dependencies installed');
    
    // Generate Prisma client
    logInfo('Generating Prisma client...');
    execCommand('npx prisma generate', { cwd: srcPath });
    logSuccess('Prisma client generated');
    
    // Build the application
    logInfo('Building Next.js application...');
    execCommand('npm run build', { cwd: srcPath });
    logSuccess('Application built successfully');
    
  } catch (error) {
    throw new Error(`Build failed: ${error.message}`);
  }
}

function managePM2Process() {
  log('Managing PM2 process...');
  
  try {
    // Check if the process is already running
    let processExists = false;
    try {
      const processes = execCommand('pm2 jlist');
      const processList = JSON.parse(processes);
      processExists = processList.some(proc => proc.name === CONFIG.pm2AppName);
    } catch (error) {
      logWarning('Could not check existing PM2 processes');
    }
    
    if (processExists) {
      logInfo(`Reloading existing PM2 process: ${CONFIG.pm2AppName}`);
      execCommand(`pm2 reload ${CONFIG.pm2AppName}`);
      logSuccess('PM2 process reloaded (zero-downtime deployment)');
    } else {
      logInfo('Starting new PM2 process...');
      // Stop all processes first (in case there are any running)
      try {
        execCommand('pm2 stop all');
        execCommand('pm2 delete all');
        logInfo('Stopped and deleted all existing PM2 processes');
      } catch (error) {
        logWarning('No existing PM2 processes to stop');
      }
      
      // Start the production process
      execCommand(`pm2 start ${CONFIG.pm2ConfigFile} --only ${CONFIG.pm2AppName}`);
      logSuccess('PM2 process started');
    }
    
    // Save PM2 configuration
    execCommand('pm2 save');
    logSuccess('PM2 configuration saved');
    
    // Show process status
    logInfo('Current PM2 status:');
    const status = execCommand('pm2 status');
    console.log(status);
    
  } catch (error) {
    throw new Error(`PM2 management failed: ${error.message}`);
  }
}

function checkDeployment() {
  log('Checking deployment status...');
  
  try {
    // Wait a moment for the process to start
    setTimeout(() => {
      const status = execCommand('pm2 status');
      const processes = execCommand('pm2 jlist');
      const processList = JSON.parse(processes);
      
      const swapkaProcess = processList.find(proc => proc.name === CONFIG.pm2AppName);
      
      if (swapkaProcess && swapkaProcess.pm2_env.status === 'online') {
        logSuccess('Deployment successful! Application is running.');
        logInfo(`Process ID: ${swapkaProcess.pid}`);
        logInfo(`Uptime: ${swapkaProcess.pm2_env.pm_uptime}`);
        logInfo(`Memory usage: ${Math.round(swapkaProcess.monit.memory / 1024 / 1024)}MB`);
      } else {
        logError('Deployment may have issues. Check PM2 logs with: pm2 logs');
      }
    }, 3000);
    
  } catch (error) {
    logWarning(`Could not verify deployment status: ${error.message}`);
  }
}

// Main deployment function
async function deploy() {
  const startTime = Date.now();
  
  try {
    log('🚀 Starting production deployment...');
    log(`Working directory: ${CONFIG.workingDir}`);
    
    // Parse command line arguments
    const args = process.argv.slice(2);
    const checkOnly = args.includes('--check-only');
    const force = args.includes('--force');
    
    if (checkOnly) {
      log('Running in check-only mode...');
      checkPrerequisites();
      logSuccess('All prerequisites check passed!');
      return;
    }
    
    // Step 1: Check prerequisites
    checkPrerequisites();
    
    // Step 2: Pull latest changes
    const currentBranch = pullLatestChanges();
    
    // Step 3: Get and checkout latest version
    let latestVersion;
    try {
      latestVersion = getLatestVersionTag();
    } catch (error) {
      if (force || args.includes('--create-tag')) {
        logWarning('No version tags found, creating one from package.json version...');
        latestVersion = createVersionTag();
      } else {
        throw new Error(`${error.message}\nUse --create-tag flag to create a tag from package.json version`);
      }
    }

    checkoutVersion(latestVersion);
    
    // Step 4: Build application
    buildApplication();
    
    // Step 5: Manage PM2 process
    managePM2Process();
    
    // Step 6: Check deployment
    checkDeployment();
    
    const duration = Math.round((Date.now() - startTime) / 1000);
    logSuccess(`🎉 Deployment completed successfully in ${duration} seconds!`);
    
  } catch (error) {
    logError(`Deployment failed: ${error.message}`);
    process.exit(1);
  }
}

// Run deployment
if (require.main === module) {
  deploy();
}

module.exports = { deploy };
