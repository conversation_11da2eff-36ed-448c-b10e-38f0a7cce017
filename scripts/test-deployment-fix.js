#!/usr/bin/env node

/**
 * Test script to verify the deployment fix works correctly
 * This script simulates the conditions that caused the original deployment failure
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

// ANSI color codes
const colors = {
  reset: '\x1b[0m',
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

function log(message, color = colors.cyan) {
  console.log(`${color}[TEST]${colors.reset} ${message}`);
}

function logSuccess(message) {
  log(`✅ ${message}`, colors.green);
}

function logError(message) {
  log(`❌ ${message}`, colors.red);
}

function logWarning(message) {
  log(`⚠️  ${message}`, colors.yellow);
}

function logInfo(message) {
  log(`ℹ️  ${message}`, colors.blue);
}

function execCommand(command, options = {}) {
  try {
    const result = execSync(command, {
      stdio: 'pipe',
      encoding: 'utf8',
      ...options
    });
    return result.trim();
  } catch (error) {
    throw new Error(`Command failed: ${command}\n${error.message}`);
  }
}

function testDeploymentFix() {
  log('🧪 Testing deployment fix for version-info.json conflicts...');
  
  try {
    // Step 1: Create a version-info.json file to simulate the conflict
    const versionInfoPath = path.join(process.cwd(), 'src', 'version-info.json');
    const testVersionInfo = {
      version: "0.24",
      buildId: "test-build-123",
      timestamp: Date.now(),
      environment: "test",
      buildDate: new Date().toISOString(),
      cacheBusting: false
    };
    
    logInfo('Creating test version-info.json file...');
    fs.writeFileSync(versionInfoPath, JSON.stringify(testVersionInfo, null, 2));
    logSuccess('Test version-info.json created');
    
    // Step 2: Check if the file would cause git conflicts
    logInfo('Checking git status...');
    try {
      const status = execCommand('git status --porcelain');
      if (status.includes('version-info.json')) {
        logWarning('version-info.json appears in git status (this would cause conflicts)');
      } else {
        logSuccess('version-info.json is properly ignored by git');
      }
    } catch (error) {
      logWarning('Could not check git status');
    }
    
    // Step 3: Test the checkout function logic
    logInfo('Testing checkout conflict resolution...');
    
    // Remove the file (simulating what our fixed deployment script does)
    if (fs.existsSync(versionInfoPath)) {
      fs.unlinkSync(versionInfoPath);
      logSuccess('Successfully removed version-info.json (conflict resolution works)');
    }
    
    // Step 4: Test git checkout -- . command
    logInfo('Testing git checkout -- . command...');
    try {
      execCommand('git checkout -- .');
      logSuccess('git checkout -- . executed successfully');
    } catch (error) {
      logInfo('git checkout -- . had no effect (no tracked files to reset)');
    }
    
    // Step 5: Verify the file is in .gitignore
    logInfo('Verifying .gitignore configuration...');
    const gitignorePath = path.join(process.cwd(), '.gitignore');
    if (fs.existsSync(gitignorePath)) {
      const gitignoreContent = fs.readFileSync(gitignorePath, 'utf8');
      if (gitignoreContent.includes('src/version-info.json')) {
        logSuccess('version-info.json is properly listed in .gitignore');
      } else {
        logError('version-info.json is NOT in .gitignore');
      }
    }
    
    // Step 6: Check if file is tracked by git
    logInfo('Checking if version-info.json is tracked by git...');
    try {
      const trackedFiles = execCommand('git ls-files | grep version-info.json || echo "not-found"');
      if (trackedFiles === 'not-found') {
        logSuccess('version-info.json is not tracked by git (good!)');
      } else {
        logError('version-info.json is still tracked by git');
        logInfo('Tracked files containing version-info.json:');
        console.log(trackedFiles);
      }
    } catch (error) {
      logWarning('Could not check tracked files');
    }
    
    logSuccess('🎉 Deployment fix test completed successfully!');
    logInfo('The deployment script should now handle version-info.json conflicts properly.');
    
  } catch (error) {
    logError(`Test failed: ${error.message}`);
    process.exit(1);
  }
}

// Run the test
if (require.main === module) {
  testDeploymentFix();
}

module.exports = { testDeploymentFix };
