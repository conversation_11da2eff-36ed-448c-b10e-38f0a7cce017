#!/bin/bash

# Swapka Production Deployment Script
# This script automates the entire production deployment process

set -e  # Exit on any error

# Configuration
WORKING_DIR="/home/<USER>/www/swapka"
SRC_DIR="src"
PM2_APP_NAME="swapka-prod"
PM2_CONFIG_FILE="ecosystem.config.js"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Logging functions
log() {
    echo -e "${CYAN}[DEPLOY]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[DEPLOY] ✅ $1${NC}"
}

log_error() {
    echo -e "${RED}[DEPLOY] ❌ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}[DEPLOY] ⚠️  $1${NC}"
}

log_info() {
    echo -e "${BLUE}[DEPLOY] ℹ️  $1${NC}"
}

# Error handling
handle_error() {
    log_error "Deployment failed at step: $1"
    log_error "Error details: $2"
    exit 1
}

# Check prerequisites
check_prerequisites() {
    log "Checking prerequisites..."
    
    # Check if we're in the correct directory
    if [ "$(pwd)" != "$WORKING_DIR" ]; then
        log_warning "Current directory: $(pwd)"
        log_warning "Expected directory: $WORKING_DIR"
        log_warning "Please run this script from the correct directory"
    fi
    
    # Check if git is available
    if ! command -v git &> /dev/null; then
        handle_error "Prerequisites" "Git is not available"
    fi
    log_success "Git is available"
    
    # Check if PM2 is available
    if ! command -v pm2 &> /dev/null; then
        handle_error "Prerequisites" "PM2 is not available"
    fi
    log_success "PM2 is available"
    
    # Check if src directory exists
    if [ ! -d "$SRC_DIR" ]; then
        handle_error "Prerequisites" "Source directory not found: $SRC_DIR"
    fi
    log_success "Source directory exists"
    
    # Check if ecosystem.config.js exists
    if [ ! -f "$PM2_CONFIG_FILE" ]; then
        handle_error "Prerequisites" "PM2 config file not found: $PM2_CONFIG_FILE"
    fi
    log_success "PM2 config file exists"
}

# Pull latest changes from git
pull_latest_changes() {
    log "Pulling latest changes from git repository..."

    # Check git status first
    if git status --porcelain | grep -q .; then
        log_warning "Working directory has uncommitted changes:"
        git status --porcelain
        log_info "These changes will be handled during checkout..."
    fi

    if ! git fetch --all --tags; then
        handle_error "Git Pull" "Failed to fetch latest changes"
    fi
    log_success "Fetched latest changes"

    CURRENT_BRANCH=$(git branch --show-current)
    log_info "Current branch: $CURRENT_BRANCH"
}

# Get latest version tag
get_latest_version() {
    log "Finding latest version tag..."

    # Get all tags and filter for semantic version tags
    LATEST_VERSION=$(git tag --list | grep -E '^v[0-9]+\.[0-9]+(\.[0-9]+)?$' | sort -V -r | head -1)

    if [ -z "$LATEST_VERSION" ]; then
        handle_error "Version Detection" "No git tags found. Please create a version tag first (e.g., git tag v0.22)"
    fi

    log_success "Latest version tag found: $LATEST_VERSION"

    # Show available tags for reference
    ALL_TAGS=$(git tag --list | grep -E '^v[0-9]+\.[0-9]+(\.[0-9]+)?$' | sort -V -r | head -5 | tr '\n' ', ' | sed 's/,$//')
    log_info "Available tags: $ALL_TAGS"
}

# Create version tag from package.json
create_version_tag() {
    log "Creating new version tag..."

    # Check if package.json exists in src directory
    if [ ! -f "$SRC_DIR/package.json" ]; then
        handle_error "Tag Creation" "package.json not found in src directory"
    fi

    # Extract version from package.json
    PACKAGE_VERSION=$(grep '"version"' "$SRC_DIR/package.json" | sed 's/.*"version": *"\([^"]*\)".*/\1/')

    if [ -z "$PACKAGE_VERSION" ]; then
        handle_error "Tag Creation" "No version found in package.json"
    fi

    TAG_NAME="v$PACKAGE_VERSION"
    log_info "Creating tag: $TAG_NAME"

    # Check if tag already exists
    if git rev-parse "$TAG_NAME" >/dev/null 2>&1; then
        log_warning "Tag $TAG_NAME already exists"
        LATEST_VERSION="$TAG_NAME"
        return
    fi

    # Create the tag
    if ! git tag "$TAG_NAME"; then
        handle_error "Tag Creation" "Failed to create tag $TAG_NAME"
    fi
    log_success "Created tag: $TAG_NAME"

    # Push the tag to remote
    if git push origin "$TAG_NAME" 2>/dev/null; then
        log_success "Pushed tag to remote: $TAG_NAME"
    else
        log_warning "Failed to push tag to remote"
    fi

    LATEST_VERSION="$TAG_NAME"
}

# Checkout specific version
checkout_version() {
    log "Checking out version: $LATEST_VERSION..."

    # Handle potential conflicts with generated files
    log_info "Cleaning up generated files that might cause conflicts..."

    # Remove version-info.json if it exists (it's generated during build)
    if [ -f "$SRC_DIR/version-info.json" ]; then
        rm -f "$SRC_DIR/version-info.json"
        log_info "Removed existing version-info.json"
    fi

    # Discard any local changes to tracked files that might conflict
    if git checkout -- . 2>/dev/null; then
        log_info "Discarded local changes to tracked files"
    else
        log_warning "Could not discard local changes (this is usually fine)"
    fi

    # Now checkout the version
    if ! git checkout "$LATEST_VERSION"; then
        handle_error "Git Checkout" "Failed to checkout version $LATEST_VERSION"
    fi
    log_success "Checked out version: $LATEST_VERSION"
}

# Build the application
build_application() {
    log "Building application..."
    
    # Navigate to src directory
    cd "$SRC_DIR" || handle_error "Build" "Failed to navigate to src directory"
    
    # Check if package.json exists
    if [ ! -f "package.json" ]; then
        handle_error "Build" "package.json not found in src directory"
    fi
    
    # Install dependencies
    log_info "Installing dependencies..."
    if ! npm ci; then
        handle_error "Build" "Failed to install dependencies"
    fi
    log_success "Dependencies installed"
    
    # Generate Prisma client
    log_info "Generating Prisma client..."
    if ! npx prisma generate; then
        handle_error "Build" "Failed to generate Prisma client"
    fi
    log_success "Prisma client generated"
    
    # Build the application
    log_info "Building Next.js application..."
    if ! npm run build; then
        handle_error "Build" "Failed to build application"
    fi
    log_success "Application built successfully"
    
    # Navigate back to root directory
    cd .. || handle_error "Build" "Failed to navigate back to root directory"
}

# Manage PM2 process
manage_pm2_process() {
    log "Managing PM2 process..."
    
    # Check if the process is already running
    if pm2 describe "$PM2_APP_NAME" &> /dev/null; then
        log_info "Reloading existing PM2 process: $PM2_APP_NAME"
        if ! pm2 reload "$PM2_APP_NAME"; then
            handle_error "PM2 Management" "Failed to reload PM2 process"
        fi
        log_success "PM2 process reloaded (zero-downtime deployment)"
    else
        log_info "Starting new PM2 process..."
        
        # Stop all processes first (in case there are any running)
        pm2 stop all 2>/dev/null || log_warning "No existing PM2 processes to stop"
        pm2 delete all 2>/dev/null || log_warning "No existing PM2 processes to delete"
        
        # Start the production process
        if ! pm2 start "$PM2_CONFIG_FILE" --only "$PM2_APP_NAME"; then
            handle_error "PM2 Management" "Failed to start PM2 process"
        fi
        log_success "PM2 process started"
    fi
    
    # Save PM2 configuration
    if ! pm2 save; then
        log_warning "Failed to save PM2 configuration"
    else
        log_success "PM2 configuration saved"
    fi
    
    # Show process status
    log_info "Current PM2 status:"
    pm2 status
}

# Check deployment status
check_deployment() {
    log "Checking deployment status..."
    
    # Wait a moment for the process to start
    sleep 3
    
    if pm2 describe "$PM2_APP_NAME" &> /dev/null; then
        STATUS=$(pm2 describe "$PM2_APP_NAME" | grep -E "status.*online" || echo "")
        if [ -n "$STATUS" ]; then
            log_success "Deployment successful! Application is running."
            pm2 describe "$PM2_APP_NAME" | grep -E "(pid|uptime|memory)"
        else
            log_error "Deployment may have issues. Check PM2 logs with: pm2 logs"
        fi
    else
        log_error "Process not found. Check PM2 logs with: pm2 logs"
    fi
}

# Main deployment function
main() {
    START_TIME=$(date +%s)
    
    log "🚀 Starting production deployment..."
    log "Working directory: $WORKING_DIR"
    
    # Parse command line arguments
    CHECK_ONLY=false
    CREATE_TAG=false
    if [[ "$*" == *"--check-only"* ]]; then
        CHECK_ONLY=true
    fi
    if [[ "$*" == *"--create-tag"* ]]; then
        CREATE_TAG=true
    fi

    if [ "$CHECK_ONLY" = true ]; then
        log "Running in check-only mode..."
        check_prerequisites
        log_success "All prerequisites check passed!"
        exit 0
    fi

    # Execute deployment steps
    check_prerequisites
    pull_latest_changes

    # Get latest version or create tag if needed
    if ! get_latest_version 2>/dev/null && [ "$CREATE_TAG" = true ]; then
        log_warning "No version tags found, creating one from package.json version..."
        create_version_tag
    elif [ -z "$LATEST_VERSION" ]; then
        log_error "No version tags found. Use --create-tag flag to create a tag from package.json version"
        exit 1
    fi

    # Checkout the version
    checkout_version
    
    build_application
    manage_pm2_process
    check_deployment
    
    END_TIME=$(date +%s)
    DURATION=$((END_TIME - START_TIME))
    log_success "🎉 Deployment completed successfully in ${DURATION} seconds!"
}

# Run main function
main "$@"
