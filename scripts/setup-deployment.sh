#!/bin/bash

# Setup script for Swapka deployment automation
# Run this once on the production server to set up deployment automation

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Configuration
WORKING_DIR="/home/<USER>/www/swapka"
SCRIPTS_DIR="$WORKING_DIR/scripts"

log() {
    echo -e "${CYAN}[SETUP]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SETUP] ✅ $1${NC}"
}

log_error() {
    echo -e "${RED}[SETUP] ❌ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}[SETUP] ⚠️  $1${NC}"
}

log_info() {
    echo -e "${BLUE}[SETUP] ℹ️  $1${NC}"
}

# Check if we're in the correct directory
check_directory() {
    log "Checking working directory..."
    
    if [ ! -d "$WORKING_DIR" ]; then
        log_error "Working directory does not exist: $WORKING_DIR"
        log_info "Please create the directory or update the WORKING_DIR variable"
        exit 1
    fi
    
    cd "$WORKING_DIR" || exit 1
    log_success "Working directory confirmed: $(pwd)"
}

# Check prerequisites
check_prerequisites() {
    log "Checking prerequisites..."
    
    # Check Node.js
    if ! command -v node &> /dev/null; then
        log_error "Node.js is not installed"
        log_info "Please install Node.js first"
        exit 1
    fi
    log_success "Node.js is available: $(node --version)"
    
    # Check npm
    if ! command -v npm &> /dev/null; then
        log_error "npm is not installed"
        exit 1
    fi
    log_success "npm is available: $(npm --version)"
    
    # Check PM2
    if ! command -v pm2 &> /dev/null; then
        log_warning "PM2 is not installed globally"
        log_info "Installing PM2 globally..."
        npm install -g pm2
        log_success "PM2 installed"
    else
        log_success "PM2 is available: $(pm2 --version)"
    fi
    
    # Check Git
    if ! command -v git &> /dev/null; then
        log_error "Git is not installed"
        exit 1
    fi
    log_success "Git is available: $(git --version)"
}

# Setup deployment files
setup_deployment_files() {
    log "Setting up deployment files..."
    
    # Create scripts directory if it doesn't exist
    if [ ! -d "$SCRIPTS_DIR" ]; then
        mkdir -p "$SCRIPTS_DIR"
        log_success "Created scripts directory"
    fi
    
    # Make deployment scripts executable
    if [ -f "$SCRIPTS_DIR/deploy-production.sh" ]; then
        chmod +x "$SCRIPTS_DIR/deploy-production.sh"
        log_success "Made deploy-production.sh executable"
    else
        log_warning "deploy-production.sh not found"
    fi
    
    if [ -f "$SCRIPTS_DIR/deploy-production.js" ]; then
        log_success "deploy-production.js found"
    else
        log_warning "deploy-production.js not found"
    fi
}

# Install npm dependencies
install_dependencies() {
    log "Installing deployment dependencies..."
    
    if [ -f "package.json" ]; then
        npm install
        log_success "Dependencies installed"
    else
        log_warning "package.json not found in root directory"
        log_info "Deployment scripts may still work, but npm commands won't be available"
    fi
}

# Setup PM2 startup
setup_pm2_startup() {
    log "Setting up PM2 startup..."
    
    # Generate PM2 startup script
    if pm2 startup | grep -q "sudo"; then
        log_info "PM2 startup requires sudo privileges"
        log_info "Please run the following command manually:"
        pm2 startup
    else
        log_success "PM2 startup configured"
    fi
}

# Test deployment check
test_deployment() {
    log "Testing deployment check..."
    
    if [ -f "package.json" ] && npm run deploy:check &> /dev/null; then
        log_success "Deployment check passed"
    elif [ -f "$SCRIPTS_DIR/deploy-production.sh" ]; then
        if "$SCRIPTS_DIR/deploy-production.sh" --check-only &> /dev/null; then
            log_success "Shell deployment check passed"
        else
            log_warning "Shell deployment check failed - this is normal if not all prerequisites are met"
        fi
    else
        log_warning "Could not test deployment - scripts may not be properly set up"
    fi
}

# Main setup function
main() {
    log "🚀 Setting up Swapka deployment automation..."
    
    check_directory
    check_prerequisites
    setup_deployment_files
    install_dependencies
    setup_pm2_startup
    test_deployment
    
    log_success "🎉 Deployment automation setup completed!"
    log_info "You can now use the following commands:"
    log_info "  npm run deploy:prod     - Full production deployment"
    log_info "  npm run deploy:check    - Check prerequisites"
    log_info "  npm run deploy:shell    - Use shell script version"
    log_info ""
    log_info "For more information, see DEPLOYMENT.md"
}

# Run main function
main "$@"
