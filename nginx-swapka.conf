# Nginx site konfigurácia pre Swapka aplikáciu
# Súbor pre /etc/nginx/sites-available/swapka

server {
    listen 80;
    server_name your-domain.com www.your-domain.com;  # Upravte podľa vašej domény

    # Bezpečnostné h<PERSON>ky
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;

    # KRITICKÉ NASTAVENIA PRE FILE UPLOADS
    client_max_body_size 20M;          # Maximálna veľ<PERSON>ť requestu
    client_body_buffer_size 128k;      # Buffer pre request body
    client_body_timeout 60s;           # Timeout pre čítanie request body
    client_header_timeout 60s;         # Timeout pre čítanie headerov
    send_timeout 60s;                  # Timeout pre odosielanie odpovede

    # Proxy buffer nastavenia
    proxy_buffering on;
    proxy_buffer_size 4k;
    proxy_buffers 8 4k;
    proxy_busy_buffers_size 8k;
    proxy_max_temp_file_size 1024m;
    
    # Proxy timeout nastavenia
    proxy_connect_timeout 60s;
    proxy_send_timeout 60s;
    proxy_read_timeout 60s;

    # Špeciálne nastavenia pre upload endpointy
    location /api/cloudinary/upload {
        # Zvýšené limity špecificky pre upload
        client_max_body_size 25M;
        client_body_timeout 120s;
        proxy_request_buffering off;  # Vypnutie bufferovanie pre streaming uploads
        
        # Proxy nastavenia
        proxy_pass http://127.0.0.1:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        
        # Timeout nastavenia pre upload
        proxy_connect_timeout 120s;
        proxy_send_timeout 120s;
        proxy_read_timeout 120s;

        # Logging pre debugging
        access_log /var/log/nginx/swapka-upload.log;
        error_log /var/log/nginx/swapka-upload-error.log;
    }

    # Všetky ostatné API endpointy
    location /api/ {
        proxy_pass http://127.0.0.1:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }

    # Statické súbory Next.js s optimalizovaným cachovaním
    location /_next/static/ {
        proxy_pass http://127.0.0.1:3000;
        expires 1y;
        add_header Cache-Control "public, immutable";
        
        # Gzip pre statické súbory
        gzip_static on;
    }

    # Favicon a robots.txt
    location ~ ^/(favicon\.ico|robots\.txt|sitemap\.xml)$ {
        proxy_pass http://127.0.0.1:3000;
        expires 1d;
        add_header Cache-Control "public";
    }

    # Všetky ostatné requesty
    location / {
        proxy_pass http://127.0.0.1:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }

    # Custom error pages
    error_page 413 @413_error;
    location @413_error {
        return 413 '{"error": "Súbor je príliš veľký", "details": ["Maximálna povolená veľkosť je 20MB"]}';
        add_header Content-Type application/json always;
    }

    error_page 502 503 504 @50x_error;
    location @50x_error {
        return 502 '{"error": "Server je dočasne nedostupný", "details": ["Skúste to znovu neskôr"]}';
        add_header Content-Type application/json always;
    }

    # Logging
    access_log /var/log/nginx/swapka-access.log;
    error_log /var/log/nginx/swapka-error.log;
}

# HTTPS konfigurácia (ak máte SSL certifikát)
# server {
#     listen 443 ssl http2;
#     server_name your-domain.com www.your-domain.com;
#
#     ssl_certificate /path/to/your/certificate.crt;
#     ssl_certificate_key /path/to/your/private.key;
#     ssl_protocols TLSv1.2 TLSv1.3;
#     ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384;
#     ssl_prefer_server_ciphers off;
#
#     # Rovnaké nastavenia ako pre HTTP server
#     client_max_body_size 20M;
#     # ... ostatné nastavenia
# }
